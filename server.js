require("dotenv").config()
const { loadConfiguration, configurationJSON } = require("@krushal-it/common-core")
loadConfiguration(process.env.ENV_JSON)

const https = require("https")
const cron = require("node-cron")
const fs = require("fs")
const { wsServer } = require('./socket')
const { loadClassificationConfiguration, loadQueryTemplates, getCompiledQuery } = require("@krushal-it/back-end-lib")
const { dbConnections, initializeAllRepositories } = require("@krushal-it/ah-orm")
const queryTemplates = require("./database_utils/query-template/index")
const { initiatePuppeter, initiateCleanUpActionOnExit } = require("./utils/common/puppeteer/index")
const { validateEnvSetup } = require("./utils/validateEnvSchema")
const { initializeRuleEngine } = require("./services/rules/rules.service")

loadQueryTemplates({ custom_templates: queryTemplates })
loadQueryTemplates()

/*
  Validate env against schemaEnv.json file before server starts
*/
validateEnvSetup()

/* 
Compiles all the pdf templates present in the directory './pdf-templates/templates'.
Attaches it reference to the global variable with the key name as 'pdfTemplates'

a particular compiled template can be accessed via global as "global.pdfTemplates['template_file_name_with_extension']"

example global.pdfTemplates['animal-farmer-summary.hbs']
*/

const Register = require("./services/register")
const { record_statuses } = require("@krushal-it/back-end-lib/ENUMS")
const register = new Register()

const app = register.getServer()

const LISTEN_PORT = configurationJSON().DEPLOYMENT.LISTEN_PORT
const httpsEnabled = configurationJSON().DEPLOYMENT.HTTPS_ENABLED

require("./utils/configs/cron.config").forEach(({ schedule, func }) => {
  cron.schedule(schedule, func)
})

if (httpsEnabled === 1) {
  const options = {
    key: fs.readFileSync("/ssl/domain.key"),
    cert: fs.readFileSync("/ssl/signed.cert.chain"),
    port: LISTEN_PORT
  }

  const server = https.createServer(options, app)
  const httpsServer = server.listen(LISTEN_PORT)

  httpsServer.on('listening', async () => {
    try {
      await initiatePuppeter()
      await initializeAllRepositories(dbConnections())
      await loadClassificationConfiguration()
      await initializeRuleEngine()
      await loadAllRefMedicineToGlobal(dbConnections());

      console.log(
        'Server starting listing on port : ' + LISTEN_PORT + ' over https'
      )
      httpsServer.on('upgrade', (request, socket, head) => {
        wsServer.handleUpgrade(request, socket, head, (socket) => {
          wsServer.emit('connection', socket, request);
        });
      });

    } catch (error) {
      console.log("ERROR:", error)
    }
  })
} else {
  const httpServer = app.listen(LISTEN_PORT);
  httpServer.on('listening', async () => {
    try {
      await initiatePuppeter()
      await initializeAllRepositories(dbConnections())
      await loadClassificationConfiguration(dbConnections())
      await initializeRuleEngine(dbConnections())
      await loadAllRefMedicineToGlobal(dbConnections());
      // Upgrade HTTP server to handle WebSocket
      httpServer.on('upgrade', (request, socket, head) => {
        wsServer.handleUpgrade(request, socket, head, (socket) => {
          wsServer.emit('connection', socket, request);
        });
      });
      console.log('Server starting listing on port : ' + LISTEN_PORT + ' over http')
    } catch (error) {
      console.log(error)
    }
  })
}



async function loadAllRefMedicineToGlobal(connection) {
  const repository = connection.main.manager.getRepository('ref_medicine');
  const medicines = await repository.find({
    select: ["medicine_id", "medicine_name"],
    where: { active: record_statuses.ACTIVE }
  });

  // Store as a plain object for global
  const refObj = {};
  for (const ref of medicines) {
    refObj[ref.medicine_id] = ref.medicine_name;
  }
  global.ref_medicines = refObj;
  console.log("loadAllRefMedicineToGlobal", global.ref_medicines);

}
initiateCleanUpActionOnExit()
