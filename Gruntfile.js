module.exports = function(grunt) {
  grunt.initConfig({
    terser: {
      target: {
        files: [
          {
            expand: true,
            src: ["**/*.js", "!node_modules/**", "!Gruntfile.js", "!build/**", "!package-lock.json", "!env.json", "!.env"],
            dest: "build"
          }
        ]
      }
    },
    copy: {
      files: {
        src: ["**/*.hbs", "package.json", "!**/*.js", "**/*.json", "!package-lock.json", "!env.json", "!.env", "!node_modules/**", "Makefile", ".npm*", "Dockerfile*", ".gitignore*", "!Gruntfile.js", "!build/**"],
        dest: "build", // destination folder
        expand: true // required when using cwd
      }
    }
  })
  grunt.loadNpmTasks("grunt-terser")
  grunt.loadNpmTasks("grunt-contrib-copy")
  grunt.registerTask("default", ["copy", "terser"])
}
