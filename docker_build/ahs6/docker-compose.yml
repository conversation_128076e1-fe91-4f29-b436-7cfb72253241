name: ahs6_back_end_app
version: '3.9'
services:
  ahs6_back_end:
    container_name: ahs6-back-end
    image: registry.gitlab.com/krushal-it/**********************/ahs6/back-end:${IMAGE_TAG:-latest}
    env_file:
      - ${KRUSHAL_HOME_LOCATION}/envs/ahs6/coreapp.env
    volumes:
      - ${KRUSHAL_HOME_LOCATION}/repos/krushal_ssl_certificates/key/domain.key:/ssl/domain.key
      - ${KRUSHAL_HOME_LOCATION}/repos/krushal_ssl_certificates/key/signed.cert.chain:/ssl/signed.cert.chain
      - ${KRUSHAL_HOME_LOCATION}/logs/back-end:/app/logs
    build:
      context: ${KRUSHAL_HOME_LOCATION}/repos/ahs6/back_end
      args:
        GITLAB_AUTH_TOKEN: ${READ_KRUSHAL_IT_GITLAB_AUTH_TOKEN}
    command: ["/bin/sh", "-lc", "npm run prod"]
    stdin_open: true
    tty: true

networks:
  default:
    name: krushal_network
    external: true