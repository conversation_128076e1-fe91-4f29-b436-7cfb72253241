# back_end

# Steps to create minified version of projectS

# STEP 1: install these dependencies of grunt

npm i grunt grunt-contrib-uglify grunt-contrib-clean grunt-json-minification

# Step 2: Run this command

grunt both

#--------------------------------------------------------------------#

# To create docker image of minified project

docker build --no-cache -t -name- . (the last full stop is very important)

# To run the docker image

docker run -p 3001:3001 -docker_image_id-

# To push the docker image to the GitLab 
STEP :1- docker login
(with docker credentials)

STEP :2- docker tag IMAGE_NAME registry.gitlab.com/krushal-it/**********************/IMAGE_NAME

STEP :3- docker push registry.gitlab.com/krushal-it/**********************/IMAGE_NAME


# Note

env file for the production is different
