#@krushal-oc:registry=https://gitlab.com/api/v4/packages/npm/
@krushal-it:registry=https://gitlab.com/api/v4/packages/npm/
#@krushal-it:registry=https://gitlab.com/api/v4/projects/41140174/packages/npm/

# Add the token for the scoped packages URL. This will allow you to download
# `@my-org/` packages from private projects.
//gitlab.com/api/v4/packages/npm/:_authToken="${GITLAB_AUTH_TOKEN}"

# Add token for uploading to the registry. Replace <your-project-id>
# with the project you want your package to be uploaded to.
#krushal-oc below
#//gitlab.com/api/v4/projects/42118201/packages/npm/:_authToken="${GITLAB_AUTH_TOKEN}"
#krushal-it below
#//gitlab.com/api/v4/projects/41140174/packages/npm/:_authToken="${GITLAB_AUTH_TOKEN}"
