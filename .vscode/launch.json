{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "name": "Attach to Local",
      "request": "attach",
      "address": "localhost",
      "port": 34001,
      "localRoot": "${workspaceFolder}",
      "remoteRoot": "${workspaceFolder}",
      "trace": true,
      "sourceMaps": true,
      "outputCapture": "std",
      "skipFiles": [
        "<node_internals>/**"
      ],
    },
    {
      "address": "localhost",
      "localRoot": "${workspaceFolder}",
      "name": "Attach to Local Docker",
      "port": 34001,
      "remoteRoot": "/home/<USER>/app",
      "request": "attach",
      "skipFiles": [
        "<node_internals>/**"
      ],
      "type": "node",
      "outputCapture": "std"
    },
    {
      "address": "dev1.krushal.in",
      "localRoot": "${workspaceFolder}",
      "name": "Attach to Dev1 Docker",
      "port": 34001,
      "remoteRoot": "/home/<USER>/app",
      "request": "attach",
      "skipFiles": [
        "<node_internals>/**"
      ],
      "type": "node",
      "outputCapture": "std"
    },
    {
      "address": "dev2.krushal.in",
      "localRoot": "${workspaceFolder}",
      "name": "Attach to Dev2 Docker",
      "port": 34001,
      "remoteRoot": "/home/<USER>/app",
      "request": "attach",
      "skipFiles": [
        "<node_internals>/**"
      ],
      "type": "node",
      "outputCapture": "std"
    },
    {
      "address": "ahs1.krushal.in",
      "localRoot": "${workspaceFolder}",
      "name": "Attach to Stage1 Docker",
      "port": 34001,
      "remoteRoot": "/app",
      "request": "attach",
      "skipFiles": [
        "<node_internals>/**"
      ],
      "type": "node",
      "outputCapture": "std"
    },
    {
      "address": "ahs2.krushal.in",
      "localRoot": "${workspaceFolder}",
      "name": "Attach to Stage2 Docker",
      "port": 34001,
      "remoteRoot": "/app",
      "request": "attach",
      "skipFiles": [
        "<node_internals>/**"
      ],
      "type": "node",
      "outputCapture": "std"
    },
    {
      "address": "appprod.krushal.in",
      "localRoot": "${workspaceFolder}",
      "name": "Attach to Prod Docker",
      "port": 34001,
      "remoteRoot": "/app",
      "request": "attach",
      "skipFiles": [
        "<node_internals>/**"
      ],
      "type": "node",
      "outputCapture": "std",
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Launch Program",
      "skipFiles": [
        "<node_internals>/**"
      ],
      "program": "${workspaceFolder}/server.js",
      "console": "externalTerminal"
    }
  ]
}