{"gitflow4code.features": [{"name": "feature/route_details", "base": "release/rel_farmer_onboarding_v1"}, {"name": "feature/route_plan_details", "base": "main"}, {"name": "feature/20230324_rs_db_integration", "base": "release/rel_farmer_onboarding_v1"}, {"name": "feature/task_mgmt_v3", "base": "release/rel_farmer_onboarding_v1"}, {"name": "feature/farmer_level_task", "base": "release/requirement_15"}, {"name": "feature/api_health_check", "base": "develop"}, {"name": "feature/active_animals", "base": "release/requirement_16"}, {"name": "feature/farmer-data-collection", "base": "release/requirement_17"}, {"name": "feature/leniency_factor", "base": "develop"}, {"name": "feature/reactivate-animals", "base": "develop"}, {"name": "feature/entity_geography_active_flag", "base": "develop"}, {"name": "feature/20231117_RS_r_310_desc_more_data_on_screen", "base": "release_3.1.0"}, {"name": "feature/redmine_690", "base": "release_3.1.0"}, {"name": "feature/20231208_f_r400_desc_oc_hscalc_pricingcal", "base": "release_4.0.0"}, {"name": "feature/20231214_RS_f_m_desc_ongoing_oc_upgrades", "base": "main"}], "gitflow4code.releases": [{"name": "release/rel_offline_poc", "base": "main"}, {"name": "release/rel_oc_v1", "base": "develop"}], "gitflow4code.hotfixes": [{"name": "hotfix/req_19_fix", "base": "develop"}, {"name": "hotfix/staff-update-transaction", "base": "develop"}], "workbench.colorCustomizations": {"activityBar.background": "#332E08", "titleBar.activeBackground": "#48400B", "titleBar.activeForeground": "#FCFBEF"}}