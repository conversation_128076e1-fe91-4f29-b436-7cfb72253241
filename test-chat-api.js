const axios = require('axios');

// Test configuration
const BASE_URL = 'http://localhost:3001/api'; // Adjust port if needed
const CHAT_ENDPOINT = '/chat/query';

// Test data
const testCases = [
  {
    name: 'Test without session_id (new session)',
    payload: {
      symptoms: 'The cow has reduced milk production and seems lethargic'
    },
    expectedApiCall: 'http://127.0.0.1:5021/api/chat/start'
  },
  {
    name: 'Test with session_id (existing session)',
    payload: {
      symptoms: 'The cow also has a slight fever',
      session_id: 'test-session-123'
    },
    expectedApiCall: 'http://127.0.0.1:5021/api/chat/continue'
  }
];

async function testChatAPI() {
  console.log('🧪 Testing Chat API Implementation\n');

  for (const testCase of testCases) {
    console.log(`📋 ${testCase.name}`);
    console.log(`   Payload:`, JSON.stringify(testCase.payload, null, 2));
    
    try {
      const response = await axios.post(`${BASE_URL}${CHAT_ENDPOINT}`, testCase.payload, {
        headers: {
          'Content-Type': 'application/json',
          // Add any required authentication headers here
          'token': 'your-test-token-here', // Replace with actual token if needed
        }
      });

      console.log(`   ✅ Status: ${response.status}`);
      console.log(`   📤 Expected API call to: ${testCase.expectedApiCall}`);
      console.log(`   📥 Response:`, JSON.stringify(response.data, null, 2));
      
      // Verify the correct API endpoint was used
      if (response.data.api_endpoint_used === testCase.expectedApiCall) {
        console.log(`   ✅ Correct API endpoint was called`);
      } else {
        console.log(`   ❌ Wrong API endpoint called. Expected: ${testCase.expectedApiCall}, Got: ${response.data.api_endpoint_used}`);
      }

    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
      if (error.response) {
        console.log(`   📥 Error Response:`, JSON.stringify(error.response.data, null, 2));
      }
    }
    
    console.log('\n' + '─'.repeat(80) + '\n');
  }
}

// Test function to verify the service logic without making actual HTTP calls
function testServiceLogic() {
  console.log('🔧 Testing Service Logic (Unit Test Style)\n');

  // Mock the ChatService class
  class MockChatService {
    async create(data, params) {
      const { symptoms, session_id } = data;

      if (!symptoms) {
        return { error: "symptoms is required" };
      }

      const payload = { symptoms };
      if (session_id) {
        payload.session_id = session_id;
      }

      const apiUrl = session_id 
        ? "http://127.0.0.1:5021/api/chat/continue"
        : "http://127.0.0.1:5021/api/chat/start";

      // Mock response instead of making actual HTTP call
      return {
        return_code: 0,
        data: { message: "Mock response", session_id: session_id || "new-session-123" },
        session_id: session_id || "new-session-123",
        api_endpoint_used: apiUrl
      };
    }
  }

  const chatService = new MockChatService();

  testCases.forEach(async (testCase, index) => {
    console.log(`📋 Test ${index + 1}: ${testCase.name}`);
    
    try {
      const result = await chatService.create(testCase.payload, {});
      console.log(`   ✅ Logic test passed`);
      console.log(`   📤 Would call: ${result.api_endpoint_used}`);
      console.log(`   📥 Mock response:`, JSON.stringify(result, null, 2));
      
      if (result.api_endpoint_used === testCase.expectedApiCall) {
        console.log(`   ✅ Correct API endpoint logic`);
      } else {
        console.log(`   ❌ Wrong API endpoint logic`);
      }
    } catch (error) {
      console.log(`   ❌ Logic test failed: ${error.message}`);
    }
    
    console.log('\n' + '─'.repeat(50) + '\n');
  });
}

// Run tests
async function runTests() {
  console.log('🚀 Starting Chat API Tests\n');
  console.log('=' .repeat(80) + '\n');
  
  // First run logic tests (these will always work)
  testServiceLogic();
  
  console.log('=' .repeat(80) + '\n');
  
  // Then try integration tests (these require the server to be running)
  console.log('🌐 Integration Tests (requires server to be running)\n');
  await testChatAPI();
  
  console.log('✨ Tests completed!');
}

// Export for use in other files or run directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testChatAPI, testServiceLogic, runTests };
