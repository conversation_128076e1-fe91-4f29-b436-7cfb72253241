const { ACTIVE } = require("@krushal-it/back-end-lib/utils/constant");
/**
 * Typeorm placeholder
 * 1. staff_id
 * Hbs placeholder
 * 1. thumbnail
 */
module.exports = `
SELECT 
    staff.staff_name_l10n,
    staff.staff_id,
    staff.mobile_number,
    staff.active as active_status_id,
    staff.email_address,
    staff.staff_visual_id,
    staff.staff_type_id,
    staff_pan.value_string_256 as staff_pan,
    staff_aadhar.value_string_256 as staff_aadhar,
    CASE 
        WHEN 
            staff_classification.value_reference_id = 1000105001 
        THEN 
            'ONLINE'
        ELSE
            'OFFLINE'
    END as app_user_type,
    ref_staff_type.reference_name_l10n as staff_type_l10n,
{{#ifEquals thumbnail 1}}
    staff_thumbnail.document_id as staff_thumbnail_id,
{{/ifEquals}}    
    ref_active.reference_name_l10n as active_status_l10n
FROM main.staff
    LEFT JOIN main.staff_classification
        ON
            staff_classification.staff_id = staff.staff_id
            AND
            staff_classification.classifier_id = 2000000137
            AND
            staff_classification.active = ${ACTIVE}
    JOIN main.ref_reference AS ref_active
        ON 
            ref_active.reference_id = staff.active
            AND 
            ref_active.active = ${ACTIVE}
    JOIN main.ref_reference AS ref_staff_type
        ON 
            ref_staff_type.reference_id = staff.staff_type_id
            AND 
            ref_staff_type.active = ${ACTIVE}

{{#ifEquals thumbnail 1}}
    LEFT JOIN (
        SELECT 
            d.entity_1_type_id, 
            d.entity_1_entity_uuid, 
            d.document_type_id,
            MAX(created_at) max_created_at
        FROM 
            main.document d
        WHERE 
            d.document_type_id = 1000260004
        GROUP BY 
            d.entity_1_type_id, 
            d.entity_1_entity_uuid, 
            d.document_type_id
    ) AS mdoc
    ON 
        mdoc.entity_1_entity_uuid = staff.staff_id 
        and 
        mdoc.entity_1_type_id = staff.staff_type_id 
    LEFT JOIN 
        main.document as staff_thumbnail
        ON 
            mdoc.entity_1_entity_uuid = staff_thumbnail.entity_1_entity_uuid
            AND 
            staff_thumbnail.entity_1_entity_uuid = staff.staff_id
            AND 
            staff_thumbnail.created_at = mdoc.max_created_at
{{/ifEquals}}
LEFT JOIN main.staff_classification as staff_pan
ON
    staff_pan.staff_id = staff.staff_id
    AND
    staff_pan.classifier_id = 2000000223
    AND
    staff_pan.active = ${ACTIVE}
LEFT JOIN main.staff_classification as staff_aadhar
    ON
        staff_aadhar.staff_id = staff.staff_id
        AND
        staff_aadhar.classifier_id = 2000000224
        AND
        staff_aadhar.active = ${ACTIVE}
WHERE 
    staff.staff_id = :staff_id
`;