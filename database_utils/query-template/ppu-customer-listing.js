const { ACTIVE, VILLAGE_TYPE_ID, CUSTOMER_TERMS_AND_CONDITION_STATUS, PPU_PARAVET_TO_FARMER, VILLAGE_CLASSIFIER,FARMER_TO_ANIMAL } = require("@krushal-it/back-end-lib/utils/constant");

module.exports = `
WITH ANIMAL_COUNT as (
    SELECT c.customer_id, count(animal.animal_id) AS animal_count FROM main.customer AS c
        LEFT JOIN main.entity_relationship AS er_animal ON er_animal.entity_relationship_type_id=${FARMER_TO_ANIMAL}
        AND er_animal.entity_1_entity_uuid = c.customer_id AND er_animal.active=${ACTIVE}
        LEFT JOIN main.animal ON animal.animal_id=er_animal.entity_2_entity_uuid AND animal.active = ${ACTIVE}
        WHERE c.active=${ACTIVE}
        GROUP BY c.customer_id
)

SELECT
	c.customer_id,
	c.customer_name_l10n,
	c.mobile_number,
	c.customer_visual_id,
	ANIMAL_COUNT.animal_count as cattle_count,
	c.updated_at,
	c.created_at,
	REF_VILLAGE.village_name_l10n,
	REF_TALUK.taluk_name_l10n
FROM
	main.customer AS c
	LEFT JOIN main.entity_relationship AS er ON er.entity_2_entity_uuid = c.customer_id
		AND er.entity_relationship_type_id = ${PPU_PARAVET_TO_FARMER}
	LEFT JOIN ANIMAL_COUNT on ANIMAL_COUNT.customer_id = c.customer_id
	LEFT JOIN main.customer_classification AS CUSTOMER_CLASSIFICATION ON CUSTOMER_CLASSIFICATION.customer_id = c.customer_id
		AND CUSTOMER_CLASSIFICATION.classifier_id = ${VILLAGE_CLASSIFIER}
	LEFT JOIN main.CUSTOMER_CLASSIFICATION AS TERMS ON TERMS.customer_id = c.customer_id
		AND TERMS.classifier_id = ${CUSTOMER_TERMS_AND_CONDITION_STATUS}
	LEFT JOIN main.REF_VILLAGE ON REF_VILLAGE.village_id = CUSTOMER_CLASSIFICATION.value_reference_id
	LEFT JOIN main.REF_TALUK ON REF_TALUK.taluk_id = REF_VILLAGE.taluk_id
	LEFT JOIN main.REF_DISTRICT ON REF_DISTRICT.district_id = REF_TALUK.district_id
	LEFT JOIN main.REF_STATE ON REF_STATE.state_id = REF_DISTRICT.state_id
	LEFT JOIN main.ENTITY_GEOGRAPHY ON ENTITY_GEOGRAPHY.geography_id = REF_VILLAGE.village_id
		AND ENTITY_GEOGRAPHY.geography_type_id = ${VILLAGE_TYPE_ID}
		AND ENTITY_GEOGRAPHY.entity_type_id = ${VILLAGE_TYPE_ID}
		AND ENTITY_GEOGRAPHY.ACTIVE = ${ACTIVE}
WHERE
	er.entity_1_entity_uuid = :staff_id
        
	{{#if f_farmer_name}}
	AND (
		UPPER(c.customer_name_l10n->>'ul') LIKE UPPER(:f_farmer_name) OR 
		UPPER(c.customer_name_l10n->>'mr') LIKE UPPER(:f_farmer_name) OR
		UPPER(c.customer_name_l10n->>'en') LIKE UPPER(:f_farmer_name) OR 
		UPPER(c.customer_name_l10n->>'hi') LIKE UPPER(:f_farmer_name)
	)
{{/if}}

{{#if f_village_id_array}}
	AND REF_VILLAGE.village_id  IN (:...f_village_id_array)
{{/if}}
`
