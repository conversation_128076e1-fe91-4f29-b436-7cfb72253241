module.exports = `SELECT
village_id,
COALESCE(village_name_l10n->>:language, village_name_l10n->>'ul') as village_name,
taluk_id,
COALESCE(taluk_name_l10n->>:language, taluk_name_l10n->>'ul') as taluk_name,
district_id,
COALESCE(district_name_l10n->>:language, district_name_l10n->>'ul') as district_name,
state_id,
COALESCE(state_name_l10n->>:language, state_name_l10n->>'ul') as state_name
FROM main.ref_sdtv_view where
{{#ifEquals type 'village'}}
STARTS_WITH(LOWER(COALESCE(village_name_l10n->>'en', village_name_l10n->>'ul')),LOWER(:searchtext))  
{{/ifEquals}}
{{#ifEquals type 'taluk'}}
STARTS_WITH(LOWER(COALESCE(taluk_name_l10n->>'en', taluk_name_l10n->>'ul')),LOWER(:searchtext))  
{{/ifEquals}}
{{#ifEquals type 'district'}}
STARTS_WITH(LOWER(COALESCE(district_name_l10n->>'en', district_name_l10n->>'ul')),LOWER(:searchtext))  
{{/ifEquals}}
{{#ifEquals type 'state'}}
STARTS_WITH(LOWER(COALESCE(state_name_l10n->>'en', state_name_l10n->>'ul')),LOWER(:searchtext))  
{{/ifEquals}}`
