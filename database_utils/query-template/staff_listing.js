const { CONST_ID_TYPE_VILLAGE, ACTIVE } = require("@krushal-it/back-end-lib/utils/constant");
/**
 * Template placeholders
 * 1. f_staff_village
 * 2. f_staff_type
 * 
 * Typeorm placeholder
 * 1. f_staff_village
 * 2. f_staff_type
 */
module.exports = `
{{#or f_staff_village f_staff_district f_staff_taluk f_staff_state}}
WITH UNIQUE_STAFF AS
(
	SELECT distinct(staff.staff_id) as staff_id FROM main.staff
	LEFT JOIN
	main.entity_geography AS egeo 
		ON egeo.entity_uuid = staff_id
		AND egeo.geography_type_id = ${CONST_ID_TYPE_VILLAGE}
		AND egeo.active = ${ACTIVE}
	LEFT JOIN main.ref_sdtv_view as rsv on rsv.village_id = egeo.geography_id
		WHERE 1 = 1
		{{#if f_staff_village}}
		AND rsv.village_id in(:...f_staff_village)
		{{/if}}
		{{#if f_staff_district}}
		AND rsv.district_id in(:...f_staff_district)
		{{/if}}
		{{#if f_staff_state}}
		AND rsv.state_id in(:...f_staff_state)
		{{/if}}
		{{#if f_staff_taluk}}
		AND rsv.taluk_id in(:...f_staff_taluk)
		{{/if}}
		)
		{{/or}}
SELECT 
	staff.staff_id as staff_id,
	staff.staff_name_l10n as staff_name_l10n,
	staff.mobile_number as mobile_number,
	ref_staff_type.reference_id as staff_type_id,
	ref_staff_type.reference_name_l10n as staff_type_l10n,
	staff.active,
	staff.email_address,
	staff_pan.value_string_256 as staff_pan,
    staff_aadhar.value_string_256 as staff_aadhar
FROM main.staff
	LEFT JOIN main.ref_reference AS ref_staff_type 
		ON ref_staff_type.reference_id = staff.staff_type_id
	LEFT JOIN main.staff_classification as staff_pan
		ON
			staff_pan.staff_id = staff.staff_id
			AND
			staff_pan.classifier_id = 2000000223
			AND
			staff_pan.active = ${ACTIVE}
	LEFT JOIN main.staff_classification as staff_aadhar
			ON
				staff_aadhar.staff_id = staff.staff_id
				AND
				staff_aadhar.classifier_id = 2000000224
				AND
				staff_aadhar.active = ${ACTIVE}
WHERE 1=1
{{#or f_staff_village f_staff_district f_staff_taluk f_staff_state}}
  AND staff.staff_id IN (SELECT staff_id FROM UNIQUE_STAFF )
{{/or}}
	
	{{#if f_staff_type}}
	AND staff.staff_type_id IN (:...f_staff_type)
	{{/if}}

	{{#if f_active_status}}
	AND staff.active = :f_active_status
	{{/if}}

	{{#if f_staff_mobile}}
	AND staff.mobile_number LIKE :f_staff_mobile
	{{/if}}
	{{#if f_staff_name}}
	AND(
	UPPER(staff.staff_name_l10n->>'ul') LIKE UPPER(:f_staff_name) OR 
    UPPER(staff.staff_name_l10n->>'mr') LIKE UPPER(:f_staff_name) OR
    UPPER(staff.staff_name_l10n->>'en') LIKE UPPER(:f_staff_name) OR 
    UPPER(staff.staff_name_l10n->>'hi') LIKE UPPER(:f_staff_name))
	{{/if}}
ORDER BY
		staff.created_at desc
`