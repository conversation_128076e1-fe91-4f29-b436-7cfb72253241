const { ACTIVE } = require("@krushal-it/back-end-lib/utils/constant");

module.exports = `
select  
null as farmer_name,
null as ear_tag,
null as SR_NO,
tl.Amount,
tl.entity_uuid,
tl.entity_type_id,
tl.transaction_type,
tl.transaction_description,
tl.transaction_status,
tl.transaction_date
from
main.transaction_ledger as tl 
where tl.staff_uuid = :staff_uuid and
tl.entity_type_id is null  and account_id = 'KRUSHAL'
{{#if search_by_FarmerName_SRNO}}
 and 1 != 1
{{/if}}
{{#if transaction_type}} 
and tl.transaction_type IN (:...transaction_type) 
{{/if}}

UNION 
select  
c.customer_name_l10n as farmer_name,
a.value_string_256 as ear_tag,
ccc.value_string_256 as SR_NO,
tl.Amount,
tl.entity_uuid,
tl.entity_type_id,
tl.transaction_type,
tl.transaction_description,
tl.transaction_status,
tl.transaction_date
from
main.transaction_ledger as tl 
left join  main.care_calendar_classification as ccc on tl.entity_uuid = ccc.care_calendar_id
and ccc.classifier_id = **********
left join main.care_calendar as cc on tl.entity_uuid = cc.care_calendar_id and cc.active = ${ACTIVE}
left join   main.animal_classification as a on cc.entity_uuid = a.animal_id
and a.classifier_id = **********
left join main.entity_relationship as er on er.entity_relationship_type_id = ********** and er.entity_2_entity_uuid = a.animal_id and er.active = ${ACTIVE}
left join main.customer as c on c.customer_id = CASE  WHEN er.entity_1_entity_uuid is not null THEN er.entity_1_entity_uuid  ELSE cc.entity_uuid END and c.active = ${ACTIVE}
where tl.staff_uuid = :staff_uuid and tl.entity_type_id = ********** and account_id = 'KRUSHAL'
{{#if search_by_FarmerName_SRNO}}
 AND (
    UPPER(c.customer_name_l10n->>'ul') LIKE UPPER(:search_by_FarmerName_SRNO) OR 
    UPPER(c.customer_name_l10n->>'mr') LIKE UPPER(:search_by_FarmerName_SRNO) OR
    UPPER(c.customer_name_l10n->>'en') LIKE UPPER(:search_by_FarmerName_SRNO) OR 
    UPPER(c.customer_name_l10n->>'hi') LIKE UPPER(:search_by_FarmerName_SRNO) OR 
    UPPER(ccc.value_string_256) LIKE UPPER(:search_by_FarmerName_SRNO)
 )
{{/if}}
{{#if transaction_type}} 
and tl.transaction_type IN (:...transaction_type) 
{{/if}}

`
