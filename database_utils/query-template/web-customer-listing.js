const { VILLAGE_CLASSIFIER, VILLAGE_TYPE_ID, PARAVET, ACTIVE } = require("@krushal-it/back-end-lib/utils/constant");

module.exports = `
     WITH NOT_SUBSCRIBED AS (
        SELECT 
            distinct customer.customer_id 
        FROM main.customer
            LEFT JOIN main.entity_relationship as er 
                ON 
                    customer.customer_id = er.entity_1_entity_uuid
                    AND 
                    er.entity_relationship_type_id = 1000210004
            LEFT JOIN main.animal as animal_data 
                ON 
                    animal_data.animal_id = er.entity_2_entity_uuid
            LEFT JOIN main.animal_classification as ac 
                ON 
                    ac.animal_id = animal_data.animal_id
                    AND 
                    ac.classifier_id = 2000000124
        WHERE
            customer.active = 1000100001
            AND
			animal_data.active = 1000100001
			AND
            ac.value_date is null
     ),
     CUSTOMER_LISTING AS (
        SELECT
            customer.customer_id, 
            customer.customer_name_l10n, 
            customer.mobile_number,
            customer.customer_visual_id,
            customer.updated_at,
            customer.created_at,
            REF_VILLAGE.village_name_l10n,
            REF_TALUK.taluk_name_l10n,
            STAFF.staff_name_l10n as paravet_name,
            COALESCE(TERMS.value_reference_id,1000105002) as terms
        FROM		
            main.CUSTOMER 
                LEFT JOIN 
                    main.CUSTOMER_CLASSIFICATION 
                    ON 
                        CUSTOMER_CLASSIFICATION.customer_id = CUSTOMER.customer_id
                        AND 
                        CUSTOMER_CLASSIFICATION.classifier_id = ${VILLAGE_CLASSIFIER}
                LEFT JOIN 
                    main.CUSTOMER_CLASSIFICATION as TERMS
                    ON 
                        TERMS.customer_id = CUSTOMER.customer_id
                        AND 
                        TERMS.classifier_id = 2000000207
                LEFT JOIN 
                    main.REF_VILLAGE 
                    ON 
                        REF_VILLAGE.village_id = CUSTOMER_CLASSIFICATION.value_reference_id
                LEFT JOIN 
                    main.REF_TALUK 
                    ON 
                        REF_TALUK.taluk_id = REF_VILLAGE.taluk_id
                LEFT JOIN 
                    main.REF_DISTRICT 
                    ON 
                        REF_DISTRICT.district_id = REF_TALUK.district_id
                LEFT JOIN 
                    main.REF_STATE 
                    ON 
                        REF_STATE.state_id = REF_DISTRICT.state_id
                LEFT JOIN 
                    main.ENTITY_GEOGRAPHY 
                    ON 
                        ENTITY_GEOGRAPHY.geography_id = REF_VILLAGE.village_id
                        AND 
                        ENTITY_GEOGRAPHY.geography_type_id = ${VILLAGE_TYPE_ID}
                        AND 
                        ENTITY_GEOGRAPHY.entity_type_id = ${PARAVET}
                        AND 
                        ENTITY_GEOGRAPHY.ACTIVE = ${ACTIVE}
                LEFT JOIN 
                    main.STAFF 
                    ON 
                        STAFF.staff_id = ENTITY_GEOGRAPHY.entity_uuid
    WHERE
        customer.active = ${ACTIVE}
       
        {{#if terms_condition }}
            {{#ifEquals terms_condition 'Accepted'}}
            AND (
                TERMS.value_reference_id  = 1000105001
            )
            {{else}}
            AND (
                TERMS.value_reference_id  = 1000105002 OR TERMS.value_reference_id is null
            )
            {{/ifEquals}}
        {{/if}}
        {{#if f_farmer_name}}
            AND (
                
                UPPER(CUSTOMER.customer_name_l10n->>'ul') LIKE UPPER(:f_farmer_name) OR 
                UPPER(CUSTOMER.customer_name_l10n->>'mr') LIKE UPPER(:f_farmer_name) OR
                UPPER(CUSTOMER.customer_name_l10n->>'en') LIKE UPPER(:f_farmer_name) OR 
                UPPER(CUSTOMER.customer_name_l10n->>'hi') LIKE UPPER(:f_farmer_name)
             
            )
        {{/if}}
        
        {{#if f_farmer_taluka}}
        AND (
           
            UPPER(REF_TALUK.taluk_name_l10n->>'ul') LIKE UPPER(:f_farmer_taluka) OR 
            UPPER(REF_TALUK.taluk_name_l10n->>'mr') LIKE UPPER(:f_farmer_taluka) OR
            UPPER(REF_TALUK.taluk_name_l10n->>'en') LIKE UPPER(:f_farmer_taluka) OR 
            UPPER(REF_TALUK.taluk_name_l10n->>'hi') LIKE UPPER(:f_farmer_taluka)
          
        )
    {{/if}}

    {{#if f_farmer_village}}
    AND (
       
        UPPER(REF_VILLAGE.village_name_l10n->>'ul') LIKE UPPER(:f_farmer_village) OR 
        UPPER(REF_VILLAGE.village_name_l10n->>'mr') LIKE UPPER(:f_farmer_village) OR
        UPPER(REF_VILLAGE.village_name_l10n->>'en') LIKE UPPER(:f_farmer_village) OR 
        UPPER(REF_VILLAGE.village_name_l10n->>'hi') LIKE UPPER(:f_farmer_village)
       
    )
{{/if}}

     
        {{#if f_not_subscribed}}
            AND CUSTOMER.customer_id  IN (select * from NOT_SUBSCRIBED)
        {{/if}}
        {{#if f_not_assigned}}
        AND STAFF.staff_id is null
       {{/if}}
        
        {{#if f_farmer_visual_id}}
            AND (
                UPPER(customer.customer_visual_id) LIKE UPPER(:f_farmer_visual_id)
            )
        {{/if}}

        {{#if f_farmer_mobile}}
            AND (
                customer.mobile_number LIKE :f_farmer_mobile
            )
        {{/if}}

        {{#if f_paravet_name}}
            AND (
                UPPER(STAFF.staff_name_l10n->>'ul') LIKE UPPER(:f_paravet_name) OR 
                UPPER(STAFF.staff_name_l10n->>'mr') LIKE UPPER(:f_paravet_name) OR
                UPPER(STAFF.staff_name_l10n->>'en') LIKE UPPER(:f_paravet_name) OR 
                UPPER(STAFF.staff_name_l10n->>'hi') LIKE UPPER(:f_paravet_name)
            )
        {{/if}}
    )
    SELECT 
        CUSTOMER_LISTING.customer_id, 
        CUSTOMER_LISTING.customer_name_l10n, 
        CUSTOMER_LISTING.mobile_number,
        CUSTOMER_LISTING.customer_visual_id,
        CUSTOMER_LISTING.updated_at,
        CUSTOMER_LISTING.village_name_l10n,
        CUSTOMER_LISTING.taluk_name_l10n,
        CUSTOMER_LISTING.paravet_name,
        CUSTOMER_LISTING.terms
    FROM CUSTOMER_LISTING
                    ORDER BY CUSTOMER_LISTING.created_at DESC
`
