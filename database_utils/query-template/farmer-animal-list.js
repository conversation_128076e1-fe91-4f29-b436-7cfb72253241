const { VILLAGE_CLASSIFIER, CONST_ID_CLASSIFIER_TALUKA, CONST_ID_CLASSIFIER_DISTRICT, CONST_ID_CLASSIFIER_STATE, FARMER_TO_ANIMAL, ANIMAL_NAME_CLASSIFIER, ANIMAL_EAR_TAG_CLASSIFIER, ANIMAL_TYPE_CLASSIFIER, ANIMAL_SUBSCRIPTION_DATE_CLASSIFIER, ANIMAL_SUBSCRIPTION_PLAN_CLASSIFIER, ANIMAL_INACTIVE_REASON_CLASSIFIER, ANIMAL_INACTIVE_DATE_CLASSIFIER, ACTIVE } = require("@krushal-it/back-end-lib/utils/constant")
const { 
	classifiers: { 
		ACCOUNT_PAYMENT_TYPE, 
		NUMBER_OF_MONTHS_PREGNANT, 
		LAST_CALVING_DATE, 
		ANIMAL_GIVING_MILK_OR_NOT, 
		CUSTOMER_ALTERNATE_CONTACT,
		CUSTOMER_CREDIT_LIMIT,
		CUSTOMER_CREDIT_USAGE 
	}
 } = require("@krushal-it/back-end-lib/ENUMS")


module.exports = `
SELECT
	customer.customer_id,
	customer_name_l10n as farmer_name_l10n,
	customer_visual_id as farmer_visual_id,
	mobile_number as farmer_mobile_number,
	alternate_contact.value_string_256 as farmer_alt_mobile_number,
	customer_account_payment_type.value_reference_id as account_payment_type,
	customer_credit_limit.value_double as customer_credit_limit,
	customer_credit_usage.value_double as customer_credit_usage,
	customer.active as farmer_active_status,
	village_classification.value_reference_id as village_id,
	ref_village.village_name_l10n as village_name_l10n,
	taluk_classification.value_reference_id as taluk_id,
	ref_taluk.taluk_name_l10n as taluk_name_l10n,
	district_classification.value_reference_id as district_id,
	ref_district.district_name_l10n as district_name_l10n,
	state_classification.value_reference_id as state_id,
	ref_state.state_name_l10n as state_name_l10n,
	animal.animal_visual_id,
	animal.animal_id,
	CASE 
		WHEN sub_date.value_date IS NULL 
			THEN **********
		ELSE 
			animal.active 
	END as animal_active_status,
	animal_name.value_string_256 as animal_name,
	animal_etag.value_string_256 as animal_ear_tag,
	animal_type.value_reference_id as animal_type_id,
	ref_animal_type.reference_name_l10n as animal_type_l10n,
	sub_date.value_date AS healthcare_plan_subscription_date_1,
	sub_type_ref.reference_name_l10n as subscription_plan_1,
	coalesce(inactive_reason_ref.reference_name_l10n->>'en', inactive_reason_ref.reference_name_l10n->>'ul') as inactive_reason_1,
	inactive_date.value_date as inactive_date_1,
	coalesce(inactive_reason_note.note->>'en', inactive_reason_note.note->>'ul') as inactive_reason_note_1,
	CASE 
		WHEN
			COALESCE (animal_pregnant_classifier.value_double, 0) + (EXTRACT(YEAR FROM AGE(NOW(), animal_pregnant_classifier.updated_at)) * 12) + (EXTRACT(MONTH FROM AGE(NOW(), animal_pregnant_classifier.updated_at))) > 9
		THEN 
			0
		ELSE
			COALESCE (animal_pregnant_classifier.value_double, 0) + (EXTRACT(YEAR FROM AGE(NOW(), animal_pregnant_classifier.updated_at)) * 12) + (EXTRACT(MONTH FROM AGE(NOW(), animal_pregnant_classifier.updated_at)))
	END AS "number_of_months_pregnant",
	animal_last_calving_classifier.value_date as "last_calving_date",
	animal_milking_classifier.value_reference_id as "cattle_milking_status"
FROM main.customer
	LEFT JOIN main.customer_classification as customer_account_payment_type
		ON 
			customer_account_payment_type.customer_id = customer.customer_id
			AND 
			customer_account_payment_type.classifier_id = ${ACCOUNT_PAYMENT_TYPE}
			AND
			customer_account_payment_type.active = ${ACTIVE}
	LEFT JOIN main.customer_classification as village_classification
		ON 
			village_classification.customer_id = customer.customer_id
			AND 
			village_classification.classifier_id = ${VILLAGE_CLASSIFIER}
	LEFT JOIN main.ref_village 
		ON 
			ref_village.village_id = village_classification.value_reference_id
	LEFT JOIN main.customer_classification as taluk_classification
		ON 
			taluk_classification.customer_id = customer.customer_id
			AND 
			taluk_classification.classifier_id = ${CONST_ID_CLASSIFIER_TALUKA}
	LEFT JOIN main.customer_classification as customer_credit_limit
		ON 
			customer_credit_limit.customer_id = customer.customer_id
			AND 
			customer_credit_limit.classifier_id = ${CUSTOMER_CREDIT_LIMIT}
			AND
			customer_credit_limit.active = ${ACTIVE}
	LEFT JOIN main.customer_classification as customer_credit_usage
		ON 
			customer_credit_usage.customer_id = customer.customer_id
			AND 
			customer_credit_usage.classifier_id = ${CUSTOMER_CREDIT_USAGE}
			AND
			customer_credit_usage.active = ${ACTIVE}
	LEFT JOIN main.ref_taluk 
		ON 
			ref_taluk.taluk_id = taluk_classification.value_reference_id
	LEFT JOIN main.customer_classification as district_classification
		ON 
			district_classification.customer_id = customer.customer_id
			AND 
			district_classification.classifier_id = ${CONST_ID_CLASSIFIER_DISTRICT}
	LEFT JOIN main.ref_district 
		ON 
			ref_district.district_id = district_classification.value_reference_id
	LEFT JOIN main.customer_classification as state_classification
		ON 
			state_classification.customer_id = customer.customer_id
			AND 
			state_classification.classifier_id = ${CONST_ID_CLASSIFIER_STATE}
	LEFT JOIN main.customer_classification as alternate_contact
		ON 
			alternate_contact.customer_id = customer.customer_id
			AND 
			alternate_contact.classifier_id = ${CUSTOMER_ALTERNATE_CONTACT}
			AND
			alternate_contact.active = ${ACTIVE}
	LEFT JOIN main.ref_state 
		ON 
			ref_state.state_id = state_classification.value_reference_id
	LEFT JOIN main.entity_relationship as er
		ON 
			er.entity_1_entity_uuid = customer.customer_id
			AND 
			er.entity_relationship_type_id = ${FARMER_TO_ANIMAL}
	LEFT JOIN main.animal 
		ON 
			animal.animal_id = er.entity_2_entity_uuid
	LEFT JOIN main.animal_classification as animal_name
		ON 
			animal_name.animal_id = animal.animal_id
			AND 
			animal_name.classifier_id = ${ANIMAL_NAME_CLASSIFIER}
	LEFT JOIN main.animal_classification as animal_etag
		ON 
			animal_etag.animal_id = animal.animal_id
			AND 
			animal_etag.classifier_id = ${ANIMAL_EAR_TAG_CLASSIFIER}
	LEFT JOIN main.animal_classification as animal_type
		ON 
			animal_type.animal_id = animal.animal_id
			AND 
			animal_type.classifier_id = ${ANIMAL_TYPE_CLASSIFIER}
	LEFT JOIN main.ref_reference as ref_animal_type
		ON 
			ref_animal_type.reference_id = animal_type.value_reference_id
	LEFT JOIN main.animal_classification AS sub_date
		ON 
			animal.animal_id = sub_date.animal_id
			AND 
			sub_date.classifier_id = ${ANIMAL_SUBSCRIPTION_DATE_CLASSIFIER}
	LEFT JOIN main.animal_classification as sub_type
		ON 
			sub_type.animal_id = animal.animal_id
			AND 
			sub_type.classifier_id = ${ANIMAL_SUBSCRIPTION_PLAN_CLASSIFIER}
	LEFT JOIN main.ref_reference as sub_type_ref
		ON 
			sub_type_ref.reference_id = sub_type.value_reference_id			
	LEFT JOIN main.animal_classification as inactive_reason
		ON 
			animal.animal_id = inactive_reason.animal_id
			AND 
			inactive_reason.classifier_id = ${ANIMAL_INACTIVE_REASON_CLASSIFIER}
	LEFT JOIN main.ref_reference as inactive_reason_ref
		ON 
			inactive_reason_ref.reference_id = inactive_reason.value_reference_id			
	LEFT JOIN main.animal_classification as inactive_date
		ON 
			animal.animal_id = inactive_date.animal_id
			AND 
			inactive_date.classifier_id = ${ANIMAL_INACTIVE_DATE_CLASSIFIER}
	LEFT JOIN main.animal_classification as animal_pregnant_classifier
		ON
			animal.animal_id = animal_pregnant_classifier.animal_id
			AND
			animal_pregnant_classifier.classifier_id = ${NUMBER_OF_MONTHS_PREGNANT}
	LEFT JOIN main.animal_classification as animal_last_calving_classifier
		ON
			animal.animal_id = animal_last_calving_classifier.animal_id
			AND
			animal_last_calving_classifier.classifier_id = ${LAST_CALVING_DATE}
	LEFT JOIN main.animal_classification as animal_milking_classifier
		ON
			animal.animal_id = animal_milking_classifier.animal_id
			AND
			animal_milking_classifier.classifier_id = ${ANIMAL_GIVING_MILK_OR_NOT}
	LEFT JOIN main.note as inactive_reason_note
		ON 
			animal.animal_id = inactive_reason_note.entity_1_uuid
WHERE 1 = 1
{{#if f_farmer_mobile}}
AND (
    customer.mobile_number = :f_farmer_mobile 
    OR
    customer.mobile_number = :f_farmer_mobile_c_code
	OR
	alternate_contact.value_string_256 = :f_farmer_mobile 
	OR
	alternate_contact.value_string_256 = :f_farmer_mobile_c_code
    )
{{/if}}

{{#if f_ear_tag}}
AND animal_etag.value_string_256 = :f_ear_tag
{{/if}}
`
