const { 
  CURATIVE, 
  REPRODUCTIVE, 
  ACTIVE, 
  PARAVET, 
  VILLAGE_CLASSIFIER, 
  TICKET_CLASSIFIER, 
  VILLAGE_TYPE_ID, 
  CANCELLED_ACTIVITY_STATUS, 
  ABANDONED_ACTIVITY_STATUS,
  ANIMAL_TYPE_CALENDAR,
  FARMER_TO_ANIMAL
} = require("@krushal-it/back-end-lib/utils/constant");

module.exports =`
WITH numbered_rows AS (
    SELECT 
      ccc.care_calendar_id,
      ccc.value_string_256 as ticket_id,
      cc.calendar_activity_status as calendar_status,
      cc.entity_type_id as entity_type_id,
      ccc.created_at,
      ccc.updated_at,
      ref_cc_status.reference_name_l10n as calendar_status_l10n,
      cc.entity_uuid,
      cc.activity_date,
      cc.activity_id,
      ra.activity_name_l10n,
      rr.reference_name_l10n as activity_category,
      s.staff_id,
      s.staff_name_l10n as staff_name,
      ROW_NUMBER() OVER (
        PARTITION BY 
          ccc.value_string_256
        ORDER BY 
          ccc.created_at DESC, 
          ccc.updated_at DESC
      ) AS ROWNUM
    FROM 
      main.care_calendar_classification as ccc
      LEFT JOIN main.care_calendar as cc 
        ON 
          ccc.care_calendar_id = cc.care_calendar_id
      LEFT JOIN main.ref_reference as ref_cc_status 
        ON 
          ref_cc_status.reference_id = cc.calendar_activity_status 
      LEFT JOIN main.ref_activity as ra 
        ON 
          cc.activity_id = ra.activity_id
      LEFT JOIN main.ref_reference as rr 
        ON 
          rr.reference_id = ra.activity_category
      LEFT JOIN main.entity_relationship as er 
        ON 
          er.entity_2_entity_uuid = cc.entity_uuid 
          AND 
          er.entity_relationship_type_id = ${FARMER_TO_ANIMAL}
      LEFT JOIN main.customer_classification as ccl 
        ON 
          ccl.customer_id = er.entity_1_entity_uuid 
          AND 
          ccl.classifier_id = ${VILLAGE_CLASSIFIER}
      LEFT JOIN main.entity_geography as eg 
        ON 
          eg.geography_id = ccl.value_reference_id 
          AND 
          eg.geography_type_id = ${VILLAGE_TYPE_ID} 
          AND 
          eg.entity_type_id = ${PARAVET}
          AND 
          eg.active = ${ACTIVE} 
      LEFT JOIN main.staff as s 
        ON 
          s.staff_id = eg.entity_uuid 
    WHERE 
      ccc.classifier_id = ${TICKET_CLASSIFIER}
      AND 
      cc.entity_uuid = :entity_uuid
      AND 
      cc.activity_date <= CURRENT_DATE
      AND 
      cc.calendar_activity_status NOT IN (${CANCELLED_ACTIVITY_STATUS},${ABANDONED_ACTIVITY_STATUS})
  )
  SELECT 
    *
  FROM 
    numbered_rows
  WHERE 
    ROWNUM = 1
  ORDER BY
    ticket_id DESC,
    created_at DESC,
    updated_at DESC
`

