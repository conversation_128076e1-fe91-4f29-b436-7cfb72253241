const { VILLAGE_TYPE_ID, ACTIVE } = require("@krushal-it/back-end-lib/utils/constant");
/**
 * Typeorm placeholders
 * 1. entity_type_id
 * 2. entity_uuid
 */
module.exports = `
SELECT 
    rv.village_id,
    rv.village_name_l10n
FROM 
    main.entity_geography AS eg
JOIN 
    main.ref_village AS rv 
        ON 
            rv.village_id = eg.geography_id 
            AND 
            eg.geography_type_id = ${VILLAGE_TYPE_ID}
            AND 
            eg.active = ${ACTIVE}
WHERE 
    eg.entity_type_id = :entity_type_id
    AND 
    eg.entity_uuid = :entity_uuid
`;