const { CONST_ID_ACTIVE, VILLAGE_ClASSIFIER, CONST_ID_TYPE_STAFF_PARAVET, CONST_ID_TYPE_STAFF_VET } = require("../../utils/common/namespace/krushal.namespace")
const { messaging } = require("../../utils/common/auth/firebase_admin")
const { dbConnections } = require("@krushal-it/ah-orm")
const { In } = require("typeorm")
const { errorLog } = require("@krushal-it/mobile-or-server-lib")
const { VILLAGE_TYPE_ID } = require("@krushal-it/back-end-lib/utils/constant")
const { get_task_infromation_by_id } = require("@krushal-it/back-end-lib")

const getFCMofUserQuery = (options = {}) => {
  const { entity_uuid } = options
  const base_query = `
  select distinct on (entity_uuid) entity_uuid,fcm_id, updated_at from main.user_device where entity_uuid in (
    select entity_uuid  from 
    (
    select entity_type_id, entity_uuid from main.customer c
      left join main.customer_classification cc
      on c.customer_id = cc.customer_id and cc.classifier_id=2000000055
      left join main.entity_geography eg
      on eg.entity_type_id = 1000230004 and eg.geography_id=cc.value_reference_id and eg.active=1000100001
    where c.customer_id = (select entity_1_entity_uuid from main.entity_relationship 
         where entity_1_entity_uuid='${entity_uuid}' or entity_2_entity_uuid='${entity_uuid}' limit 1)
      
      union
    
    select entity_type_id, entity_uuid from main.customer c
      left join main.customer_classification cc
      on c.customer_id = cc.customer_id and cc.classifier_id=2000000055
      left join main.entity_geography eg
      on eg.entity_type_id = 1000230003 and eg.geography_id=cc.value_reference_id and eg.active=1000100001
      where c.customer_id = (select entity_1_entity_uuid from main.entity_relationship 
         where entity_1_entity_uuid='${entity_uuid}' or entity_2_entity_uuid='${entity_uuid}' limit 1) 
    
     
     union
    
    (select 1000220001 as entity_type_id, entity_1_entity_uuid as entity_uuid from main.entity_relationship 
     where entity_1_entity_uuid='${entity_uuid}' 
      or entity_2_entity_uuid='${entity_uuid}' limit 1)  
    ) as d
    
    
    ) order by  entity_uuid, updated_at desc;
    `
  return base_query
}

const getStaffFCMtByFilter = () => {
  const base_query = `
  SELECT ntc.fcm_id as fcm_token FROM main.staff 
  JOIN main.entity_geography as eg ON staff.staff_id = eg.entity_uuid 
  AND eg.active = ${CONST_ID_ACTIVE}
  AND eg.geography_type_id  = ${VILLAGE_TYPE_ID}
  AND eg.geography_id in (:...village_ids)
  JOIN main.user_device AS ntc ON ntc.entity_uuid = staff.staff_id
  AND ntc.active = ${CONST_ID_ACTIVE}
  WHERE staff.staff_type_id in (:...user_types)
    `
  return base_query
}

const getCustomerFCMBYFilter = () => {
  const base_query = `
  SELECT ntc.fcm_id as fcm_token FROM main.customer
  JOIN main.customer_classification as cv ON  cv.customer_id = customer.customer_id
  AND cv.classifier_id = ${VILLAGE_ClASSIFIER}
  AND cv.active = ${CONST_ID_ACTIVE}
  AND cv.value_reference_id IN (:...village_ids)
  JOIN main.user_device AS ntc ON ntc.entity_uuid = customer.customer_id
  AND ntc.active = ${CONST_ID_ACTIVE}
  WHERE customer.customer_type_id IN (:...user_types)
  `
  return base_query
}

const sendNotifications = async options => {
  try {
    const { title, body, registrationTokens, action, redirect, type } = options
    const message = {
      tokens: registrationTokens,
      data: {
        title,
        body,
        action,
        redirect,
        type
      }
    }
    let messages = await messaging.sendEachForMulticast(message).then(response => {
      console.log(response.successCount + " messages were sent successfully")
      if (response.failureCount > 0) {
        const failedTokens = []
        response.responses.forEach((resp, idx) => {
          if (!resp.success) {
            failedTokens.push(registrationTokens[idx])
          }
        })
        console.log("List of tokens that caused failures: " + failedTokens)
      }
    })
    console.log(messages)
    return messages
  } catch (error) {
    console.log(error)
    return { message: "No messages were sent" }
  }
}

const getFCMTokenByUserIds = async user_ids => {
  try {
    const result = await dbConnections().main.repos["user_device"].find({
      where: [{ entity_uuid: In(user_ids), active: CONST_ID_ACTIVE }],
      select: { fcm_id: true }
    })
    return result
  } catch (error) {
    console.error(error)
    return []
  }
}
const insertFCM = async (values, datasource) => {
  let mdatasource = datasource ? datasource : dbConnections().main.manager
  await mdatasource
    .createQueryBuilder()
    .insert()
    .into("main.user_device")
    .values(values)
    .execute()
}

const getFCM = async user_id => {
  let notification = await dbConnections()
    .main.manager.createQueryBuilder()
    .select("entity_uuid,fcm_id,active")
    .from("main.user_device")
    .where("entity_uuid = :user_id", { user_id: user_id })
    .orderBy("created_at", "DESC")
    .execute()

  return notification
}

const getFCMforFreelancers = async ({ calendar_id }) => {
  const query = `
  with entities as (
    (SELECT STAFF.STAFF_ID as entity_uuid, 1000230006 as entity_type
    FROM MAIN.ENTITY_RELATIONSHIP
    LEFT JOIN MAIN.STAFF ON STAFF.STAFF_TYPE_ID = 1000230006
    AND STAFF.STAFF_ID = ENTITY_RELATIONSHIP.ENTITY_1_ENTITY_UUID
    AND STAFF.ACTIVE = 1000100001
    WHERE ENTITY_RELATIONSHIP_TYPE_ID = 1000210011
      AND ENTITY_RELATIONSHIP.ENTITY_2_ENTITY_UUID = :calendar_id)
    
    UNION
    
    (SELECT STAFF.STAFF_ID as entity_uuid, 1000230007 as entity_type
    FROM MAIN.ENTITY_RELATIONSHIP
    LEFT JOIN MAIN.STAFF ON STAFF.STAFF_TYPE_ID = 1000230007
    AND STAFF.STAFF_ID = ENTITY_RELATIONSHIP.ENTITY_1_ENTITY_UUID
    AND STAFF.ACTIVE = 1000100001
    WHERE ENTITY_RELATIONSHIP.ENTITY_RELATIONSHIP_TYPE_ID = 1000210012
      AND ENTITY_RELATIONSHIP.ENTITY_2_ENTITY_UUID = :calendar_id)
    
    
    UNION 
    
    (SELECT coalesce(customer_animal.CUSTOMER_ID,customer_er.CUSTOMER_ID) as entity_uuid,1000220007 as entity_type  
    FROM MAIN.CARE_CALENDAR
    LEFT JOIN MAIN.ENTITY_RELATIONSHIP ON ENTITY_RELATIONSHIP.ENTITY_RELATIONSHIP_TYPE_ID = 1000210004
    AND ENTITY_RELATIONSHIP.ENTITY_2_ENTITY_UUID = CARE_CALENDAR.ENTITY_UUID AND ENTITY_RELATIONSHIP.ACTIVE = 1000100001
    LEFT JOIN MAIN.CUSTOMER customer_animal ON customer_animal.CUSTOMER_ID = CARE_CALENDAR.ENTITY_UUID
    LEFT JOIN MAIN.CUSTOMER customer_er ON customer_er.CUSTOMER_ID = ENTITY_RELATIONSHIP.ENTITY_1_ENTITY_UUID
    AND customer_er.ACTIVE = 1000100001
    WHERE CARE_CALENDAR.CARE_CALENDAR_ID = :calendar_id)
    )
    select * from (select user_device.fcm_id, user_device.entity_uuid,entities.entity_type, ROW_NUMBER() OVER(PARTITION BY user_device.entity_uuid
       ORDER BY user_device.updated_at DESC) AS rank from entities left join main.user_device on user_device.entity_uuid=entities.entity_uuid)as t1 where t1.rank=1
`
  const result = await dbConnections()
    .main.manager.createQueryBuilder()
    .from(`(${query})`)
    .setParameters({ calendar_id })
    .execute()
  return result
}

async function sendNotificationsByCustomerIdV2(options) {
  const { calendar_id } = options
  let manager = options.manager ? options.manager : dbConnections().main.manager
  let fcm = await getFCMforFreelancers({ calendar_id })
  console.log("fcm", fcm)
  const taskDetails = await get_task_infromation_by_id(calendar_id)
  const { task_name, ear_tag, task_date, ticket_number, customer_name, customer_id, activity_id } = taskDetails[0]

  const taskName = task_name ?? "N/A"
  const eartag = ear_tag ?? "N/A"

  const taskDate = task_date ? new Date(task_date).toLocaleDateString("en-US", { weekday: "long", year: "numeric", month: "long", day: "numeric" }) : "N/A"
  const customerName = customer_name ? customer_name.ul || customer_name.en || "N/A" : "N/A"

  const title = `Ticket #${ticket_number || " "}, (${taskName})`
  const body = `Customer - ${customerName}, Cattle #${eartag}, Visit date - ${taskDate}.`

  const registrationTokens = fcm.length > 0 ? fcm.filter(fcm => fcm.fcm_id != null).map(fcm => fcm.fcm_id) : []
  if (registrationTokens.length > 0) {
    sendNotifications({
      body: body,
      title: title,
      registrationTokens: registrationTokens,
      redirect: "true",
      action: `${customer_id},${calendar_id},${activity_id}`,
      type: "service-request"
    })
  }

  return fcm
}

module.exports = { insertFCM, getFCM, sendNotifications, getFCMofUserQuery, getFCMTokenByUserIds, getStaffFCMtByFilter, getCustomerFCMBYFilter, getFCMforFreelancers, sendNotificationsByCustomerIdV2 }
