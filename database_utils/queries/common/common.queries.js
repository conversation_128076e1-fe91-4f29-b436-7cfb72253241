const { dbConnections,preProcessRecords } = require("@krushal-it/ah-orm");
const {
  RELATION_ANIMAL,
  RELATION_CUSTOMER,
  RELATION_STAFF,
  RELATION_NOTE,
} = require("../../../utils/common/namespace/krushal.namespace");
const { getTime } = require("../../../utils/helpers/set_3_pm");
const VALID_MODELS = new Set([
  RELATION_CUSTOMER,
  RELATION_ANIMAL,
  RELATION_STAFF,
  RELATION_NOTE
]);
class CommonQueries {
  // Set of accepted table name,
  /**
   *
   * @param {TimeStamp} time
   * @param {Object} options
   * @returns {Array}
   */
  static async getCreatedAfter(datasource, time, options) {
    try {
      if (
        !typeof options.model === "string" ||
        !Array.isArray(options.columns) ||
        options.columns.length <= 0
      )
        throw new Error("Options must contain model name and column Array");

      if (!VALID_MODELS.has(options.model))
        throw new Error("Please register the model name at VALID_MODELS");

      const { model, columns } = options;
      let projection = [];
      for (let i = 0; i < columns.length; i++) {
        projection.push(`${model}.${columns[i]}`);
      }

      let result = await dbConnections()
        .main.repos[model].createQueryBuilder()
        .select(projection)
        .where(`${model}.created_at > :time`, { time })
        .getMany();

      return result;
    } catch (error) {
      console.error(error);
      return [];
    }
  }
  /**
   * 
   * @param {*} datasource 
   * @param {*} options [column,model,idCol] 
   * @returns {Array}
   */
  static async getTranslatorData(datasource, options) {
    try {
      if (!VALID_MODELS.has(options.model))
        throw new Error("Please register the model name at VALID_MODELS");
      const { column, model, idCol } = options;
      let base_query = `
      SELECT ${column},${idCol} FROM main.${model}
      WHERE ${column}->>'ul' IS NOT NULL
      `;
      return await datasource.query(base_query, []);
    } catch (error) {
      console.error(error);
      return [];
    }
  } 
  static async updateEntity(dataSource, options) {
    try {
      if (
        !typeof options.model === "string" ||
        !options.value ||
        !options.id ||
        !options.idName
      )
        throw new Error("Options must contain model name , value and id keys");
      if (!VALID_MODELS.has(options.model))
        throw new Error("Please register the model name at VALID_MODELS");
      // options.value["updated_at"] = getTime(new Date());
      let manager = dbConnections().main.manager;
      const entity_model = dbConnections().main.entities[options.model];
      const preProcssedOptionsValue = preProcessRecords(
        entity_model,
        options.value,
        { id: options.id, ...entity_model.additionalAttributes }
      );

      await manager
        .createQueryBuilder()
        .update(options.model)
        .set(preProcssedOptionsValue)
        .where(`${options.idName} = :id`, { id: options.id })
        .execute();
    } catch (error) {
      console.error(error);
    }
  }
}

module.exports = CommonQueries;