module.exports = function get_farmer_summary_query(farmer_id) {
  return `(select customer_name_l10n, mobile_number, village_name_l10n, taluk_name_l10n, district_name_l10n, state_name_l10n, animal_eartag, animal_type, subscription_plan_l10n, to_char(start_date, 'DD Mon YYYY') as subscription_plan_start_date, activity_name_l10n, activity_category_name_l10n, to_char(activity_date, 'DD Mon YYYY') as activity_date, medicine_cost, abs(pv_cost + vet_cost) as service_cost, abs(medicine_cost + pv_cost + vet_cost)as total_cost  from reporting.cost_per_cc
    where cost_per_cc.customer_uuid ='${farmer_id}' and (activity_category_name_l10n->>'ul' != 'Preventive' or activity_category_name_l10n->>'en' != 'Preventive') order by animal_eartag,  cost_per_cc.activity_date desc)
   UNION ALL
  (select customer_name_l10n, mobile_number, village_name_l10n, taluk_name_l10n, district_name_l10n, state_name_l10n, animal_eartag, animal_type, subscription_plan_l10n, to_char(start_date, 'DD Mon YYYY') as subscription_plan_start_date, activity_name_l10n, activity_category_name_l10n, to_char(activity_date, 'DD Mon YYYY') as activity_date, medicine_cost, abs(pv_cost + vet_cost) as service_cost, abs(medicine_cost + pv_cost + vet_cost)as total_cost  from reporting.cost_per_cc
    where cost_per_cc.customer_uuid ='${farmer_id}' and (activity_category_name_l10n->>'ul' = 'Preventive' or activity_category_name_l10n->>'en' = 'Preventive') order by  cost_per_cc.activity_date desc)`
}
