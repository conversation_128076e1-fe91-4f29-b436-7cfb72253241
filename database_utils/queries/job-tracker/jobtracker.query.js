const { dbConnections, preProcessRecords } = require("@krushal-it/ah-orm");
const {
  RELATION_JOBTRACKER,
} = require("../../../utils/common/namespace/krushal.namespace");

const { getTime } = require("../../../utils/helpers/set_3_pm");
const moment = require("moment-timezone").tz.setDefault("Asia/Kolkata");
class JobTrackerQueries {
  static async insertJob(values, options = {}) {
    let manager = options.datasource
      ? options.datasource
      : dbConnections().main.manager;
    const entity_job = dbConnections().main.entities[RELATION_JOBTRACKER];
    const preProcessedValues = preProcessRecords(entity_job, values, entity_job.additionalAttributes);
    return await manager
      .createQueryBuilder()
      .insert()
      .into(RELATION_JOBTRACKER)
      .values(preProcessedValues)
      .returning("job_tracker_id")
      .execute();
  }
  static async getJob(options = {}) {
    if (!options.job_type) throw new Error("undefined job-type");
    let query = dbConnections()
      .main.manager.getRepository(RELATION_JOBTRACKER)
      .createQueryBuilder()
      .where(`${RELATION_JOBTRACKER}.job_type= :job_type`, {
        job_type: options.job_type,
      });
    if (options.last_job === 1) {
      query.orderBy(`${RELATION_JOBTRACKER}.created_at`, "DESC");
      query.limit(1);
    }
    return await query.execute();
  }
  static async getJob_v2(options = {}) {
    let base_query = `select max(job_completed_at)  
    as job_completed_at from main.job_tracker 
    where job_status = 'COMPLETED' 
    AND job_type = '${options.job_type}' and job_completed_at IS NOT NULL`;
    let data = await dbConnections().main.manager.query(base_query);
    return data;
  }
  static async updateJobStatus(job_id, status, errorJson, options = {}) {
    let manager = options.datasource
      ? options.datasource
      : dbConnections().main.manager;
    const entity_job = dbConnections().main.entities[RELATION_JOBTRACKER];
    const value = {
      job_status: status,
      job_information: { errors: errorJson },
      job_completed_at: moment(),
    }
    const preProcessedValue = preProcessRecords(
      entity_job,
      value,
      entity_job.additionalAttributes
    );
    await manager
      .createQueryBuilder()
      .update(RELATION_JOBTRACKER)
      .set(preProcessedValue)
      .where("job_tracker_id = :job_id", { job_id })
      .execute();
  }
}
module.exports = JobTrackerQueries;
