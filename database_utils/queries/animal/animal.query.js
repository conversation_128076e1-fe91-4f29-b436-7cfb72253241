const moment = require("moment");
const { dbConnections} = require("@krushal-it/ah-orm");

class AnimalQueries {
 
static async animalForCalendar(options = {}) {
    let d = moment(options.created_after).format("YYYY-MM-DD HH:mm:ss");
    let base_query = `
    SELECT 
    animal.animal_id,
    animal.animal_visual_id,
    animal_type_class.value_reference_id as animal_type,
    dob_c.value_date as animal_dob_1,
    prgm_c.value_int as pregnancy_month_1,
    lmsc_c.value_int as months_since_last_calving_1,
    sd_c.value_date as healthcare_plan_subscription_date_1,
    sub_plan.value_reference_id as healthcare_subscription_plan
    FROM main.animal
    LEFT JOIN main.animal_classification as animal_type_class ON animal_type_class.animal_id = animal.animal_id AND animal_type_class.classifier_id = **********
	  LEFT JOIN main.animal_classification as prg_c ON prg_c.animal_id = animal.animal_id AND prg_c.classifier_id = **********
    LEFT JOIN main.animal_classification as dob_c ON dob_c.animal_id = animal.animal_id AND dob_c.classifier_id = **********
    LEFT JOIN main.animal_classification as lai_c ON lai_c.animal_id = animal.animal_id AND lai_c.classifier_id = **********
    LEFT JOIN main.animal_classification as lcal_c ON lcal_c.animal_id = animal.animal_id AND lcal_c.classifier_id = **********
    LEFT JOIN main.animal_classification as lprg_c ON lprg_c.animal_id = animal.animal_id AND lprg_c.classifier_id = **********
    LEFT JOIN main.animal_classification as lfmd_c ON lfmd_c.animal_id = animal.animal_id AND lfmd_c.classifier_id = **********
    LEFT JOIN main.animal_classification as lfmdhsbq_c ON lfmdhsbq_c.animal_id = animal.animal_id AND lfmdhsbq_c.classifier_id = 2000000070
    LEFT JOIN main.animal_classification as lbvac_c ON lbvac_c.animal_id = animal.animal_id AND lbvac_c.classifier_id = 2000000072
    LEFT JOIN main.animal_classification as prgm_c ON prgm_c.animal_id = animal.animal_id AND prgm_c.classifier_id = 2000000085
    LEFT JOIN main.animal_classification as lmsc_c ON lmsc_c.animal_id = animal.animal_id AND lmsc_c.classifier_id = 2000000064
    LEFT JOIN main.animal_classification as sub_plan ON sub_plan.animal_id = animal.animal_id AND sub_plan.classifier_id = 2000000051
    JOIN main.animal_classification as sd_c ON sd_c.animal_id = animal.animal_id 
    AND sd_c.classifier_id = 2000000124 AND sd_c.created_at > '${d}'`;
    
    let manager = options.manager ? options.manager : dbConnections().main.manager;
    return await manager.query(base_query, []);
  }
  
}

module.exports = AnimalQueries;
