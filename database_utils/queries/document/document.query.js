
const { dbConnections, preProcessRecords } = require("@krushal-it/ah-orm");
const entityTypeIds = { farmer: 1000220001, animal: 1000220002, staff: 1000230004 };
const uuid = require("uuid");
const { RELATION_DOC, RELATION_REFERENCE, ENTITY_TYPE_ANIMAL, RELATION_DUMP_DOC } = require("../../../utils/common/namespace/krushal.namespace");


const insertToDocument = async (value) => {
  const entity_document = dbConnections().main.entities[RELATION_DOC];
  const preProcessedValue = preProcessRecords(entity_document, value, entity_document.additionalAttributes);
  let data = await dbConnections().main.manager.createQueryBuilder()
    .insert().into(RELATION_DOC).values(preProcessedValue).returning("document_id").execute();
  return data;
};
const insertDumpedDoc = async (value, options = {}) => {
  const entity_dump_document = dbConnections().main.entities[RELATION_DUMP_DOC];
  const preProcessedValue = preProcessRecords(entity_dump_document, value, entity_dump_document.additionalAttributes);
  await global.datasource.createQueryBuilder().insert().into(RELATION_DUMP_DOC).values(preProcessedValue).execute();
  return;
};

const getImagesByAnimalAId = async (animal) => {
  try {
    const projection = [
      "document_id",
      "document_information",
      "document_name",
      "document_type_id",
    ];
    const animall = await dbConnections()
      .main.manager.getRepository(RELATION_DOC)
      .createQueryBuilder().where("entity_1_entity_uuid=:id AND entity_1_type_id = :entity_id ", {
        id: animal,
        entity_id: entityTypeIds[ENTITY_TYPE_ANIMAL],
      }).select(projection).execute();
    return animall;
  } catch (error) {
    console.error(error);
    throw error;
  }
};
const getDocumentById = async (document_id, options = {}) => {
  let manager = options.datasource ? options.datasource : dbConnections().main.manager;
  let queryBuilder = manager.createQueryBuilder();
  if (!uuid.validate(document_id)) return null;
  let data = await queryBuilder.from(RELATION_DOC)
    .where("document_id= :document_id", { document_id }).execute();
  if (data.length > 0) return data[0].document_information.url ?? data[0].document_information.key;
  return null;
};


const getDocumentReferences = async (options = {}, datasource) => {
  try {
    const doc_categoryParent = 10002600;
    let mdatasource = datasource ? datasource : dbConnections().main.repos[RELATION_REFERENCE];
    let query = mdatasource.createQueryBuilder()
      .where(
        `${RELATION_REFERENCE}.active = 1000100001 AND ${RELATION_REFERENCE}.reference_category_id = :parent`,
        { parent: doc_categoryParent });
    let data = await query.execute();
    return data;
  } catch (error) {
    console.error(error);
    return [];
  }
};

/**
 *
 * @param {Object} options
 * Options: entity_id(user id), entity_type_id(user type id), document_type_id(type of document)
 * @returns
 */
const getDocuments = async (options = {}) => {
  try {
    const { entity_id, entity_type_id, document_type_id } = options;
    let query = dbConnections().main.repos[RELATION_DOC].createQueryBuilder()
      .where("entity_1_entity_uuid= :entity_id AND entity_1_type_id = :entity_type_id AND document_type_id = :document_type_id",
        { entity_id, entity_type_id, document_type_id }).orderBy("created_at", "DESC");
    if (options.limit) query.limit(options.limit);
    const document = await query.execute();
    return document;
  } catch (error) {
    console.error(error);
    throw error;
  }
};

const deleteDocumentById = async (document_id, options = {}) => {
  const manager = options.datasource ? options.datasource : global.datasource;
  const queryBuilder = manager.createQueryBuilder();
  return await queryBuilder.delete().from(RELATION_DOC)
    .where("document_id= :document_id", { document_id }).execute();
};


module.exports = {
  insertToDocument,
  insertDumpedDoc,
  getImagesByAnimalAId,
  getDocumentById,
  getDocumentReferences,
  getDocuments,
  deleteDocumentById
};
