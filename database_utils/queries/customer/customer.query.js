const moment = require("moment");
const { dbConnections} = require("@krushal-it/ah-orm");
const {
  RELATION_CUSTOMER,
} = require("../../../utils/common/namespace/krushal.namespace");

class CustomerQueries {
  
  static async getCustomerByPhoneNumber(mobile) {
    
    const user = await dbConnections()
      .main.repos[RELATION_CUSTOMER].createQueryBuilder()
      .where(`${RELATION_CUSTOMER}.mobile_number = :mobile`, { mobile })
      .getOne();

    return user;
  }

  static async getDetailsForPlannedFarmLevelTask(options) {

    options.last_job_run_time = moment(options.last_job_run_time).format("YYYY-MM-DD HH:mm:ss");

    const base_query = `
        SELECT * FROM (
            SELECT 
                er.entity_1_entity_uuid as farmer_id,
                min(
				    CASE 
					    WHEN sd.created_at > '${options.last_job_run_time}'
					    THEN sd.value_date
				    END
			    ) as min_sub_date,
                max(
				    CASE 
					    WHEN sd.created_at < '${options.last_job_run_time}'
					    THEN sd.value_date
				    END
			    ) as max_sub_date			
            FROM 
                main.animal as a
            JOIN 
                main.animal_classification as sd 
                    ON 
                        sd.animal_id = a.animal_id
                        AND 
                        sd.classifier_id = 2000000124
                        AND 
                        sd.active = 1000100001
            JOIN 
                main.entity_relationship as er 
                    ON
                        er.entity_2_entity_uuid  = a.animal_id
                        AND 
                        er.entity_relationship_type_id = 1000210004
                        AND 
                        er.active = 1000100001
            GROUP BY 
                er.entity_1_entity_uuid
        ) T1
        WHERE
	        T1.min_sub_date IS NOT NULL
    `;
    const response = await dbConnections().main.manager.query(base_query, []);
    return response;
  }
 
}

module.exports = CustomerQueries;