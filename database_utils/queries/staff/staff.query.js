const { dbConnections, preProcessRecords } = require("@krushal-it/ah-orm");
const {
  RELATION_STAFF,
  CONST_ID_ACTIVE,
} = require("../../../utils/common/namespace/krushal.namespace");
const { VILLAGE_TYPE_ID } = require("@krushal-it/back-end-lib/utils/constant");
class staffqueries {
  
  async get_staff(options = {}) {
    try {
      const staff =
        dbConnections().main.repos[RELATION_STAFF].createQueryBuilder();
      if (options.filter.phone_number) {
        staff.where(
          `${RELATION_STAFF}.mobile_number=:phone_number AND ${RELATION_STAFF}.active=${CONST_ID_ACTIVE}`,
          options.filter
        );
      } else {
        staff.where(
          `${RELATION_STAFF}.email_address=:email AND ${RELATION_STAFF}.active=${CONST_ID_ACTIVE}`,
          options.filter
        );
      }

      return await staff.getOne();
    } catch (error) {
      console.error(error);
      throw error;
    }
  }
}
 
module.exports = staffqueries;
