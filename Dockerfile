FROM krushaloc/node:*********-alpine

RUN apk update && apk add vim busybox-extras && apk add chromium
RUN apk add --no-cache bash
RUN npm install -g --save standard
RUN apk add make git curl yq jq
RUN apk add --no-cache \
    udev \
    ttf-freefont \
     chromium
 
ENV PUPPETEER_EXECUTABLE_PATH="/usr/bin/chromium-browser" \
     PUPPETEER_SKIP_CHROMIUM_DOWNLOAD="true"
 
ARG GITLAB_AUTH_TOKEN

WORKDIR /app
COPY .npmrc .
COPY package*.json .
RUN npm ci
COPY . .
EXPOSE 3001