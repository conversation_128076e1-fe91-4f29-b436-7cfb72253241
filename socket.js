const WebSocket = require('ws');

const { AnimalMilkingV2 } = require('@krushal-it/back-end-lib/v2/services/animals/animal.service');


async function milkLogFunction(obj) {
    if (Object.keys(obj).length > 0) {
        //Milk Data update call
        const updateLog = new AnimalMilkingV2()
        const updateMilkLog = await updateLog.updateOrCreateMilkLog(obj)
        return updateMilkLog
    } else {
        // return response(-101, 400, Empty input fields, {})
        return {};
    }
}

const wsServer = new WebSocket.Server({ noServer: true });
const clientsMap = new Map();
wsServer.on('connection', (socket, request) => {
    console.log(`WebSocket connection established `);

    //Craete a customer_id to socket instance map
    const client_id = new URL(request?.url, `${request?.socket?.encrypted ? 'https' : 'http'}://${request?.headers?.host}`).searchParams?.get('client_id');
    if (client_id) clientsMap.set(client_id, socket);

    // Send a dummy message to the client immediately after connection
    setInterval(() => {
        socket.send(JSON.stringify({ TYPE: 'DUMMY_MESSAGE', CONTENT: 'Welcome, client!' }));
    }, 100000)
    // WebSocket logic here
    socket.on('message', async (message) => {
        //message: Received Data from the client

        const parsedData = JSON.parse(message)
        switch (parsedData?.TYPE) {
            case 'MILKDATA':
                try {
                    const response = await milkLogFunction(parsedData?.CONTENT);

                    if (response.length > 0 || response?.return_code === 0) {
                        //Sending message back to client
                        socket.send(JSON.stringify({ TYPE: 'MILKDATA', CONTENT: response }));

                    } else {
                        //Sending message back to client
                        socket.send(JSON.stringify({ TYPE: "MILKDATA", CONTENT: [] }));
                    }
                } catch (error) {
                    //Sending message back to client
                    socket.send(JSON.stringify({ TYPE: "MILKDATA", CONTENT: [] }));

                }
                break;
            default:
                console.log(`${parsedData?.CONTENT}, for key ${parsedData?.TYPE}`)
                setTimeout(() => {
                    socket.send(JSON.stringify({ TYPE: `${parsedData?.TYPE}`, CONTENT: { client: `${parsedData?.CONTENT}`, server: "Hello from server" } }));
                }, 10000)
                break;
        }
    });

    socket.on('close', () => {
        console.log(`WebSocket connection closed `);
        if (client_id) clientsMap.delete(client_id)

    });
});


/**
 * Just a dummy trigger Api which triggers an socket event
 */
// { TYPE: 'REQUEST_MILK_DATA', CONTENT: 'Event triggered from API' }
class TriggerApi {
    async create(data, params) {
        try {
            if (clientsMap.get(data?.client_id.toString())) {
                clientsMap.get(data?.client_id.toString()).send(JSON.stringify(data))
                return { message: 'Event triggered successfully' };
            }
            return { message: "client_id missing" }

        } catch (error) {
            throw error
        }
    }
}


module.exports = { wsServer, TriggerApi }
