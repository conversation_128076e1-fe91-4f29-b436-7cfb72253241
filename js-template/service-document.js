const { extractBasedOnLanguage } = require("@krushal-it/common-core")
const {
  account_payment_type: { PAID_BY_PROVIDER_WITH_CREDIT_LIMIT, SELF_PAY, DEDUCTION_AT_SOURCE, PAY_IN_FULL, SELF_PAY_AFTER_LIMIT, PAID_BY_PROVIDER }
} = require("@krushal-it/back-end-lib/ENUMS")

const getServiceDocumentHTML = props => {
  const { data, userLanguage, krushalLogo } = props
  const { payment_detail_break_up, activity_id, customer, staff, animals, task } = data
  const { medicine_cost_payer = [], service_cost_payer = [] } = payment_detail_break_up ? payment_detail_break_up : {}

  const service_cost = service_cost_payer.reduce((acc, item) => acc + item.amount, 0)
  const medicine_cost = medicine_cost_payer.reduce((acc, item) => acc + item.amount, 0)

  const noticeHtmlTable = defaultTable(payment_detail_break_up)

  return baseHTMLTemplate({
    krushalLogo,
    userLanguage,
    customer,
    staff,
    task,
    animals,
    importantNoticeHTMLTable: noticeHtmlTable,
    service_cost,
    medicine_cost
  })
}

const payerIdToName = {
  **********: "Customer",
  **********: "Shreeja",
  **********: "Good & Green"
}
function defaultTable(payment_detail_break_up) {
  if (!payment_detail_break_up) {
    return ""
  }

  const { medicine_cost_payer, service_cost_payer, current_credit_limit, current_credit_usage } = payment_detail_break_up

  const combined_medicine_cost = []
  const combined_service_cost = []

  for (const payer of medicine_cost_payer) {
    const { payer_type, payment_type, amount } = payer
    combined_medicine_cost.push(payerBuilder(payer_type, payment_type, amount, "Medicine cost"))
  }

  for (const payer of service_cost_payer) {
    const { payer_type, payment_type, amount } = payer
    combined_service_cost.push(payerBuilder(payer_type, payment_type, amount, "Service cost"))
  }

  return combined_service_cost.join("") + combined_medicine_cost.join("") + getCurrentUsage(current_credit_limit, current_credit_usage)
}

function payerBuilder(payer_type, payment_type, amount, cost_type) {
  if (payment_type) {
    return `<tr><td> <span style="color:green;font-weight:500">${cost_type}</span> of Rs. <strong>${Math.floor(amount)}</strong> has to be paid by <strong>${payerIdToName[payer_type]}</strong>. ${payment(payment_type)}</td></tr> `
  }
  return `<tr><td><span style="color:green;font-weight:500">${cost_type}</span> of <strong>Rs. ${Math.floor(amount)}</strong> has to be paid by <strong>${payerIdToName[payer_type]}</strong></td></tr>`
}

function payment(payment_type) {
  switch (payment_type) {
    case DEDUCTION_AT_SOURCE:
      return "<i>(Total amount to be deducted from DAS at the end of the current cycle.)</i>"
    case SELF_PAY:
    case PAY_IN_FULL:
    case SELF_PAY_AFTER_LIMIT:
      return "<i>(This can be paid by UPI/Cash)</i>"
    case PAID_BY_PROVIDER:
    case PAID_BY_PROVIDER_WITH_CREDIT_LIMIT:
      return "<i>(This will be paid by provider)</i>"
  }
}

function getCurrentUsage(current_credit_limit, current_credit_usage) {
  if (current_credit_limit > 0 && current_credit_usage >= 0) {
    const remaining = Math.max(current_credit_limit - current_credit_usage, 0)
    return `<tr><td  style="width:100%;display:flex;justify-content:center;back"><p style="width:fit-content;text-align:center; background:rgba(252, 45, 3, 0.424);padding-left:3px;padding-right:3px;">Current remaining balance is Rs. <strong>${Math.floor(remaining)}</strong></p></td></tr>`
  }
  return ""
}

function baseHTMLTemplate({ krushalLogo, customer, staff, task, animals, importantNoticeHTMLTable, service_cost, medicine_cost, userLanguage }) {
  return `<!DOCTYPE html>
  <html xml:lang="en" lang="en">
  <head>
      <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
      <title>Prescription</title>
  
  
      <style>
          * {
              --line-h-pos: 102px;
              --background-color-a: hsla(255, 36%, 83%, 1);
              --background-color-b: hsla(255, 36%, 83%, .5);
              --text-color-a: #cbc6e6;
  
              --header-height: 65px;
              --brand-logo-height: 50px;
              --heading-left-margin: 200px;
              --border-color: hsla(255, 36%, 61%, 0.989);
  
          }
  
          body {
              margin: 0;
              font-family: "Noto Sans", sans-serif;
              padding: 0px;
              min-width: fit-content;
              margin: 0 auto;
          }
  
  
  
  
          @page {
              size: A4;
              margin: 0;
              margin-top: 25px;
              margin-bottom: 25px;
  
          }
  
          @page :first {
  
              margin-top: 0%;
          }
  
  
  
          header {
              display: flex;
              justify-content: center;
              align-items: center;
              height: var(--header-height);
              margin-bottom: 50px;
              padding: 5px;
  
          }
  
  
          .heading-box {
              display: flex;
              align-items: center;
              position: relative;
              height: fit-content;
              margin-top: 50px;
              width: 100%;
  
  
          }
  
  
  
          th {
              border: 1px solid #512DA8;
              padding: 5px;
              font-weight: 550;
              text-transform: capitalize;
              background-color: #FF5722;
              color: white;
          }
  
          td {
              border: 1px solid #512DA8;
              padding: 5px 10px;
          }
  
  
  
          table>caption {
  
              border: thin solid #512DA8;
              padding: 10px;
              font-weight: 600;
              font-size: 20px;
          }
  
          .total-savings {
              display: flex;
              flex-direction: row;
              justify-content: center;
              align-items: center;
              column-gap: 15px;
              background-color: #cbc6e661;
              color: #7A4090;
          }
  
          .total-savings>h1:first-child:after {
              content: " :";
          }
  
          .total-savings>h1:last-child:before {
              content: '';
          }
  
          .table-type-1 {
              margin-top: 50px;
              width: 100%;
              table-layout: fixed;
          }
  
          .table-type-1>table {
              border: thin solid #512DA8;
              width: calc(100% - 40px);
              margin-top: 50px;
              margin-left: 20px;
              margin-right: 20px;
              border-collapse: collapse;
          }
  
  
  
          .table-type-1>table>thead>tr>th {
              padding: 10px;
              font-size: 14px;
  
          }
  
          .table-type-1>table>thead>tr>th:first-child {
              width: 6%;
          }
  
          .table-type-1>table>tbody>tr>td {
              padding: 5px;
              padding-left: 20px;
              font-size: 17px;
              text-align: left;
  
          }
  
          .table-type-1>table>tbody>tr>th {
              padding: 5px;
              padding-left: 20px;
              font-size: 17px;
              text-align: left;
  
          }
  
          .capitalize {
              text-transform: capitalize;
          }
  
          .animal_table {
              width: 100%;
              margin-top: 50px;
              page-break-inside: avoid;
          }
  
          .animal_table>table {
              border-collapse: collapse;
              width: calc(100% - 40px);
              margin: 0 auto;
          }
  
          .noticeTable {
  
              width: calc(100% - 40px);
              margin: 0 auto;
              margin-top: 50px;
              border: thin solid #512DA8;
             
          }
  
          .noticeTable>table {
              width: 100%;
              border-collapse: collapse;
          }
  
          .noticeTable>table>tbody>tr {
             page-break-inside: avoid;
          }
          .noticeTable>table>thead>tr {
             page-break-inside: avoid;
          }
  
          .noticeTable>table>tbody>tr>th {
              border: none
          }
  
          .noticeTable>table>tbody>tr>td {
              border: none
          }
  
      </style>
  </head>
  
  <body>
  
      <header style='display:flex; justify-content:center; align-items:center; height:fit-content; margin-top:10px;' ><img src="data:image/png;base64,${krushalLogo[userLanguage]}" height="40px" /> </header>
      <div class="table-type-1">
          <table>
              <thead>
                  <tr>
                      <th colspan="2" style="font-size: 17px; background-color: #512DA8;color: white;">Farmer Details</th>
                  </tr>
              </thead>
              <tbody>
                  <tr>
                      <th>id</th>
                      <td>${customer.customer_visual_id ? customer.customer_visual_id : " - "}</td>
                  </tr>
                  <tr>
                      <th> name</th>
                      <td>${customer.customer_name_l10n ? extractBasedOnLanguage(customer.customer_name_l10n, userLanguage) : " - "}</td>
                  </tr>
                  <tr>
                      <th> village</th>
                      <td>${customer.village_name_l10n ? extractBasedOnLanguage(customer.village_name_l10n, userLanguage) : " - "}</td>
                  </tr>
                  <tr>
                      <th>mobile number</th>
                      <td>${customer.mobile_number}</td>
                  </tr>
              </tbody>
          </table>
      </div>
      <div class="table-type-1">
          <table>
              <thead>
                  <tr>
                      <th colspan="2" style="font-size: 17px; background-color: #512DA8;color: white;">task details</th>
                  </tr>
              </thead>
              <tbody>
                  <tr>
                      <th>ticket no.</th>
                      <td>${task.ticket_no ? task.ticket_no : " - "}</td>
                  </tr>
                  <tr>
                      <th> complaint date time</th>
                      <td>${task.activity_date ? task.activity_date : " - "}</td>
                  </tr>
                  <tr>
                      <th> follow up date</th>
                      <td>${task.follow_up_date ? task.follow_up_date : " - "}</td>
                  </tr>
                  <tr>
                      <th>activity_name</th>
                      <td>${task.activity_name ? extractBasedOnLanguage(task.activity_name, userLanguage) : " - "}</td>
                  </tr>
                  <tr>
                  <th>LSS name</th>
                  <td>${staff.paravet_name ? extractBasedOnLanguage(staff.paravet_name, userLanguage) : " - "}</td>
                  </tr>
                  <tr>
                      <th>Vet Name</th>
                      <td>${staff.vet_name ? extractBasedOnLanguage(staff.vet_name, userLanguage) : " - "}</td>
                  </tr>
  
              </tbody>
          </table>
      </div>
      <div class="animal_table">
          <table>
              <thead>
                  <tr>
                      <th colspan="3" style="font-size: 17px; background-color: #512DA8;color: white;">Animal</th>
                  </tr>
                  <tr>
                      <th>id</th>
                      <th>name</th>
                      <th>ear-tag</th>
                  </tr>
              </thead>
              <tbody>
                  ${animals
                    .map(animal => {
                      return `
                  <tr>
                      <td style="text-transform: uppercase;">${animal.animal_visual_id ? animal.animal_visual_id : " - "}</td>
                      <td>${animal.animal_name ? animal.animal_name : " - "}</td>
                      <td style="text-transform: uppercase;">#${animal.animal_ear_tag ? animal.animal_ear_tag : " - "}</td>
                  </tr>
                  `
                    })
                    .join("")}
              </tbody>
          </table>
      </div>
      <div class="animal_table">
          <table>
              <thead>
                  <tr>
                      <th colspan="3" style="font-size: 17px; background-color: #512DA8;color: white;">Service Details
                      </th>
                  </tr>
                  <tr>
                      <th>description</th>
                      <th>cost</th>
                  </tr>
              </thead>
              <tbody>
                  <tr>
                      <td style="text-transform: capitalize;">service cost</td>
                      <td style="text-transform: uppercase;">&#8377 ${service_cost ? service_cost : 0}</td>
                  </tr>
                  <tr>
                      <td style="text-transform: capitalize;">medicine cost</td>
                      <td style="text-transform: uppercase;">&#8377 ${medicine_cost ? medicine_cost : 0}</td>
                  </tr>
              </tbody>
          </table>
      </div>
      <div class="noticeTable">
          <table>
              <thead>
                  <tr>
                      <td style="font-size: 17px;text-transform: capitalize; text-align:center;background-color: #512DA8;color: white;">
                          important notice
                      </td>
                  </tr>
              </thead>
              <tbody>
                  ${importantNoticeHTMLTable.length > 5 ? importantNoticeHTMLTable : `<tr><td> &nbsp; </td></tr>`}
              </tbody>
          </table>
      </div>
  </body >
  
  </html >`
}

module.exports = { getServiceDocumentHTML, defaultTable }
