{"type": "object", "properties": {"MAILER": {"type": "object", "properties": {"TYPE": {"type": "string"}, "PROJECT_ID": {"type": "string"}, "FROM_EMAIL": {"type": "string"}, "PRIVATE_KEY_ID": {"type": "string"}, "private_key": {"type": "string"}, "CLIENT_EMAIL": {"type": "string"}, "CLIENT_ID": {"type": "string"}, "AUTH_URI": {"type": "string"}, "TOKEN_URI": {"type": "string"}, "AUTH_PROVIDER_X509_CERT_URL": {"type": "string"}, "CLIENT_X509_CERT_URL": {"type": "string"}, "UNIVERSE_DOMAIN": {"type": "string"}}, "required": ["TYPE", "PROJECT_ID", "FROM_EMAIL", "PRIVATE_KEY_ID", "private_key", "CLIENT_EMAIL", "CLIENT_ID", "AUTH_URI", "TOKEN_URI", "AUTH_PROVIDER_X509_CERT_URL", "CLIENT_X509_CERT_URL", "UNIVERSE_DOMAIN"]}, "IS_SERVER": {"type": "string"}, "IS_NODE_SERVER": {"type": "number", "enum": [1, 0]}, "APP_CONFIG": {"type": "object", "properties": {"LOG_TYPE": {"type": "string", "enum": ["JSON"]}}, "required": ["LOG_TYPE"]}, "ZOHO": {"type": "object", "properties": {"refresh_token": {"type": "string"}, "client_id": {"type": "string"}, "client_secret": {"type": "string"}, "organization_id": {"type": "number"}, "redirect_uri": {"type": "string"}, "authurl": {"type": "string"}, "apiurl": {"type": "string"}}, "required": ["refresh_token", "client_id", "client_secret", "organization_id", "redirect_uri", "authurl", "a<PERSON><PERSON><PERSON>"]}, "OBJECT_STORAGE": {"type": "object", "properties": {"ROOT_LOCATION": {"type": "string"}, "DEFAULTS": {"type": "object", "properties": {"ROOT_LOCATION": {"type": "string", "enum": ["ah"]}}, "required": ["ROOT_LOCATION"]}}, "required": ["ROOT_LOCATION", "DEFAULTS"]}, "MULTERCONFIG": {"type": "object", "properties": {"STORAGE": {"type": "string", "enum": ["disk", "memory"]}}, "required": ["STORAGE"]}, "CRON_JOBS": {"type": "object", "properties": {"ANIMAL_BACKUP": {"type": "string"}, "SNAPSHOT_VISIT_PENALTY_REPORT": {"type": "string"}, "DAILY_CATTLE_RX_REPORT": {"type": "string"}, "DAILY_CATTLE_ANTIBIOTIC_REPORT": {"type": "string"}, "MONTHLY_CUSTOMER_ANTIBIOTIC_REPORT": {"type": "string"}}, "required": ["ANIMAL_BACKUP", "DAILY_CATTLE_RX_REPORT", "DAILY_CATTLE_ANTIBIOTIC_REPORT", "MONTHLY_CUSTOMER_ANTIBIOTIC_REPORT", "SNAPSHOT_VISIT_PENALTY_REPORT"]}, "APP_ID": {"type": "object", "properties": {"WEB_APP": {"type": "string"}}, "required": ["WEB_APP"]}, "DEPLOYMENT": {"type": "object", "properties": {"LISTEN_PORT": {"type": "number", "enum": [3001]}, "HTTPS_ENABLED": {"type": "number", "enum": [-1, 1]}}}, "GOOGLE": {"type": "object", "properties": {"API_KEY": {"type": "string"}, "GOOGLE_API_KEY": {"type": "string"}, "GOOGLE_PROJECT_ID": {"type": "string"}, "TRANSLATION": {"type": "object", "properties": {"ENABLE_TRANSLATION": {"type": "string", "enum": ["true", "false"]}, "CRON_EXPRESSION": {"type": "string"}, "DEST_LANGS": {"type": "array", "items": {"type": "string"}}, "BATCH_SIZE": {"type": "number"}, "TABLES": {"type": "array", "items": {"type": "object", "properties": {"table_name": {"type": "string"}, "db": {"type": "string"}, "primary_col": {"type": "string"}, "columns": {"type": "array", "items": {"type": "string"}}}, "required": ["table_name", "db", "primary_col", "columns"]}}}}}, "required": ["API_KEY", "GOOGLE_API_KEY", "GOOGLE_PROJECT_ID", "TRANSLATION"]}, "FIREBASE_AUTH": {"type": "object", "properties": {"type": {"type": "string"}, "project_id": {"type": "string"}, "private_key_id": {"type": "string"}, "private_key": {"type": "string"}, "client_email": {"type": "string"}, "client_id": {"type": "string"}, "auth_uri": {"type": "string"}, "token_uri": *********************************************************************, "client_x509_cert_url": {"type": "string"}, "universe_domain": *************************************************************************************************************************************************************************************************************************, "AWS": {"type": "object", "properties": {"AWS_REGION": {"type": "string"}, "AWS_ACCESS_KEY": {"type": "string"}, "AWS_SECRET_ACCESS_KEY": {"type": "string"}, "AWS_BUCKET_NAME": {"type": "string"}, "AWS_BUCKET_ROOT_LOCATION": {"type": "string"}}, "required": ["AWS_REGION", "AWS_ACCESS_KEY", "AWS_SECRET_ACCESS_KEY", "AWS_BUCKET_NAME", "AWS_BUCKET_ROOT_LOCATION"]}, "LOCATION_ANALYTICS": {"type": "object", "properties": {"STAY_POINT_DIST_THRESHOLD": {"type": "number"}, "STAY_POINT_TIME_THRESHOLD": {"type": "number"}, "STAY_POINT_TASK_TIME_MINUTES": {"type": "number"}}}, "DB_VARIABLES": {"type": "object", "properties": {"POSTGRES_COMMON": {"type": "object", "properties": {"HOST": {"type": "string"}, "PORT": {"type": "number"}, "USER_NAME": {"type": "string"}, "PASSWORD": {"type": "string"}, "DATABASE": {"type": "string"}}}, "POSTGRES_MAIN": {"type": "object", "properties": {"HOST": {"type": "string"}, "PORT": {"type": "number"}, "USER_NAME": {"type": "string"}, "PASSWORD": {"type": "string"}, "DATABASE": {"type": "string"}}}, "POSTGRES_REPORTING": {"type": "object", "properties": {"HOST": {"type": "string"}, "PORT": {"type": "number"}, "USER_NAME": {"type": "string"}, "PASSWORD": {"type": "string"}, "DATABASE": {"type": "string"}}}, "POSTGRES_CONFIG": {"type": "object", "properties": {"HOST": {"type": "string"}, "PORT": {"type": "number"}, "USER_NAME": {"type": "string"}, "PASSWORD": {"type": "string"}, "DATABASE": {"type": "string"}}}, "POSTGRES_LOCATION": {"type": "object", "properties": {"HOST": {"type": "string"}, "PORT": {"type": "number"}, "USER_NAME": {"type": "string"}, "PASSWORD": {"type": "string"}, "DATABASE": {"type": "string"}}}}, "required": ["POSTGRES_COMMON", "POSTGRES_MAIN", "POSTGRES_REPORTING", "POSTGRES_LOCATION", "POSTGRES_CONFIG"]}}, "required": ["MAILER", "IS_SERVER", "IS_NODE_SERVER", "APP_CONFIG", "ZOHO", "OBJECT_STORAGE", "MULTERCONFIG", "CRON_JOBS", "APP_ID", "DEPLOYMENT", "GOOGLE", "FIREBASE_AUTH", "AWS", "LOCATION_ANALYTICS", "DB_VARIABLES"], "additionalProperties": true}