SHELL=/bin/bash

# Below two lines allows us to accept extra arguments (by doing nothing when we get a job that doesn't match, rather than throwing an error)
%:
	@:

INPUT_ARGS := $(wordlist 2,$(words $(MAKECMDGOALS)),$(MAKECMDGOALS))
serviceRegistryName=$$(jq -r .serviceRegistry package.json)
serviceVersion = $$(jq -r .serviceVersion package.json)
servicePackageName = $(word 1,$(INPUT_ARGS))
SYSTEM_DATE=$$(date)
KRUSHAL_PACKAGES=$$(jq -r '.dependencies|keys|.[]' package.json | grep '@krushal-it' | tr "\n" " ")

ifeq ($(servicePackageName),)  
	# If no input is provided via command line, defaults to value of servicePackageName key in package.json 
    servicePackageName = $$(jq -r .serviceName package.json)
endif

CURRENT_DEPLOYED_IMAGE := $$(docker inspect --format='{{.Config.Image}}' $$(docker ps -qf name=back-end))

$(info )
$(info )

.PHONY:
.DEFAULT_GOAL: guide

restart-and-show-logs:
	@docker restart back-end
	@docker logs -f -n 100 back-end

show-logs:	
	@docker logs -f -n 100 back-end

stop-docker:
	@docker stop back-end

install:
	@export GITLAB_AUTH_TOKEN=${READ_KRUSHAL_IT_GITLAB_AUTH_TOKEN} && npm install

clean-krushal-packages-and-install:
	@rm -rf node_modules/@krushal-it node_modules/@krushal-oc package-lock.json
	@export GITLAB_AUTH_TOKEN=${READ_KRUSHAL_IT_GITLAB_AUTH_TOKEN} && npm install
clean-krushal-packages-and-install-force:
	@rm -rf node_modules/@krushal-it node_modules/@krushal-oc package-lock.json
	@export GITLAB_AUTH_TOKEN=${READ_KRUSHAL_IT_GITLAB_AUTH_TOKEN} && npm install --force

clean-install:
	@rm -rf node_modules package-lock.json
	@export GITLAB_AUTH_TOKEN=${READ_KRUSHAL_IT_GITLAB_AUTH_TOKEN} && npm install

push-env:
	@sh updateEnvJsonToDotEnv.sh

clean-build:
	@rm -rf build && grunt --gruntfile Gruntfile.js

docker-build-latest:
	@cd build && docker build --no-cache --build-arg GITLAB_AUTH_TOKEN=${READ_KRUSHAL_IT_GITLAB_AUTH_TOKEN} -t $(serviceRegistryName)/$(servicePackageName):latest .

docker-build:
	@cd build && docker build --no-cache --build-arg GITLAB_AUTH_TOKEN=${READ_KRUSHAL_IT_GITLAB_AUTH_TOKEN} -t $(serviceRegistryName)/$(servicePackageName):$(serviceVersion) .

docker-push-latest:
	@docker login registry.gitlab.com -u $(GITLAB_REGISTRY_USER) -p $(GITLAB_REGISTRY_PASSWORD)
	@cd build && docker push $(serviceRegistryName)/$(servicePackageName):latest
	@docker logout registry.gitlab.com

docker-push:
	@docker login registry.gitlab.com -u $(GITLAB_REGISTRY_USER) -p $(GITLAB_REGISTRY_PASSWORD)
	@cd build && docker push $(serviceRegistryName)/$(servicePackageName):$(serviceVersion)
	@docker logout registry.gitlab.com

force-clean-deploy:
	@echo "                            STARTING THE BUILD AND DEPLOY PROCESS"; \
	echo "==============================================================================================="; \
	echo ""; \
	echo "===============  PULLING LATEST CODE  ==============="; \
	echo ""; \
	git pull; \
	echo ""; \
	read -p "Enter the environment on which this needs to be deployed (ahs1, ahs2, ahs3, ahs4,ahs6 , kamdhenu-live, shreeja-live, paras-live, krushal-live, itc-live) (Mandatory Input): " DEPLOYMENT_ENVIRONMENT_INPUT; \
	echo ""; \
	if [ -z "$$DEPLOYMENT_ENVIRONMENT_INPUT" ]; then echo "ERROR: Deploy Environment cant be blank. Aborting the build"; echo ""; exit; fi; \
	echo ""; \
	echo "===============  CURRENT IMAGE DETAILS ON $$DEPLOYMENT_ENVIRONMENT_INPUT ==============="; \
	echo ""; \
	echo ${CURRENT_DEPLOYED_IMAGE}; \
	echo ""; \
	echo "-----------------------------------------------------------------------------------------------"; \
	echo ""; \
	read -p "Enter Image Tag Name / version (press ENTER for using latest): " IMAGE_TAG_INPUT; \
	if [ -z "$$IMAGE_TAG_INPUT" ]; then IMAGE_TAG_INPUT=latest; echo ""; echo "Defaulting the IMAGE TAG to latest"; fi; \
	echo ""; \
	echo "===============  INSTALLING ALL DEPENDENCIES  ==============="; \
	echo ""; \
	npm install; \
	echo ""; \
	echo "===============  FORCE UPDATING KRUSHAL DEPENDENCIES  ==============="; \
	echo ""; \
	npm install $(KRUSHAL_PACKAGES); \
	echo ""; \
	echo "===============  BUILDING AND DEPLOYING DOCKER IMAGE VERSION - $$IMAGE_TAG_INPUT TO $$DEPLOYMENT_ENVIRONMENT_INPUT ==============="; \
	echo ""; \
	export IMAGE_TAG=$$IMAGE_TAG_INPUT && docker compose -f docker_build/$$DEPLOYMENT_ENVIRONMENT_INPUT/docker-compose.yml up --build -d --no-deps
deploy-tag:
	@echo "                            STARTING THE DEPLOY PROCESS"; \
	echo "==============================================================================================="; \
	echo ""; \
	read -p "Enter the environment on which this needs to be deployed (ahs1, ahs2, ahs3, ahs4 ,ahs6 , kamdhenu-live, shreeja-live, paras-live, krushal-live, itc-live) (Mandatory Input): " DEPLOYMENT_ENVIRONMENT_INPUT; \
	echo ""; \
	if [ -z "$$DEPLOYMENT_ENVIRONMENT_INPUT" ]; then echo "ERROR: Deploy Environment cant be blank. Aborting the build"; echo ""; exit; fi; \
	read -p "Enter Image Tag Name / version (Mandatory Input): " IMAGE_TAG_INPUT; \
	if [ -z "$$IMAGE_TAG_INPUT" ]; then echo "ERROR: Deploy Image Tag cant be blank. Aborting the deploy"; echo ""; exit; fi; \
	echo ""; \
	echo "===============  DEPLOYING DOCKER IMAGE TAG - $$IMAGE_TAG_INPUT TO $$DEPLOYMENT_ENVIRONMENT_INPUT ==============="; \
	echo ""; \
	export IMAGE_TAG=$$IMAGE_TAG_INPUT && docker compose -f docker_build/$$DEPLOYMENT_ENVIRONMENT_INPUT/docker-compose.yml up --no-build -d --no-deps
