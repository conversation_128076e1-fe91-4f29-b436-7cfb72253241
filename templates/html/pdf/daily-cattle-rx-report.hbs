<!DOCTYPE html>
<html xml:lang="en" lang="en">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>Prescription</title>


    <style>
        * {
            --line-h-pos: 102px;
            --background-color-a: hsla(255, 36%, 83%, 1);
            --background-color-b: hsla(255, 36%, 83%, .5);
            --text-color-a: #cbc6e6;

            --header-height: 65px;
            --brand-logo-height: 50px;
            --heading-left-margin: 200px;
            --border-color: hsla(255, 36%, 61%, 0.989);

        }

        body {
            margin: 0;
            font-family: sans-serif;
            padding: 0px;
            min-width: fit-content;
            margin: 0 auto;
        }




        @page {
            size: landscape A4;
            margin: 0;
            margin-top: 25px;
            margin-bottom: 25px;

        }

        @page :first {

            margin-top: 0%;
        }



        header {
            display: flex;
            justify-content: center;
            align-items: center;
            height: var(--header-height);
            margin-bottom: 50px;
            padding: 5px;

        }


        .heading-box {
            display: flex;
            align-items: center;
            position: relative;
            height: fit-content;
            margin-top: 50px;
            width: 100%;


        }



        th {
            border: 1px solid var(--border-color);
            padding: 5px;
            font-weight: 550;
            text-transform: capitalize;
            background-color: #cbc6e6;
        }

        td {
            border: 1px solid hsla(255, 36%, 61%, 0.325);
            ;
            padding: 5px 10px;
        }



        table>caption {

            border: thin solid var(--border-color);
            padding: 10px;
            font-weight: 600;
            font-size: 20px;
        }

        .total-savings {
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            column-gap: 15px;
            background-color: #cbc6e661;
            color: #7A4090;
        }

        .total-savings>h1:first-child:after {
            content: " :";
        }

        .total-savings>h1:last-child:before {
            content: '';
        }

        /* .total-savings>h1:last-child::after {
            content: '\2215\2013';
        } */

        .table-type-1 {
            margin-top: 50px;
            width: 100%;
            table-layout: fixed;
        }

        .table-type-1>table {
            border: thin solid var(--background-color-b);
            width: calc(100% - 40px);
            margin-top: 50px;
            margin-left: 20px;
            margin-right: 20px;
            border-collapse: collapse;
        }



        .table-type-1>table>thead>tr>th {
            padding: 10px;
            font-size: 12px;
        }

        .table-type-1>table>thead>tr>th:first-child {
            width: 6%;
        }

        .table-type-1>table>tbody>tr>td {
            padding: 3px;
            font-size: 12px;
            text-align: center;

        }

        .capitalize {
            text-transform: capitalize;
        }
    </style>
</head>

<body>

    <header>
        <img src="data:image/png;base64,{{data.image}}" width="350px" />
    </header>
    <div class="total-savings">
        <h1 class="capitalize">daily reports of animal</h1>
        <h1>{{data.antibiotic_report_date}}</h1>
    </div>
    <div class="table-type-1">


        <table>
            <thead>
                <tr>
                    <th> No.</th>
                    <th style="width:auto">Taluk</th>
                    <th style="width:auto">Village</th>
                    <th style="width:auto">BCO</th>
                    <th style="width:auto">Ph No.</th>
                    <th style="width:auto">Farmer</th>
                    <th style="width:auto">Ph No.</th>
                    <th style="width:auto">Ear-Tag</th>
                    <th style="width:auto"># diaganosis</th>
                    <th style="width:auto">diaganosis-1</th>
                    <th style="width:auto">diaganosis-2</th>
                    <th style="width:auto"># medicines</th>
                    <th style="width:auto">medicine-1</th>
                    <th style="width:auto">medicine-2</th>
                    <th style="width:auto">medicine-3</th>
                </tr>
            </thead>
            <tbody>
                {{#if data.antibiotic_report}}
                {{#each data.antibiotic_report}}
                <tr>
                    <td style="text-align: center;">{{ inc @index}}</td>
                    <td>{{taluk_name_l10n}}</td>
                    <td>{{village_name_l10n}}</td>
                    <td>{{staff_name_l10n}}</td>
                    <td>{{staff_mobile_number}}</td>
                    <td>{{customer_name_l10n}}</td>
                    <td>{{customer_mobile_number}}</td>
                    <td>{{ear_tag}}</td>
                    <td>{{diagnosis_count}}</td>
                    <td>{{diagnosis_1}}</td>
                    <td>{{diagnosis_2}}</td>
                    <td>{{medicine_count}}</td>

                    {{#ifEquals medicine_1_antibiotic_status "Y"}}
                    <td style="">
                        <span style="background-color: #ee05055e;">{{medicine_1}}</span>
                    </td>
                    {{/ifEquals}}
                    {{#ifEquals medicine_1_antibiotic_status "N"}}
                    <td>{{medicine_1}}</td>
                    {{/ifEquals}}
                    {{#unless medicine_1}}
                    <td>{{medicine_1}}</td>
                    {{/unless}}


                    {{#ifEquals medicine_2_antibiotic_status "Y"}}
                    <td style="">
                        <span style="background-color: #ee05055e;">{{medicine_2}}</span>
                    </td>
                    {{/ifEquals}}
                    {{#ifEquals medicine_2_antibiotic_status "N"}}
                    <td>{{medicine_2}}</td>
                    {{/ifEquals}}
                    {{#unless medicine_2}}
                    <td>{{medicine_2}}</td>
                    {{/unless}}


                    {{#ifEquals medicine_3_antibiotic_status "Y"}}
                    <td style="">
                        <span style="background-color: #ee05055e;">{{medicine_3}}</span>
                    </td>
                    {{/ifEquals}}
                    {{#ifEquals medicine_3_antibiotic_status "N"}}
                    <td>{{medicine_3}}</td>
                    {{/ifEquals}}
                    {{#unless medicine_3}}
                    <td>{{medicine_3}}</td>
                    {{/unless}}


                </tr>
                {{/each}}
                {{/if}}
            </tbody>
        </table>

    </div>
</body>

</html>