<!DOCTYPE html>
<html xml:lang="en" lang="en">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>Prescription</title>
    <style>
        * {
            --line-h-pos: 102px;
            --background-color-a: #FF5722;
            --background-color-b: #512DA8;
            --text-color-a: #ffffff;

            --header-height: 65px;
            --brand-logo-height: 50px;
            --heading-left-margin: 200px;
            --border-color: #522da8c1;
        }

        body {
            margin: 0;
            font-family: sans-serif;
            padding: 0px;
            min-width: fit-content;
            margin: 0 auto;
        }


        @page {
            size: A4;
            margin: 0;
            margin-top: 20px;
            margin-bottom: 50px;
        }

        @page :first {

            margin-top: 0%;
        }




        header {
            display: flex;
            justify-content: center;
            align-items: center;
            height: var(--header-height);
            margin-bottom: 50px;
            padding: 5px;

        }


        .heading-box {
            display: flex;
            align-items: center;
            position: relative;
            height: fit-content;
            margin-top: 20px;
            width: 100%;


        }

        .heading {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: var(--background-color-a);
            color: var(--text-color-a);
            height: 25px;
            width: 350px;
            font-size: 20px;
            padding: 5px;
            page-break-before: auto;
        }

        #left {
            height: 31.5px;
            width: 55px;
            margin: 0;
            border: none;
            border-bottom: 3px solid var(--background-color-a);
            margin-bottom: 0px;

        }

        #right {
            height: 32.5px;
            width: calc(100% - 350px - 55px);
            margin: 0;
            border: none;
            border-top: 3px solid var(--background-color-a);
            margin-top: 0px;
        }

        .cattle-information {
            width: 100%;
            margin-left: 50px;
            margin-top: 30px;
            margin-right: 20px;
            margin-bottom: 40px;
        }

        .cattle-information>table {
            width: 100%;
        }



        .cattle-information>table>tbody>tr>th::after {
            content: ' : ';
            font-weight: bold;
            margin-left: auto;
        }

        .cattle-information>table>tbody>tr>td {
            width: fit-content;
            border: none;
            background-color: white;
            padding: 5px;
            font-size: 17px;
        }

        .cattle-information>table>tbody>tr>th {
            border: none;
            background-color: white;
            padding: 5px;
            font-size: 17px;
            text-align: start;
            color: black;
        }

        th {
            border: 1px solid var(--border-color);
            padding: 5px;
            font-weight: 550;
            text-transform: capitalize;
            background-color: var(--background-color-a);
            color: var(--text-color-a)
        }

        td {
            border: 1px solid var(--background-color-b);
            padding: 5px 10px;
        }

        .animal-summary {
            margin-top: 50px;
        }

        .animal-summary>table {
            border: 1px solid var(--background-color-b);
            width: calc(100% - 100px);
            margin-top: 30px;
            margin-left: 50px;
            margin-right: 50px;
            border-collapse: collapse;
        }



        .animal-summary>table>thead>tr>th {
            padding: 5px;
            font-size: 12px;
        }

        .animal-summary>table>tbody>tr>td {
            padding: 5px;
            font-size: 13px;
            font-weight: 500;
        }

        table>caption {

            border: thin solid var(--border-color);
            padding: 10px;
            font-weight: 600;
            font-size: 20px;
        }

        .total-savings {
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            column-gap: 15px;
            background-color: #512DA8;
            color: var(--text-color-a);
            margin-bottom: 0;
        }

        .total-savings>h1:first-child:after {
            content: " -";
        }



        /* .total-savings>h1:last-child::after {
            
          } */

        .summary {
            margin-top: 50px;
        }

        .summary>table {
            border: 1px solid var(--background-color-a);
            width: calc(100% - 100px);
            margin-top: 20px;
            margin-left: 50px;
            margin-right: 50px;
            border-collapse: collapse;
        }



        .summary>table>thead>tr>th {
            padding: 5px;
            font-size: 12px;
        }

        .summary>table>tbody>tr>td {
            text-align: center;
            padding: 5px;
            font-size: 13px;
            font-weight: 500
        }

        #reproductive-curative-table-heading {
            color: var(--text-color-a);
            background-color: var(--background-color-b);
            border: thin solid var(--background-color-b);
            font-size: 15px;
        }

        #preventive-table-heading {
            background-color: var(--background-color-b);
            font-size: 15px;
            border: thin solid var(--background-color-b)
        }

        #cost_summary_by_cattle_heading {
            background-color: var(--background-color-b);
            font-size: 15px;
        }

        #each-table-total {
            background-color: var(--background-color-a);
            font-size: 14px;
            font-weight: 600;
            text-align: center;
            color: white;
        }

        .ruppee::before {
            content: "\20B9";

        }

        .empty-row {
            height: 15px;
        }
    </style>
</head>

<body>
    <header>
        <img src="data:image/png;base64,{{data.image}}" height="60px" />
    </header>
    <div>
        <div class="heading-box">
            <hr id="left">
            <div class="heading">{{data.constant.customer_infomation}}</div>
            <hr id="right">
            <p></p>
        </div>

        <div class="cattle-information">
            <table>
                {{#if data}}
                <tbody>
                    <tr>
                        <th>{{data.constant.name}}</th>
                        <td>{{data.customer.customer_name_l10n}}</td>
                    </tr>
                    <tr>
                        <th>{{data.constant.village}}</th>
                        <td>{{data.customer.village_name_l10n}}</td>
                    </tr>
                    <tr>
                        <th>{{data.constant.taluk}}</th>
                        <td>{{data.customer.taluk_name_l10n}}</td>
                    </tr>
                    <tr>
                        <th>{{data.constant.district}}</th>
                        <td>{{data.customer.district_name_l10n}}</td>
                    </tr>
                    <tr>
                        <th>{{data.constant.state}}</th>
                        <td>{{data.customer.state_name_l10n}}</td>
                    </tr>
                    <tr>
                        <th>{{data.constant.mobile_number}}</th>
                        <td>{{data.customer.mobile_number}}</td>
                    </tr>

                </tbody>
                {{/if}}
                {{#unless data}}
                <tbody>
                    <tr>
                        <th>{{data.constant.name}}</th>
                        <td>N/A</td>
                    </tr>
                    <tr>
                        <th>{{data.constant.village}}</th>
                        <td>N/A</td>
                    </tr>
                    <tr>
                        <th>{{data.constant.taluk}}</th>
                        <td>N/A</td>
                    </tr>
                    <tr>
                        <th>{{data.constant.district}}</th>
                        <td>N/A</td>
                    </tr>
                    <tr>
                        <th>{{data.constant.state}}</th>
                        <td>N/A</td>
                    </tr>
                    <tr>
                        <th>{{data.constant.mobile_number}}</th>
                        <td>N/A</td>
                    </tr>
                </tbody>
                {{/unless}}
            </table>
        </div>
    </div>
    <div class="total-savings"
        style="display: flex;flex-direction: row;justify-content: center;align-items: center;column-gap: 15px;background-color: #512DA8;color:#ffffff; ;margin-bottom: 0;">
        <h1>{{data.constant.cost_to_krushal}} {{data.summary_generated_on_date}}<sup>*</sup></h1>
        <h1>&#8377;{{data.total_cost}}</h1>
    </div>
    <p style="font-size:16px;text-align: center;margin:0">
        *{{data.constant.money_spent_before_sep_2023_not_included}}
    </p>
    <div class="summary">
        <div class="heading-box">
            <hr id="left" style=" border-bottom: 3px solid #512DA8;">
            <div id='cost_summary_by_cattle_heading' class="heading">{{data.constant.cost_summary_by_cattle}}</div>
            <hr id="right" style=" border-top: 3px solid #512DA8;">
            <p></p>
        </div>

        <table>
            <thead>
                <tr>
                    <th style="width: 15px;"></th>
                    <th>{{data.constant.cattle_type}}</th>
                    <th>{{data.constant.ear_tag}}</th>
                    <th>{{data.constant.subscription_plan}}</th>
                    <th>{{data.constant.start_date}}</th>
                    <th>{{data.constant.medicine_cost}}</th>
                    <th>{{data.constant.service_cost}}</th>
                    <th>{{data.constant.total_cost}}</th>
                </tr>
            </thead>
            <tbody>
                {{#if data.animal_summary}}
                {{#each data.animal_summary}}
                <tr>
                    <td style="text-align: center;">{{ inc @index}}</td>
                    <td>{{animal_type}}</td>
                    <td>{{animal_eartag}}</td>
                    <td>{{animal_subscription_plan_l10n}}</td>
                    <td>{{animal_subscription_plan_start_date}}</td>
                    <td class="ruppee">{{medicine_cost}}</td>
                    <td class="ruppee">{{service_cost}}</td>
                    <td class="ruppee">{{total_cost}}</td>
                </tr>
                {{/each}}
                {{/if}}

                {{#if data.animal_summary}}
                <tr>
                    <td id="each-table-total" colspan="5"> {{data.constant.total_cost}}</td>
                    <td class="ruppee">{{data.summary_total_medicine_cost}}</td>
                    <td class="ruppee">{{data.summary_total_service_cost}}</td>
                    <td class="ruppee">{{data.total_cost}}</td>
                </tr>
                {{/if}}

                {{#unless data.animal_summary}}
                <tr>
                    <td class='empty-row' colspan="8" style="text-align: center; color:#ff6868">-</td>
                </tr>
                {{/unless}}
            </tbody>
        </table>

    </div>
    <div style="page-break-before: always;" class="animal-summary">
        <div class="heading-box">
            <hr id="left">
            <div class="heading">{{data.constant.detailed_cost}}</div>
            <hr id="right">
            <p></p>
        </div>
        <table>
            <thead>
                <tr>
                    <th id="reproductive-curative-table-heading" colspan="7">
                        {{data.constant.curative}} / {{data.constant.reproductive}}</th>
                </tr>
                <tr>
                    <th style="width: 15px;"></th>
                    <th style="width: 12%;">{{data.constant.date}}</th>
                    <th style="width: 13%;">{{data.constant.ear_tag}}</th>
                    <th style="width: 35%;">{{data.constant.activity_name}}</th>
                    <th>{{data.constant.medicine_cost}}</th>
                    <th>{{data.constant.service_cost}}</th>
                    <th>{{data.constant.total_cost}}</th>
                </tr>
            </thead>
            <tbody>
                {{#if data.animal_detailed_record_by_activity_category.Reproductive_Curative}}
                {{#each data.animal_detailed_record_by_activity_category.Reproductive_Curative}}
                <tr>
                    <td style="text-align: center;">{{inc @index}}</td>
                    <td>{{activity_date}}</td>
                    <td>{{animal_eartag}}</td>
                    <td>{{activity_name_l10n}}</td>
                    <td class="ruppee">{{medicine_cost}}</td>
                    <td class="ruppee">{{service_cost}}</td>
                    <td class="ruppee">{{total_cost}}</td>
                </tr>
                {{/each}}
                {{/if}}

                {{#if data.hasCurativeAndReproductive}}
                <tr>
                    <td id="each-table-total" colspan="4"> {{data.constant.total_cost}}</td>
                    <td class="ruppee">{{data.curative_reproductive_total_medicine_cost}}</td>
                    <td class="ruppee">{{data.curative_reproductive_total_service_cost}}</td>
                    <td class="ruppee">{{data.curative_reproductive_total_cost}}</td>
                </tr>
                {{/if}}

                {{#unless data.hasCurativeAndReproductive}}
                <tr>
                    <td class='empty-row' style="text-align: center; color:#ff6868"></td>
                    <td class='empty-row' style="text-align: center; color:#ff6868"></td>
                    <td class='empty-row' style="text-align: center; color:#ff6868"></td>
                    <td class='empty-row' style="text-align: center; color:#ff6868"></td>
                    <td class='empty-row' style="text-align: center; color:#ff6868"></td>
                    <td class='empty-row' style="text-align: center; color:#ff6868"></td>
                    <td class='empty-row' style="text-align: center; color:#ff6868"></td>
                </tr>
                {{/unless}}
            </tbody>
        </table>

        <table>
            <thead>
                <tr>
                    <th id='preventive-table-heading' colspan="7">{{data.constant.preventive}}
                    </th>
                </tr>
                <tr>
                    <th style="width: 15px;"></th>
                    <th style="width: 12%;">{{data.constant.date}}</th>
                    <th style="width: 13%;">{{data.constant.ear_tag}}</th>
                    <th style="width: 35%;">{{data.constant.activity_name}}</th>
                    <th>{{data.constant.medicine_cost}}</th>
                    <th>{{data.constant.service_cost}}</th>
                    <th>{{data.constant.total_cost}}</th>
                </tr>
            </thead>
            <tbody>
                {{#if data.animal_detailed_record_by_activity_category.Preventive}}
                {{#each data.animal_detailed_record_by_activity_category.Preventive}}
                <tr>
                    <td style="text-align: center;">{{inc @index}}</td>
                    <td>{{activity_date}}</td>
                    <td>{{animal_eartag}}</td>
                    <td style="font-weight:500">{{activity_name_l10n}}</td>
                    <td class="ruppee">{{medicine_cost}}</td>
                    <td class="ruppee">{{service_cost}}</td>
                    <td class="ruppee">{{total_cost}}</td>
                </tr>
                {{/each}}
                {{/if}}

                {{#if data.animal_detailed_record_by_activity_category.Preventive}}
                <tr>
                    <td id="each-table-total" colspan="4"> {{data.constant.total_cost}}</td>
                    <td class="ruppee">{{data.preventive_total_medicine_cost}}</td>
                    <td class="ruppee">{{data.preventive_total_service_cost}}</td>
                    <td class="ruppee">{{data.preventive_total_cost}}</td>
                </tr>
                {{/if}}

                {{#unless data.animal_detailed_record_by_activity_category.Preventive}}
                <tr>
                    <td class='empty-row' colspan="7" style="text-align: center; color:#ff6868"></td>
                </tr>
                {{/unless}}
            </tbody>
        </table>

    </div>
</body>

</html>