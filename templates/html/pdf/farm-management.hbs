<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Farm Visit Report</title>
    <style>
        body {
            font-family: "Noto Sans", sans-serif;
            line-height: 1.5;
            margin: 0;
            padding: 20px;
        }

        @page {
            size: A4;
            margin: 0;
            margin-top: 20px;
            margin-bottom: 20px;
        }

        @page :first {
            margin-top: 0%;
        }

        .logo {
            text-align: center;
            margin-bottom: 20px;
        }

        .logo img {
            max-width: 200px;
        }

        h1,
        h2 {
            text-align: center;
        }

        h4 {
            margin: 5px 0;
            font-size: 18px;
        }

        .farmer-name {
            text-align: center;
            font-size: 1.2em;
            margin-bottom: 20px;
            font-size: 16px;
        }

        .visit-info {
            border-top: 1px solid #ec6237;
            border-bottom: 1px solid #ec6237;
            padding: 10px 0;
            margin: 20px 0;
            display: flex;
            justify-content: space-between;
        }

        .row-data {
            font-size: 14px;
        }

        .subcategory {
            list-style-type: none;
            padding: 0;
            font-size: 18px;
        }

        .subcategory li {
            margin-bottom: 2px;
            padding-left: 25px;
            position: relative;
        }

        .sub-name {
            display: flex;
            align-items: center;
            font-size: 16px;
        }

        .sub-name::before {
            content: "●";
            margin-right: 8px;
            color: black;
            font-size: 1.2em;
        }

        .advice-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 5px;
        }

        .advice-bullet {
            width: 12px;
            height: 12px;
            margin-right: 8px;
            margin-top: 6px;
            flex-shrink: 0;
        }

        .advice-bullet.warning {
            background-color: #FFFF00;
            border: 1px solid #000;
        }

        .advice-bullet.danger {
            background-color: #d60606;
            border: 1px solid #000;
        }

        .advice-text {
            flex-grow: 1;
        }

        ol {
            padding-left: 0;
            margin-top: 0;
            margin-bottom: 0;
        }

        ol li {
            margin-bottom: 1px;
            padding-left: 20px;
            list-style-position: inside;
            font-size: 16px;
            word-wrap: break-word;
            overflow-wrap: break-word;
            white-space: normal;
            padding: 5px 20px;
        }

        .warning {
            background-color: #FFFF00;
        }

        .danger {
            background-color: #d60606;
            color: white;
        }

        .checklist {
            padding-left: 20px;
        }

        .side-heading {
            margin-top: 20px;
            margin-bottom: 10px;
            color: #ec6237;
        }

        .year-table {
            width: 75%;
            margin-bottom: 20px;
            margin-left: 80px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
        }

        th,
        td {
            border: 1px solid rgba(103, 58, 183, 0.1);
            padding: 4px;
            text-align: center;
            background-color: #fff;
        }

        th {
            border: 1px solid rgba(42, 21, 79, 0.1);
            background-color: #673ab7;
            color: white;
        }

        th:first-child,
        td:first-child {
            width: 28%;
            text-align: center;
            padding-left: 8px;
            white-space: normal;
        }

        th:not(:first-child),
        td:not(:first-child) {
            width: 6%;
        }

        th {
            border: 1px solid rgba(42, 21, 79, 0.1);
            background-color: #673ab7;
            color: white;
            font-size: 11px;
            padding: 6px 2px;
        }

        .color1 {
            --color1: #d60606;
        }

        .color2 {
            --color2: #FFFF00;
        }

        .color3 {
            --color3: #00FF00;
        }

        td.color1:not(.striped) {
            background-color: var(--color1);
        }

        td.color2:not(.striped) {
            background-color: var(--color2);
        }

        td.color3:not(.striped) {
            background-color: var(--color3);
        }

        td.striped {
            position: relative;
        }

        td.striped.color1.color2:not(.color3) {
            background-image: linear-gradient(90deg, var(--color1) 0%, var(--color1) 50%, var(--color2) 50%, var(--color2) 100%);
        }

        td.striped.color2.color3:not(.color1) {
            background-image: linear-gradient(90deg, var(--color2) 0%, var(--color2) 50%, var(--color3) 50%, var(--color3) 100%);
        }

        td.striped.color1.color3:not(.color2) {
            background-image: linear-gradient(90deg, var(--color1) 0%, var(--color1) 50%, var(--color3) 50%, var(--color3) 100%);
        }

        td.striped.color1.color2.color3 {
            background-image: linear-gradient(90deg, var(--color1) 0%, var(--color1) 33.33%, var(--color2) 33.33%, var(--color2) 66.66%, var(--color3) 66.66%, var(--color3) 100%);
        }

        .page-break {
            page-break-after: always;
        }

        .year-heading-empty {
            display: none;
        }

        .visit-info-empty {
            padding: 10px 0;
            margin: 20px 0;
            display: flex;
            justify-content: space-between;
        }

        @media print {
            page-break {
                page-break-after: always;
            }

            body {
                padding-top: 50px;
            }

            @page {
                margin-top: 30px;
            }

            .year-table {
                page-break-inside: avoid;
            }

            .no-data-message {
                background-color: #f8d7da;
                border: 1px solid #f5c6cb;
                color: #721c24;
                padding: 10px;
                margin: 10px 0;
                border-radius: 5px;
            }

            .visit-info-empty {
                padding: 10px 0;
                margin: 20px 0;
                display: flex;
                justify-content: space-between;
            }

            .year-heading-empty {
                display: none;
            }
        }
    </style>
</head>

<body>
    <header style='display:flex; justify-content:center; align-items:center; height:fit-content; margin-top:10px;'>
        <img src="data:image/png;base64,{{data.image}}" height="40px" alt="Krushal Logo" />
    </header>
    <h2 style='margin-bottom:6px'>Farm Management Recommendations and Report</h2>
    {{#if data.visits}}
    {{#each data.visits}}

    <h3 style="text-align:center; margin-bottom:5px">
        {{#if data.customer.customer_name_l10n}}{{data.customer.customer_name_l10n}}{{/if}}
    </h3>
    <h3 style="text-align:center; margin-bottom:6px">{{#if
        data.customer.customer_visual_id}}{{data.customer.customer_visual_id}}{{else}}-{{/if}}
        /{{#if data.customer.mobile_number}}{{data.customer.mobile_number}}{{else}}-{{/if}}
    </h3>
    <div class="visit-info">
        <span>Visit Date: {{formatDate activity_date}}</span>
    </div>
    <h4>General</h4>
    <table class="details-table">
        {{#each data.currentData}}
        <tr>
            <td>{{#if question_name_l10n}}{{question_name_l10n}}{{else}}-{{/if}}</td>
            <td>{{#if value}}{{value}}{{else}}-{{/if}}</td>
        </tr>
        {{/each}}
    </table>

    <h4>Advice provided</h4>
    {{#if data.visits}}
    {{#each data.visits}}
    <div class="visit-info">
        <span>Visit Date: {{formatDate activity_date}}</span>
    </div>

    <ul class="subcategory">
        {{#each categories}}
        <li>
            <h4>{{name_l10n}}</h4>
            <div class="checklist">
                {{#each questions}}
                <div class="sub-name">
                    {{name_l10n}}
                </div>
                {{#if advices.length}}
                <ol class='item-name'>
                    {{#each advices}}
                    <li>
                        <div style="display: inline-block; width: 10px; height: 10px;{{#if totalRepeatedCount}}{{#if (eq totalRepeatedCount 2)}}background-color: #FFFF00;{{else if (gte totalRepeatedCount 3)}}background-color: #d60606;{{/if}}{{/if}} margin-right: 5px;"></div>
                        {{advice_l10n}}
                    </li>
                    {{/each}}
                </ol>
                {{/if}}
                {{/each}}
            </div>
        </li>
        {{/each}}
    </ul>
    {{/each}}
    {{/if}}

    <div class="page-break"></div>
    <h3></h3>
    {{#if data.progressChart}}
    <h4 class="side-heading">Overall Progress (Last twelve months)</h4>
    {{#each data.progressChart}}
    <div class="year-table">
        <table>
            <tr>
                <th colspan="13" class="year-heading">{{@key}}</th>
            </tr>
            <tr>
                <th>Category</th>
                {{#each ../data.months}}
                <th>{{this.en}}</th>
                {{/each}}
            </tr>
            {{#each categories}}
            <tr>
                <td>{{name}}</td>
                {{#each cells}}
                <td class="{{getColorClass severities}}">
                    &nbsp;
                </td>
                {{/each}}
            </tr>
            {{/each}}
        </table>
    </div>
    <div class="page-break"></div>
    {{/each}}
    {{/if}}
    {{else}}
    <p class="no-data-message"></p>
    {{/if}}
</body>

</html>