const color = { green: "\x1b[32m", blue: "\x1b[34m", red: "\x1b[31m", reset: "\x1b[0m", bgColorWhite: "\x1b[47m" }
const handlebars = require("handlebars")
const fs = require("fs")
const Path = require("node:path")

function recursiveTemplateMapBuilder(directory) {
  const map = {}
  const parentPath = directory
  const entityNames = fs.readdirSync(parentPath)
  for (let i = 0; i < entityNames.length; i++) {
    const entityName = entityNames[i]
    if (entityName.includes(".js")) continue

    const path = Path.resolve(parentPath, entityName)

    const isDirectory = fs.lstatSync(path).isDirectory()

    if (isDirectory) {
      map[entityName] = recursiveTemplateMapBuilder(path)
    } else {
      template_str = fs.readFileSync(path, "utf-8")
      map[entityName] = handlebars.compile(template_str, { compat: true })
    }
  }

  return map
}

module.exports = { handlerBarTemplates: recursiveTemplateMapBuilder(__dirname) }
