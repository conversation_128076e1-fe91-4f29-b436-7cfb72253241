const { configurationJSON } = require('@krushal-it/common-core')
const { co } = require('translate-google/languages')

const verifyXAppId = (app_name, allowedUsers) => {
  return (req, res, next) => {
    try {
      const token = req.headers.token
      const { user_type } = token
      if (!user_type) return res.status(403).json({ message: 'forbidden' })

      const APP_ID = configurationJSON().APP_ID[app_name]

      const appIDFomReq = req.headers['x-app-id']

      if (!appIDFomReq) return next()

      if (appIDFomReq != APP_ID) return res.status(403).json({ message: 'forbidden' })

      if (allowedUsers.has(user_type)) return next()

      return res.status(403).json({ message: 'forbidden' })
    } catch (error) {
      return res.status(401).json({ message: error.message })
    }
  }
}

module.exports = { verifyXAppId }
