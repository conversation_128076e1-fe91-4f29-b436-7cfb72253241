const { body } = require("express-validator");

const customPushNotification = (value) => {
    if (!value.user_ids && !value.filters) {
        return false;
    }
    return true;
};

const customCheckMinLength = (minLen) => {
    return (value) => {
        return value.length >= minLen;
    };
};

const pushNotification = [
    body()
        .custom(customPushNotification).withMessage("must contain user_id or appropriate filters"),
    body("user_ids")
        .optional()
        .isArray()
        .withMessage("user_ids must be an Array")
        .custom(customCheckMinLength(1)).withMessage("user_ids cannot be an empty Array"),
    body("filters")
        .optional(),
    body("filters.village_ids")
        .if(body("filters").exists())
        .isArray().withMessage("village_ids must be an Array")
        .custom(customCheckMinLength(1)).withMessage("village_ids cannot be an empty Array"),
    body("filters.user_types")
        .if(body("filters").exists())
        .isArray().withMessage("user_types must be an Array")
        .custom(customCheckMinLength(1)).withMessage("user_types cannot be an empty Array"),
    body("notification.body").trim().notEmpty().withMessage("notification body is required"),
    body("notification.title").trim().notEmpty().withMessage("notification title is required"),
    body("notification.action").trim().notEmpty().withMessage("notification action is required"),
    body("notification.redirect").trim().notEmpty().withMessage("notification redirect is required")

];

module.exports = { pushNotification };