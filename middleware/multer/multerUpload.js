const multer = require('multer');
const path = require("path");
const S3Utils = require("../../utils/common/aws/s3.utils");
const allowedImages = new Set(["jpg", "jpeg", "png", "gif", "blob"]);
const allowedDocs = new Set(["pdf"]);
const allowedVideos = new Set(["mp4", "mov", "avi", "wmv", "webm", "mkv", "3gp", "x-flv", "flv", "x-matroska", "3gpp"]);
const allowedMime = new Set(["video", "image"]);
const LIMIT_SIZE = 20000000; //BYTES
const fs = require('fs')

const storage = multer.diskStorage({
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + "_" + Math.round(Math.random() * 1e9);
    const ext = path.extname(file.originalname);
    let name = `${file.fieldname}_${uniqueSuffix}${ext}`;
    cb(null, name);
  }
});

const storage2 = multer.memoryStorage()

const fileFilter = (req, file, cb) => {
  const mime = file.mimetype.split("/")[0];
  const fileType = file.mimetype.split("/")[1];
  if (allowedImages.has(fileType) || allowedVideos.has(fileType) || allowedDocs.has(fileType) || allowedMime.has(mime)) {
    return cb(null, true);
  }
  return cb(new multer.MulterError("LIMIT_UNEXPECTED_FILE"));
};

const multerUp = multer({ storage, fileFilter, limits: { fileSize: LIMIT_SIZE } });

const multerUp2 = multer({ storage2, fileFilter, limits: { fileSize: LIMIT_SIZE } });

const injectFile = (req, res, next) => {
  req.body.files = req.files;
  return next();
};

const streamPipe = async (req, res, next) => {

  try {
    if (!res.data) return res.status(400).json({ msg: "invalid request" });
    if (req._headerSent) return;
    if (req.method === "GET" && res.data.pipeStream != false) {
      let s3Manager = new S3Utils();
      let fileMeta = await s3Manager.getFileMeta(res.data);
      if (!fileMeta) {
        return res.status(204).json({ result: "not found" });
      }
      const fileStream = await s3Manager.s3Downloader(res.data);

      let contentType = fileMeta.ContentType;
      if (allowedImages.has(contentType)) contentType = `image/${contentType}`;
      else if (allowedDocs.has(contentType)) contentType = `application/${contentType}`;
      else if (allowedVideos.has(contentType)) contentType = `video/${contentType}`;
      res.header("Content-Type", contentType);
      fileStream.on("error", (error) => {
        try {
          console.log("AWS_ERROR", error.code);
          console.log("AWS_PATH", res.data);
          !res.hook ? console.log("DOC_ID", undefined) : console.log("AWS_PATH", res.hook.id);
          if (error.code === "AccessDenied") {
            res.status(404).send("Not found");
            return;
          }
          else if (error.code === "TimeoutError") {
            res.status(404).send("Request Timeout Error");
            return;
          }
          else return res.status(404).json({ message: "Not found", result: false });
        } catch (error) {
          console.log(error);
          if (!res._headerSent) return res.status(500).json({ message: "Internal server Error", result: false });
        }
      });
      if (!res._headerSent) fileStream.pipe(res);
      return;
    } else if (!res._headerSent) return next();
  } catch (error) {
    if (!res._headerSent) res.status(500).json({ message: "Internal server Error", result: false });
    console.log("OUTERERROR", error);
    return;
  }
};

const invoiceStreamPipe = async (req, res, next) => {
  try {
    console.log('req',res)
    if (req.method === "GET" || req.method === "find"){
      let filename = path.basename(res.data);
      console.log(filename)
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Content-Type', 'application/octet-stream');

      const fileStream = fs.createReadStream(res.data);

      fileStream.pipe(res);

      fileStream.on('error', (err) => {
        console.error('Error reading file:', err);
        res.statusCode = 500;
        res.end('Internal Server Error');
      });

      return;
    }
    else return next();

  } catch (error) {
    console.log("OUTERERROR", error);
    return;
  }
};

const multerError = (err, req, res, next) => {
  if (err instanceof multer.MulterError) {

    if (err.code === "LIMIT_FILE_SIZE") {
      return res.status(413).json({ error: `File size limit exceeded, Allowed Size is ${LIMIT_SIZE / 1000000} MB` });
    }
    if (err.code === "LIMIT_UNEXPECTED_FILE") {
      return res.status(415).json({ error: "Invalid file type" });
    }
  }

  next(err);
};

module.exports = { multerUp,multerUp2, injectFile, streamPipe, multerError, invoiceStreamPipe };
