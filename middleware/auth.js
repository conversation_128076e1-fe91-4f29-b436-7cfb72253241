const authenticate = require("../utils/common/auth/authentication")
const { staffType, customerType } = require("../utils/configs/entity.config")
const { errorLog, infoLog } = require("@krushal-it/mobile-or-server-lib")
const moment = require("moment")
const auth = async (req, res, next) => {
  try {
    const token = req.headers.token
    const refreshtoken = req.headers.refreshtoken
    const j = new authenticate(token, refreshtoken)
    const isresolve = await j.authenticate_user()

    if (isresolve === false || (!staffType[isresolve.user_type] && !customerType[isresolve.user_type])) {
      return res.status(401).json({
        message: "unauthorised_access",
        result: false
      })
    }

    isresolve.user_type_resolved = isresolve.staff_id ? staffType[isresolve.staff_type] : customerType[isresolve.user_type]
    req.headers.token = isresolve
    next()
  } catch (err) {
    console.error(err.message)
    const errRes = {
      message: err.message,
      result: false,
      status: 401
    }
    if (err.code) errRes.message = err.code
    return res.status(401).json(errRes)
  }
}

const checkUsertype = userType => {
  return (req, res, next) => {
    const { user_type } = req.headers.token
    if (user_type === userType) return next()
    return res.status(403).json({ message: "Unauthorized" })
  }
}

const logReq = (req, res, next) => {
  const startTime = moment()

  res.once("finish", () => {
    const endTime = moment()
    const elapsedTime = moment.duration(endTime.diff(startTime)).asSeconds()
    const { user_type, user_id } = req.headers.token
    infoLog({ request_url: req.url }, { request_header: req.headers }, { request_body: req.body }, { user_id }, { user_type }, { elapsedTime }, { response_status_code: res.statusCode })
  })

  next()
}

module.exports = { auth, checkUsertype, logReq }
