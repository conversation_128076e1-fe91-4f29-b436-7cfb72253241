const handleServerError = (req, res, next) => {
    if (!res.headersSent) {
        if (res.data.data === -1) {
            const newResponse = { message: res.data.error, newToken: res.data?.newToken, id: res.data.id };
            if (res.data.status) {
                return res.status(res.data.status).json(newResponse);
            }
            else {
                return res.status(500).json({ message: "Internal server error" })
            }
        }
        else next();
    }
};

module.exports = { handleServerError };