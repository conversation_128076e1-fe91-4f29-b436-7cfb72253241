const { LibTaskV2 } = require("@krushal-it/back-end-lib");
const response = require("../utils/responseTemplate");
const { RETURN_CODE } = require("@krushal-it/back-end-lib/v2/utils/constants");

class FarmerTaskV2 {
  farmerTaskHandlerV2 = new LibTaskV2();
  async create(data, params) {
    let result = await this.farmerTaskHandlerV2.getFarmerLevelTaskV2(data, params);
    return result;
  }
}

class AnimalTaskV2 {
  animalTaskHandler = new LibTaskV2();
  async create(data, params) {
    let result = await this.animalTaskHandler.getAnimalLevelTaskV2(data, params);
    return result;
  }
}



class AnimalTaskHistoryV2 {
  /*
  Method : Post
  Function: Get Animal Task history based on dates and animal id
*/
  async create(data, params) {
    try {
      const historytaskV2 = new LibTaskV2();
      const result = await historytaskV2.getAnimalTaskHistoryV2(data, params)
      return result

    } catch (error) {
      return response(RETURN_CODE.FAILED, 400, error.message, { list: [], totalCount: 0 })
    }
  }

}

class AllCattleTasksV2 {
  /*
  Method : Post
  Function: Get Animal Task details for particular customer based on dates 
*/
  async create(data, params) {
    try {
      const allTask = new LibTaskV2();
      const result = await allTask.getAllAnimalsTaskV2(data, params)
      return result;
    } catch (error) {
      return response(RETURN_CODE.FAILED, 400, error.message, { list: [], totalCount: 0 })
    }

  }
}

class AnimalActivityDetailsSummary {
  /*
  Method : Post
  Function: Get Animal Summary By care_caldendar_id and animal_id
*/
  async create(data, params) {
    try {
      const summary = new LibTaskV2();
      const result = await summary.getActivitySummaryV2(data, params)
      return result;
    } catch (error) {
      return response(RETURN_CODE.FAILED, 400, error.message, [])
    }

  }
}



module.exports = { FarmerTaskV2, AnimalTaskV2, AnimalTaskHistoryV2, AllCattleTasksV2, AnimalActivityDetailsSummary }