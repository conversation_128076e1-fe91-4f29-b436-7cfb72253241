const { AnimalServicesV2, AnimalCategoryV2, AnimalMilkingV2 } = require("@krushal-it/back-end-lib");
const response = require("../../utils/responseTemplate");
const { RETURN_CODE } = require("@krushal-it/back-end-lib/v2/utils/constants");

/*
    Method : Post
    Function: Get Animal List with/without filters
*/
class AnimalFilterDetails {
    async create(data, params) {
        try {
            const animalService = new AnimalServicesV2();
            const response = await animalService.create(data, params);
            return response;
        } catch (error) {
            return response(RETURN_CODE.FAILED, 400, error.message, { data: [], totalCount: 0 })
        }
    }
}

/*
    Method : Get
    Function: Get Animal Categories
*/
class AnimalType {
    async find(params) {
        try {
            const animalcategories = new AnimalCategoryV2();
            const response = await animalcategories.find(params)
            return response
        } catch (error) {
            return response(RETURN_CODE.FAILED, 400, error.message, {})
        }
    }
}

class AnimalMilkingDetails {

    animalMilkingv2 = new AnimalMilkingV2();


    async create(data, params) {

        const { type } = params.query;

        /*
            checking for the params from query
            if type is "farmer" - getMilkLogByFamerID
        */
        if (type === 'farmer') {
            const { user_id } = params.headers.token;
            const { from, to } = data;

            try {
                const response = await this.animalMilkingv2.getMilkLogByFarmerId(user_id, from, to)
                return response;
            } catch (error) {
                return response(RETURN_CODE.FAILED, 400, error.response, {})
            }
        }


        if (type === 'animal') {
            const { animal_id } = params.query;
            const { from, to } = data;

            try {
                const response = await this.animalMilkingv2.getMilkLogByAnimalId(animal_id, from, to);
                return response;
            } catch (error) {
                return response(RETURN_CODE.FAILED, 400, error.response, {});
            }
        }

        if (type === 'update') {
            try {
                const response = await this.animalMilkingv2.updateOrCreateMilkLog(data, params)
                return response;
            } catch (error) {
                return response(RETURN_CODE.FAILED, 400, error.message, {})
            }
        }
    }
}

module.exports = { AnimalFilterDetails, AnimalType, AnimalMilkingDetails }