const { RETURN_CODE } = require("@krushal-it/back-end-lib/v2/utils/constants");
const response = require("../../utils/responseTemplate");
const { UsersLogin } = require("@krushal-it/back-end-lib");

class UpdateUserLatLong {
    /*
        Method : Post
        Function: Update user_device data with lat long data
    */
    async create(data, params) {
        try {
            const userDevice = new UsersLogin();
            const response = await userDevice.updateLatLong(data, params);
            return response;
        } catch (error) {
            return response(RETURN_CODE.FAILED, 400, error.message, [])
        }
    }

}

class LogoutUserDevice {
    /*
        Method : Post
        Function: Update user_device data to inavtive State
    */
    async create(data, params) {
        try {
            const userDevice = new UsersLogin();
            const response = await userDevice.userSignOut(data, params);
            return response;
        } catch (error) {
            return response(RETURN_CODE.FAILED, 400, error.message, [])
        }
    }
}

class LogoutUserDeviceFromOffice {
    /*
        Method : Post
        Function: Update user_device data to inavtive State
    */
    async create(data, params) {
        try {
            const userDevice = new UsersLogin();
            const response = await userDevice.userSignOutFromOffice(data, params);
            return response;
        }
        catch (error) {
            return response(RETURN_CODE.FAILED, 400, error.message, [])
        }
    }
}


module.exports = { UpdateUserLatLong, LogoutUserDevice, LogoutUserDeviceFromOffice }