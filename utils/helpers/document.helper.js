
const { dbConnections, preProcessRecords } = require("@krushal-it/ah-orm");
const {
  ENTITY_TYPE_FARMER,
  ENTITY_TYPE_ANIMAL,
  ENTITY_TYPE_STAFF_PARAVET,
  ENTITY_TYPE_STAFF_BACK_OFFICE_PERSON,
  ENTITY_TYPE_STAFF_ROUTE_OFFICER,
  ENTITY_TYPE_STAFF_ZONAL_OFFICER,
  RELATION_DOC,
  RELATION_REFERENCE,
  ENTITY_TYPE_TASK,
  ENTITY_TYPE_REQUISITION
} = require("../common/namespace/krushal.namespace");


const getDocumentJSONConfig = async (options = {}) => {
  try {
    const validKeys = new Set(["ref_reference_reference_id", "ref_reference_field_name"]);
    let { key_type } = options;
    key_type = validKeys.has(key_type) ? key_type : "ref_reference_field_name";

    let documentRefs = await getDocumentReferences();
    if (!documentRefs) throw new Error("No data found");
    let docJSONConfig = {};
    for (let i = 0; i < documentRefs.length; i++) {
      docJSONConfig[documentRefs[i][`${key_type}`]] = documentRefs[i];
    }
    return docJSONConfig;
  } catch (error) {
    console.error(error);
    return {};
  }
};
/**
 *
 * @param {String} options.entity entity type in string, accepts farmer|animal
 * @param {String} options.entity_1_id Id of the entity
 * @param {Object[]} options.files Array of uploaded files, retrieved through multer
 * @returns {Object[]}
 */
const handleFileEntries = async (options = {}) => {
  try {
    const validEntity = new Set([
      ENTITY_TYPE_FARMER,
      ENTITY_TYPE_STAFF_ROUTE_OFFICER,
      ENTITY_TYPE_STAFF_ZONAL_OFFICER,
      ENTITY_TYPE_ANIMAL,
      ENTITY_TYPE_STAFF_PARAVET,
      ENTITY_TYPE_STAFF_BACK_OFFICE_PERSON,
      ENTITY_TYPE_TASK,
      ENTITY_TYPE_REQUISITION
    ]);
    const entityTypeIds = {
      farmer: 1000220001,
      animal: 1000220002,
      paravet: 1000230004,
      route_officer: 1000230001,
      back_office_person: 1000230002,
      zonal_office: 1000230005,
      task: 1000220003,
      animal_classification: 1000220004,
      requisition:1000460006
    };

    const documentTypes = await getDocumentJSONConfig();

    let {
      entity, 
      url, 
      entity_1_type_id, 
      entity_1_id, 
      document_type, 
      document_information,
      fileType, 
      uploaded_at, 
      document_information_json
    } = options;

    if (Array.isArray(entity_1_id) && entity_1_id.length > 0) {
      entity_1_id = entity_1_id[0]
    }

    if (typeof entity_1_id !== "string" && typeof entity_1_id !== "number")
      throw new Error("Require entity_1_id");

    // if (!validEntity.has(entity)) throw new Error("Invalid Entity");
    if (!url) throw new Error("file url required");
    let doctype = documentTypes[document_type];
    if (!doctype) throw new Error("Invalid doc type");

    let commonInserObject = {
      entity_1_type_id: entity_1_type_id ? entity_1_type_id : entityTypeIds[entity],
      entity_1_entity_uuid: entity_1_id,
      document_name: doctype.reference_name,
      document_name_l10n: doctype.reference_name_l10n,
      document_type_id: doctype.ref_reference_reference_id,
    };

    if (uploaded_at) {
      const date = new Date(uploaded_at)
      const formattedDate = date.getFullYear()+"-"+String(date.getMonth()+1).padStart(2,'0')+"-"+String(date.getDate()).padStart(2,'0')
      commonInserObject["document_meta_data_information"] = {uploaded_at: formattedDate}

    }
    
    // if (document_information !== null)
    // type string
    if (document_information_json) {
      commonInserObject["document_information"] = {
        url,
        fileType,
        description: document_information,
        ...document_information_json
      };
    } else {
      commonInserObject["document_information"] = {
        url,
        fileType,
        description: document_information
      };
    }
      
    let res = await insertToDocument(commonInserObject);
    return res;
  } catch (error) {
    console.error(error);
    throw error;
  }
};
const addTodumped = async (files, errorMessage) => {
  let { file } = files;
  if (Array.isArray(file) && file.length > 0) {
    for (let fileIndex = 0; fileIndex < file.length; fileIndex++) {
      try {
        await insertDumpedDoc({
          error: errorMessage,
          document_key: file[fileIndex],
        });
      } catch (error) {
        console.error(error);
        continue;
      }
    }
  }
  return;
};
const getDocumentReferences = async (options = {}) => {
  try {
    const doc_categoryParent = 10002600;
    let mdatasource = dbConnections().main.repos[RELATION_REFERENCE];
    const { columns } = options;
    let query = mdatasource
      .createQueryBuilder()
      .where(
        `${RELATION_REFERENCE}.active = 1000100001 AND ${RELATION_REFERENCE}.reference_category_id = :parent`,
        { parent: doc_categoryParent }
      );
    let data = await query.execute();
    return data;
  } catch (error) {
    console.error(error);
    return [];
  }
};
const insertToDocument = async (value) => {
  const entity_document = dbConnections().main.entities[RELATION_DOC];
  const preProcessedValue = preProcessRecords(entity_document, value, entity_document.additionalAttributes);
  let data = await dbConnections()
    .main.manager.createQueryBuilder()
    .insert()
    .into(RELATION_DOC)
    .values(preProcessedValue)
    .returning("document_id")
    .execute();
  return data;
};

const mapDocumentType = async files => {
  let docJSONConfig = await getDocumentJSONConfig({
    key_type: "reference_id",
  });

  for (let i = 0; i < files.length; i++) {
    files[i].document_type =
      docJSONConfig[files[i].document_type_id].ref_reference_field_name;
  }
  return files;
};
module.exports = { getDocumentJSONConfig, mapDocumentType, insertToDocument, handleFileEntries, addTodumped };
