const { dbConnections, preProcessRecords } = require("@krushal-it/ah-orm");
const {farm_management_question , farm_management_question_key , record_statuses} = require("@krushal-it/back-end-lib/ENUMS")
const { saveClassificationData } = require("@krushal-it/back-end-lib")
const moveDataToCustomerClassification = async () => {
    const mainDBConnection = dbConnections().main.manager
    const jobStartedAt = new Date()
    await mainDBConnection.transaction(async (transactionalEntityManager) => {
        try {
            let lastCronRanOn = new Date(-1)

            const getLastRan = await transactionalEntityManager.getRepository("job_tracker")
            .findOne({
                select: ["job_tracker_id", "job_status",'created_at'], 
                where: { job_type: "move-tqd-customer-classification" , job_status : "COMPLETED"},
                order: { created_at: "DESC" } 
            });
            if(getLastRan) {
                lastCronRanOn = getLastRan.created_at
            }
            const taskDatas = await transactionalEntityManager
            .getRepository("task_question_data")
            .createQueryBuilder("taskQuestionData")
            .select([
                "taskQuestionData.form_configuration AS form_configuration",
                "taskQuestionData.saved_at AS saved_at ",
                "care_calendar.entity_uuid AS entity_uuid" 
            ])
            .innerJoin(
                "care_calendar", 
                "care_calendar",
                "taskQuestionData.care_calendar_id = care_calendar.care_calendar_id" 
            )
            .where("taskQuestionData.updated_at > :lastCronRanOn and taskQuestionData.updated_at < :jobStartedAt", { lastCronRanOn ,jobStartedAt})
            .andWhere("taskQuestionData.question_uuid = :questionUuid", { questionUuid: farm_management_question.Cost_of_Milk_Production })
            .andWhere("taskQuestionData.active = :active", { active: record_statuses.ACTIVE })
            .andWhere("taskQuestionData.saved_at is not null")
            .getRawMany();
            for(const taskData of taskDatas) {
                const currentObject  = taskData.form_configuration?.form_fields?.find((state) =>  state.key === farm_management_question_key.CURRENT)
                if (currentObject && currentObject?.selections?.values.length) {
                    const upsertData = {
                        cost_of_milk_production_as_of_date: taskData.saved_at,
                        cost_of_milk_production: parseFloat(currentObject?.selections?.values[0])
                    }
                    await saveClassificationData(transactionalEntityManager, 'CUSTOMER_CLASSIFICATION', taskData.entity_uuid, upsertData)
                }
    
            }
             await transactionalEntityManager.getRepository("job_tracker").save(
                {
                    job_type: "move-tqd-customer-classification",
                    job_status: "COMPLETED",
                    job_completed_at: new Date(),
                    created_at: jobStartedAt
                }
            ) 
        } catch (error) {
            await mainDBConnection.getRepository("job_tracker").save(
                {
                    job_type: "move-tqd-customer-classification",
                    job_status: "FAILED",
                    job_information: {"errors": error},
                    job_completed_at: new Date(),
                    created_at: jobStartedAt

                }
            ) 
        }
    })
}
module.exports = { moveDataToCustomerClassification };

