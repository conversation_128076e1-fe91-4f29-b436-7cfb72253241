// upload a docx template - not coded now. uploaded manually
// download a template
// generate a document based on template and data
// generate a pdf based on document

const PizZip = require("pizzip")
const Docxtemplater = require("docxtemplater")
const concat = require('concat-stream')
// const fs = require("fs")
// const path = require("path")
// const util = require('util')
// const stream = require('stream')
const { Readable } = require('stream')
const { PassThrough } = require('stream')
// const mammoth = require("mammoth");
// const libre = require('libreoffice-convert')
const { google } = require('googleapis')
// const { authenticate } = require('@google-cloud/local-auth')
const { v4: uuidv4 } = require('uuid');

// const pipeline = util.promisify(stream.pipeline)
// const convertToPdf = util.promisify(libre.convert)

const { extractBasedOnLanguage, configurationJSON } = require('@krushal-it/common-core')

// const googleAuth = require('../../google-dev1.json')
const S3Utils = require('../../utils/common/aws/s3.utils')

const extractTemplateConfigurationForTemplateKey = (key) => {
  const templates = configurationJSON().TEMPLATES
  if (key === undefined) {
    throw new Error('Without a template key, generating a template is not possible')
  }
  if (templates === undefined) {
    throw new Error('no templates available')
  }
  const templateInformation = templates[key]
  if (templateInformation === undefined) {
    throw new Error('no template information available for the template with key ' + key)
  }
  if (!(templateInformation['LATEST_VERSION'] && Number.isInteger(templateInformation['LATEST_VERSION']))) {
    throw new Error('Template configuration does not have valid version number for key ' + key)
  }
  const latestVersion = templateInformation['LATEST_VERSION']
  if (!templateInformation['VERSION_DOCUMENTS']) {
    throw new Error('Template configuration does not have version documents for key ' + key)
  }
  const versionDocumentInformationArray = templateInformation['VERSION_DOCUMENTS'].filter(object => object['VERSION'] === latestVersion)
  if (versionDocumentInformationArray.length === 0) {
    throw new Error('No matching version available in template configuration for ' + key)
  }
  return versionDocumentInformationArray[0]
}

const extractS3KeyFromConfigurationForTemplateKey = (key, templateLanguage) => {
  const templates = configurationJSON().TEMPLATES
  if (key === undefined) {
    throw new Error('Without a template key, generating a template is not possible')
  }
  if (templates === undefined) {
    throw new Error('no templates available')
  }
  const templateInformation = templates[key]
  if (templateInformation === undefined) {
    throw new Error('no template information available for the template with key ' + key)
  }
  if (!(templateInformation['LATEST_VERSION'] && Number.isInteger(templateInformation['LATEST_VERSION']))) {
    throw new Error('Template configuration does not have valid version number for key ' + key)
  }
  const latestVersion = templateInformation['LATEST_VERSION']
  if (!templateInformation['VERSION_DOCUMENTS']) {
    throw new Error('Template configuration does not have version documents for key ' + key)
  }
  const versionDocumentInformationArray = templateInformation['VERSION_DOCUMENTS'].filter(object => object['VERSION'] === latestVersion)
  if (versionDocumentInformationArray.length === 0) {
    throw new Error('No matching version available in template configuration for ' + key)
  }
  const templateS3KeysAcrossLanguages = versionDocumentInformationArray[0]['TEMPLATE_S3_KEY']
  if (!templateS3KeysAcrossLanguages) {
    throw new Error('No matching template available for the latest version in template configuration for ' + key)
  }
  const templateS3Key = extractBasedOnLanguage(templateS3KeysAcrossLanguages, templateLanguage)
  if (!templateS3Key) {
    throw new Error('No matching template available for the latest version in template configuration for specific language ' + templateLanguage + ' for ' + key)
  }
  return templateS3Key
}

const extractS3KeyFromTemplateConfiguration = (templateConfiguration, templateLanguage) => {
  const templateS3KeysAcrossLanguages = templateConfiguration['TEMPLATE_S3_KEY']
  if (!templateS3KeysAcrossLanguages) {
    throw new Error('No matching template available for the latest version in template configuration for ' + key)
  }
  const templateS3Key = extractBasedOnLanguage(templateS3KeysAcrossLanguages, templateLanguage)
  if (!templateS3Key) {
    throw new Error('No matching template available for the latest version in template configuration for specific language ' + templateLanguage + ' for ' + key)
  }
  return templateS3Key
}

const downloadLatestTemplate = async (key, templateConfiguration, templateLanguage) => {
  console.log('u h tH dLT 1, key = ', key)
  // const templateS3Key = extractS3KeyFromConfigurationForTemplateKey(key, templateLanguage)
  const templateS3Key = extractS3KeyFromTemplateConfiguration(templateConfiguration, templateLanguage)
  console.log('u h tH dLT 2, templateS3Key = ', templateS3Key)
  const templateS3KeyWithFullPath = configurationJSON().AWS.AWS_BUCKET_ROOT_LOCATION + '/' + 'templates' + '/' + templateS3Key
  console.log('u h tH dLT 3, templateS3KeyWithFullPath = ', templateS3KeyWithFullPath)
  const s3Manager = new S3Utils()
  const fileMeta = await s3Manager.getFileMeta(templateS3KeyWithFullPath)
  console.log('u h tH dLT 4, fileMeta = ', fileMeta)
  const templateFileAsReadStream = await s3Manager.s3Downloader(templateS3KeyWithFullPath)
  // console.log('u h dLT 5, templateFileAsReadStream = ', templateFileAsReadStream)
  // Create a writable stream to save the data locally
  
  /* const localFileStream = fs.createWriteStream('output.docx')

  try {
    await pipeline(templateFileAsReadStream, localFileStream);
    console.log('File downloaded successfully.');
  } catch (error) {
    console.error('Error downloading file:', error);
  }
  return */

  
  
  return templateFileAsReadStream
}

const generateFinalDocumentFromTemplate = async (templateFileAsReadStream, dataForTemplate) => {
  const buffer = await new Promise((resolve, reject) => {
    templateFileAsReadStream.pipe(concat({ encoding: 'buffer' }, resolve))
    templateFileAsReadStream.on('error', reject)
  })

  // Create a PizZip instance and load the data
  const zip = new PizZip(buffer)

  const doc = new Docxtemplater(zip, {
    paragraphLoop: false,
    linebreaks: false,
  })

  console.log('Docxtemplater instance created successfully.')

  // console.log('u h tH dLT 8, doc = ', doc)

  // Render the document (Replace {first_name} by John, {last_name} by Doe, ...)
  doc.render(dataForTemplate)
  console.log('u h tH dLT 10')
  // Get the zip document and generate it as a nodebuffer
  const generatedDocumentAsBuffer = doc.getZip().generate({
    type: "nodebuffer",
    // compression: DEFLATE adds a compression step.
    // For a 50MB output document, expect 500ms additional CPU time
    compression: "DEFLATE",
  })
  console.log('u h tH dLT 11')
  return generatedDocumentAsBuffer

  // buf is a nodejs Buffer, you can either write it to a
  // file or res.send it with express for example.
  // fs.writeFileSync(path.resolve(__dirname, "output.docx"), generatedDocumentAsBuffer)
  // return null
}

const convertWordDocumentToPDFInStream = async (inputDocxBuffer, folderId) => {
  try {
    console.log('u h th cWDTPIS 1')
    // const inputPath = path.join(__dirname, 'output.docx')
    // const outputPDFPath = path.join(__dirname, 'output.pdf');
    // console.log('u h th cWDTPIS 2, inputPath = ', inputPath)
    // const docxBuf = await fs.readFileSync(inputPath)
    // console.log('u h th cWDTPIS 3, docxBuf = ', docxBuf)
    /* const {value} = await mammoth.convertToHtml({buffer: docxBuf})

    const outputPath = 'output.html';
    fs.writeFileSync(outputPath, value);
    console.log(`HTML content saved to ${outputPath}`);
    
    console.log('u h th cWDTPIS 5, value = ', value) */
    // const folderId = configurationJSON().AWS.DOCX_TO_PDF_CONVERSION_FOLDER_ID // '1gY-3A29_Z1p23dPVBSXxnV1ezB0wxwh6'
    const firebaseServiceAccount = configurationJSON().FIREBASE_AUTH;

    const auth = new google.auth.GoogleAuth({
      credentials: firebaseServiceAccount,
      scopes: 'https://www.googleapis.com/auth/drive.file',
    })
    // console.log('u h th cWDTPIS 4, auth = ', auth)
  
    const drive = google.drive({ version: 'v3', auth })
    // console.log('u h th cWDTPIS 5, drive = ', drive)
  
    const uploadFileMetadata = {
      name: 'output.document', // Name of the PDF file
      // mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      mimeType: "application/vnd.google-apps.document",
      parents: [folderId],
    }
    console.log('u h th cWDTPIS 6, uploadFileMetadata = ', uploadFileMetadata)

    const bufferStream = new Readable()
    bufferStream.push(inputDocxBuffer)
    bufferStream.push(null)
    // console.log('u h th cWDTPIS 7, bufferStream = ', bufferStream)
    
    const media = {
      mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      // body: require('fs').createReadStream(docxFilePath),
      // body: require('fs').createReadStream(inputPath),
      body: bufferStream
    }
    // console.log('u h th cWDTPIS 8, media = ', media)
  
    const response = await drive.files.create({
      resource: uploadFileMetadata,
      media: media,
      fields: 'id',
    })
    // console.log('u h th cWDTPIS 9, response = ', response)
  
    const fileId = response.data.id
    console.log('u h th cWDTPIS 10, fileId = ', fileId)

    /* const destMedia = {
      mimeType: 'application/pdf',
      // body: require('fs').createWriteStream(pdfFilePath),
      body: require('fs').createWriteStream(outputPDFPath),
    }
    console.log('u h th cWDTPIS 11, destMedia = ', destMedia)
  
    await drive.files.export({
      fileId: fileId,
      mimeType: 'application/pdf',
    }, { responseType: 'stream' }).then(res => {
      res.data.pipe(destMedia.body)
    })*/
    const exportedPDFAsStream = await drive.files.export({
      fileId: fileId,
      mimeType: 'application/pdf',
    }, { responseType: 'stream' })

    const deleteFileResponse = await drive.files.delete({
      fileId: fileId,
    })
    // console.log('u h th cWDTPIS 11, deleteFileResponse = ', deleteFileResponse)
    // console.log('u h th cWDTPIS 12, exportedPDFAsStream = ', exportedPDFAsStream)
    return exportedPDFAsStream

    /* const outputStream = fs.createWriteStream('output.pdf')

    // Create a Promise to await the 'finish' event
    const finishPromise = new Promise((resolve, reject) => {
      outputStream.on('finish', resolve)
      outputStream.on('error', reject)
    })

    // Pipe the content from the response stream to the output stream
    exportedPDFAsStream.data.pipe(outputStream)

    // Wait for the 'finish' event using await
    await finishPromise

    console.log('PDF saved successfully.') */
    
  } catch (error) {
    console.log('u h th cWDTPIS 10, error')
    console.log('u h th cWDTPIS 10a, error = ', error)
    throw error
  }
}

const saveGeneratedDocumentIntoKey = async (documentStream, documentNameWithExtension, pathToStoreInRelativeToBasePath, contentType) => {
  const passThroughStream = new PassThrough()
  documentStream.data.pipe(passThroughStream)
  const basePath = configurationJSON().AWS.AWS_BUCKET_ROOT_LOCATION
  const indexOfDot = documentNameWithExtension.lastIndexOf('.')
  let nameWithoutExtension
  let dotAndExtension
  if (indexOfDot !== -1) {
    nameWithoutExtension = documentNameWithExtension.substr(0, indexOfDot)
    dotAndExtension = '.' + documentNameWithExtension.substr(indexOfDot + 1)
  } else {
    nameWithoutExtension = documentNameWithExtension
    dotAndExtension = ''
  }
  const fileNameAsKey = nameWithoutExtension + '-' + uuidv4() + dotAndExtension
  const s3Manager = new S3Utils()
  const returnValue = await s3Manager.s3UploaderV2(passThroughStream, basePath + '/' + pathToStoreInRelativeToBasePath + '/' + fileNameAsKey, contentType)
  console.log('u h th sGDIK, returnValue = ', returnValue)
  return returnValue
}

/* const constructDocumentOutOfTemplate = async () => {
  const fs = require("fs")
  const path = require("path")

  // Load the docx file as binary content
  const content = fs.readFileSync(
      path.resolve(__dirname, "input.docx"),
      "binary"
  )

  // Unzip the content of the file
  const zip = new PizZip(content)

  // This will parse the template, and will throw an error if the template is
  // invalid, for example, if the template is "{user" (no closing tag)
  const doc = new Docxtemplater(zip, {
      paragraphLoop: true,
      linebreaks: true,
  })

  // Render the document (Replace {first_name} by John, {last_name} by Doe, ...)
  doc.render({
      first_name: "John",
      last_name: "Doe",
      phone: "0652455478",
      description: "New Website",
  })

  // Get the zip document and generate it as a nodebuffer
  const buf = doc.getZip().generate({
      type: "nodebuffer",
      // compression: DEFLATE adds a compression step.
      // For a 50MB output document, expect 500ms additional CPU time
      compression: "DEFLATE",
  })

  // buf is a nodejs Buffer, you can either write it to a
  // file or res.send it with express for example.
  fs.writeFileSync(path.resolve(__dirname, "output.docx"), buf);
} */

module.exports = {
  downloadLatestTemplate,
  extractTemplateConfigurationForTemplateKey,
  generateFinalDocumentFromTemplate,
  convertWordDocumentToPDFInStream,
  saveGeneratedDocumentIntoKey
}