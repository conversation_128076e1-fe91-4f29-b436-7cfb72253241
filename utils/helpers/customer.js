const { customerListing } = require('../../services/customer/customer.js');
const getCustomerList_v3 = async (options = {}) => {
    const { user_id, user_type, page_limit, page_number ,terms_condition} = options;
    let customerOptions = {
      user_id,
      user_type,
      page_limit,
      page_number,
      f_farmer_name: options.f_farmer_name,
      f_farmer_mobile: options.f_farmer_mobile,
      f_farmer_village: options.f_farmer_village,
      f_farmer_taluka: options.f_farmer_taluka,
      f_farmer_visual_id: options.f_farmer_visual_id,
      f_paravet_name: options.f_paravet_name,
      f_not_assigned:options.f_not_assigned,
      f_not_subscribed:options.f_not_subscribed,
      terms_condition 
    };
    let { result, total_page } = await customerListing(customerOptions);
    result = result.map((data)=> {
       return {
        ...data,
        terms: data.terms === 1000105001 ? "Accepted" : "Not Accepted"
       }
    })
    return {
      data: result,
      total_page,
    };
  };

  module.exports = {getCustomerList_v3}