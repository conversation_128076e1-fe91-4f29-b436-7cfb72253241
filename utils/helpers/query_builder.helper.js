const generateWhereClause = (data) => {
    let whereClause = ' WHERE 1 = 1 '
    if (data.filters && Object.keys(data.filters).length > 0) {
      const columnFilterKeys = Object.keys(data.filters)
      columnFilterKeys.forEach((searchColumn, index) => {
        if (searchColumn === 'ignoreColumn') {
          return
        }
        const searchValue = data.filters[searchColumn]
        if (searchValue === null) {
          return
        }
        let columnToSearchOn = searchColumn
        if(data.searchConfiguration[searchColumn].ignore && data.searchConfiguration[searchColumn].ignore == true){
          return
        }
        if (data.searchConfiguration[searchColumn].altColumn && data.searchConfiguration[searchColumn].altColumn != null && data.searchConfiguration[searchColumn].altColumn != '') {
          columnToSearchOn = data.searchConfiguration[searchColumn].altColumn
        }
        let arrayAsString = ''
        let valueContainsNa = false
        if (data.searchConfiguration[searchColumn].type === 'default_array' || data.searchConfiguration[searchColumn].type === 'single_select_array') {
          if (data.searchConfiguration[searchColumn].dataType === 'int') {
            arrayAsString = searchValue.join(',')
          } else if (data.searchConfiguration[searchColumn].dataType === 'string') {
            searchValue.forEach((searchValueItem) => {
              if (arrayAsString !== '') {
                arrayAsString = arrayAsString + ", "
              }
              if(searchValueItem !== 'N/A') {
                arrayAsString = arrayAsString + "'" + searchValueItem + "'"
              }
              else{
                valueContainsNa = true
              }

            })
          }
          if (arrayAsString !== '') {
            whereClause = whereClause + ' AND ' + columnToSearchOn + ' IN (' + arrayAsString + ') '
          }
          if (data.searchConfiguration[searchColumn].altColumn && data.searchConfiguration[searchColumn].altColumn != null && data.searchConfiguration[searchColumn].altColumn != '' && valueContainsNa && data.searchConfiguration[searchColumn].type === 'single_select_array') {
              whereClause = whereClause + ' AND ' + columnToSearchOn + ' is null '
          }
        } else if (data.searchConfiguration[searchColumn].type === 'string') {
          whereClause = whereClause + " AND UPPER(" + columnToSearchOn + ") LIKE '%" + searchValue[0].toUpperCase() + "%'"
        } else if (data.searchConfiguration[searchColumn].type === 'dayjs_range') {
          const startDate = new Date(searchValue[0][0])
          const endDate = new Date(searchValue[0][1])
          whereClause = whereClause + " AND " + columnToSearchOn + " >= '" + startDate.toISOString() + "' AND " + columnToSearchOn + " <= '" + endDate.toISOString() + "' "
        } else if (data.searchConfiguration[searchColumn].type === 'number') {
          const numberToMatch = parseInt(searchValue[0])
          whereClause = whereClause + " AND " + columnToSearchOn + " = " + numberToMatch + " "
        } else if (data.searchConfiguration[searchColumn].type === 'number_range') {
          let minValue, maxValue 
          if (searchValue[0][0] !== undefined && searchValue[0][0] !== null) {
            minValue = parseInt(searchValue[0][0])
          }
          if (searchValue[0][1] !== undefined && searchValue[0][1] !== null) {
            maxValue = parseInt(searchValue[0][1])
          }
          if (minValue !== undefined) {
            whereClause = whereClause + " AND " + columnToSearchOn + " >= " + minValue + " "
          }
          if (maxValue !== undefined) {
            whereClause = whereClause + " AND " + columnToSearchOn + " <= " + maxValue + " "
          }
        }
      })
    }
    let orClause = ''
    if (data.globalSearch && data.globalSearch !== null && data.globalSearch !== '') {
      const searchConfigurationColumns = Object.keys(data.searchConfiguration)
      console.log('searchConfigurationColumns = ', searchConfigurationColumns)
      searchConfigurationColumns.forEach((column, index) => {
        if (data.searchConfiguration[column].includeInOmnisearch === true) {
          if (orClause !== '') {
            orClause = orClause + ' OR '
          }
          orClause = orClause + " ( UPPER(" + column + ") LIKE '%" + data.globalSearch.toUpperCase() + "%')"
        }
      })
    }
    if (orClause !== '') {
      whereClause = whereClause + ' AND ( ' + orClause + ' ) '
    }
    return whereClause
  }
  
  const generateLimitString = (data) => {
    let limitString = ``
    if (data.start && data.start !== -1 && data.size && data.size !== -1) {
      limitString = `LIMIT ${data.size} OFFSET ${data.start}`
    }
    return limitString
  }
  
  const generateSortString = (data) => {
    let sortString = ``
    if (data.sorting && Object.keys(data.sorting).length > 0 && data.sorting.columnKey !== 'ignoreColumn') {
      sortString = `ORDER BY ${data.sorting.columnKey} `
      if (data.sorting.order === 'descend') {
        sortString = sortString + ` DESC `
      } else {
        sortString = sortString + ` ASC `
      }
    }
    return sortString
  }

  module.exports = { generateWhereClause ,generateLimitString , generateSortString}