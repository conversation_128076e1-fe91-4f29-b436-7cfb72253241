const schemaEnv = require("../schemaEnv.json")
const { ajv } = require("./helpers/ajv.Instance")
const { configurationJSON } = require("@krushal-it/common-core")
const env = configurationJSON()
const validate = ajv.compile(schemaEnv)

function validateEnvSetup() {
  const isValid = validate(env)

  if (validate.errors) {
    let errorString = "Invalid environment file. Please verify!\n  " + validate.errors.map(error => error.message)
    console.log(errorString)
  }

  if (!isValid) {
    throw new Error("Invalid environment file. Please verify!")
  }
}

module.exports = { validateEnvSetup }
