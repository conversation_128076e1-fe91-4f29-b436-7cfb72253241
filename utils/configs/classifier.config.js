const ONE = "one";
const MANY = "many";

// Customer config starts here
exports.curtomerClassifierConfig = {
  aadhaar_1: { cardinality: ONE },
  farmer_dairy_location_1: { cardinality: ONE },
  type_of_dairy_farm_1: {
    cardinality: ONE,
    category_parent: 10001100,
  },
  total_num_of_cow_1: { cardinality: ONE },
  total_num_of_cow_calves_1: { cardinality: ONE },
  total_num_of_buffalo_1: { cardinality: ONE },
  total_num_of_buff_calves_1: { cardinality: ONE },
  acreage_range_for_foliage_crops_1: {
    cardinality: ONE,
    category_parent: 10001200,
  },
  other_acreage_range_for_foliage_crops_1: { cardinality: ONE },
  acreage_for_foliage_crops_1: {
    cardinality: ONE,
    category_parent: 10001300,
  },
  foliage_crops_grown_1: {
    cardinality: MANY,
    category_parent: 10001300,
  },
  other_foliage_crops_grown_1: { cardinality: ONE },
  num_of_lab_used_for_cow_farm_1: { cardinality: ONE },
  monthly_labour_cost_1: { cardinality: ONE },
  total_dairy_income_1: { cardinality: ONE },
  total_number_of_adult_animals_bought_in_last_3_years_1: { cardinality: ONE },
  total_number_of_adult_animals_sold_in_last_3_years_1: { cardinality: ONE },
  total_number_of_female_calves_buffalo_and_cow_sold_in_last_3_years_1: {
    cardinality: ONE,
  },
  average_price_of_cow_bought_or_sold_1: { cardinality: ONE },
  average_price_of_buffalo_bought_or_sold_1: { cardinality: ONE },
  average_price_of_female_calf_sold_1: { cardinality: ONE },
  average_price_of_male_calf_sold_1: { cardinality: ONE },
  total_income_1: { cardinality: ONE },
  total_land_inc_foilage_1: { cardinality: ONE },
  total_number_of_aged_dependents_1: { cardinality: ONE },
  total_number_of_adults_in_household_1: { cardinality: ONE },
  total_number_of_children_in_household_1: { cardinality: ONE },
  assets_owned_1: { cardinality: MANY, category_parent: 10003600 },
  current_loan_1: { cardinality: ONE },
  total_emi_per_month_1: { cardinality: ONE },
  farmer_state_1: { cardinality: ONE },
  farmer_district_1: { cardinality: ONE },
  farmer_taluka_1: { cardinality: ONE },
  farmer_village_1: { cardinality: ONE },
  farmer_address_1: { cardinality: ONE },
  Date_of_Birth_1: { cardinality: ONE },
  Age_1: { cardinality: ONE },
  Age_as_of_1: { cardinality: ONE },
};
exports.customerLocalClassifier = {
  customer_name_l10n: { field_name: "customer_name_l10n", isJson: true },
  mobile_number: { field_name: "mobile_number" },
};
// Customer config ends here

exports.animalClassifierConfig = {
  animal_name_1: { cardinality: ONE },
  ear_tag_1: { cardinality: ONE },
  animal_breed_1: { cardinality: ONE, category_parent: 10001500 },
  animal_other_breed_1: { cardinality: ONE }, //two breads
  animal_age_1: { cardinality: ONE },
  age_of_animal_as_of_1: { cardinality: ONE },
  animal_dob_1: { cardinality: ONE },
  animal_weight_1: { cardinality: ONE },
  number_of_calvings_1: { cardinality: ONE },
  milking_status_1: { cardinality: ONE, category_parent: 10001600 }, //illness,preventive doubt
  max_lpd_of_animal_1: { cardinality: ONE },
  three_month_animal_illness_1: {
    cardinality: MANY,
    category_parent: 10001700,
  },
  other_three_month_animal_illness_1: { cardinality: ONE },
  two_month_preventive_healthcare_1: {
    cardinality: MANY,
    category_parent: 10001800,
  },
  other_two_month_preventive_healthcare_1: { cardinality: ONE },
  pregnancy_range_of_animal_1: { cardinality: ONE, category_parent: 10001900 },
  insurance_status_of_animal_1: { cardinality: ONE, category_parent: 10002000 },
  subscription_plan_1: { cardinality: ONE, category_parent: 10002400 }, //not in form
  last_deworming_date_1: { cardinality: ONE },
  number_of_month_since_last_deworming_1: { cardinality: ONE },
  last_dip_cups_replacement_date_1: { cardinality: ONE },
  number_of_month_since_last_dip_cups_replacement_1: { cardinality: ONE },
  last_tick_control_date_1: { cardinality: ONE },
  number_of_month_since_last_tick_control_1: { cardinality: ONE },
  last_calving_date_1: { cardinality: ONE },
  months_since_last_calving_1: { cardinality: ONE },
  last_ai_date_1: { cardinality: ONE },
  last_pregnancy_determination_date_1: { cardinality: ONE },
  number_of_months_pregnant_1: { cardinality: ONE },
  last_fmd_vaccination_date_1: { cardinality: ONE },
  numbers_of_months_since_last_fmd_vaccination_1: { cardinality: ONE },
  last_fmd_or_hs_or_bq_vaccination_date_1: { cardinality: ONE },
  numbers_of_month_since_last_fmd_or_hs_or_bq_vaccination_1: {
    cardinality: ONE,
  },
  brucellosis_vaccination_done_or_not_1: {
    cardinality: ONE,
    category_parent: 10001050,
  },
  last_thileriosis_vaccination_date_1: { cardinality: ONE },
  number_of_months_since_last_theileriosis_vaccination_1: {
    cardinality: ONE,
  },
  last_anthrax_vaccination_date_1: { cardinality: ONE },
  number_of_months_since_last_anthrax_vaccination_1: { cardinality: ONE },
  pregnancy_month_1: { cardinality: ONE },
  animal_type: { cardinality: ONE, category_parent: 10001400 },
  healthcare_plan_subscription_date_1: { cardinality: ONE },
};
exports.animalLocalClassifier = {
  animal_type: {
    field_name: "animal_type",
  },
};

exports.animalDiseaseClassifierConfig = {
  animal_disease_type_1: { cardinality: ONE, category_parent: 10001700 },
  animal_disease_date_1: { cardinality: ONE },
  animal_disease_duration_1: { cardinality: ONE },
  animal_disease_medication_1: { cardinality: MANY, category_parent: 10003300 },
  animal_disease_paravet_name_1: { cardinality: ONE },
  animal_disease_vet_name_1: { cardinality: ONE },
  anima_disease_prescription_image_1: { cardinality: ONE },
  anima_disease_other_images_1: { cardinality: ONE },
};

exports.careCalendarClassifierConfig = {
  medicine_1: { cardinality: ONE },
  medicine_1_quantity: { cardinality: ONE },
  medicine_2: { cardinality: ONE },
  medicine_2_quantity: { cardinality: ONE },
  medicine_3: { cardinality: ONE },
  medicine_3_quantity: { cardinality: ONE },
  medicine_4: { cardinality: ONE },
  medicine_4_quantity: { cardinality: ONE },
  medicine_5: { cardinality: ONE },
  medicine_5_quantity: { cardinality: ONE },
  medicine_6: { cardinality: ONE },
  medicine_6_quantity: { cardinality: ONE },
  medicine_7: { cardinality: ONE },
  medicine_7_quantity: { cardinality: ONE },
  medicine_8: { cardinality: ONE },
  medicine_8_quantity: { cardinality: ONE },
  medicine_9: { cardinality: ONE },
  medicine_9_quantity: { cardinality: ONE },
  medicine_10: { cardinality: ONE },
  medicine_10_quantity: { cardinality: ONE },
  ticket_1: { cardinality: ONE },
};
