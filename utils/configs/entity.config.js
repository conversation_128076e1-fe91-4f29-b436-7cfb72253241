const {
  ENTITY_TYPE_FARMER,
  ENTITY_TYPE_CARE_CALENDAR,
  ENTITY_TYPE_STAFF_ROUTE_OFFICER,
  ENTITY_TYPE_STAFF_BACK_OFFICE_PERSON,
  ENTITY_TYPE_STAFF_PARAVET,
  ENTITY_TYPE_STAFF_VET,
  ENTITY_TYPE_STAFF_ZONAL_OFFICER,
  ENTITY_TYPE_STAFF_PPU_PARAVET,
  ENTITY_TYPE_PPU_FARMER,
  ENTITY_TYPE_STAFF_CENTRALIZED_VET,
  ENTITY_TYPE_STAFF_BCO_ADMIN
} = require("../common/namespace/krushal.namespace");

exports.staffType = {
  1000230001: {
    type: ENTITY_TYPE_STAFF_ROUTE_OFFICER,
  },
  1000230005: {
    type: ENTITY_TYPE_STAFF_ZONAL_OFFICER,
  },
  1000230002: {
    type: ENTITY_TYPE_STAFF_BACK_OFFICE_PERSON, // Admin
  },
  1000230003: {
    type: ENTITY_TYPE_STAFF_VET,
  },
  1000230004: {
    type: ENTITY_TYPE_STAFF_PARAVET,
  },
  1000230006: {
    type: ENTITY_TYPE_STAFF_PPU_PARAVET
  },
  1000230007: {
    type: ENTITY_TYPE_STAFF_CENTRALIZED_VET
  },
  1000230011: {
    type: ENTITY_TYPE_STAFF_BCO_ADMIN
  }
};

exports.customerType = {
  1000220001: {
    type: ENTITY_TYPE_FARMER,
  },
  1000220007: {
    type: ENTITY_TYPE_PPU_FARMER
  }

};

exports.taskType = {
  1000380001: {
    type: ENTITY_TYPE_CARE_CALENDAR,
  },
};
