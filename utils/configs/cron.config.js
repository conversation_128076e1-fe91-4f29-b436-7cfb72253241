const { configurationJSON } = require("@krushal-it/common-core")
const { translate } = require("../scheduler/scripts/translator_v2")
const { AnimalClassificationHistorySync } = require("../../services/animalClassificationHistory/animalClassificationHistory.class")
const { dailyCustomerAntibioticReport, dailyCattleRxReport, monthlyCustomerAntibioticReport } = require("../../services/reports/index")
const { moveDataToCustomerClassification }  = require("../helpers/farm_management")
module.exports = [
  {
    schedule: configurationJSON().GOOGLE.TRANSLATION.CRON_EXPRESSION,
    func: () => {
      if (configurationJSON().GOOGLE.TRANSLATION.ENABLE_TRANSLATION === true) {
        translate()
      }
    }
  },
  {
    schedule: configurationJSON().CRON_JOBS.ANIMAL_BACKUP,
    func: async () => {
      const animal_classification = new AnimalClassificationHistorySync()
      animal_classification.find()
    }
  },
  {
    schedule: configurationJSON().CRON_JOBS.DAILY_CATTLE_RX_REPORT,
    func: async () => {}
  },
  {
    schedule: configurationJSON().CRON_JOBS.DAILY_CATTLE_ANTIBIOTIC_REPORT,
    func: async () => {}
  },
  {
    schedule: configurationJSON().CRON_JOBS.MONTHLY_CUSTOMER_ANTIBIOTIC_REPORT,
    func: async () => {}
  },
  {
    schedule: configurationJSON().CRON_JOBS.MOVE_TQD_CUSTOMER_CLASSIFICATION ?? "0 4 * * *",
    func:  async () => {
      moveDataToCustomerClassification()
    }
  }
]
