const lodashObject = require("lodash");

const moment = require("moment")

const generateWhereClauseForReportInnerQuery = (data) => {
  let whereClause = ' WHERE 1 = 1 '
  if (data.filters && Object.keys(data.filters).length > 0) {
    const columnFilterKeys = Object.keys(data.filters)
    columnFilterKeys.forEach((searchColumn, index) => {
      if (searchColumn === 'ignoreColumn') {
        return
      }
      const searchValue = data.filters[searchColumn]
      if (searchValue === null) {
        return
      }
      let columnToSearchOn = searchColumn
      if (data.searchConfiguration[searchColumn].altColumn && data.searchConfiguration[searchColumn].altColumn != null && data.searchConfiguration[searchColumn].altColumn != '') {
        columnToSearchOn = data.searchConfiguration[searchColumn].altColumn
      }
      let arrayAsString = ''
      if (['default_array', 'single_select_array', 'multi_select_array'].includes(data.searchConfiguration[searchColumn].type)) {
        if (['int'].includes(data.searchConfiguration[searchColumn].dataType)) {
          arrayAsString = searchValue.join(',')
        } else if (['string', 'uuid'].includes(data.searchConfiguration[searchColumn].dataType)) {
          searchValue.forEach((searchValueItem) => {
            if (arrayAsString !== '') {
              arrayAsString = arrayAsString + ", "
            }
            arrayAsString = arrayAsString + "'" + searchValueItem + "'"
          })
        }
        if (arrayAsString !== '') {
          whereClause = whereClause + ' AND ' + columnToSearchOn + ' IN (' + arrayAsString + ') '
        }
      } else if (data.searchConfiguration[searchColumn].type === 'uuid') {
        if (Array.isArray(searchValue)) {
          let inUUIDValues = ''
          for (const individualUUIDValue of searchValue) {
            if (inUUIDValues !== '') {
              inUUIDValues = inUUIDValues + ', '
            }
            inUUIDValues = inUUIDValues + "'" + individualUUIDValue + "'"
          }
          whereClause = whereClause + " AND " + columnToSearchOn + " IN ( " + inUUIDValues + ") "
        } else {
          whereClause = whereClause + " AND " + columnToSearchOn + " = '" + searchValue + "'"
        }
      } else if (data.searchConfiguration[searchColumn].type === 'string') {
        whereClause = whereClause + " AND UPPER(" + columnToSearchOn + ") LIKE '%" + searchValue[0].toUpperCase() + "%'"
      } else if (data.searchConfiguration[searchColumn].type === 'dayjs_range') {
        const startDate = new Date(searchValue[0][0])
        const endDate = new Date(searchValue[0][1])
        if ((searchValue[0][0] === undefined || searchValue[0][0] === null) && (searchValue[0][1] !== undefined && searchValue[0][1] !== null)) {
          whereClause = whereClause + " AND " + columnToSearchOn + " <= '" + endDate.toISOString() + "' "
        } else if ((searchValue[0][1] === undefined || searchValue[0][1] === null) && (searchValue[0][0] !== undefined && searchValue[0][0] !== null)) {
          whereClause = whereClause + " AND " + columnToSearchOn + " >= '" + startDate.toISOString() + "' "
        } else if (searchValue[0][1] !== undefined && searchValue[0][0] !== undefined) {
          whereClause = whereClause + " AND " + columnToSearchOn + " >= '" + startDate.toISOString() + "' AND " + columnToSearchOn + " <= '" + endDate.toISOString() + "' "
        }
      } else if (data.searchConfiguration[searchColumn].type === 'dayjs_date_till') {
        const endDate = moment(searchValue[0]).tz('Asia/Kolkata').endOf('day').utc().toDate()
        whereClause = whereClause + " AND " + columnToSearchOn + " <= '" + endDate.toISOString() + "' "
      } else if (data.searchConfiguration[searchColumn].type === 'number') {
        const numberToMatch = parseInt(searchValue[0])
        whereClause = whereClause + " AND " + columnToSearchOn + " = " + numberToMatch + " "
      } else if (data.searchConfiguration[searchColumn].type === 'number_range') {
        let minValue, maxValue
        if (searchValue[0][0] !== undefined && searchValue[0][0] !== null) {
          minValue = parseInt(searchValue[0][0])
        }
        if (searchValue[0][1] !== undefined && searchValue[0][1] !== null) {
          maxValue = parseInt(searchValue[0][1])
        }
        if (minValue !== undefined) {
          whereClause = whereClause + " AND " + columnToSearchOn + " >= " + minValue + " "
        }
        if (maxValue !== undefined) {
          whereClause = whereClause + " AND " + columnToSearchOn + " <= " + maxValue + " "
        }
      }
    })
  }
  if (data.pageParams && data.pageParams.filterParams && Object.keys(data.pageParams.filterParams).length > 0) {
    const columnFilterKeys = Object.keys(data.pageParams.filterParams)
    columnFilterKeys.forEach((searchColumn, index) => {
      if (searchColumn === 'ignoreColumn') {
        return
      }
      if (data.searchConfiguration[searchColumn].allowFilterInPageParams !== true) {
        return
      }
      if (data.searchConfiguration[searchColumn].allowSettingFilterFromPageParams) {
        return
      }
      const searchValue = data.pageParams.filterParams[searchColumn]
      if (searchValue === null) {
        return
      }
      let columnToSearchOn = searchColumn
      if (data.searchConfiguration[searchColumn].altColumn && data.searchConfiguration[searchColumn].altColumn != null && data.searchConfiguration[searchColumn].altColumn != '') {
        columnToSearchOn = data.searchConfiguration[searchColumn].altColumn
      }
      let arrayAsString = ''
      if (data.searchConfiguration[searchColumn].paramSearchType === 'array') {
        if (data.searchConfiguration[searchColumn].paramSearchDataType === 'int') {
          arrayAsString = searchValue.join(',')
        } else if (data.searchConfiguration[searchColumn].paramSearchDataType === 'string') {
          searchValue.forEach((searchValueItem) => {
            if (arrayAsString !== '') {
              arrayAsString = arrayAsString + ", "
            }
            arrayAsString = arrayAsString + "'" + searchValueItem + "'"
          })
        }
        if (arrayAsString !== '') {
          whereClause = whereClause + ' AND ' + columnToSearchOn + ' IN (' + arrayAsString + ') '
        }
      } else if (data.searchConfiguration[searchColumn].paramSearchType === 'string') {
        whereClause = whereClause + " AND UPPER(" + columnToSearchOn + ") LIKE '%" + searchValue[0].toUpperCase() + "%'"
      } else if (data.searchConfiguration[searchColumn].paramSearchType === 'dayjs_range') {
        const startDate = new Date(searchValue[0][0])
        const endDate = new Date(searchValue[0][1])
        whereClause = whereClause + " AND " + columnToSearchOn + " >= '" + startDate.toISOString() + "' AND " + columnToSearchOn + " <= '" + endDate.toISOString() + "' "
      } else if (data.searchConfiguration[searchColumn].paramSearchType === 'number') {
        const numberToMatch = parseInt(searchValue[0])
        whereClause = whereClause + " AND " + columnToSearchOn + " = " + numberToMatch + " "
      } else if (data.searchConfiguration[searchColumn].paramSearchType === 'number_range') {
        let minValue, maxValue
        if (searchValue[0][0] !== undefined && searchValue[0][0] !== null) {
          minValue = parseInt(searchValue[0][0])
        }
        if (searchValue[0][1] !== undefined && searchValue[0][1] !== null) {
          maxValue = parseInt(searchValue[0][1])
        }
        if (minValue !== undefined) {
          whereClause = whereClause + " AND " + columnToSearchOn + " >= " + minValue + " "
        }
        if (maxValue !== undefined) {
          whereClause = whereClause + " AND " + columnToSearchOn + " <= " + maxValue + " "
        }
      }
    })
  }
  let orClause = ''
  if (data.globalSearch && data.globalSearch !== null && data.globalSearch !== '') {
    const searchConfigurationColumns = Object.keys(data.searchConfiguration)
    console.log('searchConfigurationColumns = ', searchConfigurationColumns)
    searchConfigurationColumns.forEach((column, index) => {
      if (data.searchConfiguration[column].includeInOmnisearch === true) {
        if (orClause !== '') {
          orClause = orClause + ' OR '
        }
        if (data.searchConfiguration[column].dataType === 'uuid') {
          orClause = orClause + " ( UPPER(" + column + "::text) LIKE '%" + data.globalSearch.toUpperCase() + "%')"
        } else {
          orClause = orClause + " ( UPPER(" + column + ") LIKE '%" + data.globalSearch.toUpperCase() + "%')"
        }
      }
    })
  }
  if (orClause !== '') {
    whereClause = whereClause + ' AND ( ' + orClause + ' ) '
  }
  return whereClause
}

const generateLimitStringForReportInnerQuery = (data) => {
  let limitString = ``
  if ((data.start || data.start === 0) && data.start !== -1 && data.size && data.size !== -1) {
    limitString = `LIMIT ${data.size} OFFSET ${data.start}`
  }
  return limitString
}

const generateSortStringForReportInnerQuery = (data) => {
  let sortString = ``
  if (data.sorting && Object.keys(data.sorting).length > 0 && data.sorting.columnKey !== 'ignoreColumn' && data.sorting.order !== undefined) {
    if (data.sorting.field) {
      sortString = `ORDER BY ${data.sorting.field} `
    } else {
      sortString = `ORDER BY ${data.sorting.columnKey} `
    }
    if (data.sorting.order === 'descend') {
      sortString = sortString + ` DESC `
    } else {
      sortString = sortString + ` ASC `
    }
  }
  return sortString
}

const updateEntityStatusToAdjustEndTimeForEntity2AsNull = `
  WITH ranked_rows AS ( 
    SELECT
      entity_status_uuid,
      status_category_id,
      status_id,
      entity_1_type_id,
      entity_1_entity_uuid,
      start_date,
      end_date,
      ROW_NUMBER() OVER (PARTITION BY status_category_id, entity_1_type_id, entity_1_entity_uuid ORDER BY start_date ASC) AS row_num
    FROM main.entity_status es
    WHERE es.entity_2_type_id is null
  ),
  linked_rows as (
    select es.entity_status_uuid, es.status_category_id, es.status_id, es.entity_1_type_id, es.entity_1_entity_uuid,
    es.start_date, es.end_date, nr.start_date end_date_to_be_updated_as
    from main.entity_status es
    inner join ranked_rows cr on es.entity_status_uuid = cr.entity_status_uuid
    left join ranked_rows as nr on nr.row_num = cr.row_num + 1 and es.status_category_id = nr.status_category_id
      and es.entity_1_type_id = nr.entity_1_type_id and es.entity_1_entity_uuid = nr.entity_1_entity_uuid
    where es.entity_2_type_id is null
  )
  UPDATE main.entity_status as es
  SET end_date = lr.end_date_to_be_updated_as
  FROM linked_rows lr
  WHERE es.entity_2_type_id is null and es.entity_status_uuid = lr.entity_status_uuid
    and lr.end_date_to_be_updated_as is not null
`

const updateEntityStatusToAdjustEndTimeForEntity2AsNotNull = `
  WITH ranked_rows AS ( 
    SELECT
      entity_status_uuid,
      status_category_id,
      status_id,
      entity_1_type_id,
      entity_1_entity_uuid,
      entity_2_type_id,
      entity_2_entity_uuid,
      start_date,
      end_date,
      ROW_NUMBER() OVER (PARTITION BY status_category_id, entity_1_type_id, entity_1_entity_uuid, entity_2_type_id, entity_2_entity_uuid ORDER BY start_date ASC) AS row_num
    FROM main.entity_status es
    WHERE es.entity_2_type_id is not null
  ),
  linked_rows as (
    select es.entity_status_uuid, es.status_category_id, es.status_id, es.entity_1_type_id, es.entity_1_entity_uuid,
    es.entity_2_type_id, es.entity_2_entity_uuid,
    es.start_date, es.end_date, nr.start_date end_date_to_be_updated_as
    from main.entity_status es
    inner join ranked_rows cr on es.entity_status_uuid = cr.entity_status_uuid
    left join ranked_rows as nr on nr.row_num = cr.row_num + 1 and es.status_category_id = nr.status_category_id
      and es.entity_1_type_id = nr.entity_1_type_id and es.entity_1_entity_uuid = nr.entity_1_entity_uuid
      and es.entity_2_type_id = nr.entity_2_type_id and es.entity_2_entity_uuid = nr.entity_2_entity_uuid
    where es.entity_2_type_id is not null
  )
  UPDATE main.entity_status as es
  SET end_date = lr.end_date_to_be_updated_as
  FROM linked_rows lr
  WHERE es.entity_2_type_id is not null and es.entity_status_uuid = lr.entity_status_uuid
    and lr.end_date_to_be_updated_as is not null
`

const adjustStartDatesOfEntityStatusByCategoryAndEntities = async (transactionalEntityManager) => {
  const queryResult = await transactionalEntityManager.query(updateEntityStatusToAdjustEndTimeForEntity2AsNull)
  console.log('oc UASS c 4, newlyCreatedEntityStatusqueryResult = ', queryResult)
  const queryResult2 = await transactionalEntityManager.query(updateEntityStatusToAdjustEndTimeForEntity2AsNotNull)
  console.log('oc UASS c 4a, newlyCreatedEntityStatusqueryResult = ', queryResult2)
}

const reportQueryClassificationJoinConfiguration = {
  SELLABLEANIMALSREPORT: {
    queryClassificationData: [
      {
        column: 'subscriptionDate',
        type: 'date',
        joinTable: 'main.oc_item_classification',
        joinColumn: 'oc_item_uuid',
        classifier_id: 2000000124,
        // mandatoryJoin: true,
        // additionalJoinCondition: {
        //   column: value_reference_id,
        //   value: **********,
        // },
        joinWith: 'oi.oc_item_uuid',
        selectColumnAlias: 'with_krushal_from_date'
      },
      {
        column: 'numberOfCalvings',
        joinTable: 'main.oc_item_classification',
        joinColumn: 'oc_item_uuid',
        type: 'int',
        classifier_id: **********,
        joinWith: 'oi.oc_item_uuid',
        selectColumnAlias: 'number_of_calvings'
      },      
      {
        column: 'numberOfMonthsPregnant',
        classifier_id: **********,
        type: 'referenceId',
        joinTable: 'main.oc_item_classification',
        joinColumn: 'oc_item_uuid',
        // mandatoryJoin: true,
        joinWith: 'oi.oc_item_uuid',
        joinValueReferenceToReference: true,
        selectColumnAlias: 'number_of_months_pregnant'
      },
      {
        column: 'healthScore',
        type: 'double',
        joinTable: 'main.oc_item_classification',
        joinColumn: 'oc_item_uuid',
        classifier_id: **********,
        joinWith: 'oi.oc_item_uuid',
        selectColumnAlias: 'health_score'
      },
      {
        column: 'bodyScore',
        type: 'double',
        joinTable: 'main.oc_item_classification',
        joinColumn: 'oc_item_uuid',
        classifier_id: **********,
        joinWith: 'oi.oc_item_uuid',
        selectColumnAlias: 'body_score'
      },
      {
        column: 'udderAndTeatScore',
        type: 'double',
        joinTable: 'main.oc_item_classification',
        joinColumn: 'oc_item_uuid',
        classifier_id: **********,
        joinWith: 'oi.oc_item_uuid',
        selectColumnAlias: 'udder_and_teat_score'
      },
      {
        column: 'animalInformationTrackerLink',
        type: 'string256',
        joinTable: 'main.oc_item_classification',
        joinColumn: 'oc_item_uuid',
        classifier_id: **********,
        joinWith: 'oi.oc_item_uuid',
        selectColumnAlias: 'animal_information_tracker_link'
      },
    ]
  },
  POTENTIALYSELLABLEANIMALSREPORT: {
    queryClassificationData: [
      {
        column: 'earTag',
        type: 'string256',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: 2000000034,
        mandatoryJoin: true,
        additionalJoinConditions: [
          {
            column: 'value_string_256',
            operation: 'nn',
            value: **********,
          }
        ],
        joinWith: 'a.animal_id',
        selectColumnAlias: 'animal_ear_tag'
      },
      {
        column: 'animalType',
        type: 'referenceId',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: 2000000035,
        mandatoryJoin: false,
        /* additionalJoinConditions: [
          {
            column: 'value_string_256',
            operation: 'nn',
            value: **********,
          }
        ], */
        joinWith: 'a.animal_id',
        joinValueReferenceToReference: true,
        selectColumnAlias: 'animal_type'
      },
      {
        column: 'subscriptionPlan',
        type: 'referenceId',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: 2000000051,
        // mandatoryJoin: true,
        // additionalJoinCondition: {
        //   column: value_reference_id,
        //   value: **********,
        // },
        joinWith: 'a.animal_id',
        joinValueReferenceToReference: true,
        selectColumnAlias: 'current_subscription_plan'
      },
      {
        column: 'subscriptionDate',
        type: 'date',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: 2000000124,
        // mandatoryJoin: true,
        // additionalJoinCondition: {
        //   column: value_reference_id,
        //   value: **********,
        // },
        joinWith: 'a.animal_id',
        selectColumnAlias: 'with_krushal_from_date',
        otherLoadColumns: ['updated_at'],
        otherLoadColumnAliases: ['with_krushal_from_date_updated_at']
      },
      {
        column: 'animalWeight',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        type: 'double',
        classifier_id: 2000000041,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'animal_weight',
        otherLoadColumns: ['updated_at'],
        otherLoadColumnAliases: ['latest_animal_weight_date']
      },
      /* {
        column: 'latestAnimalWeightDate',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        type: 'updated_at',
        classifier_id: 2000000041,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'latest_animal_weight_date',
      }, */
      {
        column: 'numberOfCalvings',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        type: 'int',
        classifier_id: **********,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'number_of_calvings'
      },
      {
        column: 'lastCalvingDate',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        type: 'date',
        classifier_id: **********,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'last_calving_date'
      },
      /* {
        column: 'numberOfMonthsPregnant',
        classifier_id: **********,
        type: 'referenceId',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        // mandatoryJoin: true,
        joinWith: 'a.animal_id',
        joinValueReferenceToReference: true,
        selectColumnAlias: 'number_of_months_pregnant'
      }, */
      {
        column: 'healthScore',
        type: 'double',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: **********,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'health_score',
        otherLoadColumns: ['updated_at'],
        otherLoadColumnAliases: ['latest_health_score_date']
      },
      {
        column: 'latestLPD',
        type: 'double',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: **********,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'latest_lpd',
        otherLoadColumns: ['updated_at'],
        otherLoadColumnAliases: ['latest_lpd_date']
      },
      {
        column: 'maximumLPD',
        type: 'double',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: **********,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'maximum_lpd',
        otherLoadColumns: ['updated_at'],
        otherLoadColumnAliases: ['maximum_lpd_date']
      },
      /* {
        column: 'latestHealthScoreDate',
        type: 'updated_at',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: **********,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'latest_health_score_date'
      }, */
      {
        column: 'healthScoreVerificationStatus',
        type: 'referenceId',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: **********,
        joinWith: 'a.animal_id',
        joinValueReferenceToReference: true,
        selectColumnAlias: 'latest_health_score_verification_status',
        referenceIdAlias: 'latest_health_score_verification_status_id',
        otherLoadColumns: ['updated_at'],
        otherLoadColumnAliases: ['latest_health_score_verification_status_updated_at'],
        nullHandling: {
          alternateValue: 'None',
          inQuotes: true,
          alternateReferenceId: **********,
        },
        // mandatoryJoin: false,
        /* additionalJoinConditions: [
          {
            column: 'value_string_256',
            operation: 'nn',
            value: **********,
          }
        ], */
      },
      {
        column: 'bodyScore',
        type: 'double',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: **********,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'body_score'
      },
      {
        column: 'udderAndTeatScore',
        type: 'double',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: **********,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'udder_and_teat_score'
      },
      {
        column: 'animalExchangeInformationTrackerLink',
        type: 'string256',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: **********,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'animal_information_tracker_link',
      }
    ]
  },
  POTENTIALBUYERFARMERSFORANIMALREPORTBUYERFARMERSECTION: {
    queryClassificationData: [
      {
        column: 'numberOfAdultCows',
        type: 'int',
        joinTable: 'main.customer_classification',
        joinColumn: 'customer_id',
        classifier_id: 2000000006,
        joinWith: 'c.customer_id',
        selectColumnAlias: 'number_of_adult_cows',
        nullHandling: {
          alternateValue: 0,
          inQuotes: false
        }
      },
      {
        column: 'numberOfAdultCows',
        type: 'int',
        joinTable: 'main.customer_classification',
        joinColumn: 'customer_id',
        classifier_id: 2000000007,
        joinWith: 'c.customer_id',
        selectColumnAlias: 'number_of_cow_calves',
        nullHandling: {
          alternateValue: 0,
          inQuotes: false
        }
      },
      {
        column: 'numberOfAdultCows',
        type: 'int',
        joinTable: 'main.customer_classification',
        joinColumn: 'customer_id',
        classifier_id: 2000000008,
        joinWith: 'c.customer_id',
        selectColumnAlias: 'number_of_adult_buffaloes',
        nullHandling: {
          alternateValue: 0,
          inQuotes: false
        }
      },
      {
        column: 'numberOfAdultCows',
        type: 'int',
        joinTable: 'main.customer_classification',
        joinColumn: 'customer_id',
        classifier_id: 2000000009,
        joinWith: 'c.customer_id',
        selectColumnAlias: 'number_of_buffalo_calves',
        nullHandling: {
          alternateValue: 0,
          inQuotes: false
        }
      },
      {
        column: 'numberOfMonthsPregnant',
        classifier_id: 2000000005,
        type: 'referenceId',
        joinTable: 'main.customer_classification',
        joinColumn: 'customer_id',
        // mandatoryJoin: true,
        joinWith: 'c.customer_id',
        joinValueReferenceToReference: true,
        selectColumnAlias: 'type_of_dairy_farm'
      }
    ]
  },
  FARMERSWHOMAYWANTTOBUYFARMERSECTION: {
    queryClassificationData: [
      {
        column: 'numberOfAdultCows',
        type: 'int',
        joinTable: 'main.customer_classification',
        joinColumn: 'customer_id',
        classifier_id: 2000000006,
        joinWith: 'c.customer_id',
        selectColumnAlias: 'number_of_adult_cows',
        nullHandling: {
          alternateValue: 0,
          inQuotes: false
        }
      },
      {
        column: 'numberOfAdultCows',
        type: 'int',
        joinTable: 'main.customer_classification',
        joinColumn: 'customer_id',
        classifier_id: 2000000007,
        joinWith: 'c.customer_id',
        selectColumnAlias: 'number_of_cow_calves',
        nullHandling: {
          alternateValue: 0,
          inQuotes: false
        }
      },
      {
        column: 'numberOfAdultCows',
        type: 'int',
        joinTable: 'main.customer_classification',
        joinColumn: 'customer_id',
        classifier_id: 2000000008,
        joinWith: 'c.customer_id',
        selectColumnAlias: 'number_of_adult_buffaloes',
        nullHandling: {
          alternateValue: 0,
          inQuotes: false
        }
      },
      {
        column: 'numberOfAdultCows',
        type: 'int',
        joinTable: 'main.customer_classification',
        joinColumn: 'customer_id',
        classifier_id: 2000000009,
        joinWith: 'c.customer_id',
        selectColumnAlias: 'number_of_buffalo_calves',
        nullHandling: {
          alternateValue: 0,
          inQuotes: false
        }
      },
      {
        column: 'numberOfMonthsPregnant',
        classifier_id: 2000000005,
        type: 'referenceId',
        joinTable: 'main.customer_classification',
        joinColumn: 'customer_id',
        // mandatoryJoin: true,
        joinWith: 'c.customer_id',
        joinValueReferenceToReference: true,
        selectColumnAlias: 'type_of_dairy_farm'
      }
    ]
  },
  FARMERSWHOMAYWANTTOBUYITEMECTION: {
    queryClassificationData: [
      {
        column: 'numberOfCalvings',
        type: 'int',
        joinTable: 'main.oc_item_classification',
        joinColumn: 'oc_item_uuid',
        classifier_id: **********,
        joinWith: 'oi.oc_item_uuid',
        selectColumnAlias: 'number_of_calvings',
      },
      /* {
        column: 'numberOfMonthsPregnant',
        type: 'referenceId',
        joinTable: 'main.oc_item_classification',
        joinColumn: 'oc_item_uuid',
        classifier_id: **********,
        joinWith: 'oi.oc_item_uuid',
        selectColumnAlias: 'number_of_months_pregnant',
        joinValueReferenceToReference: true,
      }, */
      {
        column: 'healthScore',
        type: 'double',
        joinTable: 'main.oc_item_classification',
        joinColumn: 'oc_item_uuid',
        classifier_id: **********,
        joinWith: 'oi.oc_item_uuid',
        selectColumnAlias: 'health_score',
      },
      {
        column: 'bodyScore',
        type: 'double',
        joinTable: 'main.oc_item_classification',
        joinColumn: 'oc_item_uuid',
        classifier_id: **********,
        joinWith: 'oi.oc_item_uuid',
        selectColumnAlias: 'body_score',
      },
      {
        column: 'udderAndTeatScore',
        type: 'double',
        joinTable: 'main.oc_item_classification',
        joinColumn: 'oc_item_uuid',
        classifier_id: **********,
        joinWith: 'oi.oc_item_uuid',
        selectColumnAlias: 'udder_and_teat_score',
      },
      {
        column: 'animalInformationTrackerLink',
        type: 'string256',
        joinTable: 'main.oc_item_classification',
        joinColumn: 'oc_item_uuid',
        classifier_id: **********,
        joinWith: 'oi.oc_item_uuid',
        selectColumnAlias: 'animal_information_tracker_link',
        
      }
    ]
  },
  CUSTOMER: {
    queryClassificationData: [
      {
        column: 'cowCount',
        type: 'int',
        joinTable: 'main.customer_classification',
        joinColumn: 'customer_id',
        classifier_id: 2000000006,
        joinWith: 'c.customer_id',
        selectColumnAlias: 'cow_count',
        otherLoadColumns: ['updated_at'],
        otherLoadColumnAliases: ['cow_count_updated_at']
      },
      {
        column: 'buffaloCount',
        type: 'int',
        joinTable: 'main.customer_classification',
        joinColumn: 'customer_id',
        classifier_id: 2000000008,
        joinWith: 'c.customer_id',
        selectColumnAlias: 'buffalo_count',
        otherLoadColumns: ['updated_at'],
        otherLoadColumnAliases: ['buffalo_count_updated_at']
      },
      {
        column: 'locationUpdatedTimeStamp',
        type: 'updated_at',
        joinTable: 'main.customer_classification',
        joinColumn: 'customer_id',
        mandatoryJoin: true,
        classifier_id: 2000000004,
        joinWith: 'c.customer_id',
        selectColumnAlias: 'location_updated_timestamp',
      },
      {
        column: 'location',
        type: 'valueJson',
        joinTable: 'main.customer_classification',
        joinColumn: 'customer_id',
        mandatoryJoin: true,
        classifier_id: 2000000004,
        joinWith: 'c.customer_id',
        selectColumnAlias: 'location_json',
      },
      {
        column: 'village',
        type: 'referenceId',
        joinTable: 'main.customer_classification',
        joinColumn: 'customer_id',
        mandatoryJoin: true,
        classifier_id: 2000000055,
        joinValueReferenceToSDTV: {columnToJoin: 'village_id', columnsToChoose: [
          {columnName: 'village_name_l10n', operation: 'coalesceL10N', alias: 'village_name'},
          {columnName: 'village_id', alias: 'village_id'},
          {columnName: 'taluk_name_l10n', operation: 'coalesceL10N', alias: 'taluk_name'},
        ]},
        joinWith: 'c.customer_id',
        selectColumnAliases: ['village_name', 'taluk_name']
      }
    ]
  },
  ANIMAL: {
    queryClassificationData: [
      {
        column: 'earTag',
        type: 'string256',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: 2000000034,
        mandatoryJoin: true,
        additionalJoinConditions: [
          {
            column: 'value_string_256',
            operation: 'nn',
            value: **********,
          }
        ],
        joinWith: 'a.animal_id',
        selectColumnAlias: 'animal_ear_tag'
      },
      {
        column: 'earTag',
        type: 'string256',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: 2000000034,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'ear_tag_1'
      },
      {
        column: 'animalName',
        type: 'string256',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: 2000000033,
        mandatoryJoin: false,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'animal_name'
      },
      {
        column: 'animalType',
        type: 'referenceId',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: 2000000035,
        mandatoryJoin: false,
        /* additionalJoinConditions: [
          {
            column: 'value_string_256',
            operation: 'nn',
            value: **********,
          }
        ], */
        joinWith: 'a.animal_id',
        joinValueReferenceToReference: true,
        selectColumnAlias: 'animal_type'
      },
      {
        column: 'subscriptionPlan',
        type: 'referenceId',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: 2000000051,
        // mandatoryJoin: true,
        // additionalJoinCondition: {
        //   column: value_reference_id,
        //   value: **********,
        // },
        joinWith: 'a.animal_id',
        joinValueReferenceToReference: true,
        selectColumnAlias: 'current_subscription_plan'
      },
      {
        column: 'breed',
        type: 'referenceId',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: 2000000036,
        // mandatoryJoin: true,
        // additionalJoinCondition: {
        //   column: value_reference_id,
        //   value: **********,
        // },
        joinWith: 'a.animal_id',
        joinValueReferenceToReference: true,
        selectColumnAlias: 'breed'
      },
      {
        column: 'animalStage',
        type: 'referenceId',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: 2000000146,
        // mandatoryJoin: true,
        // additionalJoinCondition: {
        //   column: value_reference_id,
        //   value: **********,
        // },
        joinWith: 'a.animal_id',
        joinValueReferenceToReference: true,
        selectColumnAlias: 'animal_stage'
      },
      {
        column: 'subscriptionDate',
        type: 'date',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: 2000000124,
        // mandatoryJoin: true,
        // additionalJoinCondition: {
        //   column: value_reference_id,
        //   value: **********,
        // },
        joinWith: 'a.animal_id',
        selectColumnAlias: 'with_krushal_from_date',
        otherLoadColumns: ['updated_at'],
        otherLoadColumnAliases: ['with_krushal_from_date_updated_at']
      },
      {
        column: 'animalWeight',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        type: 'double',
        classifier_id: 2000000041,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'animal_weight',
        otherLoadColumns: ['updated_at'],
        otherLoadColumnAliases: ['latest_animal_weight_date']
      },
      {
        column: 'animalWeight2',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        type: 'double',
        classifier_id: 2000000041,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'animal_weight_1',
        otherLoadColumns: ['updated_at'],
        otherLoadColumnAliases: ['latest_animal_weight_date']
      },
      /* {
        column: 'latestAnimalWeightDate',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        type: 'updated_at',
        classifier_id: 2000000041,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'latest_animal_weight_date',
      }, */
      {
        column: 'numberOfCalvings',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        type: 'int',
        classifier_id: **********,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'number_of_calvings'
      },
      {
        column: 'lastCalvingDate',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        type: 'date',
        classifier_id: **********,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'last_calving_date'
      },
      {
        column: 'dateOfBirth',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        type: 'date',
        classifier_id: **********,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'date_of_birth'
      },
      /* {
        column: 'numberOfMonthsPregnant',
        classifier_id: **********,
        type: 'referenceId',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        // mandatoryJoin: true,
        joinWith: 'a.animal_id',
        joinValueReferenceToReference: true,
        selectColumnAlias: 'number_of_months_pregnant'
      }, */
      {
        column: 'healthScore',
        type: 'double',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: **********,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'health_score',
        otherLoadColumns: ['updated_at'],
        otherLoadColumnAliases: ['latest_health_score_date']
      },
      {
        column: 'latestLPD',
        type: 'double',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: **********,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'latest_lpd',
        otherLoadColumns: ['updated_at'],
        otherLoadColumnAliases: ['latest_lpd_date']
      },
      {
        column: 'latestLPD2',
        type: 'double',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: **********,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'animal_average_lpd_1',
        otherLoadColumns: ['updated_at'],
        otherLoadColumnAliases: ['latest_lpd_date']
      },
      {
        column: 'maximumLPD',
        type: 'double',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: **********,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'maximum_lpd',
        otherLoadColumns: ['updated_at'],
        otherLoadColumnAliases: ['maximum_lpd_date']
      },
      {
        column: 'milkFat',
        type: 'double',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: 2000000172,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'animal_milk_fat_1',
      },
      {
        column: 'noOfMonthsPregnant',
        type: 'double',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: 2000000067,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'number_of_months_pregnant_1',
      },
      {
        column: 'noOfMonthsPregnantAsOfDate',
        type: 'date',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: 2000000165,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'number_of_months_pregnant_as_of_date_1',
      },
      {
        column: 'age',
        type: 'double',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: 2000000038,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'animal_age_1',
      },
      {
        column: 'ageAsOf',
        type: 'date',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: 2000000039,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'age_as_of_date_1',
      },
      {
        column: 'ageAsOf',
        type: 'date',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: 2000000039,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'age_of_animal_as_of_1',
      },
      {
        column: 'monthsSinceLastCalving',
        type: 'int',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: **********,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'months_since_last_calving_1',
      },
      {
        column: 'monthsSinceLastCalvingAsOfDate',
        type: 'date',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: **********,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'month_since_calving_as_of_data_1',
      },
      /* {
        column: 'latestHealthScoreDate',
        type: 'updated_at',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: **********,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'latest_health_score_date'
      }, */
      {
        column: 'healthScoreVerificationStatus',
        type: 'referenceId',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: **********,
        joinWith: 'a.animal_id',
        joinValueReferenceToReference: true,
        selectColumnAlias: 'latest_health_score_verification_status',
        referenceIdAlias: 'latest_health_score_verification_status_id',
        otherLoadColumns: ['updated_at'],
        otherLoadColumnAliases: ['latest_health_score_verification_status_updated_at'],
        nullHandling: {
          alternateValue: 'None',
          inQuotes: true,
          alternateReferenceId: **********,
        },
        // mandatoryJoin: false,
        /* additionalJoinConditions: [
          {
            column: 'value_string_256',
            operation: 'nn',
            value: **********,
          }
        ], */
      },
      {
        column: 'bodyScore',
        type: 'double',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: **********,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'body_score'
      },
      {
        column: 'udderAndTeatScore',
        type: 'double',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: **********,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'udder_and_teat_score'
      },
      {
        column: 'animalExchangeInformationTrackerLink',
        type: 'string256',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: **********,
        joinWith: 'a.animal_id',
        selectColumnAlias: 'animal_information_tracker_link',
        nullHandling: {
          alternateValue: '',
          inQuotes: true
        }
      }
    ]
  },
  MEDICINE: {
    queryClassificationData: [
      {
        column: 'earTag',
        type: 'string256',
        joinTable: 'main.animal_classification',
        joinColumn: 'animal_id',
        classifier_id: 2000000034,
        mandatoryJoin: true,
        additionalJoinConditions: [
          {
            column: 'value_string_256',
            operation: 'nn',
            value: **********,
          }
        ],
        joinWith: 'a.animal_id',
        selectColumnAlias: 'animal_ear_tag'
      },
    ]
  },
  PARTNER: {
    queryClassificationData: [
      {
        column: 'primaryContactName',
        type: 'valueL10n',
        joinTable: 'main.partner_classification',
        joinColumn: 'partner_id',
        classifier_id: 2000000275,
        mandatoryJoin: false,
        joinWith: 'p.partner_id',
        selectColumnAlias: 'primary_contact_name'
      },
      {
        column: 'village',
        type: 'referenceId',
        joinTable: 'main.partner_classification',
        joinColumn: 'partner_id',
        mandatoryJoin: false,
        classifier_id: 2000000276,
        joinValueReferenceToSDTV: {columnToJoin: 'village_id', columnsToChoose: [
          {columnName: 'village_name_l10n', operation: 'coalesceL10N', alias: 'village_name'},
          {columnName: 'village_id', alias: 'village_id'},
          {columnName: 'taluk_name_l10n', operation: 'coalesceL10N', alias: 'taluk_name'},
          {columnName: 'taluk_id', alias: 'taluk_id'},
        ]},
        joinWith: 'p.partner_id',
        selectColumnAliases: ['village_name' , 'taluk_name' ]
      },
      {
        column: 'bcoLocation',
        type: 'valueJson',
        joinTable: 'main.partner_classification',
        joinColumn: 'partner_id',
        mandatoryJoin: true,
        classifier_id: 2000000412,
        joinWith: 'p.partner_id',
        additionalJoinConditions: [
          {
            column: 'value_json',
            operation: 'nn',
          }
        ],
        selectColumnAlias: 'bco_location',
      },
    ]
  },
}

const generateSelectJoinClauseForClassification = (classificationJoinConfiguration, configurationKey, counterPrefix, columnsToInclude, columnsToExclude) => {
  let selectClause = ''
  let joinClause = ' '
  const jsonColumns = []
  const doubleColumns = []
  console.log('oc r gJC 1')
  if (classificationJoinConfiguration && classificationJoinConfiguration[configurationKey] && classificationJoinConfiguration[configurationKey].queryClassificationData && Array.isArray(classificationJoinConfiguration[configurationKey].queryClassificationData)) {
    const configurationQueryClassificationData = classificationJoinConfiguration[configurationKey].queryClassificationData
    console.log('oc r gJC 2')
    let counter = -1
    for (const columnConfiguration of configurationQueryClassificationData) {
      // const columnConfiguration = configurationQueryClassificationData[counter]
      const selectColumnAliases = columnConfiguration.selectColumnAliases === undefined ? [] : columnConfiguration.selectColumnAliases
      if (columnConfiguration.selectColumnAlias !== undefined) {
        selectColumnAliases.push(columnConfiguration.selectColumnAlias)
      }
      // if (columnsToInclude && Array.isArray(columnsToInclude) && columnsToInclude.length > 0 && !columnsToInclude.includes(columnConfiguration.selectColumnAlias)) {
      if (columnsToInclude && Array.isArray(columnsToInclude) && columnsToInclude.length > 0 && lodashObject.intersection(columnsToInclude, selectColumnAliases).length === 0) {
        continue
      }
      // if (columnsToExclude && Array.isArray(columnsToExclude) && columnsToExclude.length > 0 && columnsToExclude.includes(columnConfiguration.selectColumnAlias)) {
      if (columnsToExclude && Array.isArray(columnsToExclude) && columnsToExclude.length > 0 && lodashObject.intersection(columnsToExclude, selectColumnAliases).length > 0) {
        continue
      }
      counter++
      const joinTableAlias = 'cJ' + (counterPrefix ? counterPrefix : '') + (counter + 1)
      const joinTableReferenceAlias = 'cR' + (counterPrefix ? counterPrefix : '') + (counter + 1)
      console.log('oc r gJC 3, columnConfiguration = ', columnConfiguration)
      if (columnConfiguration.mandatoryJoin === true) {
        joinClause = joinClause + ' \n\
          inner join '
      } else {
        joinClause = joinClause + ' \n\
          left join '
      }
      joinClause = joinClause + columnConfiguration.joinTable + ' ' + joinTableAlias + ' on ' + joinTableAlias + '.active = 1000100001 and ' + joinTableAlias + '.' + columnConfiguration.joinColumn + ' = ' + columnConfiguration.joinWith + ' \n\
      '
      joinClause = joinClause + ' and ' + joinTableAlias + '.classifier_id = ' + columnConfiguration.classifier_id + ' \n\
      '

      if (columnConfiguration.additionalJoinConditions && Array.isArray(columnConfiguration.additionalJoinConditions) && columnConfiguration.additionalJoinConditions.length > 0) {
        for (let innerCounter = 0 ; innerCounter < columnConfiguration.additionalJoinConditions.length ; innerCounter++) {
          const additionalJoinCondition = columnConfiguration.additionalJoinConditions[innerCounter]
          switch (additionalJoinCondition.operation) {
            case 'nn':
              joinClause = joinClause + ' and ' + joinTableAlias + '.' + additionalJoinCondition.column + ' is not NULL\n'
              break
            default:
              throw new Error('Not supported operation')
          }
        }
      }
      if (columnConfiguration.joinValueReferenceToReference === true) {
        if (columnConfiguration.mandatoryJoin === true) {
          joinClause = joinClause + ' \n\
          inner join '
        } else {
          joinClause = joinClause + ' \n\
          left join '
        }
        joinClause = joinClause + ' main.ref_reference ' + joinTableReferenceAlias + ' on ' + joinTableReferenceAlias + '.reference_id = ' + joinTableAlias + '.value_reference_id'
      }
      if (columnConfiguration.joinValueReferenceToSDTV !== undefined) {
        if (typeof columnConfiguration.joinValueReferenceToSDTV === 'object') {
          if (columnConfiguration.mandatoryJoin === true) {
            joinClause = joinClause + ' \n\
            inner join '
          } else {
            joinClause = joinClause + ' \n\
            left join '
          }
          joinClause = joinClause + ' main.ref_sdtv_view ' + joinTableReferenceAlias + ' on ' + joinTableReferenceAlias + '.village_id = ' + joinTableAlias + '.value_reference_id'
        }
      }
      let selectColumn
      switch (columnConfiguration.type) {
        case 'date': 
          selectColumn = 'value_date'
          break
        case 'double': 
          selectColumn = 'value_double'
          doubleColumns.push(columnConfiguration.selectColumnAlias)
          break
        case 'int': 
          selectColumn = 'value_int'
          break
        case 'referenceId': 
          selectColumn = 'value_reference_id'
          break
        case 'valueJson': 
          selectColumn = 'value_json'
          jsonColumns.push(columnConfiguration.selectColumnAlias)
          break
        case 'valueL10n': 
          selectColumn = 'value_l10n'
          jsonColumns.push(columnConfiguration.selectColumnAlias)
          break
        case 'string256':
          selectColumn = 'value_string_256'
          break
        case 'string2000':
          selectColumn = 'value_string_2000'
          break
        case 'updated_at':
          selectColumn = 'updated_at'
          break
        default:
          throw new Error('Not supported')
      }
      if (selectClause !== undefined && selectClause !== '') {
        selectClause = selectClause + ', \n\
        '
      } else {
        selectClause = ' '
      }
      if (columnConfiguration.joinValueReferenceToReference === true) {
        if (columnConfiguration.nullHandling) {
          let alternateValue = columnConfiguration.nullHandling.alternateValue
          if (columnConfiguration.nullHandling.inQuotes) {
            alternateValue = '\'' + alternateValue + '\''
          }
          selectClause = selectClause + ' coalesce(coalesce(' + joinTableReferenceAlias + ".reference_name_l10n->>'en', " + joinTableReferenceAlias + ".reference_name_l10n->>'ul'), " + alternateValue + ") as "
        } else {
          selectClause = selectClause + ' coalesce(' + joinTableReferenceAlias + ".reference_name_l10n->>'en', " + joinTableReferenceAlias + ".reference_name_l10n->>'ul') as "
        }
        selectClause = selectClause + columnConfiguration.selectColumnAlias
      } else if (columnConfiguration.joinValueReferenceToSDTV !== undefined) {
        if (typeof columnConfiguration.joinValueReferenceToSDTV === 'object') {
          const sdtvColumns = columnConfiguration.joinValueReferenceToSDTV.columnsToChoose
          for (let innerCounter = 0; innerCounter < sdtvColumns.length; innerCounter++) {
            if (innerCounter !== 0) {
              selectClause = selectClause + ', '
            }
            const sdtvColumnConfiguration = sdtvColumns[innerCounter]
            if (sdtvColumnConfiguration.operation === 'coalesceL10N') {
              selectClause = selectClause + " coalesce(" + joinTableReferenceAlias + "." + sdtvColumnConfiguration.columnName + "->>'en', " + joinTableReferenceAlias + "." + sdtvColumnConfiguration.columnName + "->>'ul') as " + sdtvColumnConfiguration.alias + " "
            } else {
              selectClause = selectClause + joinTableReferenceAlias + "." + sdtvColumnConfiguration.columnName + " as " + sdtvColumnConfiguration.alias + " "
            }
          }
        }
      } else {
        if (columnConfiguration.nullHandling) {
          let alternateValue = columnConfiguration.nullHandling.alternateValue
          if (columnConfiguration.nullHandling.inQuotes) {
            alternateValue = '\'' + alternateValue + '\''
          }
          selectClause = selectClause + ' coalesce(' + joinTableAlias + '.' + selectColumn + ', ' + alternateValue + ') as '
        } else if (columnConfiguration.type === 'valueL10n') {
          // selectClause = selectClause + ' ' + joinTableAlias + '.' + selectColumn + ' as '
          selectClause = selectClause + " coalesce(" + joinTableAlias + ".value_l10n->>'en', " + joinTableAlias + ".value_l10n->>'ul') as "
        } else {
          selectClause = selectClause + ' ' + joinTableAlias + '.' + selectColumn + ' as '
        }
        selectClause = selectClause + columnConfiguration.selectColumnAlias
      }
      if (columnConfiguration.joinValueReferenceToReference === true && columnConfiguration.referenceIdAlias) {
        if (columnConfiguration.nullHandling && columnConfiguration.nullHandling.alternateReferenceId) {
          selectClause = selectClause + ',\n coalesce(' + joinTableReferenceAlias + '.reference_id' + ', ' + columnConfiguration.nullHandling.alternateReferenceId + ') as ' + columnConfiguration.referenceIdAlias + ' '
        } else {
          selectClause = selectClause + ',\n' + joinTableReferenceAlias + '.reference_id' + ' as ' + columnConfiguration.referenceIdAlias + ' '
        }
      }
      if (columnConfiguration && columnConfiguration.otherLoadColumns && columnConfiguration.otherLoadColumnAliases
            && Array.isArray(columnConfiguration.otherLoadColumns) && Array.isArray(columnConfiguration.otherLoadColumnAliases)
            && columnConfiguration.otherLoadColumns.length > 0 && columnConfiguration.otherLoadColumns.length === columnConfiguration.otherLoadColumnAliases.length) {
        for (let counter = 0 ; counter < columnConfiguration.otherLoadColumns.length ; counter++) {
          const otherColumn = columnConfiguration.otherLoadColumns[counter]
          const otherColumnAlias = columnConfiguration.otherLoadColumnAliases[counter]
          if (selectClause !== undefined) {
            selectClause = selectClause + ', \n\
            '
          } else {
            selectClause = ' '
          }
          selectClause = selectClause + ' ' + joinTableAlias + '.' + otherColumn + ' as '
          selectClause = selectClause + otherColumnAlias
        }
      }
    }
  }
  return [selectClause, joinClause, jsonColumns, doubleColumns]
}

const identifyMandatoryColumnsForReportQuery = (filterParams, sortParams, globalSearch, searchConfiguration) => {
  const mandatoryColumns = []
  if (globalSearch !== undefined && globalSearch !== null && globalSearch !== '') {
    const searchConfigurationColumns = Object.keys(searchConfiguration)
    for (const searchConfigurationColumn of searchConfigurationColumns) {
      const columnConfiguration = searchConfiguration[searchConfigurationColumn]
      if (columnConfiguration.includeInOmnisearch === true) {
        mandatoryColumns.push(searchConfigurationColumn)
      }
    }
  }
  if (sortParams && Object.keys(sortParams).length > 0 && sortParams['columnKey'] !== undefined && sortParams['columnKey'] !== null && sortParams['columnKey'] !== '' && sortParams['order'] !== undefined) {
    if (!mandatoryColumns.includes(sortParams['columnKey'])) {
      mandatoryColumns.push(sortParams['columnKey'])
    }
  }
  if (filterParams && Object.keys(filterParams).length > 0) {
    const filterColumnNames = Object.keys(filterParams)
    for (const filterColumnName of filterColumnNames) {
      if (filterColumnName === 'ignoreColumn') {
        continue
      }
      if (filterParams[filterColumnName] === undefined || filterParams[filterColumnName] === null) {
        continue
      }
      if (!mandatoryColumns.includes(filterColumnName)) {
        mandatoryColumns.push(filterColumnName)
      }
    }
  }
  return mandatoryColumns
}

const extractFieldsNeedingFilterData = (columnConfigurations, selectedColumns) => {
  const filtersFieldToReturnKeyMap = {}
  if (columnConfigurations !== undefined) {
    const availableColumnsInColumnConfiguration = Object.keys(columnConfigurations)
    for (const availableColumnInColumnConfiguration of availableColumnsInColumnConfiguration) {
      const columnConfiguration = columnConfigurations[availableColumnInColumnConfiguration]
      if (columnConfiguration.updateFilterDataDuringReportCall === true && selectedColumns.includes(availableColumnInColumnConfiguration)) {
        let filterValuesKey = availableColumnInColumnConfiguration + '_filter_values'
        if (columnConfiguration.filterValuesKey !== undefined) {
          filterValuesKey = columnConfiguration.filterValuesKey
        }
        filtersFieldToReturnKeyMap[availableColumnInColumnConfiguration] = filterValuesKey
      }
    }
  }
  return filtersFieldToReturnKeyMap
}

const extractFieldsNeedingPageFilterData = (columnConfigurations, selectedColumns) => {
  const pageFiltersFieldToReturnKeyMap = {}
  if (columnConfigurations !== undefined) {
    const availableColumnsInColumnConfiguration = Object.keys(columnConfigurations)
    for (const availableColumnInColumnConfiguration of availableColumnsInColumnConfiguration) {
      const columnConfiguration = columnConfigurations[availableColumnInColumnConfiguration]
      if (columnConfiguration.updateFilterDataDuringFilterCall === true && selectedColumns.includes(availableColumnInColumnConfiguration)) {
        let filterValuesKey = availableColumnInColumnConfiguration + '_filter_values'
        if (columnConfiguration.filterValuesKey !== undefined) {
          filterValuesKey = columnConfiguration.filterValuesKey
        }
        pageFiltersFieldToReturnKeyMap[availableColumnInColumnConfiguration] = filterValuesKey
      }
    }
  }
  return pageFiltersFieldToReturnKeyMap
}

module.exports = { generateWhereClauseForReportInnerQuery, generateLimitStringForReportInnerQuery,
  generateSortStringForReportInnerQuery, adjustStartDatesOfEntityStatusByCategoryAndEntities,
  reportQueryClassificationJoinConfiguration, generateSelectJoinClauseForClassification, identifyMandatoryColumnsForReportQuery,
  extractFieldsNeedingFilterData, extractFieldsNeedingPageFilterData
}