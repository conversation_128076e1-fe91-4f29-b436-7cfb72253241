const { configurationJSO<PERSON> } = require("@krushal-it/common-core");
const axios = require("axios");
const ZOHO_CLIENT_ID = configurationJSON().ZOHO.client_id
const ZOHO_CLIENT_SECRET = configurationJSON().ZOHO.client_secret
const ZOHO_REFRESH_TOKEN = configurationJSON().ZOHO.refresh_token
const ZOHO_REDIRECT_URI = configurationJSON().ZOHO.redirect_uri
const ZOHO_AUTH_URL = configurationJSON().ZOHO.authurl
const ZOHO_API_URL = configurationJSON().ZOHO.apiurl
const ZOHO_ORG_ID = configurationJSON().ZOHO.organization_id

class ZohoUtils {

    async getAccessTokenFromRefreshToken() {
        try{
            let url=`${ZOHO_AUTH_URL}/token?client_id=${ZOHO_CLIENT_ID}&client_secret=${ZOHO_CLIENT_SECRET}&redirect_uri=${encodeURIComponent(ZOHO_REDIRECT_URI)}&refresh_token=${ZOHO_REFRESH_TOKEN}&grant_type=refresh_token`
            const tokenresponse = await axios.post(url, null, {});
            return tokenresponse.data.access_token;
        }
        catch(e){
            console.log('error in getting access token from zoho', e)
            throw e
        }
    }

    async CreateBill(billdata){
        try{
            let token = await this.getAccessTokenFromRefreshToken()
            const billcreateresponse = await axios.post(`${ZOHO_API_URL}/bills?organization_id=${ZOHO_ORG_ID}`, billdata, {
                headers: {
                    "Authorization": `Zoho-oauthtoken ${token}`,
                    "content-type":`application/json`
                },
            });
            return billcreateresponse.data
        }
        catch(e){
            console.log('error in creating bill in zoho', e)
            throw e
        }
    }

    async UpdateBill(billdata){
        try{
            let token = await this.getAccessTokenFromRefreshToken()
            const billupdateresponse = await axios.put(`${ZOHO_API_URL}/bills/${billdata.bill_id}?organization_id=${ZOHO_ORG_ID}`, billdata, {
                headers: {
                    "Authorization": `Zoho-oauthtoken ${token}`,
                    "content-type":`application/json`
                },
            });
            return billupdateresponse.data
        }
        catch(e){
            console.log('error in updating bill in zoho', e)
            throw e
        }
    }

    async getBillById(bill_id){
        try{
            let token = await this.getAccessTokenFromRefreshToken()
            const billresponse = await axios.get(`${ZOHO_API_URL}/bills/${bill_id}?organization_id=${ZOHO_ORG_ID}`, {
                headers: {
                    "Authorization": `Zoho-oauthtoken ${token}`,
                },
            });
            return billresponse.data
        }
        catch(e){
            console.log('error in getting bill by bill id in zoho', e)
            throw e
        }
    }

    async getAllBillsByOrgId(){
        try{
            let token = await this.getAccessTokenFromRefreshToken()
            const allbillresponse = await axios.get(`${ZOHO_API_URL}/bills?organization_id=${ZOHO_ORG_ID}`, {
                headers: {
                    "Authorization": `Zoho-oauthtoken ${token}`,
                },
            });
            return allbillresponse.data
        }
        catch(e){
            console.log('error in getting bill by org id in zoho', e)
            throw e
        }
    }

    async CreateAttachmentForBill(formData,params,billid){
        try{
            let token = await this.getAccessTokenFromRefreshToken()
            const billattachresponse = await axios.post(`${ZOHO_API_URL}/bills/${billid}/attachment?organization_id=${ZOHO_ORG_ID}`, formData, {
                headers: {
                    "Authorization": `Zoho-oauthtoken ${token}`,
                },
                maxBodyLength: Infinity,
                maxContentLength: Infinity,
                responseType: 'json'
            });
            return billattachresponse.data
        }
        catch(e){
            console.log('error in creating attachment from bill in zoho', e)
            throw e
        }
    }

    async getAttachmentForBill(billid){
        try{
            let token = await this.getAccessTokenFromRefreshToken()
            const billgetattachmentresponse = await axios.get(`${ZOHO_API_URL}/bills/${billid}/attachment?organization_id=${ZOHO_ORG_ID}`, {
                headers: {
                    "Authorization": `Zoho-oauthtoken ${token}`
                },
            });
            return billgetattachmentresponse.data
        }
        catch(e){
            console.log('error in getting attachment from bill in zoho', e)
            throw e
        }
    }
}

module.exports = ZohoUtils
