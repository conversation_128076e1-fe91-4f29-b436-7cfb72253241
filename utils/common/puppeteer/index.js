const puppeteer = require("puppeteer")

async function initiatePuppeter() {
  try {
    global.browser = await puppeteer.launch({
      headless: true,
      args: ["--disable-gpu", "--disable-setuid-sandbox", "--no-sandbox", "--no-zygote"]
    })
  } catch (error) {
    console.log("puppeteer Error", error.message)
  }
}

function initiateCleanUpActionOnExit() {
  process.on("exit", () => {
    if (global.browser) global.browser.close()
    else console.log("no browser instance found")
  })
}

module.exports = { initiatePuppeter, initiateCleanUpActionOnExit }
