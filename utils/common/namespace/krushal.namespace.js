exports.SCHEMA_MAIN = "main";
exports.RELATION_ACTIVITY = "ref_activity";
exports.RELATION_ANIMAL = "animal";
exports.RELATION_ANIMAL_CLASSIFICATION = "animal_classification";
exports.RELATION_ANIMAL_DISEASE = "animal_disease";
exports.RELATION_CALENDAR = "care_calendar";
exports.RELATION_CUSTOMER = "customer";
exports.RELATION_CUSTOMER_CLASSIFICATION = "customer_classification";
exports.RELATION_DOC = "document";
exports.RELATION_DISEASE_CLASSIFICATION = "animal_disease_classification";
exports.RELATION_DUMP_DOC = "dump_document";
exports.RELATION_JOBTRACKER = "job_tracker";
exports.RELATION_NOTE = "note";
exports.RELATION_STAFF = "staff";
exports.RELATION_STAFF_ACTIVITY = "staff_activity";
exports.RELATION_STATE = "ref_state";
exports.RELATION_TALUKA = "ref_taluk";
exports.RELATION_VILLAGE = "ref_village";
exports.RELATION_DISTRICT = "ref_district";
exports.RELATION_REFERENCE = "ref_reference";
exports.RELATION_RELATIONSHIP = "entity_relationship";
exports.REALATION_ENTITY_GEO = "entity_geography";
exports.RELATION_CALENDAR_CLASSIFICATION = "care_calendar_classification";

exports.ENTITY_TYPE_ANIMAL = "animal";
exports.ENTITY_TYPE_CARE_CALENDAR = "care-calendar";
exports.ENTITY_TYPE_CUSTOMER = "customer";
exports.ENTITY_TYPE_DISEASE = "disease";
exports.ENTITY_TYPE_FARMER = "farmer";
exports.ENTITY_TYPE_PPU_FARMER = "ppu_farmer";

exports.ENTITY_TYPE_TASK = "task";
exports.ENTITY_TYPE_STAFF_BACK_OFFICE_PERSON = "back_office_person";
exports.ENTITY_TYPE_TASK = "task";
exports.ENTITY_TYPE_STAFF_PARAVET = "paravet";
exports.ENTITY_TYPE_STAFF_ROUTE_OFFICER = "route_officer";
exports.ENTITY_TYPE_STAFF_ZONAL_OFFICER = "zonal_officer";
exports.ENTITY_TYPE_STAFF_VET = "vet";
exports.ENTITY_TYPE_STAFF_PPU_PARAVET = "pay_per_use_paravet"
exports.ENTITY_TYPE_STAFF_CENTRALIZED_VET = "centralized_paravet"
exports.ENTITY_TYPE_STAFF_BCO_ADMIN = "bco_admin"
exports.ENTITY_TYPE_REQUISITION = "requisition"
exports.JOB_TYPE_CALENDAR = "care-calendar";
exports.JOB_TYPE_TRANSLATE = "translate";

exports.JOB_ACTION_START = "start";
exports.JOB_ACTION_STATUS = "status";

exports.STATUS_COMPLETE = "COMPLETED";
exports.STATUS_FAILED = "FAILED";
exports.STATUS_PARTIAL = "PARTIAL";
exports.STATUS_PENDING = "PENDING";

exports.LOCATION_TYPE_DISTRICT = "district";
exports.LOCATION_TYPE_STATE = "state";
exports.LOCATION_TYPE_TALUKA = "taluk";
exports.LOCATION_TYPE_VILLAGE = "village";

exports.SEQ_CUSTOMER_VISUAL_ID = "customer_visual_id";

exports.CONST_ID_ACTIVE = 1000100001;
exports.APP_ID = 123456789;
exports.CONST_ID_TYPE_STAFF_PARAVET = 1000230004;
exports.CONST_ID_TYPE_STAFF_VET = 1000230003;
exports.CENTRALIZED_VET = 1000230007
exports.PPU_PARAVET = 1000230006;
exports.VILLAGE_ClASSIFIER = 2000000055;
exports.ENTITY_TYPE_STAFF_BACK_OFFICE = 1000230002;
exports.CONST_ID_TYPE_STAFF_RO = 1000230001;
exports.CONST_ID_TYPE_STAFF_ZO = 1000230005;
exports.CONST_ID_CATEGORY_STAFF = 10002300;
exports.CONST_ID_CATEGORY_MED = 10005000;
exports.CONST_ID_CATEGORY_ACTIVITY_STATUS = 10003000;
exports.CONST_ENTITY_TYPE_ID_PARAVET = 1000230004;
exports.CONST_ID_CLASSIFIER_VILLAGE = 2000000055;
exports.CONST_ID_CLASSIFIER_TALUKA = 2000000054;
exports.CONST_PADDING_VISUAL_ID_CUSTOMER = 6;
exports.CONST_PADDING_VISUAL_ID_ANIMAL = 3;
exports.CONST_ID_TYPE_VILLAGE = 1000320004;
exports.PPU_FARMER = 1000220007;
exports.FARMER = 1000220001;

exports.CONST_STATUS_ACTIVITY_COMPLETED = 1000300050;
exports.CONST_STATUS_ACTIVITY_PENDING = 1000300001;
exports.CONST_ARRAY_REFERENCE_COLS = [
  "reference_id",
  "reference_name",
  "field_name",
  "type_of_data",
  "reference_name_l10n",
];

exports.MIN_BALANCE = 250;