const { getDBConnections } = require('@krushal-it/ah-orm')

const getUserInfoBasedOnHeaderInformation = async (headers) => {
  const dbConnections = getDBConnections()
  const userDeviceIdAsString = headers.userdeviceid || headers.userDeviceId
  if (userDeviceIdAsString) {
    const userDeviceId = parseInt(userDeviceIdAsString)
    const result = await dbConnections.main.repos['user_device']
      .createQueryBuilder('user_device')
      .select(['user_device_id', 'entity_type_id', 'entity_uuid'])
      .where({ user_device_id: userDeviceId, active: 1000100001 })
      .getRawMany()
    if (result.length > 0) {
      return result[0]
    } else {
      return { user_device_id: -1 }
    }
  } else {
    return { user_device_id: -1 }
  }
}
module.exports = { getUserInfoBasedOnHeaderInformation }
