require("dotenv").config();
const {admin} = require("./firebase_admin");
const staffqueries = require("../../../database_utils/queries/staff/staff.query");
const CustomerQueries = require("../../../database_utils/queries/customer/customer.query");
const axios = require("axios");
const { configurationJSON } = require("@krushal-it/common-core");
class authentication {
  constructor(token, refreshtoken, options = {}) {
    this.token = token;
    this.refreshtoken = refreshtoken
    // this.table = options.table_name;
  }
  async authenticate_user() {
    if (!this.token) {
      let e = new Error("token not defined");
      e.code = "token_not_found";
      throw e;
    }
    let data;
    let newAuthToken;
    let tokenPayload = {};
    try {
      data = await admin.auth().verifyIdToken(this.token);
    }
    catch (err) {
      if (err.errorInfo.code === "auth/id-token-expired" || err.errorInfo.code==="auth/argument-error") {
        const object = {
          grant_type: "refresh_token",
          refresh_token: this.refreshtoken,
        };
        const h = await axios.post(
          `https://securetoken.googleapis.com/v1/token?key=${configurationJSON().GOOGLE.API_KEY}`,
          object
        );
        this.token = h.data.access_token;
        tokenPayload.new_token = h.data.access_token; 
        data = await admin.auth().verifyIdToken(this.token);
      }
      else {
        return {
          status:401,
          result:false
        }
      }
    }
    const user = new staffqueries();
    let filter = data.phone_number?{phone_number:data.phone_number}:{email:data.email};
    let user_data = await user.get_staff({filter});
    if (user_data) {
      let resolved = {
        staff_id: user_data.staff_id,
        user_id: user_data.staff_id,
        name: user_data.staff_name_l10n,
        mobile: user_data.mobile_number,
        email: user_data.email_address,
        staff_type: user_data.staff_type_id,
        user_type: user_data.staff_type_id,
      };
     
      return {...tokenPayload,...resolved};
    }
    else {
      user_data = await CustomerQueries.getCustomerByPhoneNumber(data.phone_number);
      if (user_data) {
        let resolved =  {
          customer_id: user_data.customer_id,
          user_id: user_data.customer_id,
          name: user_data.customer_name_l10n,          
          mobile: user_data.mobile_number,
          email: user_data.email_address,
          user_type: user_data.customer_type_id,
        };
        return {...tokenPayload,...resolved};
      }
    }
    return false;
  }
}
module.exports = authentication;
