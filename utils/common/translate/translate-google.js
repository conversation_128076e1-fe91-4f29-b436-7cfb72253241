//const { Translate } = require("@google-cloud/translate").v2;
const translate = require("translate-google"); // here is a change too
class ManageTranslation {
  /**
   *
   * @param {String} key Google API key
   * @param {String} projectId Google project ID
   */
  constructor(key, projectId) {
    this.projectId = projectId;
    this.key = key;
    //this.translator = new Translate({ projectId, key });
  }

  /**
   * Translate the given text to the languages provided in param lang
   * @param {String} text
   * @param {Array} lang
   * @returns {Object} key value pairs of language and translated text
   */
  async mtranslate(text, lang, fieldName) {
    try {
      let result = {};
      if (!Array.isArray(lang)) throw new Error("Param 'lang' must be an Array");

      for (let i = 0; i < lang.length; i++) {
        //let translationResponse = await this.translator.translate(text, {to: lang[i], format: "text"});
        let translationResponse = await translate(text, { from: "en", to: lang[i] });
        result[lang[i]] = translationResponse;
      }
      if (fieldName) {
        let res = {};
        res[fieldName] = result;
        return res;
      }
      return result;
    } catch (error) {
      console.error("Cannot translate", error);
      return result;
    }
  }
  async detectLanguage(text) {
    try {
      let detectedLang = await this.translator.detect(text);
      return detectedLang;
    } catch (error) {
      console.error(error);
      throw error;
    }
  }
}

// Testing utils
// let text = "Rajeshwar Sharma";
// let t = new ManageTranslation(
//   "AIzaSyCWlutdmROIHsPpPd2fOWICzkNxGD9jADo",
//   "black-function-303714"
// );

// t.translate(text, ["hi", "en", "mr","mai","ta"]);
// t.detectLanguage(text);
//पियुष पीयूष
module.exports = ManageTranslation;