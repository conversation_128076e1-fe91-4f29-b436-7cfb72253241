const s3 = require("aws-sdk/clients/s3")
const fs = require("fs")
const crypto = require("crypto")
const { configurationJSON } = require("@krushal-it/common-core")

class S3Utils {
  #s3Client
  constructor() {
    this.#s3Client = new s3({
      region: configurationJSON().AWS.AWS_REGION,
      accessKeyId: configurationJSON().AWS.AWS_ACCESS_KEY,
      secretAccessKey: configurationJSON().AWS.AWS_SECRET_ACCESS_KEY
    })
    this.BUCKET_NAME = configurationJSON().AWS.AWS_BUCKET_NAME
  }

  /**
   * Uploads a single file to aws S3 bucket
   * @param {bufferstream} file
   * @param {string} root send school edu code here
   * @param {string} type image/file
   * @returns {string} file key
   */
  async s3Uploader(file, root = "ah", location, contentType) {
    try {
      const ext = file.originalname.split(".").slice(-1)[0]
      const key = Date.now() + crypto.randomBytes(8).toString("hex") + `.${ext}`
      const bucket = this.BUCKET_NAME + `/${root}/${location}`
      let params = {
        Bucket: bucket,
        Body: fs.createReadStream(file.path),
        Key: key,
        ContentType: contentType || null
      }
      let r = await this.#s3Client.upload(params).promise()
      return r.Key
    } catch (error) {
      console.log(error)
      throw new Error("Fail to upload files")
    }
  }

  /**
   * Creates a readable stream to retrive file from s3
   * @param {String} Key File identifier
   * @returns {ReadableStream}
   */
  async s3Downloader(Key) {
    const params = {
      Key,
      Bucket: this.BUCKET_NAME
    }
    return this.#s3Client.getObject(params).createReadStream()
  }

  async getS3ObjectAsBase64(Key) {
    try {
      const params = {
        Key,
        Bucket: this.BUCKET_NAME
      }
      const data = await this.#s3Client.getObject(params).promise()

      // Convert the Body buffer to Base64
      return data.Body.toString("base64")
    } catch (error) {
      console.error("Error fetching S3 object:", error.message)
      throw error
    }
  }

  /**
   * Deletes object from S3
   * @param {String} Key File identifier
   */
  async deleteOneFromS3(Key) {
    const params = {
      Bucket: BUCKET_NAME,
      Key
    }
    await this.#s3Client.deleteObject(params).promise()
  }

  async upload(files, entity = "uncategotized") {
    let result = {}
    for (const [key, value] of Object.entries(files)) {
      let promiseArray = []
      for (let fileindex = 0; fileindex < value.length; fileindex++) {
        const contentType = value[fileindex].mimetype.split("/")[1]
        promiseArray.push(this.s3Uploader(value[fileindex], configurationJSON().AWS.AWS_BUCKET_ROOT_LOCATION, entity, contentType))
      }
      try {
        result[key] = await Promise.all(promiseArray)
      } catch (error) {
        console.error(error)
        continue
      }
    }
    return result
  }

  async getFileMeta(filekey) {
    try {
      let params = { Bucket: this.BUCKET_NAME, Key: filekey }
      let meta = await this.#s3Client.headObject(params).promise()
      return meta
    } catch (error) {
      console.error(error)
      return null
    }
  }

  /**
   * Uploads a single file to aws S3 bucket
   * @param {bufferstream} file
   * @param {string} root send school edu code here
   * @param {string} type image/file
   * @returns {string} file key
   */
  async s3UploaderV2(fileStream, fullKey, contentType) {
    try {
      let params = {
        Bucket: this.BUCKET_NAME,
        Body: fileStream,
        Key: fullKey,
        ContentType: contentType || null
      }
      let r = await this.#s3Client.upload(params).promise()
      return r.Key
    } catch (error) {
      console.log(error)
      throw new Error("Fail to upload files")
    }
  }
}

module.exports = S3Utils
