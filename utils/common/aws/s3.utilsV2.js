const s3 = require("aws-sdk/clients/s3")
const crypto = require("crypto")
const { configurationJSO<PERSON> } = require("@krushal-it/common-core")
const { createWriteStream } = require("fs")

const _s3Client = new s3({
  region: configurationJSON().AWS.AWS_REGION,
  accessKeyId: configurationJSON().AWS.AWS_ACCESS_KEY,
  secretAccessKey: configurationJSON().AWS.AWS_SECRET_ACCESS_KEY
})

async function uploadFile({ buffer, bucket, fileName, contentType }) {
  try {
    const s3Client = _s3Client

    let params = {
      Bucket: bucket,
      Body: buffer,
      Key: fileName,
      ContentType: contentType || null
    }
    let r = await s3Client.upload(params).promise()
    return r.Key
  } catch (error) {
    console.log(error)
    throw new Error("Fail to upload files")
  }
}

async function downloadFile({ key, bucketName }) {
  const s3Client = _s3Client
  const params = {
    Key: key,
    Bucket: bucketName
  }
  try {
    let meta = await s3Client.headObject(params).promise()
    const data = s3Client.getObject(params)
    return data.createReadStream()
  } catch (error) {
    throw new Error("Invalid key given!")
  }
}

function streamToBase64(stream) {
  const chunks = []
  return new Promise((resolve, reject) => {
    stream.on("data", chunk => chunks.push(Buffer.from(chunk)))
    stream.on("error", err => reject(err))
    stream.on("end", () => resolve(Buffer.concat(chunks).toString("base64")))
  })
}

module.exports = {
  uploadFile,
  downloadFile,
  _s3Client,
  streamToBase64
}
