const { RELATION_CUSTOMER, RELATION_STAFF, RELATION_NOTE } = require("../common/namespace/krushal.namespace")

exports.getTranslateSupportedRelations = () => {
  const supported = {}
  supported[RELATION_CUSTOMER] = {}
  supported[RELATION_CUSTOMER]["cols"] = ["customer_name_l10n"]
  supported[RELATION_CUSTOMER]["idName"] = "customer_id"
  supported[RELATION_STAFF] = {}
  supported[RELATION_STAFF]["cols"] = ["staff_name_l10n"]
  supported[RELATION_STAFF]["idName"] = ["staff_id"]
  supported[RELATION_NOTE] = {}
  supported[RELATION_NOTE]["cols"] = ["note"]
  supported[RELATION_NOTE]["idName"] = ["note_id"]
  return supported
}

exports.configuration = {
  batchSize: 150
}
