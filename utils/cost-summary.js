const { extractBasedOnLanguage } = require("@krushal-it/common-core")
const Pdf = require("@krushal-it/krushal-pdf-generator/index.cjs")
const path = require("node:path")
const kurshal_image = require("../services/createPDF/image.js")
const { dbConnections } = require("@krushal-it/ah-orm")
const get_farmer_summary_query = require("../database_utils/queries/farmer_summary.js")
const constants = require("../utils/constants/constants.js")
module.exports = async function get_farmer_cost_summary_pdf(farmer_id, params) {
  try {
    const { language } = params.query

    const query = get_farmer_summary_query(farmer_id)
    let cost_per_cc = await dbConnections().main.manager.query(query)
    if (cost_per_cc.length === 0) {
      throw new Error("No Summary available for the farmer.")
    }
    /* 
    parsing stringified numbers to integer 
    */
    cost_per_cc = cost_per_cc.map(ele => {
      const total_cost = parseInt(ele.total_cost)
      const medicine_cost = parseInt(ele.medicine_cost)
      const service_cost = parseInt(ele.service_cost)

      ele.total_cost = total_cost ? total_cost : 0
      ele.medicine_cost = medicine_cost ? medicine_cost : 0
      ele.service_cost = service_cost ? service_cost : 0

      return ele
    })

    const customer = cost_per_cc[0]
    const animal_detailed_record_by_activity_category = getAnimalDetailedRecordByActivityCategory(cost_per_cc)
    const animal_summary = getSegregatedAnimalsByEarTag(cost_per_cc)

    const total_cost = animal_summary.reduce((acc, cur) => acc + cur.total_cost, 0)
    const summary_total_medicine_cost = animal_summary.reduce((acc, cur) => acc + cur.medicine_cost, 0)
    const summary_total_service_cost = animal_summary.reduce((acc, cur) => acc + cur.service_cost, 0)
    const summary_generated_on_date = new Date(new Date() - 1 * 24 * 1000 * 60 * 60).toLocaleDateString("en-IN", { year: "numeric", month: "2-digit", day: "2-digit" })
    const preventive_total_medicine_cost = animal_detailed_record_by_activity_category.Preventive.reduce((acc, cur) => acc + cur.medicine_cost, 0)
    const preventive_total_service_cost = animal_detailed_record_by_activity_category.Preventive.reduce((acc, cur) => acc + cur.service_cost, 0)
    const preventive_total_cost = preventive_total_medicine_cost + preventive_total_service_cost
    const curative_reproductive_total_medicine_cost = summary_total_medicine_cost - preventive_total_medicine_cost
    const curative_reproductive_total_service_cost = summary_total_service_cost - preventive_total_service_cost
    const curative_reproductive_total_cost = total_cost - preventive_total_cost
    const hasCurativeAndReproductive = animal_detailed_record_by_activity_category.Curative || animal_detailed_record_by_activity_category.Reproductive ? true : false

    const data = {
      constant: constants.language_constants.farmer_summary_pdf,
      animal_summary: animal_summary,
      animal_detailed_record_by_activity_category: animal_detailed_record_by_activity_category,
      customer: customer,
      image: kurshal_image,
      total_cost: total_cost,
      summary_generated_on_date: summary_generated_on_date,
      summary_total_service_cost,
      summary_total_medicine_cost,
      preventive_total_medicine_cost,
      preventive_total_service_cost,
      preventive_total_cost,
      curative_reproductive_total_medicine_cost,
      curative_reproductive_total_service_cost,
      curative_reproductive_total_cost,
      hasCurativeAndReproductive
    }
    const animal_summary_template = global.pdfTemplates["animal-summary.hbs"]

    const pdf = new Pdf(animal_summary_template, {
      language,
      _extractBasedOnLanguage: extractBasedOnLanguage
    })
    const pdfBuffer = await pdf.setData(data).convert()

    return pdfBuffer
  } catch (error) {
    throw new Error("get_farmer_cost_summary_pdf: " + error)
  }
}

function getAnimalDetailedRecordByActivityCategory(data) {
  const activity_category_map = {}
  data.forEach(ele => {
    const category_name = ele.activity_category_name_l10n.ul || ele.activity_category_name_l10n.en
    if (activity_category_map[category_name]) {
      activity_category_map[category_name].push(ele)
    } else {
      activity_category_map[category_name] = [ele]
    }
  })
  return activity_category_map
}

function getSegregatedAnimalsByEarTag(data) {
  const animal_ear_tag_map = {}
  data.forEach(animal => {
    animal_ear_tag_map[animal.animal_eartag] = {
      animal_type: animal.animal_type,
      animal_eartag: animal.animal_eartag,
      animal_subscription_plan_l10n: animal.subscription_plan_l10n,
      animal_subscription_plan_start_date: animal.subscription_plan_start_date,
      total_cost: animal_ear_tag_map[animal.animal_eartag] ? animal_ear_tag_map[animal.animal_eartag].total_cost + animal.total_cost : animal.total_cost,
      medicine_cost: animal_ear_tag_map[animal.animal_eartag] ? animal_ear_tag_map[animal.animal_eartag].medicine_cost + animal.medicine_cost : animal.medicine_cost,
      service_cost: animal_ear_tag_map[animal.animal_eartag] ? animal_ear_tag_map[animal.animal_eartag].service_cost + animal.service_cost : animal.service_cost
    }
  })

  return Object.values(animal_ear_tag_map)
}
