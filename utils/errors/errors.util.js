const {NotFound,<PERSON><PERSON>rror,BadRequest,NotAuthenticated,} = require("@feathersjs/errors");
const ERROR_CONFIG = {
    "BADREQ":{
        eClass:BadRequest
    },
    "NOTFOUND":{
        eClass:NotFound
    },
    "NOAUTH":{
        eClass:NotAuthenticated
    },
}

const handleErrors=(options)=>{
    let message= options.message?options.message:"";
    let params = options.eparams?options.eparams:{};
    if(typeof options.errorType ==="string" && ERROR_CONFIG[options.errorType]){
        return new ERROR_CONFIG[options.errorType].eClass(message,params)
    }
    return new GeneralError(message,500,"unhandled",params)
}
module.exports ={handleErrors};