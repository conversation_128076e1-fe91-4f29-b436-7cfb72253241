const JobTrackerQueries = require("../../database_utils/queries/job-tracker/jobtracker.query");
const {
  JOB_TYPE_CALENDAR,
  STATUS_FAILED,
  STATUS_COMPLETE,
  STATUS_PARTIAL,
  RELATION_CALENDAR,
} = require("../common/namespace/krushal.namespace");

const CareCalendarJob = require("./scripts/care-calender-job");

class Scheduler {
  
  constructor(job_type, job_id, lastJobTime, options = {}) {
    this.job_type = job_type
    this.job_id = job_id
    this.lastJobTime = lastJobTime
  }

  async start(options = {}) {
    
    let errorArray = []

    switch (this.job_type) {

      case JOB_TYPE_CALENDAR:
        errorArray = []
        let careCalendarJob = new CareCalendarJob({
          scheduledTime: this.lastJobTime,
        })
        try {
          await careCalendarJob.start()
        } catch (error) {
          errorArray.push({ table: RELATION_CALENDAR, message: error.message })
        }
        await this.#writeFinishToDB(errorArray, 1)
        return
      default:
        return
    }
  }

  async #writeFinishToDB(errorArray, total_relations) {
    
    let job_status = ""
    if (errorArray.length === 0) job_status = STATUS_COMPLETE
    else if (errorArray.length === total_relations) job_status = STATUS_FAILED
    else job_status = STATUS_PARTIAL
    
    await JobTrackerQueries.updateJobStatus(this.job_id, job_status, errorArray)

    return
  }
}

module.exports = Scheduler;