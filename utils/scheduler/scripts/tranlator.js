require("dotenv").config({ path: "../../../.env" });
const { configurationJSON } = require("@krushal-it/common-core");
const CommonQueries = require("../../../database_utils/queries/common/common.queries");
const ManageTranslation = require("../../common/translate/translate-google");
const { dbConnections } = require("@krushal-it/ah-orm");
//const BATCHSIZE = 20;
const BATCHSIZE_PROMISE = 10;
const BATCHSIZE_UPDATE = 5;
const langs = ["en", "hi", "mr"];
class TraslateDatabase {

  constructor(options) {

    if (!options.model) throw new Error("Require a model name");
    if (!Array.isArray(options.columns) || options.columns.length <= 0) throw new Error("Require array of columns");
    // TODO: Put check for valid scheduled time
    this.scheduledTime = options.scheduledTime;
    this.model = options.model;
    this.columns = options.columns;
    this.idName = options.idName;
    this.translator = new ManageTranslation(configurationJSON().GOOGLE.GOOGLE_API_KEY, configurationJSON().GOOGLE.GOOGLE_PROJECT_ID);
  }
  async #getDataFromDB(column) {

    let dataSource = dbConnections().main.manager;
    // Meant to be used for independent invoks
    // try {
    //     await this.DataConnection.initializeDatabase();    
    //     dataSource = this.DataConnection.getDataSource();
    // } catch (error) {
    //     dataSource = global.dataSource;
    // }

    //this.data = await CommonQueries.getCreatedAfter(dataSource,this.scheduledTime,{model:this.model,columns:[...this.columns,this.idName]});
    this.data = await CommonQueries.getTranslatorData(dataSource, { model: this.model, column, idCol: this.idName })
    this.idArray = [];
    for (let i = 0; i < this.data.length; i++) {
      this.idArray.push(this.data[i][this.idName]);
      delete this.data[i][this.idName];
    }
  }
  async #batchUpdate(model, translatedData, idArray, idName, colName) {
    try {
      await this.DataConnection.getDataSource().manager.transaction(async (transactionalEntityManager) => {
        let promises = [];
        for (let i = 0; i < translatedData.length; i++) {
          // setting default as en
          // translatedData[i][colName]["ul"] = translatedData[i][colName]["en"];
          promises.push(CommonQueries.updateEntity(transactionalEntityManager, {
            model,
            value: translatedData[i],
            id: idArray[i],
            idName
          }));
          if (promises.length === BATCHSIZE_UPDATE || (i + 1) == translatedData.length) {
            await Promise.all(promises);
            promises = [];

          }
        }
      });
    } catch (error) {
      console.error(error);
    }
  }
  async #batchUpdate_v2(model, translatedData, idArray, idName) {
    try {
      console.log(`UPDATING ${model}...`);
      const datasource = global.datasource;
      for (let dataIndex = 0; dataIndex < translatedData.length; dataIndex++) {
        console.log(model, idName, idArray[dataIndex], "translated")
        await CommonQueries.updateEntity(datasource, {
          model,
          value: translatedData[dataIndex],
          id: idArray[dataIndex],
          idName
        });
      }
      console.log(`UPDATED ${model}...`);
    } catch (error) {
      console.error(error);
    }
  }
  async #updateSingleRow(model, translatedData, id, idName) {
    const datasource = global.datasource;
    await CommonQueries.updateEntity(datasource, {
      model,
      value: translatedData,
      id,
      idName
    })
  }
  // Optimized, issue: translator removed delimiter
  // async translateAndProcess(){
  //     const PRIMARY_DELIMITER = "/";   // Deliminate Rows
  //     const SECONDARY_DELIMITER = "#"; // Deliminate Columns
  //     await this.#getDataFromDB();
  //     let translatePromise = [];
  //     let translated = null;
  //     let text = "";
  //     for(let i = 0;i<this.data.length;i++){
  //         for(let columnindex = 0;columnindex<this.columns.length;columnindex++){

  //             if(columnindex == 0 && i != 0) text+=PRIMARY_DELIMITER;

  //             else if(columnindex!=0) text+=SECONDARY_DELIMITER;

  //             if (!this.data[i][this.columns[columnindex]].ul)
  //                 throw new Error(`${this.model}.${this.columns[columnindex]} is not compatible for translation`);
  //                 text += this.data[i][this.columns[columnindex]].ul;
  //         }

  //         // translation batch applied
  //         if((i+1)%BATCHSIZE===0 || (i+1) === this.data.length){
  //             translatePromise.push(this.translator.mtranslate(text,langs));
  //             text = "";
  //         }
  //     }

  //     let translationResult = await Promise.all(translatePromise);
  //     for(let batchIndex = 0;batchIndex<translationResult.length;batchIndex++){
  //         let temp_translated = translationResult[batchIndex];
  //         if(translated === null){
  //             translated = {};
  //             for(let langIndex =0;langIndex<langs.length;langIndex++){
  //                 translated[langs[langIndex]] = temp_translated[langs[langIndex]];
  //             }
  //         }else{
  //             for(let langIndex =0;langIndex<langs.length;langIndex++){
  //                 translated[langs[langIndex]] += temp_translated[langs[langIndex]];
  //             }
  //         }
  //     }
  //     //let translated = await this.translator.mtranslate(text,langs)
  //     let{hi,en,mr} = translated;    
  //     hi = hi.split(PRIMARY_DELIMITER);
  //     en = en.split(PRIMARY_DELIMITER);
  //     mr = mr.split(PRIMARY_DELIMITER);
  //     console.log(this.data.length,en.length,hi.length,mr.length);
  //     let updateBatch = [];
  //     if(hi.length !==this.data.length || en.length !== this.data.length || mr.length !== this.data.length)
  //         throw new Error(`PRIMARY DELIMITER: "${PRIMARY_DELIMITER}" Failed`);
  //     for(let i = 0;i<this.data.length;i++){
  //         let hi_cl = hi[i].split(SECONDARY_DELIMITER);
  //         let en_cl = en[i].split(SECONDARY_DELIMITER);
  //         let mr_cl = mr[i].split(SECONDARY_DELIMITER);
  //         if(mr_cl.length!==this.columns.length || hi_cl.length !== this.columns.length || en_cl.length !== this.columns.length)
  //             throw new Error(`SECONDARY DELIMITER: "${SECONDARY_DELIMITER}" Failed`);

  //         let temp = {};

  //         for(let columnindex = 0;columnindex<this.columns.length;columnindex++){
  //             temp[this.columns[columnindex]] = {
  //                 ul:en_cl[columnindex],
  //                 en:en_cl[columnindex],
  //                 hi:hi_cl[columnindex],
  //                 mr:mr_cl[columnindex]
  //             };    
  //         }
  //         updateBatch.push(temp);
  //     }
  //     // await this.#batchUpdate(this.model,updateBatch,this.idArray,this.idName);
  // }

  /**
   * Naive approach [ data_length*column_length*language_length (n^3) ]
   **/
  async translateAndProcess() {
    await this.#getDataFromDB();
    if (this.data.length <= 0) return;
    for (let colIndex = 0; colIndex < this.columns.length; colIndex++) {
      if (!this.data[0][this.columns[colIndex]].hasOwnProperty("ul")) continue;

      let promiseArray = [];
      let resolvedArray = [];
      for (let dataIndex = 0; dataIndex < this.data.length; dataIndex++) {
        let text = this.data[dataIndex][this.columns[colIndex]].ul;
        promiseArray.push(this.translator.mtranslate(text, langs, this.columns[colIndex]));
        if (promiseArray.length === BATCHSIZE_PROMISE || (dataIndex + 1) === this.data.length) {
          resolvedArray = [...resolvedArray, ...await Promise.all(promiseArray)];
          promiseArray = [];
        }
      }
      await this.#batchUpdate(this.model, resolvedArray, this.idArray, this.idName, this.columns[colIndex]);
    }
    // await this.DataConnection.closeConnection();
  }
  async translateAndProcess_v2() {
    for (let colIndex = 0; colIndex < this.columns.length; colIndex++) {
      let column = this.columns[colIndex];
      console.log("TRANSLATING 🏃", this.model, "column:", column);
      await this.#getDataFromDB(column);
      if (this.data.length <= 0) return;
      if (!this.data[0][column].hasOwnProperty("ul")) continue;
      let translatedJsonArray = [];
      for (let dataIndex = 0; dataIndex < this.data.length; dataIndex++) {
        let text = this.data[dataIndex][column].ul;
        let translatedJson = await this.translator.mtranslate(text, langs, column);
        await this.#updateSingleRow(this.model, translatedJson, this.idArray[dataIndex], this.idName);
        console.log(text, "->", JSON.stringify(translatedJson), "UPDATED TO DB");
        translatedJsonArray.push(translatedJson);
      }
      // await this.#batchUpdate_v2(this.model, translatedJsonArray, this.idArray, this.idName);
      console.log("TRANSLATED ✅", this.model, "column:", column);
    }
  }
}
module.exports = TraslateDatabase;
// Tests
// 
// let S = new TraslateDatabase({model:"mst_customer",columns:["name"],scheduledTime:moment().subtract(10,'days').startOf('day'),idName:"customer_id"});
// S.translateAndProcess();