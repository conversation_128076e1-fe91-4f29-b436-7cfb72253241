const { infoLog } = require('@krushal-it/mobile-or-server-lib');
const { TranslationServiceClient } = require('@google-cloud/translate').v3;
const { ILike, Brackets } = require("typeorm");
const { dbConnections } = require("@krushal-it/ah-orm");

const { v4: uuidv4 } = require("uuid");
const { configurationJSON } = require("@krushal-it/common-core");

const LATIN_THRESHOLD = 60;

const DEST_LANGS = new Set(configurationJSON().GOOGLE.TRANSLATION.DEST_LANGS);
const BATCH_SIZE = configurationJSON().GOOGLE.TRANSLATION.BATCH_SIZE;
const projectId = configurationJSON().GOOGLE.TRANSLATION.SERVICE_ACCOUNT.project_id;
const parent_scope = configurationJSON().GOOGLE.TRANSLATION.SERVICE_ACCOUNT.parent_scope;
const translationConfig = configurationJSON().GOOGLE.TRANSLATION.TABLES;


const translationServiceClient = new TranslationServiceClient({ credentials: configurationJSON().GOOGLE.TRANSLATION.SERVICE_ACCOUNT });

const translateText = async (text, source, destination) => {
    const translateRequest = {
        parent: translationServiceClient.locationPath(projectId, parent_scope),
        transliteration_config: { enable_transliteration: true },
        sourceLanguageCode: source,
        targetLanguageCode: destination,
        mimeType: "text/html",
        contents: text
    };
    const [resTranslate] = await translationServiceClient.translateText(translateRequest, {
        otherArgs: {
            transliteration_config: { enable_transliteration: true }
        }
    });
    return resTranslate.translations;
};

const detectLanguage = async (textArray) => {
    if (!textArray) return null;
    if (!Array.isArray(textArray)) textArray = [textArray];
    const detectRequest = {
        parent: translationServiceClient.locationPath(projectId, parent_scope),
        mimeType: "text/plain",
        content: textArray
    };
    const [res] = await translationServiceClient.detectLanguage(detectRequest);
    if (!Array.isArray(res.languages) || res.languages.length === 0) return null;
    return res.languages[0].languageCode;
};
/**
 * 
 * @param {string} str 
 */
const isLatin = (str) => {
    if (!str) return false;
    if (str.length === 0) return false;
    const pattern = /^(?=.*[a-zA-Z]).*$/;
    str = str.replace(/\\"/g, '"');
    const words = str.split(" ");
    let latinCount = 0;
    for (word of words) {
        latinCount += pattern.test(word) ? 1 : 0;
    }
    const percentage = (latinCount / words.length) * 100;
    return percentage >= LATIN_THRESHOLD;
};


/**
 * 
 * @param {string} str 
 * @returns {Object|null}
 */
const getTranslationFromLocalDatabase = async (str) => {
    infoLog("Checking database for previous translations");
    const columns = { text: true, language: true, text_id: true };
    const rowFilter = { text: ILike(str) };
    const translateRepo = dbConnections().main.repos["translate"];
    let firstMatchlocalDict = await translateRepo.find({ select: columns, where: rowFilter });
    if (firstMatchlocalDict.length === 0) return null;
    firstMatchlocalDict = firstMatchlocalDict[0];
    const { text_id } = firstMatchlocalDict;
    rowFilter.text_id = text_id;
    delete rowFilter.text;
    let allMatchLocalDict = await translateRepo.find({ select: columns, where: rowFilter });

    if (!Array.isArray(allMatchLocalDict) || !allMatchLocalDict) allMatchLocalDict = [];

    allMatchLocalDict.push(firstMatchlocalDict);

    const result = { translation: allMatchLocalDict };
    result.text_id = firstMatchlocalDict.text_id;
    return result;
};

const insertTranslationTOLocalTranslationDB = async (values, transaction) => {
    infoLog(`Inserting new translation to DB`);
    infoLog(`Number of new translation: ${values.length}`);
    const manager = transaction ? transaction : dbConnections().main.manager;
    const queryBuilder = manager.createQueryBuilder().insert().into("translate");
    await queryBuilder.values(values).execute();
};

const getDataFromDB = async (table, select, column, batchIndex, transaction) => {
    infoLog("Pulling data from database");
    select = select.map((elem) => `${table}.${elem}`);
    const queryBuilder = transaction.getRepository(table).createQueryBuilder(table).select(select);
    queryBuilder.andWhere(new Brackets((qb) => {
        for (const lang of DEST_LANGS) {
            qb.orWhere(`${column}->>'${lang}' is null`);
            qb.orWhere(`${column}->>'${lang}' = ''`);
        }
    }));
    queryBuilder.andWhere(`${column} is not null`);
    const dataFromDB = await queryBuilder.limit(BATCH_SIZE).skip(batchIndex * BATCH_SIZE).getMany();
    infoLog(`Number of records found in DB:${dataFromDB.length}`);
    return dataFromDB;

};

const countBatches = async (db, table, column) => {
    const queryBuilder = dbConnections()[db].repos[table].createQueryBuilder();
    queryBuilder.andWhere(new Brackets((qb) => {
        for (const lang of DEST_LANGS) {
            qb.orWhere(`${column}->>'${lang}' is null`);
            qb.orWhere(`${column}->>'${lang}' = ''`);
        }
    }));
    queryBuilder.andWhere(`${column} is not null`);
    queryBuilder.andWhere(`${column} != '{}'`);
    const totalCount = await queryBuilder.getCount();
    const pages = parseInt(totalCount / BATCH_SIZE) + (totalCount % BATCH_SIZE !== 0 ? 1 : 0);
    return { totalCount, pages };
};

const translate = async () => {
    infoLog("starting translation job");
    const prevJob = await dbConnections().main.repos["job_tracker"]
        .find({
            select: { job_tracker_id: true, job_status: true },
            where: { job_type: "translate-v2" },
            order: { created_at: "DESC" },
            take: 1
        });
    if (prevJob.length > 0 && prevJob[0].job_status === "PENDING") {
        infoLog(`Job ID ${prevJob[0].job_tracker_id} is still in progress`);
        return;
    }
    const jobTrackerEntry = await dbConnections().main.repos["job_tracker"].save({ job_type: "translate-v2", job_status: "PENDING", created_at: new Date() });
    const jobInformation = {};
    try {
        // await initializeAllRepositories(dbConnections());
        for (const table of translationConfig) {
            
            const { table_name, db, primary_col, columns } = table;
            infoLog(`Translating table: \t${table_name}`);
            jobInformation[table_name] = {};
            for (const column of columns) {
                const { totalCount, pages } = await countBatches(db, table_name, column);

                jobInformation[table_name][column] = { batch_completed: 0, total_batch: pages, total_records: totalCount };

                // console.timeEnd("countBatches");
                for (let pageIndex = 0; pageIndex < pages; pageIndex++) {
                    infoLog(`Processing column: \t${column}\nBatch:\t${pageIndex + 1}`);
                    await dbConnections().main.manager.transaction(async (transaction) => {
                        const select = [column, primary_col];
                        // console.time("getDataFromDB");
                        let tobeTranslated = await getDataFromDB(table_name, select, column, pageIndex, transaction);
                        tobeTranslated = cleanData(tobeTranslated, column);
                        // console.timeEnd("getDataFromDB");
                        const translatedArray = [];
                        for (const row of tobeTranslated) {
                            translatedArray.push({
                                ...row, existingLangs: new Set(Object.keys(row[column])),
                                hasUL: row[column].ul ? true : false,
                                toLocalDict: new Set(),
                                missingLangs: new Set()
                            });
                            translatedArray[translatedArray.length - 1].missingLangs = new Set([...DEST_LANGS].filter(item => !translatedArray[translatedArray.length - 1].existingLangs.has(item)));
                        }
                        // console.time("translate Local 100 entries");
                        for (let translateObjectIndex = 0; translateObjectIndex < translatedArray.length; translateObjectIndex++) {
                            let sourceLang = null;
                            if (translatedArray[translateObjectIndex].hasUL == true) {
                                if (isLatin(translatedArray[translateObjectIndex][column]["ul"])) {
                                    sourceLang = "en";
                                }
                                else {
                                    sourceLang = await detectLanguage(translatedArray[translateObjectIndex][column]);
                                    sourceLang = sourceLang === "la" ? "en" : sourceLang;
                                }
                                if (translatedArray[translateObjectIndex].missingLangs.has(sourceLang)) {
                                    translatedArray[translateObjectIndex][column][sourceLang] = translatedArray[translateObjectIndex][column].ul;
                                    translatedArray[translateObjectIndex].toLocalDict.add(sourceLang);
                                    translatedArray[translateObjectIndex].insertMissinToLocalDB = true;
                                    translatedArray[translateObjectIndex].missingLangs.delete(sourceLang);
                                }
                                translatedArray[translateObjectIndex].existingLangs.delete("ul");
                                translatedArray[translateObjectIndex].hasUL = false;
                                delete translatedArray[translateObjectIndex][column].ul;
                                translatedArray[translateObjectIndex]["sourceLang"] = sourceLang;
                            }
                            else if (translatedArray[translateObjectIndex].existingLangs.size > 0) {
                                [sourceLang] = translatedArray[translateObjectIndex].existingLangs;
                                translatedArray[translateObjectIndex]["sourceLang"] = sourceLang;
                                translatedArray[translateObjectIndex].existingLangs.delete("ul");
                                translatedArray[translateObjectIndex].hasUL = false;
                                delete translatedArray[translateObjectIndex][column].ul;
                            }
                            else {
                                // console.log(200, translatedArray[translateObjectIndex]);
                                translatedArray.splice(translateObjectIndex, 1);
                                continue;
                            }
                            const formatedScrLang = translatedArray[translateObjectIndex]["sourceLang"];
                            if (!translatedArray[translateObjectIndex][column][formatedScrLang]) {
                                // console.log(205, translatedArray[translateObjectIndex]);
                                translatedArray.splice(translateObjectIndex, 1);
                            };
                            const translationFromDB = undefined;
                            await getTranslationFromLocalDatabase(translatedArray[translateObjectIndex][column][formatedScrLang]);
                            if (translationFromDB && translationFromDB.translation.length > 0) {
                                for (let value of translationFromDB.translation) {
                                    if (translatedArray[translateObjectIndex].missingLangs.has(value.language) || translatedArray[translateObjectIndex].toLocalDict.has(value.language)) {
                                        translatedArray[translateObjectIndex][column][value.language] = value.text;
                                        translatedArray[translateObjectIndex].missingLangs.delete(value.language);
                                        translatedArray[translateObjectIndex].toLocalDict.delete(value.language);
                                    }
                                }
                            }
                            if (translatedArray[translateObjectIndex].missingLangs.size > 0) {
                                translatedArray[translateObjectIndex].insertMissinToLocalDB = true;
                                translatedArray[translateObjectIndex].text_id = translationFromDB && translationFromDB.text_id ? translationFromDB.text_id : uuidv4();
                                translatedArray[translateObjectIndex].toLocalDict = new Set([...translatedArray[translateObjectIndex].toLocalDict, ...translatedArray[translateObjectIndex].missingLangs]);
                            }
                        }
                        // console.timeEnd("translate Local 100 entries");
                        const translationObjectArray = {};
                        for (const value of translatedArray) {
                            if (!value.sourceLang) continue;
                            for (const missingLang of value.missingLangs) {
                                let key = `${value.sourceLang}*${missingLang}`;
                                if (!translationObjectArray[key]) translationObjectArray[key] = { index: 0, srcText: [] };
                                translationObjectArray[key].srcText.push(value[column][value.sourceLang]);
                            }
                        }
                        // console.time("translate API");
                        for (const [key, value] of Object.entries(translationObjectArray)) {
                            const [src, dest] = key.split("*");
                            const { srcText } = value;
                            translationObjectArray[key].translatedText = await translateText(srcText, src, dest);
                        }
                        for (let translatedArrayIndex = 0; translatedArrayIndex < translatedArray.length; translatedArrayIndex++) {
                            const value = translatedArray[translatedArrayIndex];
                            if (!value.sourceLang) continue;
                            for (const missingLang of value.missingLangs) {
                                const key = `${value.sourceLang}*${missingLang}`;
                                const translatedResult = translationObjectArray[key].translatedText[translationObjectArray[key].index++];
                                translatedArray[translatedArrayIndex][column][missingLang] = translatedResult.translatedText;
                            }
                        }
                        // console.timeEnd("translate API");

                        const valuesToInsertInLocalDictDB = [];
                        const duplicateFilter = new Set();
                        for (const trasnlatedValue of translatedArray) {
                            if (trasnlatedValue.insertMissinToLocalDB === true && trasnlatedValue.toLocalDict.size > 0) {
                                for (toDictLang of trasnlatedValue.toLocalDict) {
                                    if (trasnlatedValue[column][toDictLang] && !duplicateFilter.has(trasnlatedValue[column][toDictLang])) {
                                        valuesToInsertInLocalDictDB.push({
                                            text_id: trasnlatedValue.text_id,
                                            text: trasnlatedValue[column][toDictLang],
                                            language: toDictLang
                                        });
                                        duplicateFilter.add(trasnlatedValue[column][toDictLang]);
                                    }
                                }
                            }
                        }
                        // console.time("tolocaldict");
                        await insertTranslationTOLocalTranslationDB(valuesToInsertInLocalDictDB, transaction);
                        // console.timeEnd("tolocaldict")

                        const translatedObjectPrimaryKeyMapping = {};
                        for (const value of translatedArray) {
                            translatedObjectPrimaryKeyMapping[value[primary_col]] = value;
                        }
                        for (let dbrowIndex = 0; dbrowIndex < tobeTranslated.length; dbrowIndex++) {
                            if (tobeTranslated[dbrowIndex][column] && Object.keys(tobeTranslated[dbrowIndex][column]).length > 0 && translatedObjectPrimaryKeyMapping[tobeTranslated[dbrowIndex][primary_col]]) {
                                tobeTranslated[dbrowIndex][column] = translatedObjectPrimaryKeyMapping[tobeTranslated[dbrowIndex][primary_col]][column];
                            }
                        }
                        // console.time("todesttable");
                        infoLog("Saving translated batch to destination table");
                        await transaction.save(table_name, tobeTranslated);
                        // console.timeEnd("todesttable")
                    });
                    jobInformation[table_name][column].batch_completed++;
                }
            }
        }
        jobTrackerEntry.job_information = { error: {}, translation_info: jobInformation };
        jobTrackerEntry.job_status = "COMPLETED";
        jobTrackerEntry.job_completed_at = new Date();
    } catch (error) {
        jobTrackerEntry.job_status = "FAILED";
        jobTrackerEntry.job_information = { error: { message: error.message, stack: error.stack }, translation_info: jobInformation };
        console.error(error);
    }
    await dbConnections().main.repos["job_tracker"].save(jobTrackerEntry);
};

const cleanData = (dataFromDB, column) => {
    const cleanData = [];
    for (const row of dataFromDB) {
        if (row[column] === null || row[column] === undefined) {
            continue;
        }
        for (const langKey of Object.keys(row[column])) {
            if (row[column][langKey] === undefined || row[column][langKey] === null || row[column][langKey] === "") {
                delete row[column][langKey];
            }
        }
        if (Object.keys(row[column]).length === 0) {
            continue;
        }
        cleanData.push(row);
    }
    return cleanData;
};
module.exports = { translate };
