const { dbConnections, postProcessRecords } = require('@krushal-it/ah-orm')
const { ReferencesLib, References2Lib ,References3Lib } = require('@krushal-it/back-end-lib')

class References {
  async create(data, params) {
    try {
      const referencesLib = new ReferencesLib()
      return referencesLib.create(data, params)
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class References2 {
  async create(data, params) {    
    try {
      const references2Lib = new References2Lib()
      return references2Lib.create(data, params)
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class EntityComments {
  async create /*or is it update*/(data, params) {
    try {
      const returnValue = {return_code: 0}
      if (data.query_params) {
        let noteQuery = `select note_id,
          coalesce(n.note->>'en', n.note->>'ul') as note,
          n.note_time,
          n.note_type_id,
          coalesce(n_rr.reference_name_l10n->>'en', n_rr.reference_name_l10n->>'ul') note_type
          from main.note n
          inner join main.ref_reference n_rr on n.note_type_id = n_rr.reference_id
          
        `
  
        let noteWhereClause = `
          where 1 = 1
          and n.active = 1000100001
        `

        const orderByClause = `
          order by n.note_time desc
        `

        const columns = Object.keys(data.query_params)
        
        for (let counter = 0 ; counter < columns.length ; counter++) {
          const noteColumnName = columns[counter]
          const noteColumnValue = data.query_params[noteColumnName]
          if (Array.isArray(noteColumnValue)) {
            noteWhereClause = noteWhereClause + '\nand ' + noteColumnName + ' in (' + noteColumnValue.join(', ') + ' ) '
          } else {
            const needQuotes = (typeof noteColumnValue === 'string' || noteColumnValue instanceof String)
            if (needQuotes) {
              noteWhereClause = noteWhereClause + '\nand ' + noteColumnName + " = '" + noteColumnValue + "' "
            } else {
              noteWhereClause = noteWhereClause + '\nand ' + noteColumnName + ' = ' + noteColumnValue + ' '
            }
          }
        }
        noteQuery = noteQuery + noteWhereClause + orderByClause

        const mainDBConnection = dbConnections().main
        const noteQueryResult = await mainDBConnection.manager.query(noteQuery)
        returnValue.report = noteQueryResult
  
      }
      return returnValue
    } catch (error) {
      console.log('oc UBFS c 10, error')
      console.log('oc UBFS c 10a, error = ', error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class ReferencesAndCategories {
  async create (data, params) {
    try {
      const returnValue = {return_code: 0}
      const dbConnection = dbConnections()['main']
      if (data.reference_category_array && Array.isArray(data.reference_category_array) && data.reference_category_array.length > 0) {
        const referenceTableRepo = dbConnection.repos['ref_reference']
        const whereClauseString = ' rr.active = :active_flag and rr.reference_category_id in (:...reference_category_array) '
        const whereClauseValues = { active_flag: 1000100001, reference_category_array: data.reference_category_array }
        const selectAttributes = ['reference_id', 'reference_name_l10n', 'reference_information', 'reference_category_id']
        const referenceCategoryRowsUnprocessed = await referenceTableRepo.createQueryBuilder('rr').select(selectAttributes).where(whereClauseString, whereClauseValues).orderBy("rr.reference_category_id", "ASC").getRawMany()
        const referenceCategoryRows = postProcessRecords(null, referenceCategoryRowsUnprocessed, { json_columns: ['reference_name_l10n, reference_information'] })
        const referenceCategoryMap = {}
        let lastReferenceCategoryRow = -1
        let referencesForReferenceCategory = []
        for (const referenceCategoryRow of referenceCategoryRows) {
          if (referenceCategoryRow.reference_category_id !== lastReferenceCategoryRow) {
            if (lastReferenceCategoryRow !== -1) {
              referenceCategoryMap[lastReferenceCategoryRow] = referencesForReferenceCategory
            }
            referencesForReferenceCategory = []
            lastReferenceCategoryRow = referenceCategoryRow.reference_category_id
          }
          referencesForReferenceCategory.push(referenceCategoryRow)
        }
        if (lastReferenceCategoryRow !== -1) {
          referenceCategoryMap[lastReferenceCategoryRow] = referencesForReferenceCategory
        }    
        returnValue.reference_category_map = referenceCategoryMap
      }
      if (data.crop_list === true) {
        const cropTableRepo = dbConnection.repos['ref_crop']
        const selectAttributes = ['crop_id', 'crop_name_l10n']
        const whereClauseString = ' rr.active = :active_flag '
        const whereClauseValues = { active_flag: 1000100001 }
        const cropRowsUnprocessed = await cropTableRepo.createQueryBuilder('rr').select(selectAttributes).where(whereClauseString, whereClauseValues).orderBy("rr.crop_id", "ASC").getRawMany()
        const cropRows = postProcessRecords(null, cropRowsUnprocessed, { json_columns: ['crop_name_l10n'] })
        returnValue.crops = cropRows
      }
      return returnValue
    } catch (error) {
      console.log('oc r GAD c 10, error')
      console.log('oc r GAD c 10a, error = ', error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class References3 {
  async create(data, params) {
    try {
      const references3Lib = new References3Lib()
      return references3Lib.create(data, params)
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

module.exports = { References, References2, EntityComments, ReferencesAndCategories ,References3}
