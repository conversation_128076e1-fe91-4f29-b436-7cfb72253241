const { LibCommonServices, References2Lib, FilterService } = require('@krushal-it/back-end-lib')
const {loadMeds} = require('@krushal-it/back-end-lib/services/common/common.class')
const { GeneralError } = require('@feathersjs/errors')
const { dbConnections } = require('@krushal-it/ah-orm')
const { getFCM } = require('../../database_utils/notification/notificatio.query')
class CommonServices {
  commonServiceHandler = new LibCommonServices()
  async find (params) {
    try {
      const result = await this.commonServiceHandler.find(params)
      //     await CommonServices.getFCMId(params,result)
      return result
    } catch (error) {
      console.error(error)
      return {
        result: false,
        message: error.message
      }
    }
  }

  async create (data, params) {
    try {
      const result = await this.commonServiceHandler.create(data, params)
      return result
    } catch (error) {
      console.error(error)
      return {
        result: false,
        message: error.message
      }
    }
  }

  static async getFCMId (params, result) {
    const { user_id } = params.headers.token
    const { subPath } = params.route
    if (subPath == 'resolve-token') {
      const fcm = await getFCM(user_id)
      result.fcm_token = fcm[0]?.fcm_id || null
      result.status = fcm[0]?.active || null
    } else {

    }
  }
}

class HealthCheck {
  async find (params) {
    try {
      const dbCheckStatus = await dbConnections().main.manager.query('SELECT 1 AS is_available')
      if (dbCheckStatus.length == 0) throw new GeneralError('Internal server error')
      const { is_available } = dbCheckStatus[0]
      return { is_available }
    } catch (error) {
      console.error(error)
      throw new GeneralError('Internal server error')
    }
  }
}

class References2 {
  async create (data, params) {
    try {
      const commonServiceHandler = new References2Lib()
      const response = await commonServiceHandler.create(data, params)
      return response
    } catch (error) {
      console.error(error)
      return {
        result: false,
        message: error.message
      }
    }
  }
}
class ListFilter {
  async create (data, params) {
    const handleListFilter = new FilterService()
    return await handleListFilter.create(data, params)
  }
}

class LibLoadMeds {
  async find(params) {
    return await loadMeds();
  }
}
module.exports = { HealthCheck, CommonServices, References2, ListFilter ,LibLoadMeds}
