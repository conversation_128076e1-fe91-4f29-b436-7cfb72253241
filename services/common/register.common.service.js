const { References, References2, EntityComments, ReferencesAndCategories ,References3} = require('./common.class')
const auth = require('../../middleware/auth')
const tokenInject = require('../../middleware/tokenInject.js')
const configureCommon = (app) => {
  // app.use(auth)
  app.use('/refs', new References())
  app.use('/v2/refs', new References2())
  app.use('/v3/refs', new References3())

  app.use('/oc/get-comments', new EntityComments(), tokenInject)
  app.use('/oc/load-references-for-reference-categories', new ReferencesAndCategories(), tokenInject)
}
module.exports = { configureCommon }
