const { GeneralError } = require("@feathersjs/errors")
const { verifyOtp, getOtp } = require("@krushal-it/back-end-lib")

module.exports = {
  otp: function otp(app) {
    app.use("v2/cdpl/verify/otp", {
      async create(data) {
        try {
          return await verifyOtp(data)
        } catch (error) {
          return new GeneralError(error)
        }
      },
      async get(care_calendar_id) {
        try {
          return await getOtp(care_calendar_id)
        } catch (error) {
          return new GeneralError(error)
        }
      }
    })
  }
}
