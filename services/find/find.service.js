const { BACK_OFFICE_PERSON } = require("@krushal-it/back-end-lib/utils/constant");
const { checkUsertype } = require("../../middleware/auth");
const tokenInject = require("../../middleware/tokenInject");
const { handleServerError } = require("../../middleware/server-error");
const { FindFarmer, Findanimal, Findactivity } = require("./find.class");
const { validatorFindFarmer, validatorFindAnimal,validatorFindActivity} = require("../../middleware/validators").validators;
const { handleValidationError } = require("../../middleware/validators");

const loadFindServices = (app) => {
    app.use("/find/farmer", checkUsertype(BACK_OFFICE_PERSON), validatorFindFarmer, handleValidationError, new FindFarmer(), tokenInject, handleServerError);
    app.use("/find/animal", checkUsertype(BACK_OFFICE_PERSON), validatorFindAnimal, handleValidationError, new Findanimal(), tokenInject, handleServerError);
    app.use("/find/activity", checkUsertype(BACK_OFFICE_PERSON), validatorFindActivity, handleValidationError, new Findactivity(), tokenInject, handleServerError);
};

module.exports = loadFindServices;