const { getFarmer, getAnimal, getPastAcitivity } = require("./controller")

class FindFarmer {
  async create(data, params) {
    const { f_farmer_mobile } = data
    return await getFarmer(f_farmer_mobile)
  }
}
class Findanimal {
  async create(data, params) {
    const { f_ear_tag } = data
    return await getAnimal(f_ear_tag)
  }
}
class Findactivity {
  async create(data, params) {
    const { entity_uuid } = data
    return await getPastAcitivity(data, entity_uuid)
  }
}

module.exports = { FindFarmer, Findanimal, Findactivity }
