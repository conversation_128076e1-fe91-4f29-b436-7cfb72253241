const { GeneralError } = require("@feathersjs/errors")
const { dbConnections } = require("@krushal-it/ah-orm")
const { getCompiledQuery } = require("@krushal-it/back-end-lib")
const { Like } = require("typeorm")

const getFarmer = async farmerMobile => {
  try {
    const farmerListquery = getCompiledQuery("farmer-animal-list", { f_farmer_mobile: 1 })
    const queryBuilder = dbConnections().main.manager.createQueryBuilder().from(`(${farmerListquery})`)
    queryBuilder.setParameters({ f_farmer_mobile: farmerMobile, f_farmer_mobile_c_code: `+91${farmerMobile}` })
    const rawData = await queryBuilder.execute()

    if (rawData.length === 0) {
      throw new GeneralError("Not found", {
        statusCode: 204
      })
    }

    return { data: formatData(rawData) }

  } catch (error) {
    console.error(error)
    if (error instanceof GeneralError) {
      return { data: -1, result: false, error: error.message, status: error.data.statusCode }
    }
    return { data: -1 }
  }
}

const getAnimal = async f_ear_tag => {
  try {
    const animalListquery = getCompiledQuery("farmer-animal-list", { f_ear_tag: 1 })
    const queryBuilder = dbConnections()
      .main.manager.createQueryBuilder()
      .from(`(${animalListquery})`)
    queryBuilder.setParameters({ f_ear_tag })
    const rawData = await queryBuilder.execute()
    if (rawData.length === 0) {
      throw new GeneralError("Not found", {
        statusCode: 204
      })
    }
    return { data: formatData(rawData) }
  } catch (error) {
    console.error(error)
    if (error instanceof GeneralError) {
      return { data: -1, result: false, error: error.message, status: error.data.statusCode }
    }
    return { data: -1 }
  }
}
const formatData = rawData => {
  
  const formatedData = {}
  const account_payment_type_set = new Set()
  const cattlesSet = {}
  
  for (const row of rawData) {

    const { 
      farmer_name_l10n, 
      customer_id: farmer_id, 
      farmer_visual_id, 
      farmer_mobile_number, 
      farmer_alt_mobile_number, 
      farmer_active_status, 
      village_id, 
      village_name_l10n, 
      taluk_id, 
      taluk_name_l10n, 
      district_id, 
      district_name_l10n, 
      state_id, 
      state_name_l10n, 
      account_payment_type, 
      customer_credit_limit, 
      customer_credit_usage 
    } = row
    
    if (account_payment_type) account_payment_type_set.add(account_payment_type)

    if (!formatedData[row.customer_id]) {
      formatedData[row.customer_id] = {
        customer_credit_limit,
        customer_credit_usage,
        farmer_name_l10n,
        farmer_id,
        farmer_visual_id,
        farmer_mobile_number,
        farmer_alt_mobile_number,
        farmer_active_status,
        village_id,
        village_name_l10n,
        taluk_id,
        taluk_name_l10n,
        district_id,
        district_name_l10n,
        state_id,
        state_name_l10n,
        cattles: []
      }
    }

    const { 
      animal_visual_id, 
      animal_id, 
      animal_active_status, 
      animal_name, 
      animal_ear_tag, 
      animal_type_id, 
      animal_type_l10n, 
      healthcare_plan_subscription_date_1, 
      subscription_plan_1, 
      inactive_reason_1, 
      inactive_date_1, 
      inactive_reason_note_1,
      number_of_months_pregnant,
      last_calving_date,
      cattle_milking_status
    } = row

    if(animal_id) {
      cattlesSet[animal_id] = {
        animal_visual_id,
        animal_id,
        animal_active_status,
        animal_name,
        animal_ear_tag,
        animal_type_id,
        animal_type_l10n,
        healthcare_plan_subscription_date_1,
        subscription_plan_1,
        inactive_reason_1,
        inactive_date_1,
        inactive_reason_note_1,
        number_of_months_pregnant,
        last_calving_date,
        cattle_milking_status      
      }
    }
    
  }

  formatedData[rawData[0].customer_id].cattles = Object.values(cattlesSet)

  if (account_payment_type_set.size) {
    const account_payment_type = []
    account_payment_type_set.forEach(value => account_payment_type.push(value))
    formatedData[rawData[0].customer_id].account_payment_type = account_payment_type
  }

  return Object.values(formatedData)
}
const getPastAcitivity = async (data, entity_uuid) => {
  try {
    const activityList = getCompiledQuery("past-activity");
    const queryBuilder = dbConnections().main.manager.createQueryBuilder().from(`(${activityList})`, 'past-activities');
    queryBuilder.setParameters({ entity_uuid })
    
    let total_page = 1
    
    if (!isNaN(data.page_number) && !isNaN(data.page_limit)) {

      let queryResult = await queryBuilder.select("COUNT(*) as count").getRawOne();
      const resultCount = queryResult.count;

      let offset = Math.max(data.page_limit * (data.page_number - 1), 0);
      
      total_page = Math.ceil(resultCount / data.page_limit);

      queryBuilder.skip(offset).take(data.page_limit)
    }

    const rawData = await queryBuilder.select("*").execute()
    if (rawData.length === 0) {
      throw new GeneralError("Not found", {
        statusCode: 204
      })
    }
    return { data: rawData, total_page }
  } catch (error) {
    console.error(error)
    if (error instanceof GeneralError) {
      return { data: -1, result: false, error: error.message, status: error.data.statusCode }
    }
    return { data: -1 }
  }
}

module.exports = { getFarmer, getAnimal, getPastAcitivity }
