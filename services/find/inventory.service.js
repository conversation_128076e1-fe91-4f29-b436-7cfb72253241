const { BACK_OFFICE_PERSON } = require("@krushal-it/back-end-lib/utils/constant");
const { checkUsertype } = require("../../middleware/auth");
const tokenInject = require("../../middleware/tokenInject");
const { invoiceStreamPipe } = require("../../middleware/multer/multerUpload")
const { handleServerError } = require("../../middleware/server-error");
const { RequisitionService,RequisitionList,RequisitionStatus, RequisitionInvoiceService, DownloadInvoice,UploadBillAttachmentService,BillAttachmentService, PharmacyRequisitionService } = require('../requisitions/requisition.service')
const { InventoryLedgerService,MedicineInventoryService } = require('../inventoryledger/inventoryledger.service')
const { WarehouseBlockQtyService }=require('../warehouseblockqty/warehouseblockqty.service')
const { multerUp, multerUp2,injectFile,streamPipe,multerError } = require('../../middleware/multer/multerUpload');
const { MedicineService,MedQtyService } = require("../medicines/medicine.service");

const loadInventoryService = (app) => {
    // app.use("/staffs/timeline/:staff_id", checkUsertype(BACK_OFFICE_PERSON), new StaffTimeline(), tokenInject, handleServerError);
    app.use('/v3/requisitions', new RequisitionService(), tokenInject)
    app.use('/v3/medicineqtydetails', new MedQtyService(), tokenInject)
    app.use('/v3/requisitionlist', new RequisitionList(), tokenInject)
    app.use('/v3/requisitionstatus',new RequisitionStatus(), tokenInject)
    app.use('/v3/medicines',new MedicineService(), tokenInject)
    app.use('/v3/inventorykit',new InventoryLedgerService(), tokenInject)
    app.use('/v3/medinventory',new MedicineInventoryService(), tokenInject)
    app.use('/v3/warehouseblockqty/:action',new WarehouseBlockQtyService(),tokenInject)
    app.use('/v3/requisitioninvoice',new RequisitionInvoiceService(),tokenInject)
    app.use('/v3/pharmacyrequisition/:type',new PharmacyRequisitionService(),tokenInject)
    // app.use('/v3/downloadinvoice',new DownloadInvoice(),invoiceStreamPipe)
    // app.use('/v3/requisitioninvoiceattachment',new BillAttachmentService(),tokenInject)
    app.use('/v3/uploadinvoiceattachment',
      multerUp.fields([{ name: 'file', maxCount: 5 }]),
      //multerError,
      injectFile,
      new UploadBillAttachmentService(),
      tokenInject
    )
};

module.exports = {loadInventoryService};
