/* eslint-disable camelcase */
const { getDBConnections, postProcessRecords, preProcessRecords } = require('@krushal-it/ah-orm')
const { getUserInfoBasedOnHeaderInformation } = require('../../utils/common/auth/auth')
const { configurationJSON } = require('@krushal-it/common-core')
const _ = require('lodash')
const e = require('express')

class AnimalClassificationHistory {
  async find (params) {
    try {
      let { roles, users, since } = params.query
      // query the locations and get locations of last 10 minutes or time since query parameter
      since = since || Date.now() - 10 * 60 * 1000
      const mainDBConnection = getDBConnections().main
      const mainSchemaAddition = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 'main.' : ''

      const animalClassificationHistoryQuery = `
          SELECT rl.user_device_id, rl.lat, rl.lng, rl.time_at from ${mainSchemaAddition}animal_classification_history rl
          where time_at >= ${since} 
          order by time_at
          `
      const animalClassificationHistoryResults = await mainDBConnection.manager.query(animalClassificationHistoryQuery)
      if (animalClassificationHistoryResults.length === 0) {
        return { return_code: 0, data: {} }
      } else {
        return { return_code: 0, data: animalClassificationHistoryResults }
      }
    } catch (error) {
      if (error.name === 'KrushalError') {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}
// this will update AnimalHistorywith Latest data from animalClassification
const temporalTypeReferenceId = 2000000152
class AnimalClassificationHistorySync {
  async find (params) {
    // first get latestCreated & updatedFrom both tables
    try {
      const mainDBConnection = getDBConnections().main
      const mainSchemaAddition = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 'main.' : ''
      const txnMgrResult = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
        const animalClassificationRepo = transactionalEntityManager.getRepository('animal_classification')
        const animalClassificationHistoryRepo = transactionalEntityManager.getRepository('animal_classification_history')
        let query = `
              SELECT animal_id , to_char(max(source_created_at),'yyyy-mm-dd hh24:mi:ss.ms') as source_created_at, 
                to_char(max(source_updated_at),'yyyy-mm-dd hh24:mi:ss.ms') as source_updated_at 
              from ${mainSchemaAddition}animal_classification_history
              group by animal_id `
        const historyResults = await animalClassificationHistoryRepo.query(query) // this is an array
        query = `
              SELECT animal_id , to_char(max(created_at),'yyyy-mm-dd hh24:mi:ss.ms')  as created_at, 
                to_char(max(updated_at),'yyyy-mm-dd hh24:mi:ss.ms')  as updated_at 
              from ${mainSchemaAddition}animal_classification
              group by animal_id `
        const classificationQryResults = await animalClassificationRepo.query(query) // create_new array with combination
        for (const [clIdx, classificationQryResult] of classificationQryResults.entries()) {
          const histIdx = historyResults.findIndex((histResult) => { return histResult.animal_id === classificationQryResult.animal_id })
          if (histIdx >= 0) {
            classificationQryResults[clIdx].source_created_at = historyResults[histIdx].source_created_at
            classificationQryResults[clIdx].source_updated_at = historyResults[histIdx].source_updated_at
            classificationQryResults[clIdx].animal_classification_history_id = historyResults[histIdx].animal_classification_history_id

          } else {
            classificationQryResults[clIdx].source_created_at = '2000-01-01'
            classificationQryResults[clIdx].source_updated_at = '2000-01-01'
          }
        }
        for (const result of classificationQryResults) {
        // query records for each. marshal them and then insert
        // now select records that fall in range
          const animalClassificationQuery = `
          select * from ${mainSchemaAddition}animal_classification where 
            (created_at > '${result.source_updated_at}'
            or updated_at > '${result.source_updated_at}')
          --  and 
          --  (created_at <= '${result.created_at}'
          --  or updated_at <= '${result.updated_at}')
            and animal_id = '${result.animal_id}'
        `
          const animalClassifications = await animalClassificationRepo.query(animalClassificationQuery) //
          // find temporal_entity in the results
          // assume 1 temporal_record
          const temporalRecord = _.find(animalClassifications, { classifier_id: temporalTypeReferenceId })
          if (_.isEmpty(temporalRecord)) {
          // no temporal record dont insert something wrong
            continue
          }
          //  temporalRecord = temporalRecord[0]
          // massageData and then insert
          for (const result of animalClassifications) {
            result.souce_created_at = result.created_at
            result.source_updated_at = result.updated_at
            delete result.created_at
            delete result.updated_at
            result.temporal_entity_type_id = temporalRecord.classifier_id
            result.temporal_entity_id = temporalRecord.value_reference_id
          }
          // now insert records
          const results = await animalClassificationHistoryRepo.save(animalClassifications)
        }
      })
      return { success: true }
    } catch (error) {
      return ({ status: 'error' })
    }
  }
}

// let since = Date(2008, 12, 13) // seconds ? Date.now() - seconds : Date.now() - seconds * routeTime
// const startTime = Date(2008, 12, 14) // seconds ? Date.now() - seconds : Date.now() - seconds * routeTime
const truth = (value) => {
  const valueStr = String(value).toLowerCase()
  return valueStr === 'true' || (valueStr !== 'false' && valueStr !== 'null' &&
  valueStr !== 'undefined' && valueStr !== '' && valueStr !== '[]' && valueStr !== '{}' && valueStr !== '0')
}

module.exports = { AnimalClassificationHistorySync, AnimalClassificationHistory }
