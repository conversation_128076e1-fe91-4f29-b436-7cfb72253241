const { AnimalClassificationHistory, AnimalClassificationHistorySync } = require('./animalClassificationHistory.class')
const auth = require('../../middleware/auth')
const configureAnimalClassificationHistory = (app) => {
  // app.use(auth)
  app.use('/animalClassificationHistory', new AnimalClassificationHistory())
  app.use('/animalClassificationHistorySync', new AnimalClassificationHistorySync())
}
module.exports = { configureAnimalClassificationHistory }
