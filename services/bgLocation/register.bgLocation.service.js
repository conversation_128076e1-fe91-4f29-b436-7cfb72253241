const { BgLocation, BgMapData, BgLocationSync, AttributesData, UserEvent, LiveMapData, StaffKMData, StaffDailyKMReport, StaffMonthlyKMReport, SaveStaffApprovedKM, SaveStaffApprovedTravelDay } = require('./bgLocation.class')
const auth = require('../../middleware/auth')
const configureBgLocation = (app) => {
  // app.use(auth)
  app.use('/bgLocation/locations', new BgLocation())
  app.use('/bgLocation/sync', new BgLocationSync())
  app.use('/bgLocation/mapData', new BgMapData())
  app.use('/bgLocation/attributes', new AttributesData())
  app.use('/bgLocation/userEvents', new UserEvent())
  app.use('/bgLocation/liveMapData', new LiveMapData())
  app.use('/bgLocation/staff-km-data', new StaffKMData())
  app.use('/bgLocation/staff-daily-km-report', new StaffDailyKMReport())
  app.use('/bgLocation/staff-monthly-km-report', new StaffMonthlyKMReport())
  app.use('/bgLocation/staff-save-km-report', new SaveStaffApprovedKM())
  app.use('/bgLocation/staff-save-km-approval-report', new SaveStaffApprovedTravelDay())
}
module.exports = { configureBgLocation }
