/* eslint-disable camelcase */
const { dbConnections, getDBConnections, postProcessRecords, preProcessRecords } = require("@krushal-it/ah-orm")
const { getUserInfoBasedOnHeaderInformation } = require("../../utils/common/auth/auth")
const { configurationJSON } = require("@krushal-it/common-core")
const _ = require("lodash")
const lodashObject = require("lodash")

const { adjustStartDatesOfEntityStatusByCategoryAndEntities, reportQueryClassificationJoinConfiguration, generateSelectJoinClauseForClassification, generateWhereClauseForReportInnerQuery: generateWhereClause, generateLimitStringForReportInnerQuery: generateLimitString, generateSortStringForReportInnerQuery: generateSortString, identifyMandatoryColumnsForReportQuery, extractFieldsNeedingFilterData, extractFieldsNeedingPageFilterData } = require("../../utils/query/query.helper")

const insertLocations = async data => {
  try {
    const mainDBConnection = getDBConnections().main
    const locationDBConnection = getDBConnections().location
    const rawLocationRepo = locationDBConnection.repos.raw_location
    // const rawLocationData = [...data]
    const constructedRawLocationData = []
    data.forEach((rawLoc, index) => {
      // const time_at = new Date(rawLoc.time)
      constructedRawLocationData.push({
        accuracy: rawLoc.accuracy,
        altitude: rawLoc.altitude,
        bearing	: rawLoc.bearing,
        user_device_id: rawLoc.user_device_id,
        lat	: rawLoc.latitude,
        lng	: rawLoc.longitude,
        provider: rawLoc.provider,
        speed: rawLoc.speed,
        time_at: rawLoc.time_at ,
        additional_data: {
          batteryLevel: rawLoc.batteryLevel,
          batteryStatus: rawLoc.batteryStatus,
          bearingAccuracy: rawLoc.bearingAccuracy,
          speedAccuracy: rawLoc.speedAccuracy,
          headlessStatus: rawLoc.headlessStatus,
          verticalAccuracy : rawLoc.verticalAccuracy,
          backgroundTrackingStatus : rawLoc.backgoundTrackingStatus,
          fromBackground : rawLoc.fromBackground,
          time : rawLoc.time
        }
      }
      )
    })
    const createdRawLocation = await rawLocationRepo.save(constructedRawLocationData)
    return { nLoc: createdRawLocation.length }
  } catch (error) {
    console.log(error)
    if (error.name === "KrushalError") {
      return { return_code: error.return_code, message: error.message }
    } else {
      return { return_code: -101, message: error.message }
    }
  }
}

class BgLocation {
  async find(params) {
    try {
      let { roles, users, since } = params.query
      // query the locations and get locations of last 10 minutes or time since query parameter
      since = since || Date.now() - 10 * 60 * 1000
      const locationDBConnection = getDBConnections().location
      const locationSchemaAddition = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? "location." : ""

      const rawLocationQuery = `
          SELECT rl.user_device_id, rl.lat, rl.lng, rl.time_at from ${locationSchemaAddition}raw_location rl
          where time_at >= ${since} 
          order by time_at
          `
      const rawLocationResults = await locationDBConnection.manager.query(rawLocationQuery)
      if (rawLocationResults.length === 0) {
        return { return_code: 0, data: {} }
      } else {
        return { return_code: 0, data: rawLocationResults }
      }
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async create(data, params) {
    try {
      for (const datum of data) {
        await insertUserEvents([
          {
            location_tracking: 0,
            time_at: new Date()
              .toISOString()
              .replace("T", " ")
              .replace("Z", ""),

            user_device_id: datum.user_device_id,
            lat: 0,
            lng: 0,
            additional_data: {
              batteryStatus: datum.batteryStatus,
              networkStrength: datum.networkStrength,
              batteryLevel: datum.batteryLevel
            }
          }
        ])
      }
      return insertLocations(data)
    } catch (error) {
      console.log("bgLocation/LOCATION - error", error)
    }
  }
}

class BgLocationSync {
  async find(params) {
    return {}
  }

  async create(data, params) {
    return insertLocations(data)
  }
}
// let since = Date(2008, 12, 13) // seconds ? Date.now() - seconds : Date.now() - seconds * routeTime
// const startTime = Date(2008, 12, 14) // seconds ? Date.now() - seconds : Date.now() - seconds * routeTime
const truth = value => {
  const valueStr = String(value).toLowerCase()
  return valueStr === "true" || (valueStr !== "false" && valueStr !== "null" && valueStr !== "undefined" && valueStr !== "" && valueStr !== "[]" && valueStr !== "{}" && valueStr !== "0")
}

class AttributesData {
  async find(params) {
    const user_language = params.query.user_language
    // get all the required attributes, expect language to be part of parameters
    try {
      const attributeQuery = `
        select ud.user_device_id, ud.created_at as user_device_id_created_at, s.staff_id,  s.staff_name_l10n ,s.staff_type_id, rr.reference_name_l10n as staff_type_name_l10n  
        from main.user_device ud, main.staff s, main.ref_reference rr    
        where s.staff_id  = ud.entity_uuid
        and s.staff_type_id  = rr.reference_id
        order by ud.created_at asc`
      const mainDBConnection = getDBConnections().main
      const attributeResults = await mainDBConnection.manager.query(attributeQuery)
      if (attributeResults.length === 0) {
        return { return_code: 0, data: {} }
      }
      const attributeData = {}
      attributeResults.forEach(attribute => {
        attributeData[attribute.user_device_id] = attribute
      })
      return { return_code: 0, data: attributeData }
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}
class BgMapData {
  async find(params) {
    const { locFrom, locTo, stayPtFrom, stayPtTo, routeFrom, routeTo, attrsReqd, user_device_ids } = params.query
    if (!user_device_ids) {
      return { return_code: -101, message: "user_device_ids is required" }
    }
    let user_device_id_str = ""
    if (_.isArray(user_device_ids)) {
      user_device_id_str = user_device_ids.join(", ")
      user_device_id_str = "and user_device_id in ( " + user_device_id_str + " )"
    } else {
      // assume comma separated string
      user_device_id_str = `and user_device_id in ( ${user_device_ids} )`
    }
    try {
      const locationDBConnection = getDBConnections().location
      const mainDBConnection = getDBConnections().main
      const stayPtQry = `
        select user_device_id, lat, lng, arrived_at , left_at  
        from location.stay_point 
        where   arrived_at  > '${stayPtFrom}'
        and arrived_at  < '${stayPtTo}'
        ${user_device_id_str}
        order by user_device_id, arrived_at `
      const stayPoints = await locationDBConnection.manager.query(stayPtQry)
      const routeQry = `
        select travel_route_id, user_device_id, start_lat, start_lng,
          end_lat,end_lng, start_at, end_at, distance
        from location.travel_route
        where start_at > '${stayPtFrom}'
        and end_at < '${stayPtTo}'
        ${user_device_id_str}
        order by user_device_id, start_at
      `
      const routes = await locationDBConnection.manager.query(routeQry)
      // for each route get all the points
      for (let i = 0; i < routes.length; i++) {
        const route = routes[i]
        const routePtQuery = `
          select lat, lng, time_at
          from location.raw_location
          where user_device_id = ${route.user_device_id}
          and time_at >= '${route.start_at
            .toISOString()
            .replace("T", " ")
            .replace("Z", "")}'
          and time_at <='${route.end_at
            .toISOString()
            .replace("T", " ")
            .replace("Z", "")}'
          order by user_device_id, time_at
        `
        const points = await locationDBConnection.manager.query(routePtQuery)
        route.points = points?.map(point => [point.lat, point.lng]) || []
      }

      const userEventQuery = `
        select user_event_id, user_device_id, location_tracking, lat, lng, time_at
        from main.user_event
        where   time_at  > '${stayPtFrom}'
        and time_at  <= '${stayPtTo}'
        ${user_device_id_str}
        order by user_device_id, time_at `
      const userEvents = await mainDBConnection.manager.query(userEventQuery)
      const data = { locations: [], stayPoints, routes, userEvents, farms: [] }
      return { return_code: 0, data }
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

const insertUserEvents = async data => {
  try {
    const mainDBConnection = getDBConnections().main
    const userEventRepo = mainDBConnection.repos.user_event
    const userEventData = [...data]
    userEventData.forEach((userEvent, index) => {
      userEvent.time_at = userEvent.time_at
      delete userEvent.uuid
    })
    const createdUserEvents = await userEventRepo.save(userEventData)
    return { nUserEvents: createdUserEvents.length }
  } catch (error) {
    console.log(error)
    if (error.name === "KrushalError") {
      return { return_code: error.return_code, message: error.message }
    } else {
      return { return_code: -101, message: error.message }
    }
  }
}

class UserEvent {
  async find(params) {
    try {
      let { roles, users, since } = params.query
      // query the userEvents and get userEvents of last 10 minutes or time since query parameter
      since = since || Date.now() - 10 * 60 * 1000
      const userEventDBConnection = getDBConnections().main
      const userEventSchemaAddition = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? "main." : ""

      const userEventQuery = `
          SELECT ue.user_device_id, ue.lat, ue.lng, ue.time_at from ${userEventSchemaAddition}user_event ue
          where time_at >= ${since} 
          order by time_at
          `
      const userEventResults = await userEventDBConnection.manager.query(userEventQuery)
      if (userEventResults.length === 0) {
        return { return_code: 0, data: {} }
      } else {
        return { return_code: 0, data: userEventResults }
      }
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async create(data, params) {
    return insertUserEvents(data)
  }
}

class LiveMapData {
  async find(params) {
    try {
      let { user_device_ids, since } = params.query
      // query the liveLocations and get liveLocations of last 10 minutes or time since query parameter
      since = since ? Date.now() - since : Date.now() - 10 * 60 * 1000
      since = new Date(since)
        .toISOString()
        .replace("T", " ")
        .replace("Z", "")
      if (!user_device_ids) {
        return { return_code: -101, message: "user_device_ids is required" }
      }
      let user_device_id_str = ""
      if (_.isArray(user_device_ids)) {
        user_device_id_str = user_device_ids.join(", ")
        user_device_id_str = "and user_device_id in ( " + user_device_id_str + " )"
      } else {
        // assume comma separated string
        user_device_id_str = `and user_device_id in ( ${user_device_ids} )`
      }

      const liveLocationConnection = getDBConnections().location
      const liveLocationSchemaAddition = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? "location." : ""

      const liveLocationQuery = `
    SELECT l.*
      FROM location.raw_location l
      INNER JOIN (
        SELECT user_device_id, MAX(time_at) AS latest_time
        FROM location.raw_location
        where time_at >= '${since}'
        ${user_device_id_str}
        GROUP BY user_device_id
      ) subq ON l.user_device_id = subq.user_device_id AND l.time_at = subq.latest_time`

      const liveLocationResults = await liveLocationConnection.manager.query(liveLocationQuery)
      if (liveLocationResults.length === 0) {
        return { return_code: 0, data: [] }
      } else {
        return { return_code: 0, data: liveLocationResults }
      }
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class StaffKMData {
  async create(data, params) {
    try {
      const returnValue = { return_code: 0 }
      const mainDBConnection = dbConnections().main
      const locationDBConnection = dbConnections().location

      const startDate = data.start_date
      const endDate = data.end_date
      const staffIds = data.staff_id_array
      const userDeviceIds = data.user_device_id_array

      const userDeviceEntity = mainDBConnection.entities["user_device"]
      const userDeviceIdsUnprocessed = await mainDBConnection.manager
        .createQueryBuilder()
        .select(["user_device_id"])
        .from(userDeviceEntity)
        // .from('care_calendar')
        .where("active = :activeId and entity_type_id = :staffEntityTypeId and entity_uuid in (:...staff_id_array)", { activeId: 1000100001, staffEntityTypeId: 1000460003, staff_id_array: staffIds })
        .execute()
      const userDevicesForStaffAsRows = postProcessRecords(undefined, userDeviceIdsUnprocessed, { double_columns: ["start_lat", "start_lng", "end_lat", "end_lng", "distance"] })
      const userDevicesForStaff = userDevicesForStaffAsRows.map(row => {
        return row["user_device_id"]
      })

      const travelRouteEntity = locationDBConnection.entities["travel_route"]
      const travelRouteUnprocessed = await locationDBConnection.manager
        .createQueryBuilder()
        .select(["travel_route_id", "start_lat", "start_lng", "end_lat", "end_lng", "start_at", "end_at", "distance"])
        .addSelect("distance / 1000", "distance_in_km")
        .addSelect("json_build_object('lat', start_lat, 'lng', start_lng)", "start_lat_lng")
        .addSelect("json_build_object('lat', end_lat, 'lng', end_lng)", "end_lat_lng")
        .from(travelRouteEntity)
        // .from('care_calendar')
        .where("user_device_id in (:...user_device_id_array) and start_at > :start_date and end_at < :end_date", { user_device_id_array: userDevicesForStaff, start_date: startDate, end_date: endDate })
        .orderBy("start_at", "ASC")
        .execute()
      const travelRoutes = postProcessRecords(undefined, travelRouteUnprocessed, { double_columns: ["start_lat", "start_lng", "end_lat", "end_lng", "distance", "distance_in_km"] })
      /* const travelRoutes = travelRoutesFlat.map(object => {
        return {travel_route_id: object['travel_route_id'], start_at: object['start_at'], end_at: object['end_at'], distance: object['distance'], start_lat_lng: {lat: object['start_lat'], lng: object['start_lng']}, end_lat_lng: {lat: object['end_lat'], lng: object['end_lng']},}
      }) */

      // identify all columns needed
      returnValue.count = travelRoutes.length
      returnValue.report = travelRoutes

      return returnValue
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class StaffDailyKMReport {
  async create(data, params) {
    try {
      const returnValue = { return_code: 0 }
      const tableFilterData = {}
      returnValue.tableFilters = tableFilterData
      const mainDBConnection = dbConnections().main
      const locationDBConnection = dbConnections().location

      const limitString = generateLimitString(data)
      const sortString = generateSortString(data)
      const whereClause = generateWhereClause(data)
      let searchWithinRestrictedIds = ``

      // identify all columns needed
      const selectedColumns = data.selectedColumns
      // identify mandatory columns needed
      const mandatoryColumnsForSearch = identifyMandatoryColumnsForReportQuery(data.filters, data.sorting, data.globalSearch, data.searchConfiguration)

      let restrictedIds = false

      let includeWhereClauseInDetailedColumnsQuery = false

      let innerQuery = `
        select concat(s.staff_id::text, '~~', u_d.travel_date) as staff_date_id, s.staff_id ,
          u_d.travel_date, u_d.user_distance_in_km,
          coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') staff_name,
          coalesce(st_rr.reference_name_l10n->>'en', st_rr.reference_name_l10n->>'ul') staff_type,
          coalesce(vt_rr.reference_name_l10n->>'en', vt_rr.reference_name_l10n->>'ul') vehicle_type,
          vt_sc.value_reference_id as vehicle_type_id, vpka.vehicle_per_km_allowance,
          amkm_sc.staff_classification_id as user_distance_date_row_id,
          amkm_sc.value_double as user_distance_date,
          coalesce(coalesce(amkm_sc.value_double, u_d.user_distance_in_km)*vpka.vehicle_per_km_allowance, 0) as allowance_for_date,
          ta_sc.staff_classification_id as travel_applicable_day_row_id,
          case when ta_sc.value_reference_id is null then 'Not Approved' else 'Approved' end as travel_applicable_day_saved_status,
          case when ta_sc.value_reference_id is null or ta_sc.value_reference_id = 1000105001 then 1000105001 else 1000105002 end as travel_applicable_day_reference_id,
          extract(isodow from u_d.travel_date) day_of_week_as_number,
          s.staff_type_id 
        from (
          select u_d.entity_type_id, u_d.entity_uuid, u_d.travel_date, sum(u_d.user_device_date_distance)/1000 as user_distance_in_km
          from (
            select ud.user_device_id, ud.entity_type_id, ud.entity_uuid, ud_d_d.end_at_in_ist as travel_date,
            ud_d_d.user_device_date_distance
            from main.user_device ud
            inner join (
              select sum(tr.distance) user_device_date_distance, tr.user_device_id, (end_at + INTERVAL '5.5 hours')::date as end_at_in_ist
              from "location".travel_route tr 
              group by tr.user_device_id, end_at_in_ist
            ) ud_d_d on ud_d_d.user_device_id = ud.user_device_id
          ) u_d
          group by u_d.entity_type_id, u_d.entity_uuid, u_d.travel_date
        ) u_d
        inner join main.staff s on s.staff_id = u_d.entity_uuid
        inner join main.ref_reference st_rr on s.staff_type_id = st_rr.reference_id
        left join main.staff_classification vt_sc on vt_sc.staff_id = s.staff_id and vt_sc.active = 1000100001 and vt_sc.classifier_id = 2000000408
        left join main.ref_reference vt_rr on vt_sc.value_reference_id = vt_rr.reference_id
        left join (
          SELECT
            vpka.key::int  AS vehicle_type_id,
            CAST(vpka.value AS double precision) AS vehicle_per_km_allowance
          FROM
            main.ref_reference vpka_rr,
            LATERAL jsonb_each_text(vpka_rr.reference_information) AS vpka
          WHERE
            vpka_rr.reference_id = 1001250001
        ) vpka on vpka.vehicle_type_id = vt_rr.reference_id 
        left join main.staff_classification amkm_sc on amkm_sc.active = 1000100001 and amkm_sc.classifier_id = 2000000410
          and amkm_sc.staff_id = s.staff_id and amkm_sc.value_date = u_d.travel_date
        left join main.staff_classification ta_sc on ta_sc.active = 1000100001 and ta_sc.classifier_id = 2000000411
          and ta_sc.staff_id = s.staff_id and ta_sc.value_date = u_d.travel_date
        where u_d.entity_type_id = 1000460003
      `

      const countQuery = `
        select count(*) as count from (
          ${innerQuery}
        ) outerTable
        ${whereClause}
      `
      const reportResultCount = await mainDBConnection.manager.query(countQuery)
      const count = parseInt(reportResultCount[0].count)

      if (count === 0) {
        includeWhereClauseInDetailedColumnsQuery = true
      }

      const filtersFieldToReturnKeyMap = extractFieldsNeedingFilterData(data.searchConfiguration, data.selectedColumns)
      const fieldsNeedingFilterData = Object.keys(filtersFieldToReturnKeyMap)

      const mandatoryColumnsPlusColumnsNeededForFilteringWithoutUniq = [...mandatoryColumnsForSearch, ...fieldsNeedingFilterData]
      const mandatoryColumnsPlusColumnsNeededForFiltering = lodashObject.uniq(mandatoryColumnsPlusColumnsNeededForFilteringWithoutUniq)

      if (fieldsNeedingFilterData.includes("staff_name")) {
        const distinctStaffQuery = `
          select distinct staff_id, staff_id value_uuid, staff_name value, staff_name text from (
            ${innerQuery}
          ) outerTable
          ${whereClause}
        `
        const distinctStaffQueryResultUnprocessed = await mainDBConnection.manager.query(distinctStaffQuery)
        const distinctStaffQueryResult = postProcessRecords(undefined, distinctStaffQueryResultUnprocessed, {})
        returnValue[filtersFieldToReturnKeyMap["staff_name"]] = distinctStaffQueryResult
      }

      let query = `
          select * 
          from (
            ${innerQuery}
          ) outerTable
          ${whereClause}
          ${sortString}
          ${limitString}
        `

      const reportResultUnprocessed = await mainDBConnection.manager.query(query)
      const reportResult = postProcessRecords(undefined, reportResultUnprocessed, {})

      /* const primaryKeyToRowMap = {}
      for (const reportRow of reportResult) {
        primaryKeyToRowMap[reportRow['customer_id']] = reportRow
      }
      const primaryKeysToBeQueried = Object.keys(primaryKeyToRowMap)

      let inPrimaryKeysIdString = ""
      for (const primaryKey of primaryKeysToBeQueried) {
        if (inPrimaryKeysIdString !== "") {
          inPrimaryKeysIdString = inPrimaryKeysIdString + ", "
        }
        inPrimaryKeysIdString = inPrimaryKeysIdString + "'" + primaryKey + "'"
      }
      searchWithinRestrictedIds = (inPrimaryKeysIdString !== '') ? ` and c.customer_id in (${inPrimaryKeysIdString})` : '' */

      /* query = `
        select * from (
          ${innerQuery}
        ) outerTable
        ${includeWhereClauseInDetailedColumnsQuery ? whereClause : ''}
        ${sortString}
      `

      const reportResultWithSelectedColumnsUnprocessed = await mainDBConnection.manager.query(query)
      const reportResultWithSelectedColumns = postProcessRecords(undefined, reportResultWithSelectedColumnsUnprocessed, {}) */

      returnValue.count = count
      returnValue.report = reportResult

      return returnValue
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class StaffMonthlyKMReport {
  async create(data, params) {
    try {
      const returnValue = { return_code: 0 }
      const mainDBConnection = dbConnections().main
      const locationDBConnection = dbConnections().location

      const limitString = generateLimitString(data)
      const sortString = generateSortString(data)
      const whereClause = generateWhereClause(data)
      let searchWithinRestrictedIds = ``

      // identify all columns needed
      const selectedColumns = data.selectedColumns
      // identify mandatory columns needed
      const mandatoryColumnsForSearch = identifyMandatoryColumnsForReportQuery(data.filters, data.sorting, data.globalSearch, data.searchConfiguration)

      let restrictedIds = false

      let includeWhereClauseInDetailedColumnsQuery = false

      let innerQuery = `
        with sma as(
          select sud.staff_id,
            s_da_t.daily_km_threshold,
            sud.end_at_in_ist, date_trunc('month', sud.end_at_in_ist)::date as end_at_in_ist_in_month,
            sud.tad_id,
            sud.daily_user_distance_in_km
          from (
            select s.staff_type_id, s.staff_id, coalesce(tad_sc.value_reference_id, 1000105001) tad_id, (tr.end_at + INTERVAL '5.5 hours')::date as end_at_in_ist, sum(tr.distance)/1000 daily_user_distance_in_km
            from location.travel_route tr 
            inner join main.user_device ud on ud.user_device_id = tr.user_device_id and ud.entity_type_id = 1000460003
            inner join main.staff s on s.staff_id = ud.entity_uuid
            left join main.staff_classification tad_sc on tad_sc.active = 1000100001 and tad_sc.classifier_id = 2000000411
              and tad_sc.staff_id = s.staff_id and tad_sc.value_date::date = (tr.end_at + INTERVAL '5.5 hours')::date
            group by s.staff_id, tad_id, end_at_in_ist
          ) sud
          inner join (
            SELECT
              jsonb_array_elements_text(rr.reference_information) AS staff_type_level_da_configuration,
              (jsonb_array_elements(rr.reference_information)->>'staff_type_id')::int AS staff_type_id,
              (jsonb_array_elements(rr.reference_information)->>'daily_km_threshold')::double precision AS daily_km_threshold
            FROM main.ref_reference rr
            where rr.reference_id = 1001250002
          ) s_da_t on s_da_t.staff_type_id = sud.staff_type_id
        )
        select concat(s.staff_id::text, '~~', sma2.end_at_in_ist_in_month) as staff_month_id,
          s.staff_type_id, coalesce(st_rr.reference_name_l10n->>'en', st_rr.reference_name_l10n->>'ul') staff_type,
          s.staff_id, coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') staff_name,
          vpka.vehicle_type_id,
          vt_rr.reference_id, coalesce(vt_rr.reference_name_l10n->>'en', vt_rr.reference_name_l10n->>'ul') vehicle_type, vpka.vehicle_per_km_allowance,
          sma2.end_at_in_ist_in_month as travel_month, sma2.applicable_monthly_km, s_da_t.daily_da_amount,
          amkm_sc.staff_classification_id as user_distance_month_row_id, amkm_sc.value_double as approved_monthly_km,
          case when amkm_sc.staff_classification_id is null then 'Not Approved' else 'Approved' end as approved_monthly_km_saved_status,
          coalesce(amkm_sc.value_double, sma2.applicable_monthly_km)*vpka.vehicle_per_km_allowance as approved_ta,
          sma3.applicable_travel_days_in_month,
          (sma3.applicable_travel_days_in_month*s_da_t.daily_da_amount) as approved_da
        from main.staff s
        inner join main.ref_reference st_rr on st_rr.reference_id = s.staff_type_id 
        left join (
          select sma.staff_id, sma.end_at_in_ist_in_month, sum(sma.daily_user_distance_in_km) applicable_monthly_km
          from sma
          group by sma.staff_id, sma.end_at_in_ist_in_month
        ) sma2 on sma2.staff_id = s.staff_id
        left join (
          select sma.staff_id, sma.end_at_in_ist_in_month, count(sma.end_at_in_ist) applicable_travel_days_in_month
          from sma
          where sma.tad_id = 1000105001 and sma.daily_user_distance_in_km > sma.daily_km_threshold
          group by sma.staff_id, sma.end_at_in_ist_in_month 
        ) sma3 on sma3.staff_id = s.staff_id and sma3.end_at_in_ist_in_month = sma2.end_at_in_ist_in_month
        inner join (
          SELECT
            jsonb_array_elements_text(rr.reference_information) AS staff_type_level_da_configuration,
            (jsonb_array_elements(rr.reference_information)->>'staff_type_id')::int AS staff_type_id,
            (jsonb_array_elements(rr.reference_information)->>'daily_da_amount')::double precision AS daily_da_amount
          FROM main.ref_reference rr
          where rr.reference_id = 1001250002
        ) s_da_t on s_da_t.staff_type_id = s.staff_type_id
        left join main.staff_classification vt_sc on vt_sc.active = 1000100001 and vt_sc.classifier_id = 2000000408
          and vt_sc.staff_id = s.staff_id
        left join main.ref_reference vt_rr on vt_sc.value_reference_id = vt_rr.reference_id
        left join (
          SELECT
            vpka.key::int  AS vehicle_type_id,
            CAST(vpka.value AS double precision) AS vehicle_per_km_allowance
          FROM
            main.ref_reference vpka_rr,
            LATERAL jsonb_each_text(vpka_rr.reference_information) AS vpka
          WHERE
            vpka_rr.reference_id = 1001250001
        ) vpka on vpka.vehicle_type_id = vt_rr.reference_id
        left join main.staff_classification amkm_sc on amkm_sc.active = 1000100001 and amkm_sc.classifier_id = 2000000409
          and amkm_sc.staff_id = s.staff_id and amkm_sc.value_date::date = sma2.end_at_in_ist_in_month
      `

      const countQuery = `
        select count(*) as count from (
          ${innerQuery}
        ) outerTable
        ${whereClause}
      `
      const reportResultCount = await mainDBConnection.manager.query(countQuery)
      const count = parseInt(reportResultCount[0].count)

      if (count === 0) {
        includeWhereClauseInDetailedColumnsQuery = true
      }

      const filtersFieldToReturnKeyMap = extractFieldsNeedingFilterData(data.searchConfiguration, data.selectedColumns)
      const fieldsNeedingFilterData = Object.keys(filtersFieldToReturnKeyMap)

      const mandatoryColumnsPlusColumnsNeededForFilteringWithoutUniq = [...mandatoryColumnsForSearch, ...fieldsNeedingFilterData]
      const mandatoryColumnsPlusColumnsNeededForFiltering = lodashObject.uniq(mandatoryColumnsPlusColumnsNeededForFilteringWithoutUniq)

      if (fieldsNeedingFilterData.includes("staff_name")) {
        const distinctStaffQuery = `
          select distinct staff_id, staff_id value_uuid, staff_name value, staff_name text from (
            ${innerQuery}
          ) outerTable
          ${whereClause}
        `
        const distinctStaffQueryResultUnprocessed = await mainDBConnection.manager.query(distinctStaffQuery)
        const distinctStaffQueryResult = postProcessRecords(undefined, distinctStaffQueryResultUnprocessed, {})
        returnValue[filtersFieldToReturnKeyMap["staff_name"]] = distinctStaffQueryResult
      }

      let query = `
          select * 
          from (
            ${innerQuery}
          ) outerTable
          ${whereClause}
          ${sortString}
          ${limitString}
        `

      const reportResultUnprocessed = await mainDBConnection.manager.query(query)
      const reportResult = postProcessRecords(undefined, reportResultUnprocessed, {})

      /* const primaryKeyToRowMap = {}
      for (const reportRow of reportResult) {
        primaryKeyToRowMap[reportRow['customer_id']] = reportRow
      }
      const primaryKeysToBeQueried = Object.keys(primaryKeyToRowMap)

      let inPrimaryKeysIdString = ""
      for (const primaryKey of primaryKeysToBeQueried) {
        if (inPrimaryKeysIdString !== "") {
          inPrimaryKeysIdString = inPrimaryKeysIdString + ", "
        }
        inPrimaryKeysIdString = inPrimaryKeysIdString + "'" + primaryKey + "'"
      }
      searchWithinRestrictedIds = (inPrimaryKeysIdString !== '') ? ` and c.customer_id in (${inPrimaryKeysIdString})` : '' */

      /* query = `
        select * from (
          ${innerQuery}
        ) outerTable
        ${includeWhereClauseInDetailedColumnsQuery ? whereClause : ''}
        ${sortString}
      `

      const reportResultWithSelectedColumnsUnprocessed = await mainDBConnection.manager.query(query)
      const reportResultWithSelectedColumns = postProcessRecords(undefined, reportResultWithSelectedColumnsUnprocessed, {}) */

      returnValue.count = count
      returnValue.report = reportResult

      return returnValue
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class SaveStaffApprovedKM {
  async create(data, params) {
    try {
      const returnValue = { return_code: 0 }
      const mainDBConnection = dbConnections().main

      const staffClassificationTableEntity = mainDBConnection.entities["staff_classification"]

      const periodOfApprovedKM = data.period_of_approved_km

      const classifierId = periodOfApprovedKM === "month" ? 2000000409 : periodOfApprovedKM === "date" ? 2000000410 : undefined
      if (classifierId === undefined) {
        returnValue.return_code = -102
        return returnValue
      }

      const classificationRowIdKey = classifierId === 2000000409 ? "user_distance_month_row_id" : "user_distance_date_row_id"
      const classificationRowValueDateKey = classifierId === 2000000409 ? "travel_month" : "travel_date"
      const classificationRowValueDoubleKey = classifierId === 2000000409 ? "approved_monthly_km" : "user_distance_date"
      const approvedKMData = {
        toBeUpdatedStaffKMData: data.staff_approved_km_to_be_updated_key_map !== undefined && data.staff_approved_km_to_be_updated_key_map !== null && typeof data.staff_approved_km_to_be_updated_key_map === "object" ? Object.values(data.staff_approved_km_to_be_updated_key_map) : [],
        toBeInsertedStaffKMData: data.staff_approved_km_to_be_inserted_list !== undefined && data.staff_approved_km_to_be_inserted_list !== null && Array.isArray(data.staff_approved_km_to_be_inserted_list) ? data.staff_approved_km_to_be_inserted_list : [],
        toBeDeletedStaffKMData: data.staff_approved_km_to_be_deleted_list !== undefined && data.staff_approved_km_to_be_deleted_list !== null && Array.isArray(data.staff_approved_km_to_be_deleted_list) ? data.staff_approved_km_to_be_deleted_list : []
      }
      const toBeInsertedStaffClassificationData = []
      for (const toBeInsertedIndividualStaffKMData of approvedKMData.toBeInsertedStaffKMData) {
        const staffId = toBeInsertedIndividualStaffKMData["staff_id"]
        const travelDate = toBeInsertedIndividualStaffKMData[classificationRowValueDateKey]
        const approvedKM = toBeInsertedIndividualStaffKMData[classificationRowValueDoubleKey]
        const classificationObject = {
          staff_id: staffId,
          classifier_id: classifierId,
          value_date: travelDate,
          value_double: approvedKM
        }
        toBeInsertedStaffClassificationData.push(classificationObject)
      }
      approvedKMData.toBeInsertedStaffClassificationData = toBeInsertedStaffClassificationData
      const saveResult = await mainDBConnection.manager.transaction(async transactionalEntityManager => {
        try {
          const staffClassificationRepo = transactionalEntityManager.getRepository("staff_classification")
          for (const toBeUpdatedFarm of approvedKMData.toBeUpdatedStaffKMData) {
            const toBeUpdatedFarmModified = { ...toBeUpdatedFarm }
            const staffId = toBeUpdatedFarmModified["staff_id"]
            const travelDate = toBeUpdatedFarmModified[classificationRowValueDateKey]
            const staffClassificationId = toBeUpdatedFarmModified[classificationRowIdKey]
            const approvedKM = toBeUpdatedFarmModified[classificationRowValueDoubleKey]
            const updateAssetResult = await staffClassificationRepo
              .createQueryBuilder()
              .update()
              .set({ value_double: approvedKM })
              .where("staff_classification_id = :id", { id: staffClassificationId })
              .execute()
          }
          if (approvedKMData.toBeInsertedStaffClassificationData && Array.isArray(approvedKMData.toBeInsertedStaffClassificationData) && approvedKMData.toBeInsertedStaffClassificationData.length > 0) {
            const staffClassificationInsertResults = await transactionalEntityManager
              .createQueryBuilder()
              .insert()
              .into(staffClassificationTableEntity)
              .values(approvedKMData.toBeInsertedStaffClassificationData)
              .execute()
          }
        } catch (error) {
          console.log("oc UASS c 20, error")
          console.log("oc UASS c 20a, error = ", error)
          throw error
        }
      })

      return returnValue
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class SaveStaffApprovedTravelDay {
  async create(data, params) {
    try {
      const returnValue = { return_code: 0 }
      const mainDBConnection = dbConnections().main

      const staffClassificationTableEntity = mainDBConnection.entities["staff_classification"]

      const approvedKMData = {
        toBeUpdatedStaffKMApprovedDateData: data.staff_km_approved_date_to_be_updated_key_map !== undefined && data.staff_km_approved_date_to_be_updated_key_map !== null && typeof data.staff_km_approved_date_to_be_updated_key_map === "object" ? Object.values(data.staff_km_approved_date_to_be_updated_key_map) : [],
        toBeInsertedStaffKMApprovedDateData: data.staff_km_approved_date_to_be_inserted_list !== undefined && data.staff_km_approved_date_to_be_inserted_list !== null && Array.isArray(data.staff_km_approved_date_to_be_inserted_list) ? data.staff_km_approved_date_to_be_inserted_list : [],
        toBeDeletedStaffKMApprovedDateData: data.staff_km_approved_date_to_be_deleted_list !== undefined && data.staff_km_approved_date_to_be_deleted_list !== null && Array.isArray(data.staff_km_approved_date_to_be_deleted_list) ? data.staff_km_approved_date_to_be_deleted_list : []
      }
      const toBeInsertedStaffClassificationData = []
      for (const toBeInsertedIndividualStaffKMData of approvedKMData.toBeInsertedStaffKMApprovedDateData) {
        const staffId = toBeInsertedIndividualStaffKMData["staff_id"]
        const travelDate = toBeInsertedIndividualStaffKMData["travel_date"]
        const travelApplicableDay = toBeInsertedIndividualStaffKMData["travel_applicable_day"]
        if (travelApplicableDay && Array.isArray(travelApplicableDay) && travelApplicableDay.length > 0) {
          const classificationObject = {
            staff_id: staffId,
            classifier_id: 2000000411,
            value_date: travelDate,
            value_reference_id: travelApplicableDay[0]
          }
          toBeInsertedStaffClassificationData.push(classificationObject)
        }
      }
      approvedKMData.toBeInsertedStaffClassificationData = toBeInsertedStaffClassificationData
      const saveResult = await mainDBConnection.manager.transaction(async transactionalEntityManager => {
        try {
          const staffClassificationRepo = transactionalEntityManager.getRepository("staff_classification")
          for (const toBeUpdatedStaffClassification of approvedKMData.toBeUpdatedStaffKMApprovedDateData) {
            const staffId = toBeUpdatedStaffClassification["staff_id"]
            const travelDate = toBeUpdatedStaffClassification["travel_date"]
            const staffClassificationId = toBeUpdatedStaffClassification["travel_applicable_day_row_id"]
            const travelApplicableDay = toBeUpdatedStaffClassification["travel_applicable_day"]
            if (travelApplicableDay && Array.isArray(travelApplicableDay) && travelApplicableDay.length > 0) {
              const updateAssetResult = await staffClassificationRepo
                .createQueryBuilder()
                .update()
                .set({ value_reference_id: travelApplicableDay[0] })
                .where("staff_classification_id = :id", { id: staffClassificationId })
                .execute()
            }
          }
          if (approvedKMData.toBeInsertedStaffClassificationData && Array.isArray(approvedKMData.toBeInsertedStaffClassificationData) && approvedKMData.toBeInsertedStaffClassificationData.length > 0) {
            const staffClassificationInsertResults = await transactionalEntityManager
              .createQueryBuilder()
              .insert()
              .into(staffClassificationTableEntity)
              .values(approvedKMData.toBeInsertedStaffClassificationData)
              .execute()
          }
        } catch (error) {
          console.log("oc UASS c 20, error")
          console.log("oc UASS c 20a, error = ", error)
          throw error
        }
      })

      return returnValue
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

module.exports = { BgLocation, BgLocationSync, BgMapData, AttributesData, UserEvent, LiveMapData, StaffKMData, StaffDailyKMReport, StaffMonthlyKMReport, SaveStaffApprovedKM, SaveStaffApprovedTravelDay }
