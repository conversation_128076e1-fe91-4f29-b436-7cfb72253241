const { Engine } = require("json-rules-engine")
const { referenceRepositoryInstance } = require("@krushal-it/back-end-lib")
const { dbConnections } = require("@krushal-it/ah-orm")
const { data_map } = require("@krushal-it/back-end-lib/ENUMS")

const paymentRuleFactKeys = {
  subscription_plan: undefined,
  pouring_status: undefined,
  activity_category_id: undefined,
  activity_id: undefined
}

const calendarRuleFactKeys = {
  subscription_plan: undefined
}

const medicineRecommendationFactKeys = {
  diagnosis_id: undefined
}

const ruleManager = {
  payment_rules: {
    engine: new Engine(),
    facts: paymentRuleFactKeys
  },
  care_calendar_rules: {
    engine: new Engine(),
    facts: calendarRuleFactKeys
  },
  medicine_recomendation_rules: {
    engine: new Engine(),
    facts: medicineRecommendationFactKeys
  }
}

const initializeRuleEngine = async () => {
  const rulesReferenceData = await referenceRepositoryInstance.getReferenceDetails(dbConnections().main.manager, [data_map.SR_PAYMENT_DETAILS_RULES, data_map.PREVENTIVE_CALENDAR_RULES, 1001360006])
  const paymentRules = rulesReferenceData[data_map.SR_PAYMENT_DETAILS_RULES]?.reference_information
  paymentRules?.forEach(rule => ruleManager.payment_rules.engine.addRule(rule))

  const calendarRules = rulesReferenceData[data_map.PREVENTIVE_CALENDAR_RULES]?.reference_information
  calendarRules?.forEach(rule => ruleManager.care_calendar_rules.engine.addRule(rule))

  const medicineRecommendationRules = rulesReferenceData[1001360006]?.reference_information
  medicineRecommendationRules?.forEach(rule => ruleManager.medicine_recomendation_rules.engine.addRule(rule))
}

const applyRules = async (facts, ruleType) => {
  if (!ruleManager[ruleType]) {
    throw new Error("Invalid rule type")
  }

  let response = {}
  const engine = ruleManager[ruleType].engine
  const validFactKeys = ruleManager[ruleType].facts
  const factsToRun = { ...validFactKeys, ...facts }
  await engine.run(factsToRun).then(results => {
    if (results && results.events && Array.isArray(results.events)) {
      results.events.forEach(event => {
        response[event.type] = event.params
      })
    } else {
      console.error("Unexpected results format:", results)
    }
  })

  return response
}

module.exports = { initializeRuleEngine, applyRules }
