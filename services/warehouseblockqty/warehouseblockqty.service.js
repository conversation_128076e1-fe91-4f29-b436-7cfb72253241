const { LibWarehouseService } = require("@krushal-it/back-end-lib");
const uuid = require("uuid");
const errors = require("@feathersjs/errors");
const { responseStructure } = require("../common/responsestructure");

class WarehouseBlockQtyService {
    warehouseBlockqtyHandler = new LibWarehouseService()
    async create(data,params){
        const {action}=params.route
        try {
            switch (action) {
                case 'add':
                  let res = await this.warehouseBlockqtyHandler.create(data,params);
                  return responseStructure(res);
                case 'update':
                    let result = await this.warehouseBlockqtyHandler.create(data,params);
                    return responseStructure(result);
                case 'remove':
                    let removeresult = await this.warehouseBlockqtyHandler.removeBlockQty(data,params);
                    return responseStructure(removeresult);
                default:
                    return { result: false, message: "Invalid type" };
            }
        } catch (error) {
            console.error(error);
            return { result: false, error };
        }
    }
}

module.exports = { WarehouseBlockQtyService };