const { default: axios } = require("axios")
const { BadRequest, GeneralError } = require("@feathersjs/errors")

class Predicor {
  async create(data, params) {
    try {
      const { query, session_id } = data

      const payload = session_id
        ? {
            answer: query
          }
        : {
            symptoms: query
          }

      // Determine which API endpoint to call based on session_id presence
      const apiUrl = session_id
        ? `http://127.0.0.1:5021/api/chat/${session_id}/answer` // API for existing session
        : "http://127.0.0.1:5021/api/chat/start" // API for new session

      // Configure the HTTP request
      const options = {
        method: "POST",
        url: apiUrl,
        headers: {
          "Content-Type": "application/json"
        },
        data: payload
      }

      console.log(`Making API call to: ${apiUrl}`, payload)

      const response = await axios.request(options)

      if (response.data.type === "no_matches") {
        // Set HTTP status code for no matches
        if (params.provider && params.provider === "rest") {
          params.statusCode = 204
        }

        return {
          statusCode: 204,
          message: "No matches found",
          data: null,
          session_id: response.data.session_id || session_id,
          api_endpoint_used: apiUrl
        }
      }

      // Set HTTP status code for successful response
      if (params.provider && params.provider === "rest") {
        params.statusCode = 200
      }

      return {
        return_code: 0,
        data: response.data,
        session_id: response.data.session_id || session_id,
        api_endpoint_used: apiUrl
      }
    } catch (error) {
      console.error("Chat API error:", error.message)

      // Set HTTP status code for error
      if (params.provider && params.provider === "rest") {
        params.statusCode = 204
      }

      // Always return 204 for any error
      return {
        statusCode: 204,
        message: "No matches found or error occurred",
        data: null
      }
    }
  }
}

module.exports = { Predicor }
