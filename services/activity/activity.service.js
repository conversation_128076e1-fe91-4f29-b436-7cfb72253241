const { LibActivityService, LibTaskList, LibLinkedTaskService, LibActivityObservationService,get_task_infromation_by_id } = require("@krushal-it/back-end-lib");
const { LibTask, LibActivityDiagnosisService, LibActivityPrescriptionService, ActivityPreviewService } = require("@krushal-it/back-end-lib/services/activity_routeplan/activity.class");
const { ActivityPrescriptionUpdateServiceV2  ,ActivityObservationUpdateSeviceV2,ActivityDiagnosisUpdateServiceV2 ,activitySummaryV2} = require("@krushal-it/back-end-lib/services/activity_routeplan/activityV2.class")
const { sendNotifications, getFCMofUserQuery } = require("../../database_utils/notification/notificatio.query");
const { dbConnections } = require("@krushal-it/ah-orm");
const { ActivityAdditionalMedicineServiceV2 } = require("@krushal-it/back-end-lib/services/activity_routeplan/additionalMeds.class");
const { RELATION_CALENDAR } = require("@krushal-it/back-end-lib/utils/constant");

class ActivityService {
  activityServiceHandler = new LibActivityService();
  async find(params) {
    try {
      let result = await this.activityServiceHandler.find(params);
      return result;
    } catch (error) {
      return { result: false, error };
    }
  }

  async create(data, params) {
    try {
      const { user_id, user_type } = params.headers.token;
      let result = await this.activityServiceHandler.create(data, params);
      let calendar_id = result.id;
      await ActivityService.sendNotificationsByCustomerId({ calendar_id, user_id, user_type });
      return result;
    } catch (error) {
      return { result: false, error: error.message };
    }
  }
  //
  async update(id, data, params) {
    try {
      let result = await this.activityServiceHandler.update(id, data, params);
      return result;
    } catch (error) {
      console.error(error);
      return { result: false, error: error.message };
    }
  }

  static async sendNotificationsByCustomerId(options) {
    const { calendar_id, user_id, user_type } = options;
    let manager = options.manager ? options.manager : dbConnections().main.manager;
    const ccData = await manager.getRepository(RELATION_CALENDAR).findOne({
      where: {
        care_calendar_id: calendar_id
      }
    });
    let queryFCM = getFCMofUserQuery({ entity_uuid: ccData.entity_uuid });
    let fcm = await manager.query(queryFCM, []);

    const taskDetails = await get_task_infromation_by_id(calendar_id)
    const { task_name, ear_tag, task_date, ticket_number, customer_name } = taskDetails[0]
    
    const taskName = task_name?? "N/A";
    const eartag = ear_tag ?? "N/A";

    const taskDate = task_date ? new Date(task_date).toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' }) : "N/A";
    const customerName = customer_name ? (customer_name.ul || customer_name.en || 'N/A') : 'N/A';
  
    const title = `Ticket #${ticket_number||' '}, (${taskName})`
    const body = `Customer - ${customerName}, Cattle #${eartag}, Visit date - ${taskDate}.`

    const registrationTokens = fcm.length > 0 ? fcm.filter(fcm => fcm.fcm_id != null).map(fcm => fcm.fcm_id) : [];
    if (registrationTokens.length > 0) {
      sendNotifications({
        body: body,
        title: title,
        registrationTokens: registrationTokens,
        redirect: "true",
        action: `${calendar_id}`,
        type: 'service-request'
      });
    }
  }
   
}



class TaskList {
  taskListServiceHandler = new LibTaskList();
  async create(data, params) {
    let result = await this.taskListServiceHandler.create(data, params);
    return result;
  }
}

class LinkedTaskHistory {
  taskHistoryServiceHandler = new LibLinkedTaskService();
  async get(id, params) {
    let result = await this.taskHistoryServiceHandler.get(id, params);
    return result;
  }
}

class AnimalTask {
  animalTaskHandler = new LibTask();
  async create(data, params) {
    let result = await this.animalTaskHandler.getAnimalLevelTask(data, params);
    return result;
  }
}

class FarmerTask {
  farmerTaskHandler = new LibTask();
  async create(data, params) {
    let result = await this.farmerTaskHandler.getFarmerLevelTask(data, params);
    return result;
  }
}

class TaskByID {
  taskHandler = new LibTask();
  async create(data, params) {
    let result = await this.taskHandler.getTaskById(data, params);
    return result;
  }
}
class ActivityObservation {
  observationHandler = new LibActivityObservationService();
  async get(activity_id, params) {
    const payload = { activity_id };
    return { data: await this.observationHandler.getObservations(payload, params) };
  };
  async update(id, data, params) {
    return { data: await this.observationHandler.updateObservation(data, params) };
  }
}
class ActivityDiagnosis {
  diagnosisHandler = new LibActivityDiagnosisService();
  async get(activity_id, params) {
    const payload = { activity_id };
    return { data: await this.diagnosisHandler.getDiagnosis(payload, params) };
  }
  async update(id, data, params) {
    return { data: await this.diagnosisHandler.updateDiagnosis(data, params) };
  }
}

class ActivityPrescription {
  prescriptionHeper = new LibActivityPrescriptionService();
  async get(activity_id, params) {
    const payload = { activity_id };
    return { data: await this.prescriptionHeper.getPrescription(payload, params) };
  }

  async update(id, data, params) {
    return { data: await this.prescriptionHeper.updatePrescription(data, params) };
  }
}

class ActivityPreview {
  activityPreviewHandler = new ActivityPreviewService();
  async create(data, param) {
    return { data: await this.activityPreviewHandler.create(data, param) };
  }
}

class ActivityPrescriptionServiceV2 {
  activityPrescriptionUpdateHandler = new ActivityPrescriptionUpdateServiceV2()
  async create(data, params) {
    try {
      let result = await this.activityPrescriptionUpdateHandler.handleActivity(data, params)
      return result
    } catch (error) {
      throw error
    }
  }
}

class ActivityDiagnosisServiceV2 {
  async update(id, data, params) {
    try {
      
      let result = await ActivityDiagnosisUpdateServiceV2(data,params)
      return {result}
    } catch (error) {

    }
  }
  
}

class ActivityObservationServiceV2 {
  async update(id, data, params) {
    try {
      
      let result = await ActivityObservationUpdateSeviceV2(data,params)
      return {result}
    } catch (error) {

    }
  }
}

class AdditionalMedicineServciceV2 {
  medicineService =new ActivityAdditionalMedicineServiceV2()
  async update (id, data ,params) {
    try{
      let result = await this.medicineService.updateAdditionalMeds(data, params);
      return result
    } catch (error) {
      return { message: "something went wrong" }
    }
  }
  async get(id, params) {
    try{
      let result = await this.medicineService.getAdditionalMedicine(id, params);
      return result
    }catch (error) {
      return { message: "something went wrong"}
    }
  }
}

class ActivitySummaryServiceV2 {
  async get (id , params) {
    try{
      let result = await activitySummaryV2(id , params)
      return result
    } catch (error) {
      return { message: "something went wrong" , error}
    }
  }
}

module.exports = { ActivityService, TaskList, LinkedTaskHistory, AnimalTask, FarmerTask, TaskByID, ActivityObservation, ActivityDiagnosis, ActivityPrescription, ActivityPreview ,ActivityPrescriptionServiceV2 ,ActivityDiagnosisServiceV2 ,ActivityObservationServiceV2 ,AdditionalMedicineServciceV2 ,ActivitySummaryServiceV2};
