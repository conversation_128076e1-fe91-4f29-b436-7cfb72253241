const { LibCardService, LibChildCards } = require("@krushal-it/back-end-lib");

class CardService {
  cardServiceHandler = new LibCardService();
  async create(data, params) {
    try {
      let result = await this.cardServiceHandler.create(data, params);
      return result;
    } catch (error) {
      console.error(error);
      return { result: false, message: error.message };
    }
  }
}
class ChildCardsRO {
  childCardHandler = new LibChildCards();
  async create(data, params) {
    try {
      let result = await this.childCardHandler.create(data, params);
      return result;
    } catch (error) {
      console.log(error);
      return { result: false, message: error.message };
    }
  }
}
module.exports = { CardService, ChildCardsRO };
