const { LibRoutePlanService } = require("@krushal-it/back-end-lib");
class RoutePlanService{
  routePlanServiceHandler = new LibRoutePlanService()
    async create(data,params){
       try {
         let result = await this.routePlanServiceHandler.create(data,params)
         return {result};
       } catch (error) {
         console.error(error);
         return {result:false,message:error.message}
       }
    }
}
module.exports = RoutePlanService;