const { dbConnections } = require("@krushal-it/ah-orm")
const { entity_type, classifiers } = require("@krushal-it/back-end-lib/ENUMS")
const { applyRules } = require("../rules/rules.service")
const { animalRepositoryInstance, customerRepositoryInstance } = require("@krushal-it/back-end-lib")
const { BadRequest } = require("@feathersjs/errors")

class TaskPaymentDetails {

  // gets the payer type and payment type for service cost and medicines cost based on the rules configured in the database
  // there is a reference entry in the database with reference id as 1001360004. This entry has the json based rules
  async create(data, params) {

    const { entity_uuid, entity_type_id, activity_category_id, activity_id } = data

    // based on the privided entity type and its uuid, get the customer and animal data
    const { animalData, customerData } = await this.getEntityDetails(entity_uuid, entity_type_id)

    // request validation
    if (!animalData || !customerData || !activity_id || !activity_category_id) {
      throw new BadRequest("Invalid Request. Please check the data and try again")
    }

    const animalSubscriptionPlanRow = await animalRepositoryInstance.findOne(animalData, classifiers.SUBSCRIPTION_PLAN)
    const pouringStatusRow = await customerRepositoryInstance.findOne(customerData, classifiers.CUSTOMER_POURING_TO_DAIRY)

    // these are the facts which are applied to the rules
    const facts = {
      subscription_plan: animalSubscriptionPlanRow?.value_reference_id,
      pouring_status: pouringStatusRow?.value_reference_id,
      activity_category_id: activity_category_id,
      activity_id: activity_id
    }

    // returns the matching rules based on the provided facts
    const matchingRulesData = await applyRules(facts, 'payment_rules')

    let response = {
      service_cost: matchingRulesData['service-payment-type'],
      medicine_cost: matchingRulesData['medicine-payment-type']
    }

    return response;
  }

  async getEntityDetails(entity_uuid, entity_type_id) {

    let animalData;
    let customerData;

    if (entity_type_id === entity_type.ANIMAL) {
      const animalRawData = await animalRepositoryInstance.getCattleDetails(dbConnections().main.manager, entity_uuid)
      animalData = animalRawData[entity_uuid]

      if (animalData) {
        const customer_id = animalData.customer_id
        const customerRawData = await customerRepositoryInstance.getCustomerDetails(dbConnections().main.manager, customer_id)
        customerData = customerRawData[customer_id]
      }
    } else {
      const customerRawData = await customerRepositoryInstance.getCustomerDetails(dbConnections().main.manager, entity_uuid)
      customerData = customerRawData[entity_uuid]

      if (customerData) {
        const animal_ids = customerData.animal_id
        const animalRawData = await animalRepositoryInstance.getCattleDetails(dbConnections().main.manager, animal_ids)

        for (const animal_id of Object.keys(animalRawData)) {
          const rowData = animalRawData[animal_id]
          const subscriptionInfo = rowData.classification.find(item => item["classifier_id"] === classifiers.SUBSCRIPTION_PLAN)
          if (subscriptionInfo?.value_reference_id) {
            animalData = rowData
            break
          }
        }
      }
    }

    return {animalData: animalData, customerData: customerData};
  }

}

module.exports = { TaskPaymentDetails }
