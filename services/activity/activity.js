const { dbConnections } = require("@krushal-it/ah-orm")
const { getClassificationConfiguration, loadClassificationData, saveClassificationData } = require("@krushal-it/back-end-lib")
const { record_statuses } = require("@krushal-it/back-end-lib/ENUMS")

async function createActivity(data) {
  const { activity_name, activity_category, activity_information } = data

  const mandatoryFields = [activity_name, activity_category]
  const isValid = mandatoryFields.every(field => field != undefined)

  if (isValid === false) throw new Error("activity_category and activity_name are required")
  const new_activity_id = await dbConnections().main.manager.query("select max(activity_id) + 1 as new_id from main.ref_activity")

  const response = await dbConnections().main.manager.insert("ref_activity", {
    activity_id: new_activity_id[0].new_id,
    activity_name,
    activity_category,
    activity_information
  })

  return { activity_id: response.identifiers[0].activity_id, return_code: 0 }
}

async function updateActivity(activity_id, data) {
  if (activity_id === undefined) throw new Error("activity_id is missing!")
  try {
    const confirm_object_for_update = getValidatedDataForUpdate(data)
    const response = await dbConnections()
      .main.manager.createQueryBuilder()
      .update("ref_activity")
      .set(confirm_object_for_update)
      .where(`ref_activity.activity_id = :activity_id`, { activity_id })
      .execute()
    return true
  } catch (error) {
    return new GeneralError(error).toJSON()
  }
}

async function patchActivity(acticity_id, data) {
  if (!acticity_id) throw new Error("Missing activity_id!")
  if (!data.classifiers) throw new Error("Missing! please provide key 'classifiers' in the data.")

  const classification_entry_obj = getValidatedDataForPatch(data)

  return await dbConnections().main.manager.transaction(async transactionalEntityManager => {
    return await saveClassificationData(transactionalEntityManager, "REF_ACTIVITY_CLASSIFICATION", acticity_id, classification_entry_obj)
  })
}

async function getActivity(activity_category_id) {
  let ref_activity = dbConnections().main.manager.getRepository("ref_activity")
  let result = undefined

  ref_activity = ref_activity
    .createQueryBuilder()
    .select("activity_id, activity_name, activity_category, rf.reference_name as activity_category_name, activity_information")
    .leftJoin("ref_reference", "rf", "rf.reference_id = activity_category")

  if (activity_category_id) {
    result = await ref_activity.where("ref_activity.active = :active and activity_category = :activity_category_id", { activity_category_id, active: record_statuses.ACTIVE }).execute()
  } else {
    result = await ref_activity.execute()
  }

  const activity_category_filter_values = await dbConnections().main.manager.query("select ref_reference.reference_id as value, reference_name as text from main.ref_reference where reference_category_id= 10002700")

  return {
    activity_category_filter_values,
    count: result.length,
    report: result,
    return_code: 0
  }
}

module.exports = {
  getActivity,
  patchActivity,
  updateActivity,
  createActivity
}

function getValidatedDataForUpdate(data) {
  const valid_for_entry = {}
  const validKeys = ["activity_name", "activity_category", "activity_information"]
  let atLeastOneIsFound = false
  for (let index = 0; index < validKeys.length; index++) {
    const element = validKeys[index]
    const value = data[element]
    if (value) {
      valid_for_entry[element] = value
      atLeastOneIsFound = true
    }
  }
  if (!atLeastOneIsFound) throw new Error("either activity_category, activity_name or activity_infomation is missing. Please provide atleast one of them.")
  else return valid_for_entry
}

function getValidatedDataForPatch(data) {
  const classifiers = data.classifiers
  const valid_ref_activity_classifiers = getClassificationConfiguration()["REF_ACTIVITY_CLASSIFICATION"].CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP
  let atLeastOneIsFound = false
  const classification_entry_obj = {}

  for (const key in valid_ref_activity_classifiers) {
    if (classifiers[key]) {
      classification_entry_obj[key] = classifiers[key]
      atLeastOneIsFound = true
    }
  }

  if (!atLeastOneIsFound) {
    throw new Error("no valid Classifier found for patching.")
  }
  return classification_entry_obj
}
