const { LibRouteDetailService } = require("@krushal-it/back-end-lib")
class RoutePlanDetailService {
  routePlanDetailsServiceHandler = new LibRouteDetailService()
  async create(data, params) {
    try {
      let result = this.routePlanDetailsServiceHandler.create(data, params)
      return result
    } catch (error) {
      console.error(error)
      throw error
    }
  }
}

module.exports = { RoutePlanDetailService }