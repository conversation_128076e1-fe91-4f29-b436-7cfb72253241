const {
  JOB_TYPE_CALENDAR,
  JOB_TYPE_TRANSLATE,
  JOB_ACTION_START,
  JOB_ACTION_STATUS,
} = require("../../utils/common/namespace/krushal.namespace");
const { staffType } = require("../../utils/configs/entity.config");
const { translate } = require("../../utils/scheduler/scripts/translator_v2");
const { startJob, getJobStatus } = require("./controllers/job");
const VALID_JOBS = new Set([JOB_TYPE_CALENDAR, JOB_TYPE_TRANSLATE]);
class JobService {
  async find(params) {
    try {
      const { token } = params.headers;
      const { user_id, user_type, user_type_resolved } = token;
      // TODO: Enable once testing is over
      //if(user_type_resolved.type !==  staffType["1000230002"].type) return {result:false,message:"no-auth"};
      const { job_type, action, date, no_entries_previous_to_this_date } = params.query;
      if (!VALID_JOBS.has(job_type))
        return { result: false, message: "Invalid job type" };
      switch (action) {
        case JOB_ACTION_START:
          let res = await startJob(job_type, { date, no_entries_previous_to_this_date });
          return res;
        case JOB_ACTION_STATUS:
          let data = await getJobStatus(job_type);
          return data;
        default:
          return { result: false, message: "Invalid action" };
      }
      //return {result:true,message:"Job initialized"};
    } catch (error) {
      console.error(error);
      return { result: false, message: error.message };
    }
  }
}
class TranslationService {
  async find(params) {
    translate();
    return { message: "Translation started" };
  }
}
module.exports = { JobService, TranslationService };
