const JobTrackerQueries = require("../../../database_utils/queries/job-tracker/jobtracker.query");
const {
  STATUS_PENDING,
  STATUS_PARTIAL,
  STATUS_COMPLETE,
  RELATION_JOBTRACKER,
} = require("../../../utils/common/namespace/krushal.namespace");
const Scheduler = require("../../../utils/scheduler/scheduler");
const moment = require("moment");
const DEFAULT_LAST_DAYS = 7;

const getJobStatus = async (job_type, options = {}) => {
  try {
    let jobQueryOptions = {
      job_type,
      last_job: 1,
    };
    return await JobTrackerQueries.getJob(jobQueryOptions);
  } catch (error) {
    console.error(error);
    return { result: false, message: error.message };
  }
};

const startJob = async (job_type, options = {}) => {
  
  let jobQueryOptions = {
    job_type,
    last_job: 1,
  };

  let lastJob = await JobTrackerQueries.getJob_v2(jobQueryOptions);
  let lastJobFinishedTime = moment().subtract(DEFAULT_LAST_DAYS, "days").startOf("day");

  if (Array.isArray(lastJob) && lastJob.length > 0) {
    lastJob = lastJob[0];
    lastJobFinishedTime = lastJob[`job_completed_at`] ? lastJob[`job_completed_at`] : lastJobFinishedTime;
  }
  lastJobFinishedTime = new Date(lastJobFinishedTime);

  let jobObject = {
    job_type,
    job_status: STATUS_PENDING,
  };
  let data = await JobTrackerQueries.insertJob(jobObject);

  let job_id = data.generatedMaps[0].job_tracker_id;
  
  let scheduler = new Scheduler(job_type, job_id, lastJobFinishedTime);
  scheduler.start({...options});
  
  return { result: true, message: "JOB Initiated" };
};

module.exports = { getJobStatus, startJob };