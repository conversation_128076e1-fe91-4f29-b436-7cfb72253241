var nodemailer = require("nodemailer")
const { loadConfiguration, configurationJSO<PERSON> } = require("@krushal-it/common-core")
const keys = configurationJSON().MAILER
/**
 * Represents a Mailer object that sends emails using Nodemailer.
 */
class Mail {
  /**
   * Create a new Mailer object.
   * @param {Object} config - The configuration object for the Mailer.
   * @param {string} config.from - The email address of the sender.
   */

  constructor(config) {
    Object.defineProperty(this, "config", { value: Object.freeze(config) })
    const transporter = nodemailer.createTransport({
      host: "smtp.gmail.com",
      port: 465,
      secure: true,
      auth: {
        type: "OAuth2",
        user: this.config?.from || keys.from_email, // Your email address
        serviceClient: keys.client_id,
        privateKey: keys.private_key,
        accessUrl: keys.token_uri
      }
    })
    Object.defineProperty(this, "transporter", { value: transporter })
  }

  /**
   * Options for sending an email.
   * @typedef {Object} MailOptions
   * @property {string} to - The email address of the recipient.
   * @property {string} subject - The subject of the email.
   * @property {string} text - The plain text version of the email body.
   * @property {string} html - The HTML version of the email body.
   * @property {Object} attachments - The attachments to include in the email.
   * @property {string} attachments.filename - The filename of the attachment.
   * @property {Buffer} attachments.content - The content of the attachment as a binary buffer.
   */

  /**
   * Sends an email using the configured transporter.
   * @param {MailOptions} mailOptions - The options for sending the email.
   * @returns {Promise<Object>} - A promise that resolves to the information about the sent email.
   */
  sendMail = async mailOptions => {
    const info = await this.transporter.sendMail(mailOptions)
    return info
  }
}

module.exports = Mail
