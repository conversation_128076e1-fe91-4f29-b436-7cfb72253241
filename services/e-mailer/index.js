const Emailer = require("./controller")
const eMailer = new Emailer()

function sendEmail(to_address, subject_text, emailTemplateText, fallBackText, attachmentsIN) {
  if (!to_address) {
    throw new Error("Recipients email are missing")
  }

  const payload = {
    to: to_address,
    subject: subject_text,
    text: fallBackText,
    html: emailTemplateText,
    attachments: attachmentsIN
  }
  eMailer.sendMail(payload)
}

module.exports = { sendEmail }
