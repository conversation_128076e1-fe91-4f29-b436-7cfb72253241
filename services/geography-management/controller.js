const { getCompiledQuery } = require("@krushal-it/back-end-lib")
const { dbConnections } = require("@krushal-it/ah-orm")

async function getGeography(language = "en", type, searchtext) {
  try {
    const query = getCompiledQuery("stdv-view", { type })
    const sdtv = await dbConnections()
      .main.manager.createQueryBuilder()
      .from(`(${query})`, "alias")
      .setParameters({ searchtext, language })
      .execute()
    return sdtv
  } catch (error) {
    throw new Error(error)
  }
}

module.exports = { getGeography }
