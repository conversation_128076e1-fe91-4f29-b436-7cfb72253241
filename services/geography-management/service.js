const { GeneralError } = require("@feathersjs/errors")
const { getGeography } = require("./controller")
const { isValidForFind } = require("./schema/index")

module.exports = {
  async find(params) {
    const { language, type, searchtext } = params.query

    try {
      const response = await getGeography(language, type, searchtext)
      return response
    } catch (error) {
      return new GeneralError(error).toJSON()
    }
  }
}
