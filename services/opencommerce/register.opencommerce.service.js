const {
  SellableAnimalsReportOld,
  SnoozeForSellableAnimals,
  UnsnoozeForSellableAnimals,
  UpdateAnimalSellingStatus,
  MarkAnimalsSellable,
  SellableAnimalsReport,
  ItemDetailByItemListingId,
  PotentialBuyerFarmersForAnimalReport,
  SnoozeForBuyerFarmers,
  UpdateBuyingFarmerStatus,
  FarmersWhoMayWantToBuyAnimals,
  CreateOrderForAnimalSale,
  AnimalExchangeOrders,
  UpdateOrderFarmerStatus,
  ManureSnoozeFarmers,
  GenerateCattleExchangePrice,
  CreateCustomerServiceTask,
  CustomerServiceTask,
  UpdateTaskStatus,
  CropDetails
} = require('./report.class.js')
const tokenInject = require('../../middleware/tokenInject.js')

const configureOpenCommerce = (app) => {
  app.use('/oc/sellable-animals-report-old', new SellableAnimalsReportOld(), tokenInject)
  app.use('/oc/snooze-for-sellable-animals', new SnoozeForSellableAnimals(), tokenInject)
  app.use('/oc/update-animal-selling-status', new UpdateAnimalSellingStatus(), tokenInject)
  app.use('/oc/mark-animals-as-sellable', new MarkAnimalsSellable(), tokenInject)
  app.use('/oc/sellable-animals', new SellableAnimalsReport(), tokenInject)
  app.use('/oc/item-detail-by-item-listing-id', new ItemDetailByItemListingId(), tokenInject)
  app.use('/oc/potential-buyer-farmers-for-animal', new PotentialBuyerFarmersForAnimalReport(), tokenInject)
  app.use('/oc/snooze-for-buyer-farmers', new SnoozeForBuyerFarmers(), tokenInject)
  app.use('/oc/update-buying-farmer-status', new UpdateBuyingFarmerStatus(), tokenInject)
  app.use('/oc/farmers-who-may-want-to-buy-animals', new FarmersWhoMayWantToBuyAnimals(), tokenInject)
  app.use('/oc/create-order-for-mal-sale', new CreateOrderForAnimalSale(), tokenInject)
  app.use('/oc/animal-exchange-orders', new AnimalExchangeOrders(), tokenInject)
  app.use('/oc/update-order-farmer-status', new UpdateOrderFarmerStatus(), tokenInject)
  app.use('/oc/unsnooze-sellable-animals', new UnsnoozeForSellableAnimals(), tokenInject)
  app.use('/oc/manure-snooze-farmers', new ManureSnoozeFarmers(), tokenInject)
  app.use('/oc/generate-cattle-exchange-price', new GenerateCattleExchangePrice(), tokenInject)
  app.use('/oc/create-cs-task', new CreateCustomerServiceTask(), tokenInject)
  app.use('/oc/customer-service-tasks', new CustomerServiceTask(), tokenInject)
  app.use('/oc/update-task-status', new UpdateTaskStatus(), tokenInject)
  app.use('/oc/crop-details', new CropDetails(), tokenInject)
  // app.use('/report/set-client-delta', new SetClientData())
}
module.exports = { configureOpenCommerce }
