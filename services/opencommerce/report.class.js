
const moment = require("moment");
const { postProcessRecords, dbConnections } = require("@krushal-it/ah-orm");
const { configurationJSON, assignL10NObjectOrObjectArrayToItSelf } = require("@krushal-it/common-core");
const { loadClassificationData, saveClassificationData } = require('@krushal-it/back-end-lib')
const { adjustStartDatesOfEntityStatusByCategoryAndEntities, reportQueryClassificationJoinConfiguration, generateSelectJoinClauseForClassification, generateWhereClauseForReportInnerQuery: generateWhereClause, generateLimitStringForReportInnerQuery: generateLimitString, generateSortStringForReportInnerQuery: generateSortString, identifyMandatoryColumnsForReportQuery} = require('../../utils/query/query.helper');
const { update } = require("lodash");
const lodashObject = require("lodash");

class SellableAnimalsReportOld {
  async create /*or is it update*/(data, params) {
    try {
      const returnValue = { return_code: 0 }
      const mainDBConnections = dbConnections().main
      // console.log('oc r SAR c 1, data = ', data)

      const limitString = data.size && data.size !== -1 ? `LIMIT ${data.size} OFFSET ${data.start}` : ``
      const innerQuery = `
        select * from main.animal
      `
      const countQuery = `
        select count(*) as count from (
          ${innerQuery}
        ) outerTable
      `
      const complaintResultCount = await mainDBConnections.manager.query(countQuery)
      const count = complaintResultCount[0].count

      const query = `
        select * from (
          ${innerQuery}
        ) outerTable
        ${limitString}
      `
      const complaintResult = await mainDBConnections.manager.query(query)
      const complaintResult2 = postProcessRecords(undefined, complaintResult, {})
      returnValue.count = count
      returnValue.report = complaintResult2
      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

/* class FarmersWithNoLocationRecordedReport {
  async create (data, params) {
    try {
      const returnValue = { return_code: 0 }
      const mainDBConnections = dbConnections().main
      // console.log('oc r SAR c 1, data = ', data)

      const limitString = generateLimitString(data)
      const sortString = generateSortString(data) 
      const whereClause = generateWhereClause(data) 

      const excludeBDM = (data.excludeBDM === true) ? true : false

      const innerQuery = `
        select c.customer_id,
          coalesce(c.customer_name_l10n->>'en', c.customer_name_l10n->>'ul') as customer_name,
          c.mobile_number, gcc.taluk, gcc.village, gcc.village_id, gcc.taluk_id,
          ${!excludeBDM ? 'fbdm.bdm_uuid, fbdm.bdm, fbdm.bdm_mobile, fbdm.paravet_uuid, fbdm.paravet, fbdm.paravet_mobile,' : '' }
          fciv.farmer_count_in_village,
          fcivnlr.farmer_count_in_village_with_no_location
        from main.customer c
        left join (
          select gcc.customer_id,
            coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') as taluk,
            coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') as village,
            rsv.village_id, rsv.taluk_id
          from main.customer_classification gcc, main.ref_sdtv_view rsv
          where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
          and gcc.value_reference_id = rsv.village_id
        ) gcc on gcc.customer_id = c.customer_id
        ${!excludeBDM ? " \
          left join ( \
            select gcc.customer_id, sg.bdm, sg.bdm_mobile, sg.bdm_uuid, pg.paravet, pg.paravet_mobile, pg.paravet_uuid \
            from main.customer_classification gcc \
            left join ( \
              select coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as bdm, s.mobile_number as bdm_mobile, sg.geography_id, s.staff_id bdm_uuid \
              from main.staff s, ( \
                select eg.geography_id, (min(eg.entity_uuid::text))::uuid as staff_id \
                from main.entity_geography eg \
                where eg.active = 1000100001 and eg.geography_type_id = 1000320004 \
                and eg.entity_type_id in (1000230001, 1000230005) \
                group by eg.geography_id \
              ) sg \
              where s.staff_id = sg.staff_id and s.active = 1000100001 \
            ) sg on sg.geography_id = gcc.value_reference_id \
            left join ( \
              select coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as paravet, s.mobile_number as paravet_mobile, pg.geography_id, s.staff_id as paravet_uuid \
              from main.staff s, ( \
                select eg.geography_id, (min(eg.entity_uuid::text))::uuid as staff_id \
                from main.entity_geography eg \
                where eg.active = 1000100001 and eg.geography_type_id = 1000320004 \
                and eg.entity_type_id in (1000230004) \
                group by eg.geography_id \
              ) pg \
              where s.staff_id = pg.staff_id and s.active = 1000100001 \
            ) pg on pg.geography_id = gcc.value_reference_id \
            where gcc.classifier_id = 2000000055 and gcc.active = 1000100001 \
          ) fbdm on fbdm.customer_id = c.customer_id \
        " : ''}
        left join (
          select gcc.customer_id, fgc.farmer_count_in_village
          from main.customer_classification gcc,
          (
            select gcc.value_reference_id, count(gcc.customer_id) as farmer_count_in_village
            from main.customer_classification gcc
            where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
            group by gcc.value_reference_id 
          ) fgc
          where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
          and gcc.value_reference_id = fgc.value_reference_id
        ) fciv on fciv.customer_id = c.customer_id
        left join (
          select gcc.customer_id, fwnrlbg.farmer_count_in_village_with_no_location
          from main.customer_classification gcc, (
            select count(gcc.customer_id) farmer_count_in_village_with_no_location, gcc.value_reference_id 
            from main.customer_classification gcc
            where gcc.customer_id not in (
              select customer_id
              from main.customer_classification lcc
              where lcc.active = 1000100001 and lcc.classifier_id = 2000000004
            )
            and gcc.classifier_id = 2000000055 and gcc.active = 1000100001
            group by gcc.value_reference_id 
          ) fwnrlbg
          where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
          and gcc.value_reference_id = fwnrlbg.value_reference_id 
        ) fcivnlr on fcivnlr.customer_id = c.customer_id
        where c.active = 1000100001 and c.customer_id not in (
          select customer_id
          from main.customer_classification cc
          where cc.classifier_id = 2000000004
          and cc.active = 1000100001
        )
      `

      const countQuery = `
        select count(*) as count from (
          ${innerQuery}
        ) outerTable
        ${whereClause}
      `
      const complaintResultCount = await mainDBConnections.manager.query(countQuery)
      const count = complaintResultCount[0].count

      const query = `
        select * from (
          ${innerQuery}
        ) outerTable
        ${whereClause}
        ${sortString}
        ${limitString}
      `
      const complaintResult = await mainDBConnections.manager.query(query)
      const complaintResult2 = postProcessRecords(undefined, complaintResult, {})

      const distinctVillageQuery = `
        select distinct village_id, village value, village text from (
          ${innerQuery}
        ) outerTable
      `
      // ${whereClause}
      const distinctVillageResult = await mainDBConnections.manager.query(distinctVillageQuery)
      const distinctVillageResult2 = postProcessRecords(undefined, distinctVillageResult, {})
      returnValue.village_filter_values = distinctVillageResult2

      returnValue.count = count
      returnValue.report = complaintResult2
      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async find(params) {
    try {
      const returnValue = {return_code: 0}
      const mainDBConnections = dbConnections().main
      const tableFilterData = {}
      returnValue.tableFilters = tableFilterData
      const distinctTalukQuery = `
        select distinct taluk_id, taluk_id as value, coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') as text
        from main.customer_classification cc
        inner join main.ref_sdtv_view rsv on rsv.village_id = cc.value_reference_id
        where cc.active = 1000100001 and cc.classifier_id = 2000000055
      `
      const distinctTalukFilterValuesResult = await mainDBConnections.manager.query(distinctTalukQuery)
      const distinctTalukFilterValuesResult2 = postProcessRecords(undefined, distinctTalukFilterValuesResult, {})
      tableFilterData.taluk_filter_values = distinctTalukFilterValuesResult2

      // const distinctVillageQuery = `
      //   select distinct village_id, coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') as value, coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') as text
      //   from main.customer_classification cc
      //   inner join main.ref_sdtv_view rsv on rsv.village_id = cc.value_reference_id
      //   where cc.active = 1000100001 and cc.classifier_id = 2000000055      
      // `
      // const distinctVillageFilterValuesResult = await mainDBConnections.manager.query(distinctVillageQuery)
      // const distinctVillageFilterValuesResult2 = postProcessRecords(undefined, distinctVillageFilterValuesResult, {})
      // returnValue.village_filter_values = distinctVillageFilterValuesResult2

      const staffQuery = `
        select s.staff_id as value, coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as text
        from main.staff s
        where s.active = 1000100001 and s.staff_type_id in (1000230001, 1000230005)
      `
      const staffFilterValuesResult = await mainDBConnections.manager.query(staffQuery)
      const staffFilterValuesResult2 = postProcessRecords(undefined, staffFilterValuesResult, {})
      tableFilterData.staff_filter_values = staffFilterValuesResult2


      return returnValue;
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class FarmersWithLocationRecordedReport {
  async create (data, params) {
    try {
      const returnValue = { return_code: 0 }
      const mainDBConnections = dbConnections().main
      // console.log('oc r SAR c 1, data = ', data)

      const limitString = generateLimitString(data)
      const sortString = generateSortString(data) 
      const whereClause = generateWhereClause(data) 
      const innerQuery = `
        select c.customer_id,
          coalesce(c.customer_name_l10n->>'en', c.customer_name_l10n->>'ul') as customer_name, c.mobile_number,
          (coalesce(cccc.cow_count, 0) + coalesce(bccc.buffalo_count, 0)) as number_of_adult_animals,
          greatest(
            coalesce(cccc.updated_at, '1970-01-01 00:00:00'),
            coalesce(bccc.updated_at, '1970-01-01 00:00:00')
          ) as herd_last_updated_timestamp,
          gcc1.updated_at location_updated_timestamp,
          gcc.taluk, gcc.village, gcc.village_id, gcc.taluk_id,
          fbdm.bdm, fbdm.bdm_mobile, fbdm.bdm_uuid, fbdm.paravet, fbdm.paravet_mobile, fbdm.paravet_uuid,
          fciv.farmer_count_in_village, fcivnlr.farmer_count_in_village_with_no_location
        from main.customer c
        inner join main.customer_classification gcc1 on gcc1.active = 1000100001 and gcc1.classifier_id = 2000000004 and gcc1.customer_id = c.customer_id
        left join (
          select cccc.value_int as cow_count, cccc.customer_id, cccc.updated_at
          from main.customer_classification cccc
          where cccc.active = 1000100001 and cccc.classifier_id = 2000000006
        ) cccc on cccc.customer_id = c.customer_id
        left join (
          select bccc.value_int as buffalo_count, bccc.customer_id, bccc.updated_at
          from main.customer_classification bccc
          where bccc.active = 1000100001 and bccc.classifier_id = 2000000008
        ) bccc on bccc.customer_id = c.customer_id
        left join (
          select gcc.customer_id,
            coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') as taluk,
            coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') as village,
            rsv.village_id, rsv.taluk_id
          from main.customer_classification gcc, main.ref_sdtv_view rsv
          where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
          and gcc.value_reference_id = rsv.village_id
        ) gcc on gcc.customer_id = c.customer_id
        left join (
          select gcc.customer_id, sg.bdm, sg.bdm_mobile, sg.bdm_uuid, pg.paravet, pg.paravet_mobile, pg.paravet_uuid
          from main.customer_classification gcc
          left join (
            select coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as bdm, s.mobile_number as bdm_mobile, sg.geography_id, s.staff_id as bdm_uuid
            from main.staff s, (
              select eg.geography_id, (min(eg.entity_uuid::text))::uuid as staff_id
              from main.entity_geography eg
              where eg.active = 1000100001 and eg.geography_type_id = 1000320004
              and eg.entity_type_id in (1000230001, 1000230005)
              group by eg.geography_id
            ) sg
            where s.staff_id = sg.staff_id and s.active = 1000100001
          ) sg on sg.geography_id = gcc.value_reference_id
          left join (
            select coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as paravet, s.mobile_number as paravet_mobile, pg.geography_id, s.staff_id as paravet_uuid
            from main.staff s, (
              select eg.geography_id, (min(eg.entity_uuid::text))::uuid as staff_id
              from main.entity_geography eg
              where eg.active = 1000100001 and eg.geography_type_id = 1000320004
              and eg.entity_type_id in (1000230004)
              group by eg.geography_id
            ) pg
            where s.staff_id = pg.staff_id and s.active = 1000100001
          ) pg on pg.geography_id = gcc.value_reference_id
          where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
        ) fbdm on fbdm.customer_id = c.customer_id
        left join (
          select gcc.customer_id, fgc.farmer_count_in_village
          from main.customer_classification gcc,
          (
            select gcc.value_reference_id, count(gcc.customer_id) as farmer_count_in_village
            from main.customer_classification gcc
            where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
            group by gcc.value_reference_id 
          ) fgc
          where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
          and gcc.value_reference_id = fgc.value_reference_id
        ) fciv on fciv.customer_id = c.customer_id
        left join (
          select gcc.customer_id, fwnrlbg.farmer_count_in_village_with_no_location
          from main.customer_classification gcc, (
            select count(gcc.customer_id) farmer_count_in_village_with_no_location, gcc.value_reference_id 
            from main.customer_classification gcc
            where gcc.customer_id not in (
              select customer_id
              from main.customer_classification lcc
              where lcc.active = 1000100001 and lcc.classifier_id = 2000000004
            )
            and gcc.classifier_id = 2000000055 and gcc.active = 1000100001
            group by gcc.value_reference_id 
          ) fwnrlbg
          where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
          and gcc.value_reference_id = fwnrlbg.value_reference_id 
        ) fcivnlr on fcivnlr.customer_id = c.customer_id
        where c.active = 1000100001
      `
      const countQuery = `
        select count(*) as count from (
          ${innerQuery}
        ) outerTable
        ${whereClause}
      `
      const complaintResultCount = await mainDBConnections.manager.query(countQuery)
      const count = parseInt(complaintResultCount[0].count)

      const query = `
        select * from (
          ${innerQuery}
        ) outerTable
        ${whereClause}
        ${sortString}
        ${limitString}
      `
      const complaintResult = await mainDBConnections.manager.query(query)
      const complaintResult2 = postProcessRecords(undefined, complaintResult, {})

      const distinctVillageQuery = `
        select distinct village_id, village value, village text from (
          ${innerQuery}
        ) outerTable
      `
      // ${whereClause}
      const distinctVillageResult = await mainDBConnections.manager.query(distinctVillageQuery)
      const distinctVillageResult2 = postProcessRecords(undefined, distinctVillageResult, {})

      returnValue.count = count
      returnValue.report = complaintResult2
      returnValue.village_filter_values = distinctVillageResult2
      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async find(params) {
    try {
      const returnValue = {return_code: 0}
      const tableFilterData = {}
      returnValue.tableFilters = tableFilterData
      const mainDBConnections = dbConnections().main
      const distinctTalukQuery = `
        select distinct taluk_id, taluk_id as value, coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') as text
        from main.customer_classification cc
        inner join main.ref_sdtv_view rsv on rsv.village_id = cc.value_reference_id
        where cc.active = 1000100001 and cc.classifier_id = 2000000055
      `
      const distinctTalukFilterValuesResult = await mainDBConnections.manager.query(distinctTalukQuery)
      const distinctTalukFilterValuesResult2 = postProcessRecords(undefined, distinctTalukFilterValuesResult, {})
      tableFilterData.taluk_filter_values = distinctTalukFilterValuesResult2

      // const distinctVillageQuery = `
      //   select distinct village_id, coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') as value, coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') as text
      //   from main.customer_classification cc
      //   inner join main.ref_sdtv_view rsv on rsv.village_id = cc.value_reference_id
      //   where cc.active = 1000100001 and cc.classifier_id = 2000000055      
      // `
      // const distinctVillageFilterValuesResult = await mainDBConnections.manager.query(distinctVillageQuery)
      // const distinctVillageFilterValuesResult2 = postProcessRecords(undefined, distinctVillageFilterValuesResult, {})
      // returnValue.village_filter_values = distinctVillageFilterValuesResult2

      const staffQuery = `
        select s.staff_id as value, coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as text
        from main.staff s
        where s.active = 1000100001 and s.staff_type_id in (1000230001, 1000230005)
      `
      const staffFilterValuesResult = await mainDBConnections.manager.query(staffQuery)
      const staffFilterValuesResult2 = postProcessRecords(undefined, staffFilterValuesResult, {})
      tableFilterData.staff_filter_values = staffFilterValuesResult2

      return returnValue;
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
} */

/* class DataForPotentiallySellableAnimalsReport {
  async create (data, params) {
    try {
      const returnValue = { return_code: 0 }
      const mainDBConnections = dbConnections().main
      // console.log('oc r SAR c 1, data = ', data)

      const limitString = generateLimitString(data)
      const sortString = generateSortString(data) 
      const whereClause = generateWhereClause(data) 

      const shouldRestrictToPrimaryKeyArray = (data.primaryKeysForRemainingColumns !== undefined && data.primaryKeysForRemainingColumns !== null && Array.isArray(data.primaryKeysForRemainingColumns)) ? true : false
      const forceLoadAllColumns = data.forceLoadAllColumns
      const shouldRestrictSearch = (shouldRestrictToPrimaryKeyArray !== true && forceLoadAllColumns !== true) ? true : false
      let inAnimalIdString = ""
      if (shouldRestrictToPrimaryKeyArray) {
        for (const  animalId of data.primaryKeysForRemainingColumns) {
          if (inAnimalIdString !== "") {
            inAnimalIdString = inAnimalIdString + ", "
          }
          inAnimalIdString = inAnimalIdString + "'" + animalId + "'"
        }
      }
      const inAnimalIdWhereClause = (inAnimalIdString !== '') ? ` and a.animal_id in (${inAnimalIdString})` : ''

      const mandatoryColumnsForSearch = shouldRestrictSearch ? identifyMandatoryColumnsForReportQuery(data.filters, data.sorting, data.globalSearch, data.searchConfiguration) : undefined

      const excludeBDM = (data.excludeBDM === true) ? true : false

      const cowScoreAttributeCountColumnConfiguration = [
        'coalesce(a_a_c.cow_score_attribute_count, 0) cow_score_attribute_count,',
        ' \
          left join ( \
          select count(animal_classification_id) as cow_score_attribute_count, animal_id \
          from main.animal_classification ac \
          where ac.active = 1000100001  and ac.classifier_id in (**********, 2000000198, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********) \
          group by ac.animal_id \
        ) a_a_c on a_a_c.animal_id = a.animal_id \
        '
      ]
      const cowScoreImageCountColumnConfiguration = [
        'coalesce(a_d_c.cow_score_image_count, 0) cow_score_image_count,',
        ' \
          left join ( \
            select count(document_id) as cow_score_image_count, animal_id \
            from ( \
              select ac.animal_id, d.* \
              from main.document d \
              inner join main.animal_classification ac on ac.animal_classification_id = d.entity_1_entity_uuid \
              where d.entity_1_type_id = 1000220004 and d.active = 1000100001 and d.document_type_id in (1000260016) \
              union \
              select d.entity_1_entity_uuid as animal_id, d.* \
              from main.document d \
              where d.active = 1000100001 and ( \
                d.entity_1_type_id in (1000220002, 1000220003) and d.document_type_id in (1000260002, 1000260016, 1000260001, 1000260012) \
              ) \
            ) a_d_c \
            group by a_d_c.animal_id \
          ) a_d_c on a_d_c.animal_id = a.animal_id \
        '
      ]
      const locationRecordedStatusColumnConfiguration = [
        "case when dfl_cc.value_json is not null then 'Location Recorded' else 'Location Not Recorded' end as farmer_location_recorded_status, dfl_cc.updated_at as location_updated_timestamp, ",
        ' \
          left join main.customer_classification dfl_cc on dfl_cc.active = 1000100001 and dfl_cc.classifier_id = 2000000004 and dfl_cc.customer_id = c.customer_id \
        '
      ]
      const snoozedStatusColumnConfiguration = [
        "case when sa.entity_relationship_id is not null or sf.entity_relationship_id is not null then 'Snoozed' else 'Not Snoozed' end as snooze_status,",
        ' \
          left join main.entity_relationship sa on sa.entity_relationship_type_id = 1000210014 and sa.entity_1_entity_uuid = a.animal_id \
            and sa.active = 1000100001 and sa.start_date < now() and sa.end_date > now() \
          left join main.entity_relationship sf on sf.entity_relationship_type_id = 1000210015 and sf.entity_1_entity_uuid = c.customer_id \
            and sf.active = 1000100001 and sf.start_date < now() and sf.end_date > now() \
        '
      ]
      const [selectClauseForClassification, joinClauseForClassification] = generateSelectJoinClauseForClassification(reportQueryClassificationJoinConfiguration, 'POTENTIALYSELLABLEANIMALSREPORT', undefined, mandatoryColumnsForSearch, ['animal_information_tracker_link'])

      const innerQuery = `
        select a.animal_id, a.active as animal_active_id,
          case when a.active = 1000100001 then 'Active' else 'Not Active' end as animal_active,
          ${(!shouldRestrictSearch || mandatoryColumnsForSearch.includes('cow_score_image_count')) ? cowScoreImageCountColumnConfiguration[0]: ''}
          ${(!shouldRestrictSearch || mandatoryColumnsForSearch.includes('cow_score_attribute_count')) ? cowScoreAttributeCountColumnConfiguration[0]: ''}
          c.customer_id, coalesce(c.customer_name_l10n->>'en', c.customer_name_l10n->>'ul') customer_name, c.mobile_number,
          rsv.village_id, coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') village_name,
          rsv.taluk_id, coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') taluk_name,
          ${!excludeBDM ? 's_g.bdm_uuid, s_g.bdm, s_g.bdm_mobile,' : ''}
          ${selectClauseForClassification !== undefined ? selectClauseForClassification + ', ': ''}
          ${(!shouldRestrictSearch || mandatoryColumnsForSearch.includes('snooze_status')) ? snoozedStatusColumnConfiguration[0]: ''}
          ${(!shouldRestrictSearch || mandatoryColumnsForSearch.includes('farmer_location_recorded_status') || mandatoryColumnsForSearch.includes('location_updated_timestamp')) ? locationRecordedStatusColumnConfiguration[0]: ''}
          case when (pm_ac.value_double > 0.5 and (EXTRACT(YEAR FROM age(now(), pm_ac.updated_at)) * 12 + EXTRACT(MONTH FROM age(now(), pm_ac.updated_at)) + pm_ac.value_double) < 9) then (EXTRACT(YEAR FROM age(now(), pm_ac.updated_at)) * 12 + EXTRACT(MONTH FROM age(now(), pm_ac.updated_at)) + pm_ac.value_double) else 0 end as number_of_months_pregnant
        from main.animal a
        inner join main.entity_relationship er on er.active = 1000100001 and er.entity_relationship_type_id = 1000210004
          and er.entity_2_entity_uuid = a.animal_id
        inner join main.customer c on c.active = 1000100001 and c.customer_id = er.entity_1_entity_uuid
        inner join main.customer_classification v_cc on v_cc.active = 1000100001 and c.customer_id = v_cc.customer_id
          and v_cc.classifier_id = 2000000055
        inner join main.ref_sdtv_view rsv on rsv.village_id = v_cc.value_reference_id
        ${!excludeBDM ? 
          " \
            left join ( \
              select s.staff_id as bdm_uuid, coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') bdm, \
                s.mobile_number bdm_mobile, eg.geography_id \
              from main.entity_geography eg \
              inner join main.staff s on s.staff_id = eg.entity_uuid and s.active = 1000100001 \
              where eg.entity_type_id in (1000230001, 1000230005) and eg.active = 1000100001 \
                and eg.geography_type_id = 1000320004 \
            ) s_g on s_g.geography_id = rsv.village_id \
          "
          : ''}
        ${joinClauseForClassification}
        ${(!shouldRestrictSearch || mandatoryColumnsForSearch.includes('farmer_location_recorded_status') || mandatoryColumnsForSearch.includes('location_updated_timestamp')) ? locationRecordedStatusColumnConfiguration[1]: ''}
        ${(!shouldRestrictSearch || mandatoryColumnsForSearch.includes('snooze_status')) ? snoozedStatusColumnConfiguration[1]: ''}
        ${(!shouldRestrictSearch || mandatoryColumnsForSearch.includes('cow_score_attribute_count')) ? cowScoreAttributeCountColumnConfiguration[1]: ''}
        ${(!shouldRestrictSearch || mandatoryColumnsForSearch.includes('cow_score_image_count')) ? cowScoreImageCountColumnConfiguration[1]: ''}
        left join main.animal_classification pm_ac on pm_ac.active = 1000100001 and pm_ac.classifier_id = ********** and pm_ac.animal_id = a.animal_id
        where 1 = 1
        ${(shouldRestrictToPrimaryKeyArray) ? inAnimalIdWhereClause : ''}
      `
      let count
      if (!shouldRestrictToPrimaryKeyArray) {
        const countQuery = `
          select count(*) as count from (
            ${innerQuery}
          ) outerTable
          ${whereClause}
        `
        const reportResultCount = await mainDBConnections.manager.query(countQuery)
        count = parseInt(reportResultCount[0].count)
      }

      let query
      if (shouldRestrictToPrimaryKeyArray) {
        query = `
          select * from (
            ${innerQuery}
          ) outerTable
        `
      } else {
        query = `
          select * from (
            ${innerQuery}
          ) outerTable
          ${whereClause}
          ${sortString}
          ${limitString}
        `
      }
      const reportResultUnprocessed = await mainDBConnections.manager.query(query)
      const reportResult = postProcessRecords(undefined, reportResultUnprocessed, {})

      if (!shouldRestrictToPrimaryKeyArray) {
        const distinctVillageQuery = `
          select distinct village_id, village_name value, village_name text from (
            ${innerQuery}
          ) outerTable
        `
        // ${whereClause}
        const distinctVillageResult = await mainDBConnections.manager.query(distinctVillageQuery)
        const distinctVillageResult2 = postProcessRecords(undefined, distinctVillageResult, {})
        returnValue.village_name_filter_values = distinctVillageResult2
      }

      if (!shouldRestrictToPrimaryKeyArray) {
        returnValue.count = count
      }
      if (!shouldRestrictToPrimaryKeyArray) {
        returnValue.report = reportResult
        if (shouldRestrictSearch) {
          const primaryKeysToBeQueried = []
          for (const reportRow of reportResult) {
            primaryKeysToBeQueried.push(reportRow['animal_id'])
          }
          returnValue.primaryKeysForRemainingColumns = primaryKeysToBeQueried
        }
      } else {
        const primaryKeyToRowMap = {}
        for (const reportRow of reportResult) {
          const {animal_id, ...remainingKeys } = reportRow
          primaryKeyToRowMap[reportRow['animal_id']] = remainingKeys
        }
        returnValue.rowsForPrimaryKeys = primaryKeyToRowMap
      }
      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async find(params) {
    try {
      const returnValue = {return_code: 0}    
      const tableFilterData = {}
      returnValue.tableFilters = tableFilterData
      const mainDBConnections = dbConnections().main
      const distinctTalukQuery = `
        select distinct taluk_id, taluk_id as value, coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') as text
        from main.customer_classification cc
        inner join main.ref_sdtv_view rsv on rsv.village_id = cc.value_reference_id
        where cc.active = 1000100001 and cc.classifier_id = 2000000055
      `
      const distinctTalukFilterValuesResult = await mainDBConnections.manager.query(distinctTalukQuery)
      const distinctTalukFilterValuesResult2 = postProcessRecords(undefined, distinctTalukFilterValuesResult, {})
      tableFilterData.taluk_name_filter_values = distinctTalukFilterValuesResult2

      const staffQuery = `
        select s.staff_id as value, coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as text
        from main.staff s
        where s.active = 1000100001 and s.staff_type_id in (1000230001, 1000230005)
      `
      const staffFilterValuesResult = await mainDBConnections.manager.query(staffQuery)
      const staffFilterValuesResult2 = postProcessRecords(undefined, staffFilterValuesResult, {})
      tableFilterData.staff_filter_values = staffFilterValuesResult2

      return returnValue;
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class PotentiallySellableAnimalsReport {
  async create (data, params) {
    try {
      const returnValue = { return_code: 0 }
      const mainDBConnections = dbConnections().main
      console.log('oc r PSAR c 1, data = ', data)
      const ignoreInitialContactStatusAnimals = (data.animal_selling_intent_already_expressed === true) ? true : false
      const checkForAnimalsMarkedForSale = (data.check_for_animals_marked_for_sale === true) ? true : false

      const limitString = generateLimitString(data)
      const sortString = generateSortString(data) 
      const whereClause = generateWhereClause(data)

      const shouldRestrictToPrimaryKeyArray = (data.primaryKeysForRemainingColumns !== undefined && data.primaryKeysForRemainingColumns !== null && Array.isArray(data.primaryKeysForRemainingColumns)) ? true : false
      const forceLoadAllColumns = data.forceLoadAllColumns
      const shouldRestrictSearch = (shouldRestrictToPrimaryKeyArray !== true && forceLoadAllColumns !== true) ? true : false
      let inAnimalIdString = ""
      if (shouldRestrictToPrimaryKeyArray) {
        for (const  animalId of data.primaryKeysForRemainingColumns) {
          if (inAnimalIdString !== "") {
            inAnimalIdString = inAnimalIdString + ", "
          }
          inAnimalIdString = inAnimalIdString + "'" + animalId + "'"
        }
      }
      const inAnimalIdWhereClause = (inAnimalIdString !== '') ? ` and a.animal_id in (${inAnimalIdString})` : ''

      const mandatoryColumnsForSearch = shouldRestrictSearch ? identifyMandatoryColumnsForReportQuery(data.filters, data.sorting, data.globalSearch, data.searchConfiguration) : undefined

      const excludeBDM = (data.excludeBDM === true) ? true : false

      const cowScoreAttributeCountColumnConfiguration = [
        'coalesce(a_a_c.cow_score_attribute_count, 0) cow_score_attribute_count,',
        ' \
          left join ( \
          select count(animal_classification_id) as cow_score_attribute_count, animal_id \
          from main.animal_classification ac \
          where ac.active = 1000100001  and ac.classifier_id in (**********, 2000000198, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********) \
          group by ac.animal_id \
        ) a_a_c on a_a_c.animal_id = a.animal_id \
        '
      ]
      const cowScoreImageCountColumnConfiguration = [
        'coalesce(a_d_c.cow_score_image_count, 0) cow_score_image_count,',
        ' \
          left join ( \
            select count(document_id) as cow_score_image_count, animal_id \
            from ( \
              select ac.animal_id, d.* \
              from main.document d \
              inner join main.animal_classification ac on ac.animal_classification_id = d.entity_1_entity_uuid \
              where d.entity_1_type_id = 1000220004 and d.active = 1000100001 and d.document_type_id in (1000260016) \
              union \
              select d.entity_1_entity_uuid as animal_id, d.* \
              from main.document d \
              where d.active = 1000100001 and ( \
                d.entity_1_type_id in (1000220002, 1000220003) and d.document_type_id in (1000260002, 1000260016, 1000260001, 1000260012) \
              ) \
            ) a_d_c \
            group by a_d_c.animal_id \
          ) a_d_c on a_d_c.animal_id = a.animal_id \
        '
      ]
      const locationRecordedStatusColumnConfiguration = [
        "case when dfl_cc.value_json is not null then 'Location Recorded' else 'Location Not Recorded' end as farmer_location_recorded_status, dfl_cc.updated_at as location_updated_timestamp, ",
        ' \
          left join main.customer_classification dfl_cc on dfl_cc.active = 1000100001 and dfl_cc.classifier_id = 2000000004 and dfl_cc.customer_id = c.customer_id \
        '
      ]
      const snoozedStatusColumnConfiguration = [
        "case when sa.entity_relationship_id is not null or sf.entity_relationship_id is not null then 'Snoozed' else 'Not Snoozed' end as snooze_status,",
        ' \
          left join main.entity_relationship sa on sa.entity_relationship_type_id = 1000210005 and sa.entity_1_entity_uuid = a.animal_id \
            and sa.active = 1000100001 and sa.start_date < now() and sa.end_date > now() \
          left join main.entity_relationship sf on sf.entity_relationship_type_id = 1000210006 and sf.entity_1_entity_uuid = c.customer_id \
            and sf.active = 1000100001 and sf.start_date < now() and sf.end_date > now() \
        '
      ]
      const sellingPriceColumnConfiguration = [
        " \
          coalesce((spr_ac.value_json->>'sellingPriceUpperRange')::numeric::int, 0) maximum_selling_price, \
          coalesce((spr_ac.value_json->>'sellingPriceLowerRange')::numeric::int, 0) minimum_selling_price, \
        ",
        ' \
          left join main.animal_classification spr_ac on spr_ac.active = 1000100001 \
            and spr_ac.classifier_id = 2000000226 and spr_ac.animal_id = a.animal_id \
            and spr_ac.value_json is not null \
        '
      ]

      const [selectClauseForClassification, joinClauseForClassification] = generateSelectJoinClauseForClassification(reportQueryClassificationJoinConfiguration, 'POTENTIALYSELLABLEANIMALSREPORT', undefined, mandatoryColumnsForSearch)
      console.log('oc r PSAR c 1a, selectClauseForClassification = ', selectClauseForClassification)
      console.log('oc r PSAR c 1b, joinClauseForClassification = ', joinClauseForClassification)
      const innerQuery = `
        select a.animal_id,
          case when a.active = 1000100001 then 'Active' else 'Not Active' end as animal_active,
          c.customer_id, coalesce(c.customer_name_l10n->>'en', c.customer_name_l10n->>'ul') customer_name, c.mobile_number,
          rsv.village_id, coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') village_name,
          rsv.taluk_id, coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') taluk_name,
          ${!excludeBDM ? 's_g.bdm_uuid, s_g.bdm, s_g.bdm_mobile,' : ''}
          ${selectClauseForClassification !== undefined ? selectClauseForClassification + ', ': ''}
          ${(!shouldRestrictSearch || mandatoryColumnsForSearch.includes('snooze_status')) ? snoozedStatusColumnConfiguration[0]: ''}
          ${(!shouldRestrictSearch || mandatoryColumnsForSearch.includes('farmer_location_recorded_status') || mandatoryColumnsForSearch.includes('location_updated_timestamp')) ? locationRecordedStatusColumnConfiguration[0]: ''}
          case when (pm_ac.value_double > 0.5 and (EXTRACT(YEAR FROM age(now(), pm_ac.updated_at)) * 12 + EXTRACT(MONTH FROM age(now(), pm_ac.updated_at)) + pm_ac.value_double) < 9) then (EXTRACT(YEAR FROM age(now(), pm_ac.updated_at)) * 12 + EXTRACT(MONTH FROM age(now(), pm_ac.updated_at)) + pm_ac.value_double) else 0 end as number_of_months_pregnant,
          coalesce(sas_rr.reference_id, 1000490000) as selling_animal_current_status_id,
          coalesce(coalesce(sas_rr.reference_name_l10n->>'en', sas_rr.reference_name_l10n->>'ul'), 'Not Spoken To') as selling_animal_current_status,
          ${(!shouldRestrictSearch || mandatoryColumnsForSearch.includes('minimum_selling_price') || mandatoryColumnsForSearch.includes('maximum_selling_price')) ? sellingPriceColumnConfiguration[0]: ''}
          a.active as animal_active_id
        from main.animal a
        inner join main.entity_relationship er on er.active = 1000100001 and er.entity_relationship_type_id = 1000210004
          and er.entity_2_entity_uuid = a.animal_id
        inner join main.customer c on c.active = 1000100001 and c.customer_id = er.entity_1_entity_uuid
        inner join main.customer_classification v_cc on v_cc.active = 1000100001 and c.customer_id = v_cc.customer_id
          and v_cc.classifier_id = 2000000055
        inner join main.ref_sdtv_view rsv on rsv.village_id = v_cc.value_reference_id
        ${!excludeBDM ? 
          " \
            left join ( \
              select s.staff_id as bdm_uuid, coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') bdm, \
                s.mobile_number bdm_mobile, eg.geography_id \
              from main.entity_geography eg \
              inner join main.staff s on s.staff_id = eg.entity_uuid and s.active = 1000100001 \
              where eg.entity_type_id in (1000230001, 1000230005) and eg.active = 1000100001 \
                and eg.geography_type_id = 1000320004 \
            ) s_g on s_g.geography_id = rsv.village_id \
          "
          : ''}
        ${joinClauseForClassification}
        ${(!shouldRestrictSearch || mandatoryColumnsForSearch.includes('farmer_location_recorded_status') || mandatoryColumnsForSearch.includes('location_updated_timestamp')) ? locationRecordedStatusColumnConfiguration[1]: ''}
        ${(!shouldRestrictSearch || mandatoryColumnsForSearch.includes('snooze_status')) ? snoozedStatusColumnConfiguration[1]: ''}
        ${ignoreInitialContactStatusAnimals ? 'inner' : 'left'} join main.entity_status es on es.status_category_id = 10004900 and es.entity_1_type_id = 1000220002
          and es.entity_1_entity_uuid = a.animal_id and es.start_date < now() and es.end_date > now()
          ${ignoreInitialContactStatusAnimals ? ' and es.status_id not in (1000490001) ' : ''}
        left join main.ref_reference sas_rr on es.status_id = sas_rr.reference_id
        left join main.animal_classification pm_ac on pm_ac.active = 1000100001 and pm_ac.classifier_id = ********** and pm_ac.animal_id = a.animal_id
        ${(!shouldRestrictSearch || mandatoryColumnsForSearch.includes('minimum_selling_price') || mandatoryColumnsForSearch.includes('maximum_selling_price')) ? sellingPriceColumnConfiguration[1]: ''}
        where 1 = 1
        ${(shouldRestrictToPrimaryKeyArray) ? inAnimalIdWhereClause : ''}
        ${checkForAnimalsMarkedForSale ? ' \
          and a.animal_id not in ( \
            select oi.linked_entity_uuid \
            from main.oc_item_listing oil \
            inner join main.oc_item oi on oi.oc_item_uuid = oil.listing_item_uuid \
              and oi.linked_entity_type_id = 1000460002 \
            where oil.start_date < now() and oil.end_date > now() \
            and oil.listing_item_type_id = 1000480001 \
          ) \
          ' : ''}
      `
      let count
      if (!shouldRestrictToPrimaryKeyArray) {
        const countQuery = `
          select count(*) as count from (
            ${innerQuery}
          ) outerTable
          ${whereClause}
        `
        const reportResultCount = await mainDBConnections.manager.query(countQuery)
        count = parseInt(reportResultCount[0].count)
      }

      let query
      if (shouldRestrictToPrimaryKeyArray) {
        query = `
          select * from (
            ${innerQuery}
          ) outerTable
        `
      } else {
        query = `
          select * from (
            ${innerQuery}
          ) outerTable
          ${whereClause}
          ${sortString}
          ${limitString}
        `
      }
      const reportResultUnprocessed = await mainDBConnections.manager.query(query)
      const reportResult = postProcessRecords(undefined, reportResultUnprocessed, {})

      if (!shouldRestrictToPrimaryKeyArray) {
        const distinctVillageQuery = `
          select distinct village_id, village_name value, village_name text from (
            ${innerQuery}
          ) outerTable
        `
        // ${whereClause}
        const distinctVillageResult = await mainDBConnections.manager.query(distinctVillageQuery)
        const distinctVillageResult2 = postProcessRecords(undefined, distinctVillageResult, {})      
        returnValue.village_name_filter_values = distinctVillageResult2
      }

      if (!shouldRestrictToPrimaryKeyArray) {
        returnValue.count = count
      }

      if (!shouldRestrictToPrimaryKeyArray) {
        returnValue.report = reportResult
        if (shouldRestrictSearch) {
          const primaryKeysToBeQueried = []
          for (const reportRow of reportResult) {
            primaryKeysToBeQueried.push(reportRow['animal_id'])
          }
          returnValue.primaryKeysForRemainingColumns = primaryKeysToBeQueried
        }
      } else {
        const primaryKeyToRowMap = {}
        for (const reportRow of reportResult) {
          const {animal_id, ...remainingKeys } = reportRow
          primaryKeyToRowMap[reportRow['animal_id']] = remainingKeys
        }
        returnValue.rowsForPrimaryKeys = primaryKeyToRowMap
      }
      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async find(params) {
    try {
      const returnValue = {return_code: 0}    

      const tableFilterData = {}
      returnValue.tableFilters = tableFilterData
      const mainDBConnections = dbConnections().main
      const distinctTalukQuery = `
        select distinct taluk_id, taluk_id as value, coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') as text
        from main.customer_classification cc
        inner join main.ref_sdtv_view rsv on rsv.village_id = cc.value_reference_id
        where cc.active = 1000100001 and cc.classifier_id = 2000000055
      `
      const distinctTalukFilterValuesResult = await mainDBConnections.manager.query(distinctTalukQuery)
      const distinctTalukFilterValuesResult2 = postProcessRecords(undefined, distinctTalukFilterValuesResult, {})
      tableFilterData.taluk_name_filter_values = distinctTalukFilterValuesResult2

      return returnValue;
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
} */

class SnoozeForSellableAnimals {
  async create /*or is it update*/(data, params) {
    try {
      console.log('oc SFSA c 1, data = ', data)
      const entityRelationship = {
        start_date: new Date(),
        end_date: new Date(data.snooze_till_date)
      }
      if (data.farmer_level_snooze_choice[0] === 1000105001) {
        // snooze farmer
        entityRelationship.entity_relationship_type_id = 1000210006
        entityRelationship.entity_1_entity_uuid = data.customer_id
      } else {
        // snooze animal
        entityRelationship.entity_relationship_type_id = 1000210005
        entityRelationship.entity_1_entity_uuid = data.animal_id

      }
      const returnValue = { return_code: 0 }
      const mainDBConnection = dbConnections().main
      const saveResult = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
        // const entityRelationshipRepo = transactionalEntityManager.getRepository('entity_relationship')
        // const results = await entityRelationshipRepo.save(entityRelationship)
        const entityRelationshipTableEntity = mainDBConnection.entities['entity_relationship']
        const results = await transactionalEntityManager.createQueryBuilder().insert().into(entityRelationshipTableEntity).values(entityRelationship).execute()

        const noteTableEntity = mainDBConnection.entities['note']
        const commentObject = {
          note_type_id: 1000440008,
          entity_1_type_id: 1000220002,
          note_time: new Date(),
          note: {ul: data.snooze_comments},
          entity_1_uuid: data.animal_id
        }
        const notesInsertResults = await transactionalEntityManager.createQueryBuilder().insert().into(noteTableEntity).values(commentObject).execute()

        const updateStatusDatesQueryResult = await adjustStartDatesOfEntityStatusByCategoryAndEntities(transactionalEntityManager)
      })
      returnValue.save_result = saveResult
      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class UnsnoozeForSellableAnimals {
  async create /*or is it update*/(data, params) {
    try {
      console.log('oc USFSA c 1, data = ', data)
      const animalIds = data.to_be_unsnoozed_animal_array
      let commaSeparatedAnimalIds = ''
      animalIds.forEach((animalId, index) => {
        if (commaSeparatedAnimalIds !== '') {
          commaSeparatedAnimalIds = commaSeparatedAnimalIds + ', '
        }
        commaSeparatedAnimalIds = commaSeparatedAnimalIds + "'" + animalId + "'"
      })

      const updateToStopSnoozeCustomerQuery = `
        update main.entity_relationship er
        set end_date = now()
        where er.entity_relationship_type_id = 1000210006
        and er.entity_1_entity_uuid in (
          select er.entity_1_entity_uuid
          from main.entity_relationship er
          where er.entity_relationship_type_id = 1000210004
          and er.entity_2_entity_uuid in (
            ${commaSeparatedAnimalIds}
          )
        )
      `
      console.log('oc USFSA c 2, updateToStopSnoozeCustomerQuery = ', updateToStopSnoozeCustomerQuery)

      const updateToStopSnoozeForAnimalsQuery = `
        update main.entity_relationship er
        set end_date = now()
        where er.entity_relationship_type_id = 1000210005
        and er.entity_1_entity_uuid in (
          ${commaSeparatedAnimalIds}
        )
      `
      console.log('oc USFSA c 3, updateToStopSnoozeForAnimalsQuery = ', updateToStopSnoozeForAnimalsQuery)

      const returnValue = { return_code: 0 }
      const mainDBConnection = dbConnections().main
      const saveResult = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
        console.log('oc USFSA c 4')
        const queryResult = await transactionalEntityManager.query(updateToStopSnoozeCustomerQuery)
        console.log('oc USFSA c 5, queryResult = ', queryResult)
        const queryResult2 = await transactionalEntityManager.query(updateToStopSnoozeForAnimalsQuery)
        console.log('oc USFSA c 6, queryResult2 = ', queryResult2)

        const updateStatusDatesQueryResult = await adjustStartDatesOfEntityStatusByCategoryAndEntities(transactionalEntityManager)
      })
      returnValue.save_result = saveResult
      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class UpdateAnimalSellingStatus {
  async create /*or is it update*/(data, params) {
    try {
      const returnValue = {return_code: 0}
      console.log('oc UASS c 1, data = ', data)
      // create item for animal
      // copy item UBFSfication from animal
      // copy item UBFSument
      // create item listing linking to item
      const mainDBConnection = dbConnections().main
      const saveResult = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
        try {
          // const entityStatusRepo = transactionalEntityManager.getRepository('entity_status')
          const entityStatusTableEntity = mainDBConnection.entities['entity_status']
          const newAnimalStatus = {
            status_category_id: 10004900,
            status_id: data.selling_animal_next_status[0],
            entity_1_type_id: 1000220002,
            entity_1_entity_uuid: data.animal_id,
            start_date: new Date(),
            end_date: moment().add(1, 'M').toDate()
          }

          if (data.animal_information_tracker_link) {
            await saveClassificationData(transactionalEntityManager, 'ANIMAL_CLASSIFICATION', data.animal_id, {information_tracker_link: data.animal_information_tracker_link});
          }
          console.log('oc UASS c 2, newAnimalStatus = ', newAnimalStatus)
          const newlyCreatedEntityStatus = await transactionalEntityManager.createQueryBuilder().insert().into(entityStatusTableEntity).values(newAnimalStatus).execute()

          const noteTableEntity = mainDBConnection.entities['note']
          const commentObject = {
            note_type_id: 1000440009,
            entity_1_type_id: 1000220002,
            note_time: new Date(),
            note: {ul: data.update_status_comments},
            entity_1_uuid: data.animal_id
          }
          const notesInsertResults = await transactionalEntityManager.createQueryBuilder().insert().into(noteTableEntity).values(commentObject).execute()


          console.log('oc UASS c 3, newlyCreatedEntityStatus = ', newlyCreatedEntityStatus)
          /* const queryResult = await transactionalEntityManager.query(updateEntityStatusToAdjustEndTimeForEntity2AsNull)
          console.log('oc UASS c 4, newlyCreatedEntityStatusqueryResult = ', queryResult)
          const queryResult2 = await transactionalEntityManager.query(updateEntityStatusToAdjustEndTimeForEntity2AsNotNull)
          console.log('oc UASS c 4a, newlyCreatedEntityStatusqueryResult = ', queryResult2) */
          const updateStatusDatesQueryResult = await adjustStartDatesOfEntityStatusByCategoryAndEntities(transactionalEntityManager)
          
        } catch (error) {
          console.log('oc UASS c 20, error')
          console.log('oc UASS c 20a, error = ', error)
          throw error
        }
      })
      console.log('oc MAS c 1a, saveResult = ', saveResult)
      returnValue.save_result = saveResult
      return returnValue
    } catch (error) {
      console.log('oc MAS c 10, error')
      console.log('oc MAS c 10a, error = ', error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class MarkAnimalsSellable {
  async create /*or is it update*/(data, params) {
    try {
      const returnValue = {return_code: 0}
      console.log('oc MAS c 1, data = ', data)
      // create item for animal
      // copy item classification from animal
      // copy item document
      // create item listing linking to item
      const mainDBConnection = dbConnections().main
      const saveResult = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
        try {
          const ocItemRepo = transactionalEntityManager.getRepository('oc_item')
          // const animalId = data[0]
          // data.forEach(async (animalId, index) => {
          console.log('oc MAS c 1a')
          const animalIdArray = data.animal_id_array
          console.log('oc MAS c 1b, animalIdArray = ', animalIdArray)
          for (let i = 0 ; i < animalIdArray.length ; i++) {
            console.log('oc MAS c 1c')
            const animalId = animalIdArray[i]
            console.log('oc MAS c 1d, animalId = ', animalId)
            const animalInformationQuery = `
              select a.animal_id, v_cc.customer_id, v_cc.value_reference_id as village_id,
                fdfl_cc.value_json as dairy_farm_location,
                ac_et.value_string_256 as animal_ear_tag, noc_et.value_int as number_of_calvings,
                coalesce(pr_rr.reference_name_l10n->>'en', pr_rr.reference_name_l10n->>'ul') number_of_months_pregnant,
                coalesce(ab_rr.reference_name_l10n->>'en', ab_rr.reference_name_l10n->>'ul') as animal_breed,
                coalesce(at_rr.reference_name_l10n->>'en', at_rr.reference_name_l10n->>'ul') as animal_type
              from main.animal a
              inner join main.entity_relationship er on er.active = 1000100001 and er.entity_relationship_type_id = 1000210004
                and er.entity_2_entity_uuid = a.animal_id
              inner join main.customer_classification v_cc on v_cc.active = 1000100001 and v_cc.classifier_id = 2000000055
                and v_cc.customer_id = er.entity_1_entity_uuid
              inner join main.animal_classification ac_et on ac_et.animal_id = a.animal_id and ac_et.active = 1000100001
                and ac_et.classifier_id = 2000000034 and ac_et.value_string_256 is not null
              left join main.customer_classification fdfl_cc on fdfl_cc.active = 1000100001 and fdfl_cc.classifier_id = 2000000004
                and fdfl_cc.customer_id = er.entity_1_entity_uuid
              left join main.animal_classification noc_et on noc_et.animal_id = a.animal_id and noc_et.active = 1000100001
                and noc_et.classifier_id = **********
              left join main.animal_classification pr_et on pr_et.animal_id = a.animal_id and pr_et.active = 1000100001
                and pr_et.classifier_id = 2000000049
              left join main.ref_reference pr_rr on pr_et.value_reference_id = pr_rr.reference_id    
              left join main.animal_classification ab_ac on ab_ac.animal_id = a.animal_id and ab_ac.active = 1000100001
                and ab_ac.classifier_id = 2000000036
              left join main.ref_reference ab_rr on ab_ac.value_reference_id = ab_rr.reference_id    
              left join main.animal_classification at_ac on at_ac.animal_id = a.animal_id and at_ac.active = 1000100001
                and at_ac.classifier_id = 2000000035
              left join main.ref_reference at_rr on at_ac.value_reference_id = at_rr.reference_id    
              where a.animal_id = '${animalId}'
            `
            console.log('oc MAS c 1e, animalInformationQuery = ', animalInformationQuery)
            const animalDetailsResults = await transactionalEntityManager.query(animalInformationQuery)
            console.log('oc MAS c 2, animalDetailsResults = ', animalDetailsResults)
            if (animalDetailsResults.length > 0) {
              // 20 LPD 2 calving 8 month pregnant HF
              console.log('oc MAS c 2a')
              const animalDetail = animalDetailsResults[0]
              let animalName = ''
              if (animalDetail.number_of_calvings !== null) {
                if (animalName !== '') {
                  animalName = animalName + ' '
                }
                animalName = animalName + animalDetail.number_of_calvings + ' calving'
              }
              if (animalDetail.number_of_months_pregnant !== null) {
                if (animalName !== '') {
                  animalName = animalName + ' '
                }
                animalName = animalName + animalDetail.number_of_months_pregnant + ' month pregnant'
              }
              if (animalDetail.animal_breed !== null) {
                if (animalName !== '') {
                  animalName = animalName + ' '
                }
                animalName = animalName + animalDetail.animal_breed
              }
              if (animalDetail.animal_type !== null) {
                if (animalName !== '') {
                  animalName = animalName + ' '
                }
                animalName = animalName + animalDetail.animal_type
              }
              console.log('oc MAS c 3, animalName = ', animalName)
              const itemObject = {
                oc_item_name_l10n: {ul: animalName},
                oc_item_type_id: 1000460002, 
                linked_entity_type_id: 1000460002,
                linked_entity_uuid: animalId,
                item_information: {price_range: {minimum_price: data.minimum_price, maximum_price: data.maximum_price}}
              }
              console.log('oc MAS c 4, itemObject = ', itemObject)
              const createdItem = await ocItemRepo.insert(itemObject)
              const createdOcItemUUID = createdItem.identifiers[0].oc_item_uuid
              console.log('oc MAS c 5, createdItem = ', createdItem)
              const insertIntoOCItemClassificationQuery = `
                insert into main.oc_item_classification(oc_item_uuid, classifier_id, value_int, value_reference_id, value_reference_uuid, value_string_256, value_string_2000, value_double, value_date, value_json, value_l10n, value_string_256_encrypted, value_string_2000_encrypted)
                select '${createdOcItemUUID}', classifier_id, value_int, value_reference_id, value_reference_uuid, value_string_256, value_string_2000, value_double, value_date, value_json, value_l10n, value_string_256_encrypted, value_string_2000_encrypted
                from main.animal_classification ac 
                where ac.animal_id = '${animalId}'
                and ac.active = 1000100001
                and ac.classifier_id in (2000000040, 2000000039, 2000000038, 2000000034,
                  2000000035, 2000000036, **********, 2000000043, **********, **********, **********,
                  2000000124, 2000000219)
              `
              console.log('oc MAS c 6, insertIntoOCItemClassificationQuery = ', insertIntoOCItemClassificationQuery)
              const itemClassificationEntryResults = await transactionalEntityManager.query(insertIntoOCItemClassificationQuery)
              console.log('oc MAS c 7, itemClassificationEntryResults = ', itemClassificationEntryResults)

              const nowDate = new Date()
              const itemClassificationArray = []
              if (animalDetail.customer_id) {
                const customerTypeItemClassification = {
                  oc_item_uuid: createdOcItemUUID,
                  classifier_id: 2000000216,
                  value_reference_id: 1000220001,
                }
                const customerItemClassification = {
                  oc_item_uuid: createdOcItemUUID,
                  classifier_id: 2000000217,
                  value_reference_uuid: animalDetail.customer_id,
                }
                itemClassificationArray.push(customerTypeItemClassification)
                itemClassificationArray.push(customerItemClassification)
              }
              if (animalDetail.village_id) {
                const itemVillageItemClassification = {
                  oc_item_uuid: createdOcItemUUID,
                  classifier_id: 2000000218,
                  value_reference_id: animalDetail.village_id,
                }
                itemClassificationArray.push(itemVillageItemClassification)
              }
              if (animalDetail.dairy_farm_location && animalDetail.dairy_farm_location !== null) {
                const itemLocationClassification = {
                  oc_item_uuid: createdOcItemUUID,
                  classifier_id: 2000000222,
                  value_json: animalDetail.dairy_farm_location,
                }
                itemClassificationArray.push(itemLocationClassification)
              }
              console.log('oc MAS c 8, itemClassificationArray = ', itemClassificationArray)
              const itemClassificationTableEntity = mainDBConnection.entities['oc_item_classification']
              const itemClassificationCreationResult = await transactionalEntityManager.createQueryBuilder().insert().into(itemClassificationTableEntity).values(itemClassificationArray).execute()
              console.log('oc MAS c 8, itemClassificationCreationResult = ', itemClassificationCreationResult)

              const ocItemListingRepo = transactionalEntityManager.getRepository('oc_item_listing')
              const ocItemListingObject = {
                listing_item_type_id: 1000480001,
                listing_item_uuid: createdOcItemUUID,
                start_date: nowDate,
                end_date: data.allow_sale_till_date,
                item_listing_information: {price_range: {minimum_price: data.minimum_price, maximum_price: data.maximum_price}}
              }
              console.log('oc MAS c 8, ocItemListingObject = ', ocItemListingObject)
              const createdItemListing = await ocItemListingRepo.insert(ocItemListingObject)
              console.log('oc MAS c 9, createdItemListing = ', createdItemListing)
              const createdOcItemListingUUID = createdItemListing.identifiers[0].oc_item_listing_uuid
              console.log('oc MAS c 10, createdOcItemListingUUID = ', createdOcItemListingUUID)
              const insertIntoOcItemDocumentQuery = `
                insert into main.document (document_type_id, document_name_l10n,
                  entity_1_type_id, entity_1_entity_uuid,
                  client_document_information, document_information, document_meta_data_information)
                select document_type_id, document_name_l10n,
                  1000220005, '${createdOcItemListingUUID}',
                  client_document_information, document_information, document_meta_data_information 
                from main.document d
                where d.active = 1000100001 and d.entity_1_type_id = 1000220002 and d.entity_1_entity_uuid = '${animalId}'
              `
              console.log('oc MAS c 11, insertIntoOcItemDocumentQuery = ', insertIntoOcItemDocumentQuery)
              const itemDocumentEntryResults = await transactionalEntityManager.query(insertIntoOcItemDocumentQuery)

              const noteTableEntity = mainDBConnection.entities['note']
              const commentObject = {
                note_type_id: 1000440010,
                entity_1_type_id: 1000220005,
                note_time: new Date(),
                note: {ul: data.mark_for_sale_comments},
                entity_1_uuid: createdOcItemListingUUID
              }
              const notesInsertResults = await transactionalEntityManager.createQueryBuilder().insert().into(noteTableEntity).values(commentObject).execute()
    
              console.log('oc MAS c 12, itemDocumentEntryResults = ', itemDocumentEntryResults)
            } else {
              console.log('oc MAS c 11')
              returnValue.return_code = -1001
            }
          }// })
        } catch (error) {
          console.log('oc MAS c 20, error')
          console.log('oc MAS c 20a, error = ', error)
          throw error
        }
      })
      console.log('oc MAS c 1a, saveResult = ', saveResult)
      returnValue.save_result = saveResult
      return returnValue
    } catch (error) {
      console.log('oc MAS c 10, error')
      console.log('oc MAS c 10a, error = ', error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class SellableAnimalsReport {
  async create /*or is it update*/(data, params) {
    try { 
      const returnValue = { return_code: 0 }
      const mainDBConnections = dbConnections().main
      // console.log('oc r SAR c 1, data = ', data)
      const ignoreInitialContactStatusAnimals = (data.animal_selling_intent_already_expressed === true) ? true : false
      const checkForAnimalsMarkedForSale = (data.check_for_animals_marked_for_sale === true) ? true : false

      const limitString = generateLimitString(data)
      const sortString = generateSortString(data) 
      const whereClause = generateWhereClause(data) 
      const [selectClauseForClassification, joinClauseForClassification] = generateSelectJoinClauseForClassification(reportQueryClassificationJoinConfiguration, 'SELLABLEANIMALSREPORT')
      console.log('oc r SAR c 2, selectClauseForClassification = ', selectClauseForClassification)
      console.log('oc r SAR c 2, joinClauseForClassification = ', joinClauseForClassification)
      const innerQuery = `
        select oil.oc_item_listing_uuid, oil.start_date, oil.end_date as sellable_till,
          coalesce((oil.item_listing_information->'price_range'->'minimum_price')::int, 0) as minimum_price,
          coalesce((oil.item_listing_information->'price_range'->'maximum_price')::int, 0) as maximum_price,
          oi.oc_item_uuid as item_linked_entity_id,
          et_ic.value_string_256 as animal_ear_tag,
          i_s.customer_id, i_s.customer_name, i_s.mobile_number,
          rsv.village_id, coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') village_name,
          rsv.taluk_id, coalesce(rsv.taluk_name_l10n ->>'en', rsv.taluk_name_l10n ->>'ul') taluk_name
          ${(selectClauseForClassification !== undefined && selectClauseForClassification !== '') ? ', ' : ''} ${selectClauseForClassification ?  selectClauseForClassification : ' '}
        from main.oc_item_listing oil
        inner join main.oc_item oi on oi.oc_item_uuid = oil.listing_item_uuid and oi.linked_entity_type_id = 1000460002 and oi.active = 1000100001
        inner join main.oc_item_classification st_ic on st_ic.oc_item_uuid = oi.oc_item_uuid and st_ic.active = 1000100001 and st_ic.classifier_id = 2000000216 
          and st_ic.value_reference_id = 1000220001
        inner join main.oc_item_classification si_ic on si_ic.oc_item_uuid = oi.oc_item_uuid and si_ic.active = 1000100001 and si_ic.classifier_id = 2000000217
        inner join main.oc_item_classification si_l on si_l.oc_item_uuid = oi.oc_item_uuid and si_l.active = 1000100001 and si_l.classifier_id = 2000000218
        inner join main.oc_item_classification et_ic on et_ic.oc_item_uuid = oi.oc_item_uuid and et_ic.active = 1000100001
          and et_ic.classifier_id = 2000000034 and et_ic.value_string_256 is not null
        inner join (
          select c.customer_id, coalesce(c.customer_name_l10n->>'en', c.customer_name_l10n->>'ul') customer_name, c.mobile_number
          from main.customer c
          where c.active = 1000100001 and c.customer_type_id in (1000220001)
        ) i_s on i_s.customer_id = si_ic.value_reference_uuid
        inner join main.ref_sdtv_view rsv on rsv.village_id = si_l.value_reference_id
        ${joinClauseForClassification}
        where oil.active = 1000100001
        and oil.listing_item_type_id = 1000480001
      `
      const countQuery = `
        select count(*) as count from (
          ${innerQuery}
        ) outerTable
        ${whereClause}
      `
      const complaintResultCount = await mainDBConnections.manager.query(countQuery)
      const count = parseInt(complaintResultCount[0].count)

      const query = `
        select * from (
          ${innerQuery}
        ) outerTable
        ${whereClause}
        ${sortString}
        ${limitString}
      `
      const complaintResult = await mainDBConnections.manager.query(query)
      const complaintResult2 = postProcessRecords(undefined, complaintResult, {})

      const distinctVillageQuery = `
        select distinct village_id, village_name value, village_name text from (
          ${innerQuery}
        ) outerTable
      `
      // ${whereClause}
      const distinctVillageResult = await mainDBConnections.manager.query(distinctVillageQuery)
      const distinctVillageResult2 = postProcessRecords(undefined, distinctVillageResult, {})
      returnValue.village_name_filter_values = distinctVillageResult2

      returnValue.count = count
      returnValue.report = complaintResult2
      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async find(params) {
    try {
      const returnValue = {return_code: 0}    
      
      const tableFilterData = {}
      returnValue.tableFilters = tableFilterData
      const mainDBConnections = dbConnections().main
      const distinctTalukQuery = `
        select distinct taluk_id, taluk_id as value, coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') as text
        from main.customer_classification cc
        inner join main.ref_sdtv_view rsv on rsv.village_id = cc.value_reference_id
        where cc.active = 1000100001 and cc.classifier_id = 2000000055
      `
      const distinctTalukFilterValuesResult = await mainDBConnections.manager.query(distinctTalukQuery)
      const distinctTalukFilterValuesResult2 = postProcessRecords(undefined, distinctTalukFilterValuesResult, {})
      tableFilterData.taluk_name_filter_values = distinctTalukFilterValuesResult2
      
      return returnValue;
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class ItemDetailByItemListingId {
  async create /*or is it update*/(data, params) {
    try {
      const returnValue = { return_code: 0 }
      const mainDBConnections = dbConnections().main
      console.log('oc r IDBILI c 1, data = ', data)
      const itemListingUUID = data.oc_item_listing_uuid
      const [selectClauseForClassification, joinClauseForClassification] = generateSelectJoinClauseForClassification(reportQueryClassificationJoinConfiguration, 'FARMERSWHOMAYWANTTOBUYITEMECTION')

      const query = `
        select oil.oc_item_listing_uuid, oi.oc_item_uuid, oi.oc_item_name_l10n->>'ul' as item_name,
          coalesce((oil.item_listing_information->'price_range'->'minimum_price')::int, 0) as minimum_price,
          coalesce((oil.item_listing_information->'price_range'->'maximum_price')::int, 0) as maximum_price,
          oi.linked_entity_type_id, oi.linked_entity_uuid,
          ${selectClauseForClassification}
        from main.oc_item_listing oil
        inner join main.oc_item oi on oi.linked_entity_type_id = 1000460002 and oi.active = 1000100001 and oi.oc_item_uuid = oil.listing_item_uuid
        ${joinClauseForClassification}
        where oil.listing_item_type_id = 1000480001 and oil.active = 1000100001 and oil.oc_item_listing_uuid = '${itemListingUUID}'
      `
      const itemDetailByItemListingIdQueryResult = await mainDBConnections.manager.query(query)
      const itemDetailByItemListingIdQueryResult2 = postProcessRecords(undefined, itemDetailByItemListingIdQueryResult, {})

      returnValue.result = itemDetailByItemListingIdQueryResult2[0]
      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class PotentialBuyerFarmersForAnimalReport {
  async create /*or is it update*/(data, params) {
    try {
      const returnValue = { return_code: 0 }
      const mainDBConnections = dbConnections().main
      // console.log('oc r SAR c 1, data = ', data)
      let ocItemListingUUID
      if (data.filters && data.filters.oc_item_listing_uuid && Array.isArray(data.filters.oc_item_listing_uuid) && data.filters.oc_item_listing_uuid.length > 0) {
        ocItemListingUUID = data.filters.oc_item_listing_uuid[0]
      }

      if (data.pageParams && data.pageParams.filterParams &&  data.pageParams.filterParams.oc_item_listing_uuid && Array.isArray(data.pageParams.filterParams.oc_item_listing_uuid) && data.pageParams.filterParams.oc_item_listing_uuid.length > 0) {
        ocItemListingUUID = data.pageParams.filterParams.oc_item_listing_uuid[0]
      }

      if (!ocItemListingUUID) {
        throw new Error('Call not allowed without specific animal')
      }

      const ignoreInitialContactStatusFarmers = (data.farmer_buying_intent_already_expressed === true) ? true : false

      const limitString = generateLimitString(data)
      const sortString = generateSortString(data) 
      const whereClause = generateWhereClause(data)

      const [selectClauseForClassification, joinClauseForClassification] = generateSelectJoinClauseForClassification(reportQueryClassificationJoinConfiguration, 'POTENTIALBUYERFARMERSFORANIMALREPORTBUYERFARMERSECTION')

      const minimumDistanceInKM = (configurationJSON().OPEN_COMMERCE?.CATTLE_EXCHANGE?.BUYER_SELLER_MINIMUM_DISTANCE_IN_KM) ? configurationJSON().OPEN_COMMERCE.CATTLE_EXCHANGE.BUYER_SELLER_MINIMUM_DISTANCE_IN_KM : 25
      const maximumDistanceInKM = (configurationJSON().OPEN_COMMERCE?.CATTLE_EXCHANGE?.BUYER_SELLER_MAXIMUM_DISTANCE_IN_KM) ? configurationJSON().OPEN_COMMERCE.CATTLE_EXCHANGE.BUYER_SELLER_MAXIMUM_DISTANCE_IN_KM : 100
      const innerQuery = `
        with seller_farmer_location as (
          select oil.oc_item_listing_uuid,
            cast(coalesce(il_ic.value_json->>'latitude', '0') as double precision) lat,
            cast(coalesce(il_ic.value_json->>'longitude', '0') as double precision) as lng
          from main.oc_item_listing oil
          inner join main.oc_item oi on oi.oc_item_uuid = oil.listing_item_uuid
            and oi.linked_entity_type_id = 1000460002 and oi.active = 1000100001
          inner join main.oc_item_classification il_ic on il_ic.oc_item_uuid = oi.oc_item_uuid and il_ic.active = 1000100001
            and il_ic.classifier_id = 2000000222
          where oil.oc_item_listing_uuid = '${ocItemListingUUID}'
          and oil.active = 1000100001 and oil.listing_item_type_id = 1000480001
        )
        , buyer_farmer_location as (
          select *
          from (
            select l_cc.customer_id, cast(coalesce(l_cc.value_json->>'latitude', '0') as double precision) lat,
              cast(coalesce(l_cc.value_json->>'longitude', '0') as double precision) as lng,
              coalesce(c.customer_name_l10n->>'en', c.customer_name_l10n->>'ul') as customer_name, c.mobile_number,
              coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') as village_name, rsv.village_id,
              coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') as taluk_name, rsv.taluk_id,
              f_sd.earliest_subscription_date as with_krushal_since,
              ${selectClauseForClassification},
              case when sa.entity_relationship_id is not null or sf.entity_relationship_id is not null then 'Snoozed' else 'Not Snoozed' end as snooze_status,
              coalesce(bfs_rr.reference_id, 1000710001) as buying_farmer_current_status_id,
              coalesce(coalesce(bfs_rr.reference_name_l10n->>'en', bfs_rr.reference_name_l10n->>'ul'), 'Not Spoken To') as buying_farmer_current_status
            from main.customer_classification l_cc
            inner join main.customer c on c.customer_id = l_cc.customer_id
              and c.active = 1000100001 and c.customer_type_id in (1000220001)
            inner join main.customer_classification v_cc on v_cc.active = 1000100001
              and v_cc.classifier_id = 2000000055 and v_cc.customer_id = c.customer_id 
            inner join main.ref_sdtv_view rsv on rsv.village_id = v_cc.value_reference_id
            left join (
              select c.customer_id, min(sd_ac.value_date) as earliest_subscription_date
              from main.customer c
              inner join main.entity_relationship er on er.active = 1000100001 and er.entity_relationship_type_id = 1000210004
                and er.entity_1_entity_uuid = c.customer_id 
              inner join main.animal a on a.animal_id = er.entity_2_entity_uuid 
              inner join main.animal_classification sd_ac on sd_ac.active = 1000100001
                and sd_ac.animal_id = a.animal_id and sd_ac.classifier_id = 2000000124
              where c.active = 1000100001 and c.customer_type_id in (1000220001)
              group by c.customer_id
            ) f_sd on f_sd.customer_id = c.customer_id
            ${joinClauseForClassification}
            left join main.entity_relationship sa on sa.entity_relationship_type_id = 1000210007
              and sa.entity_1_entity_uuid = c.customer_id and sa.entity_2_entity_uuid = '${ocItemListingUUID}'
              and sa.active = 1000100001 and sa.start_date < now() and sa.end_date > now()
            left join main.entity_relationship sf on sf.entity_relationship_type_id = 1000210008
              and sf.entity_1_entity_uuid = c.customer_id  
              and sf.active = 1000100001 and sf.start_date < now() and sf.end_date > now()
            ${ignoreInitialContactStatusFarmers ? 'inner' : 'left'} join main.entity_status es on es.status_category_id = 10007100
              and es.entity_1_type_id = 1000220001 and es.entity_1_entity_uuid = c.customer_id
              and es.entity_2_type_id = 1000220005 and es.entity_2_entity_uuid = '${ocItemListingUUID}'
              and es.start_date < now() and es.end_date > now()
              ${ignoreInitialContactStatusFarmers ? ' and es.status_id not in (1000710001, 1000710002, 1000710003) ' : ''}
            left join main.ref_reference bfs_rr on es.status_id = bfs_rr.reference_id
            where l_cc.active = 1000100001
              and l_cc.classifier_id = 2000000004
          ) f where f.lat != 0 and f.lng != 0
        )
        select sfl.oc_item_listing_uuid, bfl.customer_id,
          (earth_distance(ll_to_earth(sfl.lat, sfl.lng), ll_to_earth(bfl.lat,bfl.lng))/1000)::integer as distance_from_seller_farmer,
          bfl.customer_name, bfl.mobile_number, bfl.village_id, bfl.village_name, bfl.taluk_id, bfl.taluk_name, bfl.with_krushal_since,
          (bfl.number_of_adult_cows + bfl.number_of_cow_calves + bfl.number_of_buffalo_calves + bfl.number_of_adult_buffaloes) as number_of_adult_animals,
          bfl.type_of_dairy_farm,
          bfl.snooze_status, bfl.buying_farmer_current_status_id, bfl.buying_farmer_current_status
        from seller_farmer_location sfl, buyer_farmer_location bfl
        where 1 = 1
      `
      const countQuery = `
        select count(*) as count from (
          ${innerQuery}
        ) outerTable
        ${whereClause}
      `
      const complaintResultCount = await mainDBConnections.manager.query(countQuery)
      const count = parseInt(complaintResultCount[0].count)

      const query = `
        select * from (
          ${innerQuery}
        ) outerTable
        ${whereClause}
        ${sortString}
        ${limitString}
      `
      const complaintResult = await mainDBConnections.manager.query(query)
      const complaintResult2 = postProcessRecords(undefined, complaintResult, {})

      const distinctVillageQuery = `
        select distinct village_id, village_name value, village_name text from (
          ${innerQuery}
        ) outerTable
      `
      // ${whereClause}
      const distinctVillageResult = await mainDBConnections.manager.query(distinctVillageQuery)
      const distinctVillageResult2 = postProcessRecords(undefined, distinctVillageResult, {})
      returnValue.village_name_filter_values = distinctVillageResult2

      returnValue.count = count
      returnValue.report = complaintResult2
      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async find(params) {
    try {
      const returnValue = {return_code: 0}    
      
      const tableFilterData = {}
      returnValue.tableFilters = tableFilterData
      const mainDBConnections = dbConnections().main
      const distinctTalukQuery = `
        select distinct taluk_id, taluk_id as value, coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') as text
        from main.customer_classification cc
        inner join main.ref_sdtv_view rsv on rsv.village_id = cc.value_reference_id
        where cc.active = 1000100001 and cc.classifier_id = 2000000055
      `
      const distinctTalukFilterValuesResult = await mainDBConnections.manager.query(distinctTalukQuery)
      const distinctTalukFilterValuesResult2 = postProcessRecords(undefined, distinctTalukFilterValuesResult, {})
      tableFilterData.taluk_name_filter_values = distinctTalukFilterValuesResult2

      return returnValue;
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class SnoozeForBuyerFarmers {
  async create /*or is it update*/(data, params) {
    try {
      console.log('oc SFSA c 1, data = ', data)
      const entityRelationship = {
        start_date: new Date(),
        end_date: new Date(data.snooze_till_date)
      }
      if (data.farmer_level_snooze_choice[0] === 1000105001) {
        // snooze farmer
        entityRelationship.entity_relationship_type_id = 1000210008
        entityRelationship.entity_1_entity_uuid = data.customer_id
      } else {
        // snooze farmer for animal
        entityRelationship.entity_relationship_type_id = 1000210007
        entityRelationship.entity_2_entity_uuid = data.oc_item_listing_uuid
        entityRelationship.entity_1_entity_uuid = data.customer_id

      }
      const returnValue = { return_code: 0 }
      const mainDBConnection = dbConnections().main
      const saveResult = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
        const entityRelationshipTableEntity = mainDBConnection.entities['entity_relationship']
        const results = await transactionalEntityManager.createQueryBuilder().insert().into(entityRelationshipTableEntity).values(entityRelationship).execute()

        const noteTableEntity = mainDBConnection.entities['note']
        const commentObject = {
          note_type_id: 1000440011,
          note_time: new Date(),
          note: {ul: data.snooze_comments},
          entity_1_type_id: 1000220005,
          entity_1_uuid: data.oc_item_listing_uuid,
          entity_2_type_id: 1000220001,
          entity_2_uuid: data.customer_id,
        }
        const notesInsertResults = await transactionalEntityManager.createQueryBuilder().insert().into(noteTableEntity).values(commentObject).execute()
        const updateStatusDatesQueryResult = await adjustStartDatesOfEntityStatusByCategoryAndEntities(transactionalEntityManager)
      })
      returnValue.save_result = saveResult
      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class UpdateBuyingFarmerStatus {
  async create /*or is it update*/(data, params) {
    try {
      const returnValue = {return_code: 0}
      console.log('oc UBFS c 1, data = ', data)
      // create item for animal
      // copy item classification from animal
      // copy item document
      // create item listing linking to item
      const mainDBConnection = dbConnections().main
      // let existingPotentialBuyerToAnimalERId
      /* if (data.buying_farmer_next_status[0] === 1000710003) {
        const queryToCheckIfERForAnimalToPotentialBuyerExists = `
          select er.entity_relationship_id
          from main.entity_relationship er
          where er.active = 1000100001 and er.entity_relationship_type_id = 1000210009
            and er.entity_1_entity_uuid = '${data.customer_id}'
            and er.entity_2_entity_uuid = '${data.oc_item_listing_uuid}'
            and er.start_date < now() and er.end_date > now()
        `
        const queryToCheckIfERForAnimalToPotentialBuyerExistsResult = await mainDBConnection.manager.query(queryToCheckIfERForAnimalToPotentialBuyerExists)
        if (queryToCheckIfERForAnimalToPotentialBuyerExistsResult.length > 0) {
          existingPotentialBuyerToAnimalERId = queryToCheckIfERForAnimalToPotentialBuyerExistsResult[0].entity_relationship_id
        }
      } */
      const saveResult = await mainDBConnection.manager.transaction(
        async (transactionalEntityManager) => {
          try {
            const nowDate = new Date()
            const endDate = moment().add(1, 'M').toDate()
            const newFarmerStatus = {
              status_category_id: 10007100,
              status_id: data.buying_farmer_next_status[0],
              entity_1_type_id: 1000220001,
              entity_1_entity_uuid: data.customer_id,
              entity_2_type_id: 1000220005,
              entity_2_entity_uuid: data.oc_item_listing_uuid,
              start_date: nowDate,
              end_date: endDate
            }
            console.log('oc UBFS c 2, newFarmerStatus = ', newFarmerStatus)
            const entityStatusTableEntity = mainDBConnection.entities['entity_status']
            const newlyCreatedEntityStatus = await transactionalEntityManager.createQueryBuilder().insert().into(entityStatusTableEntity).values(newFarmerStatus).execute()

            const noteTableEntity = mainDBConnection.entities['note']
            const commentObject = {
              note_type_id: 1000440012,
              note_time: new Date(),
              note: {ul: data.update_status_comments},
              entity_1_type_id: 1000220005,
              entity_1_uuid: data.oc_item_listing_uuid,
              entity_2_type_id: 1000220001,
              entity_2_uuid: data.customer_id,
            }
            const notesInsertResults = await transactionalEntityManager.createQueryBuilder().insert().into(noteTableEntity).values(commentObject).execute()

            console.log('oc UBFS c 3, newlyCreatedEntityStatus = ', newlyCreatedEntityStatus)
            /* const queryResult = await transactionalEntityManager.query(updateEntityStatusToAdjustEndTimeForEntity2AsNull)
            console.log('oc UBFS c 4, newlyCreatedEntityStatusqueryResult = ', queryResult)
            const queryResult2 = await transactionalEntityManager.query(updateEntityStatusToAdjustEndTimeForEntity2AsNotNull)
            console.log('oc UBFS c 4a, newlyCreatedEntityStatusqueryResult = ', queryResult2) */
            const updateStatusDatesQueryResult = await adjustStartDatesOfEntityStatusByCategoryAndEntities(transactionalEntityManager)
            // if new status is 1000710003, create a new er as well with type 1000210009
            // ensure that there is no existing er. if it is there, update end date to next month
            /* if (data.buying_farmer_next_status[0] === 1000710003) {
              const entityRelationshipRepo = transactionalEntityManager.getRepository('entity_relationship')
              if (existingPotentialBuyerToAnimalERId) {
                const updateResult = await transactionalEntityManager
                  .createQueryBuilder()
                  .update(entityRelationshipRepo)
                  .set({ end_date: endDate})
                  .where("entity_relationship_id = :id", { id: existingPotentialBuyerToAnimalERId })
                  .execute()
                console.log('oc UBFS c 5, updateResult = ', updateResult)
              } else {
                const newEntityRelationship = {
                  entity_relationship_type_id: 1000210009,
                  entity_1_entity_uuid: data.customer_id,
                  entity_2_entity_uuid: data.oc_item_listing_uuid,
                  start_date: nowDate,
                  end_date: endDate
                }
                // save
                const newlyCreatedEntityStatus = await entityRelationshipRepo.insert(newEntityRelationship)
                console.log('oc UBFS c 6, newlyCreatedEntityStatus = ', newlyCreatedEntityStatus)
              }
            } */
            
          } catch (error) {
            console.log('oc UBFS c 20, error')
            console.log('oc UBFS c 20a, error = ', error)
            throw error
          }
        }
      )
      console.log('oc UBFS c 1a, saveResult = ', saveResult)
      returnValue.save_result = saveResult
      return returnValue
    } catch (error) {
      console.log('oc UBFS c 10, error')
      console.log('oc UBFS c 10a, error = ', error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class FarmersWhoMayWantToBuyAnimals {
  async create /*or is it update*/(data, params) {
    try {
      const returnValue = { return_code: 0 }
      const mainDBConnections = dbConnections().main
      // console.log('oc r SAR c 1, data = ', data)
      const limitString = generateLimitString(data)
      const sortString = generateSortString(data) 
      const whereClause = generateWhereClause(data)
      const [selectClauseForClassificationForCustomer, joinClauseForClassificationForCustomer] = generateSelectJoinClauseForClassification(reportQueryClassificationJoinConfiguration, 'FARMERSWHOMAYWANTTOBUYFARMERSECTION', '1')
      const [selectClauseForClassificationForItem, joinClauseForClassificationForItem] = generateSelectJoinClauseForClassification(reportQueryClassificationJoinConfiguration, 'FARMERSWHOMAYWANTTOBUYITEMECTION', '2')
      
      const innerQuery = `
        select es.entity_status_uuid, c.customer_id,
          case when sa.entity_relationship_id is not null or sf.entity_relationship_id is not null then 'Snoozed' else 'Not Snoozed' end as snooze_status,
          coalesce(bfs_rr.reference_id, 1000710001) as buying_farmer_current_status_id,
          coalesce(coalesce(bfs_rr.reference_name_l10n->>'en', bfs_rr.reference_name_l10n->>'ul'), 'Not Spoken To') as buying_farmer_current_status,
          coalesce(c.customer_name_l10n->>'en', c.customer_name_l10n->>'ul') customer_name, c.mobile_number,
          rsv.village_id, coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') village_name,
          rsv.taluk_id, coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') taluk_name,
          f_sd.earliest_subscription_date as with_krushal_since,
          ${selectClauseForClassificationForCustomer},
          oil.oc_item_listing_uuid, ${selectClauseForClassificationForItem}
        from main.entity_status es
        inner join main.ref_reference bfs_rr on bfs_rr.reference_id = es.status_id
        inner join main.customer c on c.customer_id = es.entity_1_entity_uuid
          and c.active = 1000100001 and c.customer_type_id in (1000220001)
        inner join main.customer_classification v_cc on v_cc.active = 1000100001
          and v_cc.customer_id = c.customer_id and v_cc.classifier_id = 2000000055
        inner join main.ref_sdtv_view rsv on rsv.village_id = v_cc.value_reference_id 
        left join (
          select c.customer_id, min(sd_ac.value_date) as earliest_subscription_date
          from main.customer c
          inner join main.entity_relationship er on er.active = 1000100001 and er.entity_relationship_type_id = 1000210004
            and er.entity_1_entity_uuid = c.customer_id 
          inner join main.animal a on a.animal_id = er.entity_2_entity_uuid 
          inner join main.animal_classification sd_ac on sd_ac.active = 1000100001
            and sd_ac.animal_id = a.animal_id and sd_ac.classifier_id = 2000000124
          where c.active = 1000100001 and c.customer_type_id in (1000220001)
          group by c.customer_id
        ) f_sd on f_sd.customer_id = c.customer_id
        ${joinClauseForClassificationForCustomer}
        inner join main.oc_item_listing oil on oil.oc_item_listing_uuid = es.entity_2_entity_uuid and oil.active = 1000100001
          and oil.listing_item_type_id = 1000480001
        inner join main.oc_item oi on oi.active = 1000100001 and oi.linked_entity_type_id = 1000460002 and oi.oc_item_uuid = oil.listing_item_uuid 
        /*inner join main.entity_relationship cio_er on cio_er.active = 1000100001 and cio_er.entity_relationship_type_id = 1000210009
        	and cio_er.entity_1_entity_uuid != c.customer_id and cio_er.entity_2_entity_uuid != oi.oc_item_uuid*/
        ${joinClauseForClassificationForItem}
        left join main.entity_relationship sa on sa.entity_relationship_type_id = 1000210007
          and sa.entity_1_entity_uuid = c.customer_id and sa.entity_2_entity_uuid = oil.oc_item_listing_uuid
          and sa.active = 1000100001 and sa.start_date < now() and sa.end_date > now()
        left join main.entity_relationship sf on sf.entity_relationship_type_id = 1000210008
          and sf.entity_1_entity_uuid = c.customer_id  
          and sf.active = 1000100001 and sf.start_date < now() and sf.end_date > now()
        where es.status_category_id = 10007100 and es.active = 1000100001
          and es.status_id in (1000710003, 1000710004, 1000710005)
          and es.start_date < now() and es.end_date > now()
          and es.entity_1_type_id = 1000220001 and es.entity_2_type_id = 1000220005
          and concat(c.customer_id, '~~', oi.oc_item_uuid) not in (
            select concat(oo.buyer_entity_uuid, '~~', cio_er.entity_2_entity_uuid)
            from main.oc_order oo
            inner join main.entity_relationship cio_er on cio_er.active = 1000100001 and cio_er.entity_relationship_type_id = 1000210009
            where oo.active = 1000100001 and oo.order_category_id = 1000730001 and oo.buyer_entity_type_id = 1000220001
          )
      `
      const countQuery = `
        select count(*) as count from (
          ${innerQuery}
        ) outerTable
        ${whereClause}
      `
      const complaintResultCount = await mainDBConnections.manager.query(countQuery)
      const count = parseInt(complaintResultCount[0].count)

      const query = `
        select *, (number_of_adult_cows + number_of_cow_calves + number_of_adult_buffaloes + number_of_buffalo_calves) as number_of_adult_animals from (
          ${innerQuery}
        ) outerTable
        ${whereClause}
        ${sortString}
        ${limitString}
      `
      const complaintResult = await mainDBConnections.manager.query(query)
      const complaintResult2 = postProcessRecords(undefined, complaintResult, {})

      returnValue.count = count
      returnValue.report = complaintResult2
      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async find(params) {
    try {
      const returnValue = {return_code: 0}    
      const mainDBConnections = dbConnections().main
      // const distinctTalukQuery = `
      //   select distinct taluk_id, taluk_id as value, coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') as text
      //   from main.customer_classification cc
      //   inner join main.ref_sdtv_view rsv on rsv.village_id = cc.value_reference_id
      //   where cc.active = 1000100001 and cc.classifier_id = 2000000055
      // `
      // const distinctTalukFilterValuesResult = await mainDBConnections.manager.query(distinctTalukQuery)
      // const distinctTalukFilterValuesResult2 = postProcessRecords(undefined, distinctTalukFilterValuesResult, {})
      // returnValue.taluk_name_filter_values = distinctTalukFilterValuesResult2

      /* 
      // const distinctVillageQuery = `
      //   select distinct village_id, coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') as value, coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') as text
      //   from main.customer_classification cc
      //   inner join main.ref_sdtv_view rsv on rsv.village_id = cc.value_reference_id
      //   where cc.active = 1000100001 and cc.classifier_id = 2000000055      
      // `
      // const distinctVillageFilterValuesResult = await mainDBConnections.manager.query(distinctVillageQuery)
      // const distinctVillageFilterValuesResult2 = postProcessRecords(undefined, distinctVillageFilterValuesResult, {})
      // returnValue.village_filter_values = distinctVillageFilterValuesResult2

      const staffQuery = `
        select s.staff_id as value, coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as text
        from main.staff s
        where s.active = 1000100001 and s.staff_type_id in (1000230001, 1000230005)
      `
      const staffFilterValuesResult = await mainDBConnections.manager.query(staffQuery)
      const staffFilterValuesResult2 = postProcessRecords(undefined, staffFilterValuesResult, {})
      returnValue.staff_filter_values = staffFilterValuesResult2
 */
      return returnValue;
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class CreateOrderForAnimalSale {
  async create /*or is it update*/(data, params) {
    try {
      const returnValue = {return_code: 0}
      console.log('oc COFAS c 1, data = ', data)
      // create order
      // copy order classification from item classification
      // copy order document from item document
      const mainDBConnection = dbConnections().main
      const saveResult = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
        try {
          const ocOrderRepo = transactionalEntityManager.getRepository('oc_order')
          const entityRelationshipRepo = transactionalEntityManager.getRepository('entity_relationship')
          // const animalId = data[0]
          // data.forEach(async (animalId, index) => {
          console.log('oc COFAS c 2')
          const customerId = data.customer_id
          const itemListingUUID = data.oc_item_listing_uuid

          const itemsFromItemListing = `
            select oi.oc_item_uuid 
            from main.oc_item_listing oil
            inner join main.oc_item oi on oi.oc_item_uuid = oil.listing_item_uuid
            where oil.listing_item_type_id = 1000480001
            and oil.oc_item_listing_uuid = '${itemListingUUID}'
          `

          const itemsFromItemListingResult = await mainDBConnection.manager.query(itemsFromItemListing)
          
          console.log('oc COFAS c 3, customerId = ', customerId, ', itemListingUUID = ', itemListingUUID)
          const orderObject = {
            order_type_id: 1000720001,
            order_category_id: 1000730001, 
            buyer_entity_type_id: 1000220001,
            buyer_entity_uuid: customerId
          }
          console.log('oc COFAS c 4, orderObject = ', orderObject)
          const createdOrder = await ocOrderRepo.insert(orderObject)
          console.log('oc COFAS c 5, createdOrder = ', createdOrder)
          const createdOcOrderUUID = createdOrder.identifiers[0].oc_order_uuid

          const entityStatusRepo = transactionalEntityManager.getRepository('entity_status')
          const nowDate = new Date()
          const newFarmerStatus = {
            status_category_id: 10007400,
            status_id: 1000740001,
            entity_1_type_id: 1000220001,
            entity_1_entity_uuid: data.customer_id,
            entity_2_type_id: 1000220006,
            entity_2_entity_uuid: createdOcOrderUUID,
            start_date: nowDate,
          }
          console.log('oc COFAS c 6, newFarmerStatus = ', newFarmerStatus)
          const newlyCreatedEntityStatus = await entityStatusRepo.insert(newFarmerStatus)
          console.log('oc COFAS c 7, newlyCreatedEntityStatus = ', newlyCreatedEntityStatus)
          const queryResult = await transactionalEntityManager.query(updateEntityStatusToAdjustEndTimeForEntity2AsNull)
          console.log('oc COFAS c 8, newlyCreatedEntityStatusqueryResult = ', queryResult)
          const queryResult2 = await transactionalEntityManager.query(updateEntityStatusToAdjustEndTimeForEntity2AsNotNull)
          console.log('oc COFAS c 9, newlyCreatedEntityStatusqueryResult2 = ', queryResult2)

          for (let counter = 0 ; counter < itemsFromItemListingResult.length ; counter++) {
            const itemUUID = itemsFromItemListingResult[counter].oc_item_uuid
            const entityRelationshipObject = {
              start_date: new Date(),
              entity_relationship_type_id: 1000210009,
              entity_1_entity_uuid: createdOcOrderUUID,
              entity_2_entity_uuid: itemUUID
            }
            console.log('oc COFAS c 10, entityRelationshipObject = ', entityRelationshipObject)
            const insertedEntityRelatonshipObject = entityRelationshipRepo.insert(entityRelationshipObject)
            console.log('oc COFAS c 11, insertedEntityRelatonshipObject = ', insertedEntityRelatonshipObject)
          }
        } catch (error) {
          console.log('oc COFAS c 20, error')
          console.log('oc COFAS c 20a, error = ', error)
          throw error
        }
      })
      console.log('oc COFAS c 1a, saveResult = ', saveResult)
      returnValue.save_result = saveResult
      return returnValue
    } catch (error) {
      console.log('oc COFAS c 10, error')
      console.log('oc COFAS c 10a, error = ', error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class AnimalExchangeOrders {
  async create /*or is it update*/(data, params) {
    try {
      const returnValue = { return_code: 0 }
      const mainDBConnections = dbConnections().main
      // console.log('oc r SAR c 1, data = ', data)

      const limitString = generateLimitString(data)
      const sortString = generateSortString(data) 
      const whereClause = generateWhereClause(data)

      const [selectClauseForClassificationForItem, joinClauseForClassificationForItem] = generateSelectJoinClauseForClassification(reportQueryClassificationJoinConfiguration, 'FARMERSWHOMAYWANTTOBUYITEMECTION', '2')

      const innerQuery = `
        select oo.oc_order_uuid,
          oo.order_type_id, coalesce(ot_rr.reference_name_l10n->>'en',ot_rr.reference_name_l10n->>'ul') order_type,
          c.customer_id, coalesce(c.customer_name_l10n->>'en', c.customer_name_l10n->>'ul') customer_name,
          c.mobile_number, oi.oc_item_uuid, coalesce(oi.oc_item_name_l10n->>'en', oi.oc_item_name_l10n->>'ul') item_name,
          fo_es_rr.reference_id as farmer_order_current_status_id,
          coalesce(fo_es_rr.reference_name_l10n->>'en', fo_es_rr.reference_name_l10n->>'ul') as farmer_order_current_status,
          ${selectClauseForClassificationForItem}
        from main.oc_order oo
        inner join main.customer c on c.active = 1000100001 and c.customer_type_id in (1000220001)
          and c.customer_id = oo.buyer_entity_uuid 
        inner join main.entity_relationship er on er.active = 1000100001 and er.entity_relationship_type_id = 1000210009
          and er.entity_1_entity_uuid = oo.oc_order_uuid 
        inner join main.oc_item oi on oi.active = 1000100001 and oi.linked_entity_type_id = 1000460002 and er.entity_2_entity_uuid = oi.oc_item_uuid
        inner join main.ref_reference ot_rr on ot_rr.reference_id = oo.order_type_id
        inner join main.entity_status fo_es on fo_es.active = 1000100001 and fo_es.start_date < now() and fo_es.end_date > now()
          and fo_es.status_category_id = 10007400 and fo_es.entity_1_type_id = 1000220001 and fo_es.entity_1_entity_uuid  = c.customer_id
          and fo_es.entity_2_type_id = 1000220006 and fo_es.entity_2_entity_uuid = oo.oc_order_uuid
        inner join main.ref_reference fo_es_rr on fo_es_rr.reference_id = fo_es.status_id
          ${joinClauseForClassificationForItem}
        where oo.order_category_id = 1000730001 and oo.buyer_entity_type_id = 1000220001
      `
      const countQuery = `
        select count(*) as count from (
          ${innerQuery}
        ) outerTable
        ${whereClause}
      `
      const complaintResultCount = await mainDBConnections.manager.query(countQuery)
      const count = parseInt(complaintResultCount[0].count)

      const query = `
        select * from (
          ${innerQuery}
        ) outerTable
        ${whereClause}
        ${sortString}
        ${limitString}
      `
      const complaintResult = await mainDBConnections.manager.query(query)
      const complaintResult2 = postProcessRecords(undefined, complaintResult, {})

      returnValue.count = count
      returnValue.report = complaintResult2
      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async find(params) {
    try {
      const returnValue = {return_code: 0}    
      const mainDBConnections = dbConnections().main
      // const distinctTalukQuery = `
      //   select distinct taluk_id, taluk_id as value, coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') as text
      //   from main.customer_classification cc
      //   inner join main.ref_sdtv_view rsv on rsv.village_id = cc.value_reference_id
      //   where cc.active = 1000100001 and cc.classifier_id = 2000000055
      // `
      // const distinctTalukFilterValuesResult = await mainDBConnections.manager.query(distinctTalukQuery)
      // const distinctTalukFilterValuesResult2 = postProcessRecords(undefined, distinctTalukFilterValuesResult, {})
      // returnValue.taluk_name_filter_values = distinctTalukFilterValuesResult2

      /* 
      // const distinctVillageQuery = `
      //   select distinct village_id, coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') as value, coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') as text
      //   from main.customer_classification cc
      //   inner join main.ref_sdtv_view rsv on rsv.village_id = cc.value_reference_id
      //   where cc.active = 1000100001 and cc.classifier_id = 2000000055      
      // `
      // const distinctVillageFilterValuesResult = await mainDBConnections.manager.query(distinctVillageQuery)
      // const distinctVillageFilterValuesResult2 = postProcessRecords(undefined, distinctVillageFilterValuesResult, {})
      // returnValue.village_filter_values = distinctVillageFilterValuesResult2

      const staffQuery = `
        select s.staff_id as value, coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as text
        from main.staff s
        where s.active = 1000100001 and s.staff_type_id in (1000230001, 1000230005)
      `
      const staffFilterValuesResult = await mainDBConnections.manager.query(staffQuery)
      const staffFilterValuesResult2 = postProcessRecords(undefined, staffFilterValuesResult, {})
      returnValue.staff_filter_values = staffFilterValuesResult2
 */
      return returnValue;
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class UpdateOrderFarmerStatus {
  async create /*or is it update*/(data, params) {
    try {
      const returnValue = {return_code: 0}
      console.log('oc UBFS c 1, data = ', data)
      // create item for animal
      // copy item classification from animal
      // copy item document
      // create item listing linking to item
      const mainDBConnection = dbConnections().main
      // let existingPotentialBuyerToAnimalERId
      /* if (data.buying_farmer_next_status[0] === 1000710003) {
        const queryToCheckIfERForAnimalToPotentialBuyerExists = `
          select er.entity_relationship_id
          from main.entity_relationship er
          where er.active = 1000100001 and er.entity_relationship_type_id = 1000210009
            and er.entity_1_entity_uuid = '${data.customer_id}'
            and er.entity_2_entity_uuid = '${data.oc_item_listing_uuid}'
            and er.start_date < now() and er.end_date > now()
        `
        const queryToCheckIfERForAnimalToPotentialBuyerExistsResult = await mainDBConnection.manager.query(queryToCheckIfERForAnimalToPotentialBuyerExists)
        if (queryToCheckIfERForAnimalToPotentialBuyerExistsResult.length > 0) {
          existingPotentialBuyerToAnimalERId = queryToCheckIfERForAnimalToPotentialBuyerExistsResult[0].entity_relationship_id
        }
      } */
      const saveResult = await mainDBConnection.manager.transaction(
        async (transactionalEntityManager) => {
          try {
            const entityStatusRepo = transactionalEntityManager.getRepository('entity_status')
            const nowDate = new Date()
            const newFarmerStatus = {
              status_category_id: 10007400,
              status_id: data.farmer_order_next_status_id[0],
              entity_1_type_id: 1000220001,
              entity_1_entity_uuid: data.customer_id,
              entity_2_type_id: 1000220006,
              entity_2_entity_uuid: data.oc_order_uuid,
              start_date: nowDate,
            }
            console.log('oc UBFS c 2, newFarmerStatus = ', newFarmerStatus)
            const newlyCreatedEntityStatus = await entityStatusRepo.insert(newFarmerStatus)
            console.log('oc UBFS c 3, newlyCreatedEntityStatus = ', newlyCreatedEntityStatus)
            const queryResult = await transactionalEntityManager.query(updateEntityStatusToAdjustEndTimeForEntity2AsNull)
            console.log('oc UBFS c 4, newlyCreatedEntityStatusqueryResult = ', queryResult)
            const queryResult2 = await transactionalEntityManager.query(updateEntityStatusToAdjustEndTimeForEntity2AsNotNull)
            console.log('oc UBFS c 4a, newlyCreatedEntityStatusqueryResult = ', queryResult2)
            // if new status is 1000710003, create a new er as well with type 1000210009
            // ensure that there is no existing er. if it is there, update end date to next month
          } catch (error) {
            console.log('oc UBFS c 20, error')
            console.log('oc UBFS c 20a, error = ', error)
            throw error
          }
        }
      )
      console.log('oc UBFS c 1a, saveResult = ', saveResult)
      returnValue.save_result = saveResult
      return returnValue
    } catch (error) {
      console.log('oc UBFS c 10, error')
      console.log('oc UBFS c 10a, error = ', error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

/* class ManureFarmersReport {
  async create (data, params) {
    try {
      const returnValue = { return_code: 0 }
      const mainDBConnections = dbConnections().main
      // console.log('oc r SAR c 1, data = ', data)

      const limitString = generateLimitString(data)
      const sortString = generateSortString(data) 
      const whereClause = generateWhereClause(data) 

      const excludeBDM = (data.excludeBDM === true) ? true : false

      const innerQuery = `
        select c.customer_id,
          coalesce(c.customer_name_l10n->>'en', c.customer_name_l10n->>'ul') as customer_name,
          c.mobile_number, gcc.taluk, gcc.village, gcc.village_id, gcc.taluk_id,
          ${!excludeBDM ? 'fbdm.bdm_uuid, fbdm.bdm, fbdm.bdm_mobile, fbdm.paravet_uuid, fbdm.paravet, fbdm.paravet_mobile,' : ''}
          fciv.farmer_count_in_village,
          fcivnlr.farmer_count_in_village_with_no_location
        from main.customer c
        left join (
          select gcc.customer_id,
            coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') as taluk,
            coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') as village,
            rsv.village_id, rsv.taluk_id
          from main.customer_classification gcc, main.ref_sdtv_view rsv
          where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
          and gcc.value_reference_id = rsv.village_id
        ) gcc on gcc.customer_id = c.customer_id
        ${!excludeBDM ? " \
          left join ( \
            select gcc.customer_id, sg.bdm, sg.bdm_mobile, sg.bdm_uuid, pg.paravet, pg.paravet_mobile, pg.paravet_uuid \
            from main.customer_classification gcc \
            left join ( \
              select coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as bdm, s.mobile_number as bdm_mobile, sg.geography_id, s.staff_id bdm_uuid \
              from main.staff s, ( \
                select eg.geography_id, (min(eg.entity_uuid::text))::uuid as staff_id \
                from main.entity_geography eg \
                where eg.active = 1000100001 and eg.geography_type_id = 1000320004 \
                and eg.entity_type_id in (1000230001, 1000230005) \
                group by eg.geography_id \
              ) sg \
              where s.staff_id = sg.staff_id and s.active = 1000100001 \
            ) sg on sg.geography_id = gcc.value_reference_id \
            left join ( \
              select coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as paravet, s.mobile_number as paravet_mobile, pg.geography_id, s.staff_id as paravet_uuid \
              from main.staff s, ( \
                select eg.geography_id, (min(eg.entity_uuid::text))::uuid as staff_id \
                from main.entity_geography eg \
                where eg.active = 1000100001 and eg.geography_type_id = 1000320004 \
                and eg.entity_type_id in (1000230004) \
                group by eg.geography_id \
              ) pg \
              where s.staff_id = pg.staff_id and s.active = 1000100001 \
            ) pg on pg.geography_id = gcc.value_reference_id \
            where gcc.classifier_id = 2000000055 and gcc.active = 1000100001 \
          ) fbdm on fbdm.customer_id = c.customer_id \
        " : ''}
        left join (
          select gcc.customer_id, fgc.farmer_count_in_village
          from main.customer_classification gcc,
          (
            select gcc.value_reference_id, count(gcc.customer_id) as farmer_count_in_village
            from main.customer_classification gcc
            where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
            group by gcc.value_reference_id 
          ) fgc
          where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
          and gcc.value_reference_id = fgc.value_reference_id
        ) fciv on fciv.customer_id = c.customer_id
        left join (
          select gcc.customer_id, fwnrlbg.farmer_count_in_village_with_no_location
          from main.customer_classification gcc, (
            select count(gcc.customer_id) farmer_count_in_village_with_no_location, gcc.value_reference_id 
            from main.customer_classification gcc
            where gcc.customer_id not in (
              select customer_id
              from main.customer_classification lcc
              where lcc.active = 1000100001 and lcc.classifier_id = 2000000004
            )
            and gcc.classifier_id = 2000000055 and gcc.active = 1000100001
            group by gcc.value_reference_id 
          ) fwnrlbg
          where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
          and gcc.value_reference_id = fwnrlbg.value_reference_id 
        ) fcivnlr on fcivnlr.customer_id = c.customer_id
        where c.active = 1000100001 and c.customer_type_id in (1000220001)
          and c.customer_id not in (
          select customer_id
          from main.customer_classification cc
          where cc.classifier_id = 2000000004
          and cc.active = 1000100001
        )
      `

      const countQuery = `
        select count(*) as count from (
          ${innerQuery}
        ) outerTable
        ${whereClause}
      `
      const complaintResultCount = await mainDBConnections.manager.query(countQuery)
      const count = complaintResultCount[0].count

      const query = `
        select * from (
          ${innerQuery}
        ) outerTable
        ${whereClause}
        ${sortString}
        ${limitString}
      `
      const complaintResult = await mainDBConnections.manager.query(query)
      const complaintResult2 = postProcessRecords(undefined, complaintResult, {})

      const distinctVillageQuery = `
        select distinct village_id, village value, village text from (
          ${innerQuery}
        ) outerTable
      `
      // ${whereClause}
      const distinctVillageResult = await mainDBConnections.manager.query(distinctVillageQuery)
      const distinctVillageResult2 = postProcessRecords(undefined, distinctVillageResult, {})
      returnValue.village_filter_values = distinctVillageResult2

      returnValue.count = count
      returnValue.report = complaintResult2
      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async find(params) {
    try {
      const returnValue = {return_code: 0}
      const mainDBConnections = dbConnections().main
      const tableFilterData = {}
      returnValue.tableFilters = tableFilterData
      const distinctTalukQuery = `
        select distinct taluk_id, taluk_id as value, coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') as text
        from main.customer_classification cc
        inner join main.ref_sdtv_view rsv on rsv.village_id = cc.value_reference_id
        where cc.active = 1000100001 and cc.classifier_id = 2000000055
      `
      const distinctTalukFilterValuesResult = await mainDBConnections.manager.query(distinctTalukQuery)
      const distinctTalukFilterValuesResult2 = postProcessRecords(undefined, distinctTalukFilterValuesResult, {})
      tableFilterData.taluk_filter_values = distinctTalukFilterValuesResult2

      // const distinctVillageQuery = `
      //   select distinct village_id, coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') as value, coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') as text
      //   from main.customer_classification cc
      //   inner join main.ref_sdtv_view rsv on rsv.village_id = cc.value_reference_id
      //   where cc.active = 1000100001 and cc.classifier_id = 2000000055      
      // `
      // const distinctVillageFilterValuesResult = await mainDBConnections.manager.query(distinctVillageQuery)
      // const distinctVillageFilterValuesResult2 = postProcessRecords(undefined, distinctVillageFilterValuesResult, {})
      // returnValue.village_filter_values = distinctVillageFilterValuesResult2

      const staffQuery = `
        select s.staff_id as value, coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as text
        from main.staff s
        where s.active = 1000100001 and s.staff_type_id in (1000230001, 1000230005)
      `
      const staffFilterValuesResult = await mainDBConnections.manager.query(staffQuery)
      const staffFilterValuesResult2 = postProcessRecords(undefined, staffFilterValuesResult, {})
      tableFilterData.staff_filter_values = staffFilterValuesResult2


      return returnValue;
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
} */

class ManureSnoozeFarmers {
  async create /*or is it update*/(data, params) {
    try {
      console.log('oc SFSA c 1, data = ', data)
      const entityRelationship = {
        start_date: new Date(),
        end_date: new Date(data.snooze_till_date)
      }
      if (data.farmer_level_snooze_choice[0] === 1000105001) {
        // snooze farmer
        entityRelationship.entity_relationship_type_id = 1000210006
        entityRelationship.entity_1_entity_uuid = data.customer_id
      } else {
        // snooze animal
        entityRelationship.entity_relationship_type_id = 1000210005
        entityRelationship.entity_1_entity_uuid = data.animal_id

      }
      const returnValue = { return_code: 0 }
      const mainDBConnection = dbConnections().main
      const saveResult = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
        // const entityRelationshipRepo = transactionalEntityManager.getRepository('entity_relationship')
        // const results = await entityRelationshipRepo.save(entityRelationship)
        const entityRelationshipTableEntity = mainDBConnection.entities['entity_relationship']
        const results = await transactionalEntityManager.createQueryBuilder().insert().into(entityRelationshipTableEntity).values(entityRelationship).execute()

        const noteTableEntity = mainDBConnection.entities['note']
        const commentObject = {
          note_type_id: 1000440008,
          entity_1_type_id: 1000220002,
          note_time: new Date(),
          note: {ul: data.snooze_comments},
          entity_1_uuid: data.animal_id
        }
        const notesInsertResults = await transactionalEntityManager.createQueryBuilder().insert().into(noteTableEntity).values(commentObject).execute()
      })
      returnValue.save_result = saveResult
      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

/* class GetFarmerDetails {
  async get(id, params) {
    try {
      const returnValue = {return_code: 0}
      console.log('oc r GFD g 1, id = ', id, ', params = ', params)
      const mainDBConnections = dbConnections().main
      const customerQuery = `
        select c.customer_id,
          coalesce(c.customer_name_l10n->>'en', c.customer_name_l10n->>'ul') as customer_name,
          c.mobile_number, gcc.district_name, gcc.taluk_name, gcc.village_name, gcc.village_id, gcc.taluk_id, gcc.district_id,
          fbdm.bdm_uuid, fbdm.bdm, fbdm.bdm_mobile, fbdm.paravet_uuid, fbdm.paravet, fbdm.paravet_mobile,
          ct_d.document_id as customer_thumbnail_document_id,
          dfl_cc.value_json dairy_farm_location_position
        from main.customer c
        left join (
          select gcc.customer_id,
          coalesce(rsv.district_name_l10n->>'en', rsv.district_name_l10n->>'ul') as district_name,
          coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') as taluk_name,
            coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') as village_name,
            rsv.district_id, rsv.village_id, rsv.taluk_id
          from main.customer_classification gcc, main.ref_sdtv_view rsv
          where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
          and gcc.value_reference_id = rsv.village_id
        ) gcc on gcc.customer_id = c.customer_id
        left join (
          select gcc.customer_id, sg.bdm, sg.bdm_mobile, sg.bdm_uuid, pg.paravet, pg.paravet_mobile, pg.paravet_uuid
          from main.customer_classification gcc
          left join (
            select coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as bdm, s.mobile_number as bdm_mobile, sg.geography_id, s.staff_id bdm_uuid
            from main.staff s, (
              select eg.geography_id, (min(eg.entity_uuid::text))::uuid as staff_id
              from main.entity_geography eg
              where eg.active = 1000100001 and eg.geography_type_id = 1000320004
              and eg.entity_type_id in (1000230001, 1000230005)
              group by eg.geography_id
            ) sg
            where s.staff_id = sg.staff_id and s.active = 1000100001
          ) sg on sg.geography_id = gcc.value_reference_id
          left join (
            select coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as paravet, s.mobile_number as paravet_mobile, pg.geography_id, s.staff_id as paravet_uuid
            from main.staff s, (
              select eg.geography_id, (min(eg.entity_uuid::text))::uuid as staff_id
              from main.entity_geography eg
              where eg.active = 1000100001 and eg.geography_type_id = 1000320004
              and eg.entity_type_id in (1000230004)
              group by eg.geography_id
            ) pg
            where s.staff_id = pg.staff_id and s.active = 1000100001
          ) pg on pg.geography_id = gcc.value_reference_id
          where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
        ) fbdm on fbdm.customer_id = c.customer_id
        left join main.customer_classification dfl_cc on dfl_cc.active = 1000100001
          and dfl_cc.customer_id = c.customer_id and dfl_cc.classifier_id = 2000000004
        left join (
          select d.document_id, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id,
            coalesce(dt_rr.reference_name_l10n->>'en', dt_rr.reference_name_l10n->>'ul') as document_type,
            d.document_information,
            d.created_at document_upload_time
          from main.document d
          inner join main.ref_reference dt_rr on dt_rr.reference_id = d.document_type_id
          left join (
            select max(d.document_id::text) max_created_at_document_id, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
            from main.document d
            inner join (
              select max(d.created_at) max_created_at, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
              from main.document d
              where 1 = 1 and d.active = 1000100001
              group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id 
            ) mcad on d.entity_1_type_id = mcad.entity_1_type_id and d.entity_1_entity_uuid = mcad.entity_1_entity_uuid
              and d.document_type_id = mcad.document_type_id and d.created_at = mcad.max_created_at
            where 1 = 1  and d.active = 1000100001
            group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
          ) mcadid on d.document_id::text = mcadid.max_created_at_document_id
        ) ct_d on ct_d.entity_1_type_id = 1000220001 and ct_d.entity_1_entity_uuid = c.customer_id and ct_d.document_type_id = 1000260003
        where 1 = 1 /* and c.active = 1000100001 and c.customer_type_id in (1000220001) *//*
          and c.customer_id = '${id}'
      `
      const customerBasicDetailsUnProcessedResult = await mainDBConnections.manager.query(customerQuery)
      const customerBasicDetailsResult = postProcessRecords(undefined, customerBasicDetailsUnProcessedResult, {})
      returnValue.result = customerBasicDetailsResult[0]

      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async create (data, params) {
    try {
      const returnValue = {return_code: 0}
      const classificationData = await loadClassificationData('CUSTOMER_CLASSIFICATION', data.customerId, data.classifierArray)
      returnValue.data = classificationData
      return returnValue
    } catch (error) {
      console.log('oc UBFS c 10, error')
      console.log('oc UBFS c 10a, error = ', error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
} */

/* class SetFarmerDetails {
  async create (data, params) {
    try {
      console.log('oc r SFD c 1')
      const returnValue = {return_code: 0}
      const mainDBConnection = dbConnections().main
      const saveResult = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
        try {
          const updateClassificationDataResult = await saveClassificationData(transactionalEntityManager, 'CUSTOMER_CLASSIFICATION', data.customerId, data.classificationData);
          returnValue.classificationDataUpdateResult = updateClassificationDataResult
        } catch (error) {
          console.log('oc UASS c 20, error')
          console.log('oc UASS c 20a, error = ', error)
          throw error
        }
      })
      return returnValue
    } catch (error) {
      console.log('oc r SFD c 10, error')
      console.log('oc r SFD c 10a, error = ', error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
} */

/* class GetAnimalDetails {
  async get(id, params) {
    try {
      const returnValue = {return_code: 0}
      console.log('oc r GAD g 1, id = ', id, ', params = ', params)
      const mainDBConnections = dbConnections().main
      const animalQuery = `
        select a.animal_id, et_ac.value_string_256 animal_ear_tag,
          concat(coalesce(b_rr.reference_name_l10n->>'en', b_rr.reference_name_l10n->>'ul'),'-',
            coalesce(at_rr.reference_name_l10n->>'en', at_rr.reference_name_l10n->>'ul')) animal_breed_and_type,
          c.customer_id,
          concat(coalesce(c.customer_name_l10n->>'en', c.customer_name_l10n->>'ul'), '-', c.mobile_number) customer_name_and_mobile,
          concat(gcc.district_name, '-', gcc.taluk_name, '-', gcc.village_name) district_taluk_village,
          concat(fbdm.bdm, '-', fbdm.bdm_mobile) bdm_and_mobile, concat(fbdm.paravet, '-', fbdm.paravet_mobile) paravet_and_mobile,
          ct_d.document_id as customer_thumbnail_document_id, at_d.document_id as animal_thumbnail_document_id,
          aet_d.document_id as animal_eartag_document_id,
          dfl_cc.value_json dairy_farm_location_position
        from main.animal a
        left join main.animal_classification et_ac on et_ac.active = 1000100001 and et_ac.animal_id = a.animal_id and et_ac.classifier_id = 2000000034
        left join main.animal_classification b_ac on b_ac.active = 1000100001 and b_ac.animal_id = a.animal_id and b_ac.classifier_id = 2000000036
        left join main.ref_reference b_rr on b_rr.reference_id = b_ac.value_reference_id
        left join main.animal_classification at_ac on at_ac.active = 1000100001 and at_ac.animal_id = a.animal_id and at_ac.classifier_id = 2000000035
        left join main.ref_reference at_rr on at_rr.reference_id = at_ac.value_reference_id
        inner join main.entity_relationship f2a on f2a.active = 1000100001 and f2a.entity_relationship_type_id = 1000210004 and f2a.entity_2_entity_uuid = a.animal_id
        inner join main.customer c on c.customer_id = f2a.entity_1_entity_uuid
          /* and c.active = 1000100001 and c.customer_type_id in (1000220001) *//*
        left join (
          select gcc.customer_id,
          coalesce(rsv.district_name_l10n->>'en', rsv.district_name_l10n->>'ul') as district_name,
          coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') as taluk_name,
          coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') as village_name,
            rsv.district_id, rsv.village_id, rsv.taluk_id
          from main.customer_classification gcc, main.ref_sdtv_view rsv
          where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
          and gcc.value_reference_id = rsv.village_id
        ) gcc on gcc.customer_id = c.customer_id
        left join (
          select gcc.customer_id, sg.bdm, sg.bdm_mobile, sg.bdm_uuid, pg.paravet, pg.paravet_mobile, pg.paravet_uuid
          from main.customer_classification gcc
          left join (
            select coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as bdm, s.mobile_number as bdm_mobile, sg.geography_id, s.staff_id bdm_uuid
            from main.staff s, (
              select eg.geography_id, (min(eg.entity_uuid::text))::uuid as staff_id
              from main.entity_geography eg
              where eg.active = 1000100001 and eg.geography_type_id = 1000320004
              and eg.entity_type_id in (1000230001, 1000230005)
              group by eg.geography_id
            ) sg
            where s.staff_id = sg.staff_id and s.active = 1000100001
          ) sg on sg.geography_id = gcc.value_reference_id
          left join (
            select coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as paravet, s.mobile_number as paravet_mobile, pg.geography_id, s.staff_id as paravet_uuid
            from main.staff s, (
              select eg.geography_id, (min(eg.entity_uuid::text))::uuid as staff_id
              from main.entity_geography eg
              where eg.active = 1000100001 and eg.geography_type_id = 1000320004
              and eg.entity_type_id in (1000230004)
              group by eg.geography_id
            ) pg
            where s.staff_id = pg.staff_id and s.active = 1000100001
          ) pg on pg.geography_id = gcc.value_reference_id
          where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
        ) fbdm on fbdm.customer_id = c.customer_id
        left join main.customer_classification dfl_cc on dfl_cc.active = 1000100001
          and dfl_cc.customer_id = c.customer_id and dfl_cc.classifier_id = 2000000004
        left join (
          select d.document_id, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id,
            coalesce(dt_rr.reference_name_l10n->>'en', dt_rr.reference_name_l10n->>'ul') as document_type,
            d.document_information,
            d.created_at document_upload_time
          from main.document d
          inner join main.ref_reference dt_rr on dt_rr.reference_id = d.document_type_id
          left join (
            select max(d.document_id::text) max_created_at_document_id, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
            from main.document d
            inner join (
              select max(d.created_at) max_created_at, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
              from main.document d
              where 1 = 1 and d.active = 1000100001
              group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id 
            ) mcad on d.entity_1_type_id = mcad.entity_1_type_id and d.entity_1_entity_uuid = mcad.entity_1_entity_uuid
              and d.document_type_id = mcad.document_type_id and d.created_at = mcad.max_created_at
            where 1 = 1  and d.active = 1000100001
            group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
          ) mcadid on d.document_id::text = mcadid.max_created_at_document_id
        ) ct_d on ct_d.entity_1_type_id = 1000220001 and ct_d.entity_1_entity_uuid = c.customer_id and ct_d.document_type_id = 1000260003
        left join (
          select d.document_id, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id,
            coalesce(dt_rr.reference_name_l10n->>'en', dt_rr.reference_name_l10n->>'ul') as document_type,
            d.document_information,
            d.created_at document_upload_time
          from main.document d
          inner join main.ref_reference dt_rr on dt_rr.reference_id = d.document_type_id
          left join (
            select max(d.document_id::text) max_created_at_document_id, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
            from main.document d
            inner join (
              select max(d.created_at) max_created_at, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
              from main.document d
              where 1 = 1 and d.active = 1000100001
              group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id 
            ) mcad on d.entity_1_type_id = mcad.entity_1_type_id and d.entity_1_entity_uuid = mcad.entity_1_entity_uuid
              and d.document_type_id = mcad.document_type_id and d.created_at = mcad.max_created_at
            where 1 = 1  and d.active = 1000100001
            group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
          ) mcadid on d.document_id::text = mcadid.max_created_at_document_id
        ) at_d on at_d.entity_1_type_id = 1000220002 and at_d.entity_1_entity_uuid = a.animal_id and at_d.document_type_id = 1000260001
        left join (
          select d.document_id, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id,
            coalesce(dt_rr.reference_name_l10n->>'en', dt_rr.reference_name_l10n->>'ul') as document_type,
            d.document_information,
            d.created_at document_upload_time
          from main.document d
          inner join main.ref_reference dt_rr on dt_rr.reference_id = d.document_type_id
          left join (
            select max(d.document_id::text) max_created_at_document_id, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
            from main.document d
            inner join (
              select max(d.created_at) max_created_at, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
              from main.document d
              where 1 = 1 and d.active = 1000100001
              group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id 
            ) mcad on d.entity_1_type_id = mcad.entity_1_type_id and d.entity_1_entity_uuid = mcad.entity_1_entity_uuid
              and d.document_type_id = mcad.document_type_id and d.created_at = mcad.max_created_at
            where 1 = 1  and d.active = 1000100001
            group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
          ) mcadid on d.document_id::text = mcadid.max_created_at_document_id
        ) aet_d on aet_d.entity_1_type_id = 1000220002 and aet_d.entity_1_entity_uuid = a.animal_id and aet_d.document_type_id = 1000260012
        where a.animal_id = '${id}'
      `
      console.log('oc r GAD g 2, animalQuery = ', animalQuery)
      const animalBasicDetailsUnProcessedResult = await mainDBConnections.manager.query(animalQuery)
      const animalBasicDetailsResult = postProcessRecords(undefined, animalBasicDetailsUnProcessedResult, {})
      console.log('oc r GAD g 3, animalBasicDetailsResult = ', animalBasicDetailsResult)
      returnValue.result = animalBasicDetailsResult[0]

      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async create (data, params) {
    try {
      console.log('oc r GAD c 1, data = ', data)
      const returnValue = {return_code: 0}
      const classificationData = await loadClassificationData('ANIMAL_CLASSIFICATION', data.animalId, data.classifierArray)
      console.log('oc r GAD c 2, classificationData = ', classificationData)
      returnValue.data = classificationData
      return returnValue
    } catch (error) {
      console.log('oc r GAD c 10, error')
      console.log('oc r GAD c 10a, error = ', error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
} */

/* class SetAnimalDetails {
  async create (data, params) {
    try {
      console.log('oc r SFD c 1')
      const returnValue = {return_code: 0}
      const mainDBConnection = dbConnections().main
      const saveResult = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
        try {
          const updateClassificationDataResult = await saveClassificationData(transactionalEntityManager, 'ANIMAL_CLASSIFICATION', data.animalId, data.classificationData);
          returnValue.classificationDataUpdateResult = updateClassificationDataResult
        } catch (error) {
          console.log('oc UASS c 20, error')
          console.log('oc UASS c 20a, error = ', error)
          throw error
        }
      })
      return returnValue
    } catch (error) {
      console.log('oc r SFD c 10, error')
      console.log('oc r SFD c 10a, error = ', error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
} */

/*
-10101 - health score not generated at all
-10102 - health score not updated in the last 1 month
-10103 - body score not generated at all
-10104 - body score not updated in the last 1 month
-10105 - body score parameters not available
-10106 - body score parameters not updated in the last 1 month
-10107 - body score parameters updated after last body score generation
-10108 - udder teat score not generated at all
-10109 - udder teat score not updated in the last 1 month
-10110 - udder teat parameters not available
-10111 - udder teat parameters not updated in the last 1 month
-10112 - udder teat parameters updated after last udder teat generation
-10113 - additional teat information not available
-10114 - udder stretchability parameter not available
-10115 - udder stretchability parameter not updated in the last 1 month
-10116 - spread of patches parameter not available
-10117 - veins on udder parameter not available
-10118 - veins on udder parameter not updated in the last 1 month
-10119 - lpd not available
-10120 - lpd not updated in the last 1 month
-10121 - weight not available
-10122 - weight not updated in the last 1 month
-10123 - calving is 0, please verify
-10124 - pregnancy months is 0, please verify
-10125 - pregnancy months as of is not available
-10126 - locomotion score is not available
-10127 - foreign body parameter is not available
-10128 - additional teat parameter is not available
-10129 - stretchability of udder parameter is not available
-10130 - spread of patches or not parameter is not available
-10131 - horns exist or not parameter is not available
-10132 - veins on udder parameter is not available
-10133 - gap in teeth parameter is not available
-10134 - max lpd not available
-10135 - max lpd not updated in the last 12 months
-10200 - data inadequate to generate price
-10201 - generation of price had an error
-10202 - could not save price
*/
const calculateBodyScore = ({bsTailAndPinBoneParameter, bsBackBoneParameter, bsHipAndPinBoneParameter}, animalPriceGenerationErrorCodes, animalPriceGenerationWarningCodes) => {
  const tailAndPinBoneScoreTranslation = {'1000660001': 5, '1000660002': 4, '1000660003': 2}
  const backBoneScoreTranslation = {'1000670001': 2, '1000670002': 4, '1000670003': 5}
  const hipAndTailBoneScoreTranslation = {'1000680001': 2, '1000680002': 4, '1000680003': 5}
  if (tailAndPinBoneScoreTranslation[String(bsTailAndPinBoneParameter[0])] === undefined
      || backBoneScoreTranslation[String(bsBackBoneParameter[0])] === undefined
      || hipAndTailBoneScoreTranslation[String(bsHipAndPinBoneParameter[0])] === undefined) {
    animalPriceGenerationErrorCodes.push(-10201)
    return undefined
  } else {
    // could not generate correct pricing because of system problems
    const bodyScore = (
      tailAndPinBoneScoreTranslation[String(bsTailAndPinBoneParameter[0])]
      + backBoneScoreTranslation[String(bsBackBoneParameter[0])]
      + hipAndTailBoneScoreTranslation[String(bsHipAndPinBoneParameter[0])]
      ) / 3
    return bodyScore  
  }  
}

const calculateUdderAndTeatScore = ({utsCleftVisibilityParameter, utsUdderPositionOrDepthParameter, utsUdderBalanceParameter, utsTeatPlacementAndDirectionParameter, utsTeatLengthParameter}, animalPriceGenerationErrorCodes, animalPriceGenerationWarningCodes) => {
  const udderCleftVisibilityScoreTranslation = {'1000620001': 0, '1000620002': 1}
  const udderPositionOrDepthScoreTranslation = {'1000590001': 0, '1000590002': 1}
  const udderBalanceScoreTranslation = {'1000610001': 0, '1000610002': 0.5, '1000610003': 0.5, '1000610004': 1}
  const teatPlacementAndDirectionScoreTranslation = {'1000630001': 0, '1000630002': 0.5, '1000630003': 0.5, '1000630004': 1}
  const teatLengthScoreTranslation = {'1000640001': 0.5, '1000640002': 0.5, '1000640003': 1}
  if (udderCleftVisibilityScoreTranslation[String(utsCleftVisibilityParameter[0])] === undefined
      || udderPositionOrDepthScoreTranslation[String(utsUdderPositionOrDepthParameter[0])] === undefined
      || udderBalanceScoreTranslation[String(utsUdderBalanceParameter[0])] === undefined
      || teatPlacementAndDirectionScoreTranslation[String(utsTeatPlacementAndDirectionParameter[0])] === undefined
      || teatLengthScoreTranslation[String(utsTeatLengthParameter[0])] === undefined
      ) {
    animalPriceGenerationErrorCodes.push(-10201)
    return undefined
  } else {
    // could not generate correct pricing because of system problems
    const udderAndTeatScore = udderCleftVisibilityScoreTranslation[String(utsCleftVisibilityParameter[0])]
      + udderPositionOrDepthScoreTranslation[String(utsUdderPositionOrDepthParameter[0])]
      + udderBalanceScoreTranslation[String(utsUdderBalanceParameter[0])]
      + teatPlacementAndDirectionScoreTranslation[String(utsTeatPlacementAndDirectionParameter[0])]
      + teatLengthScoreTranslation[String(utsTeatLengthParameter[0])]
    return udderAndTeatScore
  }

}

const generatePrice = (primaryParameters, secondaryParameters, tertiaryParameters, visualParameters, animalPriceGenerationErrorCodes, animalPriceGenerationWarningCodes) => {
  const returnValue = {}
  const {bodyScore, udderAndTeatScore, lastLPDRecorded, maxLPDRecorded, animalWeight, numberOfCalvings, numberOfMonthsPregnant} = primaryParameters
  const {locomotionScoreParameter, foreignBodyPresenceParameter} = secondaryParameters
  const {additionalTeatParameter, stretchabilityOfUdderParameter} = tertiaryParameters
  const {spreadOfPatchesParameter, hornsExistOrNotParameter, visibilityOfVeinsOnUdderParameter, gapInTeethParameter} = visualParameters
  const numberOfCalvingsPriceAdjustment = {0: 30000, 1: 15000, 2: 0, 3: -10000, 4: -20000, 5: -30000}
  const numberOfMonthsPregnantPriceAdjustment = {0: -35000, 1: -25000, 2: -25000, 3: -20000, 4: -20000, 5: -15000, 6: -10000, 7: -5000, 8: 0, 9: 0}
  const bodyScorePriceAdjustmentFor0To3Calving0To6MonthsPregnancy = {5.00: -50000, 4.67: -10000, 4.33: 0, 4.00: 0, 3.33: -25000, 2.66: -50000, 2.00: -75000}
  const bodyScorePriceAdjustmentFor0To3Calving7To9MonthsPregnancy = {5.00: -50000, 4.67: -10000, 4.33: 0, 4.00: 0, 3.33: 0, 2.66: -25000, 2.00: -75000}
  const bodyScorePriceAdjustmentFor4To6Calving0To6MonthsPregnancy = {5.00: -50000, 4.67: -10000, 4.33: 0, 4.00: 0, 3.33: -25000, 2.66: -50000, 2.00: -75000}
  const bodyScorePriceAdjustmentFor4To6Calving7To9MonthsPregnancy = {5.00: -50000, 4.67: -10000, 4.33: 0, 4.00: 0, 3.33: 0, 2.66: 0, 2.00: -75000}
  const udderAndTeatScorePriceAdjustment = {0.5:	-20000, 1: -17500, 1.5: -15000, 2:	-12500,  2.5:	-10000, 3:	-7500, 3.5:	-5000, 4:	-2500, 4.5:	0, 5:	0}
  const locomotionPriceAdjustment = {1000650005: 10000, 1000650004: 5000, 1000650003: -10000, 1000650002: -15000, 1000650001: -20000}
  const additionalTeatPriceAdjustment = {1000810001: -15000, 1000810002: 0}
  const stretchabilityOfUdderPriceAdjustmentForLessThan7MonthsPregnancy = {1000570003: -20000, 1000570002: -10000, 1000570001: 0}
  const stretchabilityOfUdderPriceAdjustmentFor7To8MonthsPregnancy = {1000570003: -10000, 1000570002: 0, 1000570001: 5000}
  const stretchabilityOfUdderPriceAdjustmentFor9MonthsPregnancy = {1000570003: 0, 1000570002: 5000, 1000570001: 10000}
  const spreadOfPatchesPriceAdjustment = {1000560001: -2500, 1000560002: -2500, 1000560003: 2500, 1000560004: 2500}
  const hornsExistOrNotPriceAdjustment = {1000780001: -10000, 1000780002: 0}
  const visibilityOfVeinsOnUdderPriceAdjustment = {1000580001: -10000, 1000580002: -5000, 1000580003:0}
  const gapInTeethPriceAdjustment = {1000770001: 0, 1000770002: 0}

  const possibleLPD = maxLPDRecorded*1.15
  let sellablePrice = possibleLPD*5000
  const basePrice = sellablePrice
  if (numberOfCalvingsPriceAdjustment[numberOfCalvings] || numberOfCalvingsPriceAdjustment[numberOfCalvings] === 0) {
    sellablePrice = sellablePrice + numberOfCalvingsPriceAdjustment[numberOfCalvings]
  } else {
    // could not generate correct pricing because of system problems
    animalPriceGenerationErrorCodes.push(-10201)
  }

  if (numberOfMonthsPregnantPriceAdjustment[parseInt(numberOfMonthsPregnant)]) {
    sellablePrice = sellablePrice + numberOfMonthsPregnantPriceAdjustment[parseInt(numberOfMonthsPregnant)]
  } else {
    // could not generate correct pricing because of system problems
    animalPriceGenerationErrorCodes.push(-10201)
  }
  let bodyScorePrice
  if (numberOfCalvings > 3) {
    if (numberOfMonthsPregnant > 6) {
      bodyScorePrice = bodyScorePriceAdjustmentFor4To6Calving7To9MonthsPregnancy
    } else {
      bodyScorePrice = bodyScorePriceAdjustmentFor4To6Calving0To6MonthsPregnancy
    }
  } else {
    if (numberOfMonthsPregnant > 6) {
      bodyScorePrice = bodyScorePriceAdjustmentFor0To3Calving7To9MonthsPregnancy
    } else {
      bodyScorePrice = bodyScorePriceAdjustmentFor0To3Calving0To6MonthsPregnancy
    }
  }
  if (bodyScorePrice[parseFloat(bodyScore).toFixed(2)] !== undefined && bodyScorePrice[parseFloat(bodyScore).toFixed(2)] !== null) {
    sellablePrice = sellablePrice + bodyScorePrice[parseFloat(bodyScore).toFixed(2)]*basePrice/150000
  } else {
    // could not generate correct pricing because of system problems
    animalPriceGenerationErrorCodes.push(-10201)
  }
  if ((udderAndTeatScorePriceAdjustment[udderAndTeatScore] !== undefined && udderAndTeatScorePriceAdjustment[udderAndTeatScore] !== null) || udderAndTeatScorePriceAdjustment[udderAndTeatScore] === 0) {
    sellablePrice = sellablePrice + udderAndTeatScorePriceAdjustment[udderAndTeatScore]
  } else {
    // could not generate correct pricing because of system problems
    animalPriceGenerationErrorCodes.push(-10201)
  }
  
  let pregnancyAdjustedAnimalWeight = animalWeight
  if (numberOfMonthsPregnant > 6) {
    pregnancyAdjustedAnimalWeight = pregnancyAdjustedAnimalWeight - 25 - (numberOfMonthsPregnant-6)*25/3
  } else {
    pregnancyAdjustedAnimalWeight = pregnancyAdjustedAnimalWeight - (numberOfMonthsPregnant)*25/6
  }

  if (locomotionScoreParameter.length > 0 && ((locomotionPriceAdjustment[locomotionScoreParameter[0]] !== undefined && locomotionPriceAdjustment[locomotionScoreParameter[0]] !== null) || locomotionPriceAdjustment[locomotionScoreParameter[0]] === 0)) {
    sellablePrice = sellablePrice + locomotionPriceAdjustment[locomotionScoreParameter[0]]
  } else {
    // could not generate correct pricing because of system problems
    animalPriceGenerationErrorCodes.push(-10201)
  }

  if (additionalTeatParameter.length > 0 && ((additionalTeatPriceAdjustment[additionalTeatParameter[0]] !== undefined && additionalTeatPriceAdjustment[additionalTeatParameter[0]] !== null) || additionalTeatPriceAdjustment[additionalTeatParameter[0]] === 0)) {
    sellablePrice = sellablePrice + additionalTeatPriceAdjustment[additionalTeatParameter[0]]
  } else {
    // could not generate correct pricing because of system problems
    animalPriceGenerationErrorCodes.push(-10201)
  }

  let stretchabilityOfUdderPriceAdjustment
  if (numberOfMonthsPregnant >= 8) {
    stretchabilityOfUdderPriceAdjustment = stretchabilityOfUdderPriceAdjustmentFor9MonthsPregnancy
  } else if (numberOfMonthsPregnant < 8 && numberOfMonthsPregnant >= 7) {
    stretchabilityOfUdderPriceAdjustment = stretchabilityOfUdderPriceAdjustmentFor7To8MonthsPregnancy
  } else {
    stretchabilityOfUdderPriceAdjustment = stretchabilityOfUdderPriceAdjustmentForLessThan7MonthsPregnancy
  }
  if (stretchabilityOfUdderParameter.length > 0 && ((stretchabilityOfUdderPriceAdjustment[stretchabilityOfUdderParameter[0]] !== undefined && stretchabilityOfUdderPriceAdjustment[stretchabilityOfUdderParameter[0]] !== null) || stretchabilityOfUdderPriceAdjustment[stretchabilityOfUdderParameter[0]] === 0)) {
    sellablePrice = sellablePrice + stretchabilityOfUdderPriceAdjustment[stretchabilityOfUdderParameter[0]]
  } else {
    // could not generate correct pricing because of system problems
    animalPriceGenerationErrorCodes.push(-10201)
  }

  if (spreadOfPatchesParameter.length > 0 && ((spreadOfPatchesPriceAdjustment[spreadOfPatchesParameter[0]] !== undefined && spreadOfPatchesPriceAdjustment[spreadOfPatchesParameter[0]] !== null) || spreadOfPatchesPriceAdjustment[spreadOfPatchesParameter[0]] === 0)) {
    sellablePrice = sellablePrice + spreadOfPatchesPriceAdjustment[spreadOfPatchesParameter[0]]
  } else {
    // could not generate correct pricing because of system problems
    animalPriceGenerationErrorCodes.push(-10201)
  }

  if (hornsExistOrNotParameter.length > 0 && ((hornsExistOrNotPriceAdjustment[hornsExistOrNotParameter] !== undefined && hornsExistOrNotPriceAdjustment[hornsExistOrNotParameter] !== null) || hornsExistOrNotPriceAdjustment[hornsExistOrNotParameter] === 0)) {
    sellablePrice = sellablePrice + hornsExistOrNotPriceAdjustment[hornsExistOrNotParameter[0]]
  } else {
    // could not generate correct pricing because of system problems
    animalPriceGenerationErrorCodes.push(-10201)
  }

  if (visibilityOfVeinsOnUdderParameter.length > 0 && ((visibilityOfVeinsOnUdderPriceAdjustment[visibilityOfVeinsOnUdderParameter[0]] !== undefined && visibilityOfVeinsOnUdderPriceAdjustment[visibilityOfVeinsOnUdderParameter[0]] !== null) || visibilityOfVeinsOnUdderPriceAdjustment[visibilityOfVeinsOnUdderParameter[0]] === 0)) {
    sellablePrice = sellablePrice + visibilityOfVeinsOnUdderPriceAdjustment[visibilityOfVeinsOnUdderParameter[0]]
  } else {
    // could not generate correct pricing because of system problems
    animalPriceGenerationErrorCodes.push(-10201)
  }
  
  if (gapInTeethParameter.length > 0 && ((gapInTeethPriceAdjustment[gapInTeethParameter[0]] !== undefined && gapInTeethPriceAdjustment[gapInTeethParameter[0]] !== null) || gapInTeethPriceAdjustment[gapInTeethParameter[0]] === 0)) {
    sellablePrice = sellablePrice + gapInTeethPriceAdjustment[gapInTeethParameter[0]]
  } else {
    // could not generate correct pricing because of system problems
    animalPriceGenerationErrorCodes.push(-10201)
  }
  returnValue.buyingPrice = sellablePrice
  returnValue.buyingPriceUpperRange = sellablePrice + 5000
  returnValue.buyingPriceLowerRange = sellablePrice - 5000
  returnValue.sellingPrice = sellablePrice - 20000
  returnValue.sellingPriceUpperRange = sellablePrice - 20000 + 5000
  returnValue.sellingPriceLowerRange = sellablePrice - 20000 - 5000
  return returnValue
}

class GenerateCattleExchangePrice {
  async create /*or is it update*/(data, params) {
    try {
      console.log('oc r GCEP c 1')
      const returnValue = {return_code: 0}
      const animalPriceGenerationResults = []
      returnValue.animal_price_generation_results = animalPriceGenerationResults
      const mainDBConnection = dbConnections().main
      const animalIdArray = data.animal_ids
      let numberOfSuccessfulPriceGeneration = 0
      let numberOfUnsuccessfulPriceGeneration = 0
      let configurationProblem = false
      for (const animalId of animalIdArray) {
        const individualAnimalPriceGenerationResult = {animal_id: animalId}
        const animalPriceGenerationErrorCodes = []
        const animalPriceGenerationWarningCodes = []
        individualAnimalPriceGenerationResult.animal_price_generation_warnings = animalPriceGenerationWarningCodes
        individualAnimalPriceGenerationResult.animal_price_generation_errors = animalPriceGenerationErrorCodes
        // check which data points are available for the animal
        // check if weight and lpd have been updated in the last 1-3 months
        // output
        // health score(animal_health_score - **********)
        // mandatory
        // weight (animal_weight_1 - **********),
        // lpd (animal_average_lpd_1 - **********), maxlpd (max_lpd_of_animal_1 - **********)
        // calvings (number_of_calvings_1 - **********),
        // months pregnancy (number_of_months_pregnant_1 - **********),
        // months pregnant as of (months_pregnant_as_of_date - **********)
        // locomotion score (animal_locomotion_score_dropdown - **********),
        // foreign body (hs_foreign_body_area_dropdown - **********),
        // body score (animal_body_score_1 - **********),
        // in case body score was not calculated after update
        // tail and pin bone assessment (bs_tail_and_pin_bone_dropdown - **********)
        // back bone assessment (bs_back_bone_dropdown - **********)
        // hip and pin bone assessment (bs_hip_and_pin_bone_dropdown - **********)
        // udder teat score (animal_udder_score_1 - **********),
        // in case udder score was not calculated after update
        // cleft visibility (uts_udder_cleft_visibility_dropdown - **********)
        // udder position / depth (uts_udder_position_dropdown - **********)
        // udder balance (uts_udder_balance_dropdown - **********)
        // teat placement and direction (uts_udder_teat_placement_dropdown - **********)
        // teat length (uts_udder_teat_length_dropdown - **********)
        // needed for price, ok for health score
        // additional teat (aphs_additional_teats_dropdown - **********)
        // stretchability of udder (aphs_stretchability_of_udder_dropdown - **********)
        // not mandatory for health score, impacts price a little
        // spread of patches (ap_spread_of_patches_dropdown - **********),
        // horns (ap_horns_exist_or_not_dropdown - **********),
        // veins on udder (aphs_visibility_of_veins_dropdown - **********), 
        // gap in teeth (ap_gap_in_teeth_dropdown - **********)
        // if anything is missing, send back data with return_code = -1
        // translate to scores if needed
        // generate price with necessary adjustments
        const animalClassifiers = ['animal_health_score', 'animal_weight_1', 'animal_average_lpd_1', 'max_lpd_of_animal_1',
          'number_of_calvings_1', 'number_of_months_pregnant_1', 'months_pregnant_as_of_date',
          'animal_locomotion_score_dropdown', 'hs_foreign_body_area_dropdown', 'animal_body_score_1',
          'bs_tail_and_pin_bone_dropdown', 'bs_back_bone_dropdown', 'bs_hip_and_pin_bone_dropdown',
          'animal_udder_score_1', 'uts_udder_cleft_visibility_dropdown', 'uts_udder_position_dropdown',
          'uts_udder_balance_dropdown', 'uts_udder_teat_placement_dropdown',
          'uts_udder_teat_length_dropdown', 'aphs_additional_teats_dropdown',
          'aphs_stretchability_of_udder_dropdown', 'ap_spread_of_patches_dropdown',
          'ap_horns_exist_or_not_dropdown', 'aphs_visibility_of_veins_dropdown', 'ap_gap_in_teeth_dropdown']
        const classificationData = await loadClassificationData('ANIMAL_CLASSIFICATION', animalId, animalClassifiers)
        console.log('oc r GCEP c 2, classificationData = ', classificationData)
        const oneMonthPrior = moment().subtract(1, 'M').toDate()
        const oneYearPrior = moment().subtract(1, 'M').toDate()

        /* health score(animal_health_score - **********)
        -10101 - health score not generated at all
        -10102 - health score not updated in the last 1 month */
        const healthScoreParameter = classificationData['animal_health_score']
        const healthScoreParameterLastUpdatedAt = classificationData['animal_health_score_updated_at']
        if (healthScoreParameter === undefined
          || healthScoreParameter === null) {
          // fairly old data
          animalPriceGenerationErrorCodes.push(-10101)
        }
        if (healthScoreParameter !== undefined
          && healthScoreParameter !== null
          && healthScoreParameterLastUpdatedAt < oneMonthPrior) {
          // fairly old data
          animalPriceGenerationWarningCodes.push(-10102)
        }

        /* body score (animal_body_score_1 - **********),
        in case body score was not calculated after update
        tail and pin bone assessment (bs_tail_and_pin_bone_dropdown - **********)
        back bone assessment (bs_back_bone_dropdown - **********)
        hip and pin bone assessment (bs_hip_and_pin_bone_dropdown - **********)
        -10103 - body score not generated at all
        -10104 - body score not updated in the last 1 month
        -10105 - body score parameters not available
        -10106 - body score parameters not updated in the last 1 month
        -10107 - body score parameters updated after last body score generation */
        let bodyScore = classificationData['animal_body_score_1']
        const bodyScoreLastUpdatedAt = classificationData['animal_body_score_1_updated_at']
        const bsTailAndPinBoneParameter = classificationData['bs_tail_and_pin_bone_dropdown']
        const bsTailAndPinBoneParameterLastUpdatedAt = classificationData['bs_tail_and_pin_bone_dropdown_updated_at']
        const bsBackBoneParameter = classificationData['bs_back_bone_dropdown']
        const bsBackBoneParameterLastUpdatedAt = classificationData['bs_back_bone_dropdown_updated_at']
        const bsHipAndPinBoneParameter = classificationData['bs_hip_and_pin_bone_dropdown']
        const bsHipAndPinBoneLastUpdatedAt = classificationData['bs_hip_and_pin_bone_dropdown_updated_at']
        
        if (bodyScore !== undefined && bodyScore !== null && bodyScoreLastUpdatedAt < oneMonthPrior) {
          animalPriceGenerationWarningCodes.push(-10104)
        }
        if (bsTailAndPinBoneParameter === undefined || bsTailAndPinBoneParameter === null
            || bsBackBoneParameter === undefined || bsBackBoneParameter === null
            || bsHipAndPinBoneParameter === undefined || bsHipAndPinBoneParameter === null) {
          animalPriceGenerationWarningCodes.push(-10105)
        } else {
          if (bsTailAndPinBoneParameterLastUpdatedAt < oneMonthPrior
              || bsBackBoneParameterLastUpdatedAt < oneMonthPrior
              || bsHipAndPinBoneLastUpdatedAt < oneMonthPrior) {
            animalPriceGenerationWarningCodes.push(-10106)
          }
          if (bsTailAndPinBoneParameterLastUpdatedAt > bodyScoreLastUpdatedAt
            && bsBackBoneParameterLastUpdatedAt > bodyScoreLastUpdatedAt
            && bsHipAndPinBoneLastUpdatedAt > bodyScoreLastUpdatedAt) {
           animalPriceGenerationWarningCodes.push(-10107)
          }
          if (bodyScore === undefined || bodyScore === null || animalPriceGenerationWarningCodes.includes(-10107)) {
            bodyScore = calculateBodyScore({bsTailAndPinBoneParameter, bsBackBoneParameter, bsHipAndPinBoneParameter}, animalPriceGenerationErrorCodes, animalPriceGenerationWarningCodes)
          }
        }
        if (bodyScore === undefined || bodyScore === null) {
          animalPriceGenerationErrorCodes.push(-10103)
        }

        /* udder teat score (animal_udder_score_1 - **********),
        in case udder score was not calculated after update
        cleft visibility (uts_udder_cleft_visibility_dropdown - **********)
        udder position / depth (uts_udder_position_dropdown - **********)
        udder balance (uts_udder_balance_dropdown - **********)
        teat placement and direction (uts_udder_teat_placement_dropdown - **********)
        teat length (uts_udder_teat_length_dropdown - **********)
        -10108 - udder teat score not generated at all
        -10109 - udder teat score not updated in the last 1 month
        -10110 - udder teat parameters not available
        -10111 - udder teat parameters not updated in the last 1 month
        -10112 - udder teat parameters updated after last udder teat generation */
        let udderAndTeatScore = classificationData['animal_udder_score_1']
        const udderAndTeatScoreLastUpdatedAt = classificationData['animal_udder_score_1_updated_at']
        const utsCleftVisibilityParameter = classificationData['uts_udder_cleft_visibility_dropdown']
        const utsCleftVisibilityParameterLastUpdatedAt = classificationData['uts_udder_cleft_visibility_dropdown_updated_at']
        const utsUdderPositionOrDepthParameter = classificationData['uts_udder_position_dropdown']
        const utsUdderPositionOrDepthParameterLastUpdatedAt = classificationData['uts_udder_position_dropdown_updated_at']
        const utsUdderBalanceParameter = classificationData['uts_udder_balance_dropdown']
        const utsUdderBalanceParameterLastUpdatedAt = classificationData['uts_udder_balance_dropdown_updated_at']
        const utsTeatPlacementAndDirectionParameter = classificationData['uts_udder_teat_placement_dropdown']
        const utsTeatPlacementAndDirectionParameterLastUpdatedAt = classificationData['uts_udder_teat_placement_dropdown_updated_at']
        const utsTeatLengthParameter = classificationData['uts_udder_teat_length_dropdown']
        const utsTeatLengthParameterLastUpdatedAt = classificationData['uts_udder_teat_length_dropdown_updated_at']

        if (udderAndTeatScore !== undefined && udderAndTeatScore !== null && udderAndTeatScoreLastUpdatedAt < oneMonthPrior) {
          animalPriceGenerationWarningCodes.push(-10109)
        }
        if (utsCleftVisibilityParameter === undefined || utsCleftVisibilityParameter === null
          || utsUdderPositionOrDepthParameter === undefined || utsUdderPositionOrDepthParameter === null
          || utsUdderBalanceParameter === undefined || utsUdderBalanceParameter === null
          || utsTeatPlacementAndDirectionParameter === undefined || utsTeatPlacementAndDirectionParameter === null
          || utsTeatLengthParameter === undefined || utsTeatLengthParameter === null) {
          animalPriceGenerationWarningCodes.push(-10110)
        } else {
          if (utsCleftVisibilityParameterLastUpdatedAt < oneMonthPrior
              || utsUdderPositionOrDepthParameterLastUpdatedAt < oneMonthPrior
              || utsUdderBalanceParameterLastUpdatedAt < oneMonthPrior
              || utsTeatPlacementAndDirectionParameterLastUpdatedAt < oneMonthPrior
              || utsTeatLengthParameterLastUpdatedAt < oneMonthPrior) {
            animalPriceGenerationWarningCodes.push(-10111)
          }
          if (utsCleftVisibilityParameterLastUpdatedAt > bodyScoreLastUpdatedAt
            && utsUdderPositionOrDepthParameterLastUpdatedAt > bodyScoreLastUpdatedAt
            && utsUdderBalanceParameterLastUpdatedAt > bodyScoreLastUpdatedAt
            && utsTeatPlacementAndDirectionParameterLastUpdatedAt > bodyScoreLastUpdatedAt
            && utsTeatLengthParameterLastUpdatedAt > bodyScoreLastUpdatedAt) {
           animalPriceGenerationWarningCodes.push(-10112)
          }
          if (udderAndTeatScore === undefined || udderAndTeatScore === null || animalPriceGenerationWarningCodes.includes(-10112)) {
            udderAndTeatScore = calculateUdderAndTeatScore({utsCleftVisibilityParameter, utsUdderPositionOrDepthParameter, utsUdderBalanceParameter, utsTeatPlacementAndDirectionParameter, utsTeatLengthParameter}, animalPriceGenerationErrorCodes, animalPriceGenerationWarningCodes)
          }
        }
        if (udderAndTeatScore === undefined || udderAndTeatScore === null) {
          animalPriceGenerationErrorCodes.push(-10108)
        }

        /* weight (animal_weight_1 - **********), lpd (animal_average_lpd_1 - **********)
        -10119 - lpd not available
        -10120 - lpd not updated in the last 1 month
        -10121 - weight not available
        -10122 - weight not updated in the last 1 month 
        -10134 - max lpd not available
        -10135 - max lpd not updated in the last 12 months */
        const lastLPDRecorded = classificationData['animal_average_lpd_1']
        const lastLPDRecordedDate = classificationData['animal_average_lpd_1_updated_at']
        const maxLPDRecorded = classificationData['max_lpd_of_animal_1']
        const maxLPDRecordedDate = classificationData['max_lpd_of_animal_1_updated_at']
        const animalWeight = classificationData['animal_weight_1']
        const animalWeightLastUpdatedAt = classificationData['animal_weight_1_updated_at']

        if (lastLPDRecorded === null || lastLPDRecorded === undefined) {
          animalPriceGenerationErrorCodes.push(-10119)
        } else if (lastLPDRecordedDate < oneMonthPrior) {
          animalPriceGenerationWarningCodes.push(-10120)
        }

        if (maxLPDRecorded === null || maxLPDRecorded === undefined) {
          animalPriceGenerationErrorCodes.push(-10134)
        } else if (maxLPDRecordedDate < oneYearPrior) {
          animalPriceGenerationWarningCodes.push(-10135)
        }

        if (animalWeight === null || animalWeight === undefined) {
          animalPriceGenerationErrorCodes.push(-10121)
        } else if (animalWeightLastUpdatedAt < oneMonthPrior) {
          animalPriceGenerationWarningCodes.push(-10122)
        }
        
        /* calvings (number_of_calvings_1 - **********),
        months pregnancy (number_of_months_pregnant_1 - **********),
        months pregnant as of (months_pregnant_as_of_date - **********)
        -10123 - calving is 0, please verify
        -10124 - pregnancy months is 0, please verify
        -10125 - pregnancy months as of is not available */
        const numberOfCalvings = classificationData['number_of_calvings_1']
        const numberOfMonthsPregnantInDB = classificationData['number_of_months_pregnant_1']
        let numberOfMonthsPregnant
        const monthsPregnantAsOfDate = classificationData['months_pregnant_as_of_date']
        if (numberOfCalvings === 0) {
          animalPriceGenerationWarningCodes.push(-10123)
        }
        if (numberOfMonthsPregnantInDB === 0) {
          animalPriceGenerationWarningCodes.push(-10124)
        }
        if ((monthsPregnantAsOfDate === undefined || monthsPregnantAsOfDate === null) && numberOfMonthsPregnantInDB !== 0) {
          animalPriceGenerationErrorCodes.push(-10125)
        }
        if (monthsPregnantAsOfDate !== undefined && monthsPregnantAsOfDate !== null && numberOfMonthsPregnantInDB !== 0) {
          if ((moment().diff(moment(monthsPregnantAsOfDate), 'months') + numberOfMonthsPregnantInDB) > 8.75) {
            numberOfMonthsPregnant = 0
          } else {
            numberOfMonthsPregnant = moment().diff(moment(monthsPregnantAsOfDate), 'days')/30 + numberOfMonthsPregnantInDB
          }
        }    

        /* locomotion score (animal_locomotion_score_dropdown - **********),
        foreign body (hs_foreign_body_area_dropdown - **********),
        -10126 - locomotion score is not available
        -10127 - foreign body parameter is not available */
        let locomotionScore
        const locomotionScoreParameter = classificationData['animal_locomotion_score_dropdown']
        const locomotionScoreLastUpdatedAt = classificationData['animal_locomotion_score_dropdown_updated_at']
        const foreignBodyPresenceParameter = classificationData['hs_foreign_body_area_dropdown']
        const foreignBodyPresenceParameterLastUpdatedAt = classificationData['hs_foreign_body_area_dropdown_updated_at']
        if (locomotionScoreParameter === undefined || locomotionScoreParameter === null) {
          animalPriceGenerationWarningCodes.push(-10126)
        }
        if (foreignBodyPresenceParameter === undefined || foreignBodyPresenceParameter === null) {
          animalPriceGenerationWarningCodes.push(-10127)
        }

        /* additional teat (aphs_additional_teats_dropdown - **********)
        stretchability of udder (aphs_stretchability_of_udder_dropdown - **********)
        -10128 - additional teat parameter is not available
        -10129 - stretchability of udder parameter is not available */

        const additionalTeatParameter = classificationData['aphs_additional_teats_dropdown']
        const stretchabilityOfUdderParameter = classificationData['aphs_stretchability_of_udder_dropdown']
        if (additionalTeatParameter === undefined || additionalTeatParameter === null) {
          animalPriceGenerationWarningCodes.push(-10128)
        }
        if (stretchabilityOfUdderParameter === undefined || stretchabilityOfUdderParameter === null) {
          animalPriceGenerationWarningCodes.push(-10129)
        }
        /* spread of patches (ap_spread_of_patches_dropdown - **********),
        horns (ap_horns_exist_or_not_dropdown - **********),
        veins on udder (aphs_visibility_of_veins_dropdown - **********), 
        gap in teeth (ap_gap_in_teeth_dropdown - **********)
        -10130 - spread of patches or not parameter is not available
        -10131 - horns exist or not parameter is not available
        -10132 - veins on udder parameter is not available
        -10133 - gap in teeth parameter is not available */


        const spreadOfPatchesParameter = classificationData['ap_spread_of_patches_dropdown']
        const hornsExistOrNotParameter = classificationData['ap_horns_exist_or_not_dropdown']
        const visibilityOfVeinsOnUdderParameter = classificationData['aphs_visibility_of_veins_dropdown']
        const gapInTeethParameter = classificationData['ap_gap_in_teeth_dropdown']
        if (spreadOfPatchesParameter === undefined || spreadOfPatchesParameter === null) {
          animalPriceGenerationWarningCodes.push(-10130)
        }
        if (hornsExistOrNotParameter === undefined || hornsExistOrNotParameter === null) {
          animalPriceGenerationWarningCodes.push(-10131)
        }
        if (visibilityOfVeinsOnUdderParameter === undefined || visibilityOfVeinsOnUdderParameter === null) {
          animalPriceGenerationWarningCodes.push(-10132)
        }
        if (gapInTeethParameter === undefined || gapInTeethParameter === null) {
          animalPriceGenerationWarningCodes.push(-10133)
        }

        if (animalPriceGenerationErrorCodes.length > 0) {
          // cannot calculate price
          numberOfUnsuccessfulPriceGeneration++
          animalPriceGenerationErrorCodes.push(-10200)
        } else {
          const priceObject = generatePrice(
            {bodyScore, udderAndTeatScore, lastLPDRecorded, maxLPDRecorded, animalWeight, numberOfCalvings, numberOfMonthsPregnant},
            {locomotionScoreParameter, foreignBodyPresenceParameter},
            {additionalTeatParameter, stretchabilityOfUdderParameter},
            {spreadOfPatchesParameter, hornsExistOrNotParameter, visibilityOfVeinsOnUdderParameter, gapInTeethParameter},
            animalPriceGenerationErrorCodes,
            animalPriceGenerationWarningCodes
          )
          if (animalPriceGenerationErrorCodes.includes(-10201)) {
            configurationProblem = true
          }
          individualAnimalPriceGenerationResult.price_object = priceObject
          const saveResult = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
            try {
              // save price
              await saveClassificationData(transactionalEntityManager, 'ANIMAL_CLASSIFICATION', animalId, {cattle_exchange_price: priceObject});
              numberOfSuccessfulPriceGeneration++
            } catch (error) {
              numberOfUnsuccessfulPriceGeneration++
              console.log('oc GCEP c 20, error')
              console.log('oc GCEP c 20a, error = ', error)
              // throw error
            }
          })
        }
        animalPriceGenerationResults.push(individualAnimalPriceGenerationResult)
      }
      if (configurationProblem) {
        returnValue.return_code = -1001
      } else if (numberOfSuccessfulPriceGeneration > 0 && numberOfUnsuccessfulPriceGeneration > 0) {
        returnValue.return_code = -1003
      } else if (numberOfSuccessfulPriceGeneration === 0 && numberOfUnsuccessfulPriceGeneration > 0) {
        returnValue.return_code = -1002
      }
      return returnValue
    } catch (error) {
      console.log('oc r GCEP c 10, error')
      console.log('oc r GCEP c 10a, error = ', error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class CreateCustomerServiceTask {
  async create /*or is it update*/(data, params) {
    try {
      console.log('oc r GCEP c 1')
      const returnValue = {return_code: 0}
      const animalPriceGenerationResults = []
      returnValue.animal_price_generation_results = animalPriceGenerationResults
      const mainDBConnection = dbConnections().main
      
      const saveResult = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
        try {          
          const taskRepo = transactionalEntityManager.getRepository('care_calendar')
          const taskEntity = mainDBConnection.entities['care_calendar']
          // const animalId = data[0]
          // data.forEach(async (animalId, index) => {
          console.log('oc COFAS c 2')

          console.log('oc COFAS c 3, animal_id = ', data.animal_id, ', activity_date = ', data.activity_date, ', activity_details = ', data.activity_details)
          const taskObject = {
            entity_type_id: 1000460008,
            entity_uuid: data.animal_id, 
            activity_id: 1000700003,
            activity_date: data.activity_date,
            calendar_activity_status: 1000290001
          }
          console.log('oc COFAS c 4, taskObject = ', taskObject)
          const createdTask = await taskRepo.insert(taskObject)
          console.log('oc COFAS c 5, createdTask = ', createdTask)
          const createdTaskUUID = createdTask.identifiers[0].care_calendar_id
          returnValue.created_task_id = createdTaskUUID

          const noteTableEntity = mainDBConnection.entities['note']
          const commentObject = {
            note_type_id: 1000440015,
            entity_1_type_id: 1000460004,
            note_time: new Date(),
            note: {ul: data.activity_details},
            entity_1_uuid: createdTaskUUID
          }
          const notesInsertResults = await transactionalEntityManager.createQueryBuilder().insert().into(noteTableEntity).values(commentObject).execute()
          
        } catch (error) {
          console.log('oc UASS c 20, error')
          console.log('oc UASS c 20a, error = ', error)
          throw error
        }
      })
      return returnValue
    } catch (error) {
      console.log('oc r GCEP c 10, error')
      console.log('oc r GCEP c 10a, error = ', error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class CustomerServiceTask {
  async create /*or is it update*/(data, params) {
    try {
      const returnValue = { return_code: 0 }
      const mainDBConnections = dbConnections().main
      // console.log('oc r SAR c 1, data = ', data)

      const limitString = generateLimitString(data)
      const sortString = generateSortString(data) 
      const whereClause = generateWhereClause(data) 

      const excludeBDM = (data.excludeBDM === true) ? true : false

      const innerQuery = `
        select cc.care_calendar_id, a.animal_id, c.customer_id,
          case when a.active = 1000100001 then 'Active' else 'Not Active' end as animal_active,
          case when sa.entity_relationship_id is not null or sf.entity_relationship_id is not null then 'Snoozed' else 'Not Snoozed' end as snooze_status,
          cc.calendar_activity_status as task_status_id, coalesce(cas_rr.reference_name_l10n->'en', cas_rr.reference_name_l10n->'ul') task_status,
          cc.completion_date as task_completion_time,
          coalesce(c.customer_name_l10n->>'en', c.customer_name_l10n->>'ul') customer_name, c.mobile_number,
          et_ac.value_string_256 animal_ear_tag,
          coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') taluk, rsv.taluk_id,
          coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') village, rsv.village_id,
          ${!excludeBDM ? 's_g.bdm_uuid, s_g.bdm, s_g.bdm_mobile,' : ''}
          /*'s_g.bdm_uuid, s_g.bdm, s_g.bdm_mobile,'*/
          pv_g.paravet_uuid, pv_g.paravet, pv_g.paravet_mobile,
          sp_ac.updated_at with_krushal_from_date_updated_at,
          coalesce(sp_rr.reference_name_l10n->>'en', sp_rr.reference_name_l10n->>'ul') subscription_plan,
          coalesce(at_rr.reference_name_l10n->>'en', at_rr.reference_name_l10n->>'ul') animal_type,
          coalesce(t_i.note->>'en', t_i.note->>'ul') task_instructions,
          a.active as animal_active_id
        from main.care_calendar cc 
        inner join main.ref_reference cas_rr on cas_rr.reference_id = cc.calendar_activity_status
        inner join main.animal a on cc.entity_uuid = a.animal_id 
        inner join main.entity_relationship er on er.entity_relationship_type_id = 1000210004 
          and er.entity_2_entity_uuid = a.animal_id and er.active = 1000100001
        inner join main.customer c on c.customer_id = er.entity_1_entity_uuid
          /* and c.active = 1000100001 and c.customer_type_id in (1000220001) */
        inner join main.customer_classification v_cc on v_cc.active = 1000100001
          and v_cc.classifier_id = 2000000055 and v_cc.customer_id = c.customer_id 
        inner join main.ref_sdtv_view rsv on rsv.village_id = v_cc.value_reference_id 
        inner join main.animal_classification et_ac on et_ac.active = 1000100001
          and et_ac.classifier_id = 2000000034 and et_ac.animal_id = a.animal_id
        ${!excludeBDM ? 
          " \
            left join ( \
              select s.staff_id as bdm_uuid, coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') bdm, \
              s.mobile_number bdm_mobile, eg.geography_id \
              from main.entity_geography eg \
              inner join main.staff s on s.staff_id = eg.entity_uuid and s.active = 1000100001 \
              where eg.entity_type_id in (1000230001, 1000230005) and eg.active = 1000100001 \
              and eg.geography_type_id = 1000320004 \
            ) s_g on s_g.geography_id = rsv.village_id \
          "
          : ''}
        /* left join (
          select s.staff_id as bdm_uuid, coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') bdm,
            s.mobile_number bdm_mobile, eg.geography_id
          from main.entity_geography eg
          inner join main.staff s on s.staff_id = eg.entity_uuid and s.active = 1000100001
          where eg.entity_type_id in (1000230001, 1000230005) and eg.active = 1000100001
            and eg.geography_type_id = 1000320004
        ) s_g on s_g.geography_id = rsv.village_id */
        left join (
          select s.staff_id as paravet_uuid, coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') paravet,
            s.mobile_number paravet_mobile, eg.geography_id
          from main.entity_geography eg
          inner join main.staff s on s.staff_id = eg.entity_uuid and s.active = 1000100001
          where eg.entity_type_id in (1000230004) and eg.active = 1000100001
            and eg.geography_type_id = 1000320004
        ) pv_g on pv_g.geography_id = rsv.village_id
        left join main.animal_classification sp_ac on sp_ac.active = 1000100001 and sp_ac.classifier_id = 2000000051
          and sp_ac.animal_id = a.animal_id
        left join main.ref_reference sp_rr on sp_rr.reference_id = sp_ac.value_reference_id
        left join main.animal_classification at_ac on at_ac.active = 1000100001 and at_ac.classifier_id = 2000000035
          and at_ac.animal_id = a.animal_id
        left join main.ref_reference at_rr on at_rr.reference_id = at_ac.value_reference_id 
        left join main.note t_i on t_i.entity_1_uuid = cc.care_calendar_id and t_i.entity_1_type_id = 1000460004
          and t_i.note_type_id = 1000440015
        left join main.entity_relationship sa on sa.entity_relationship_type_id = 1000210014 and sa.entity_2_entity_uuid = cc.care_calendar_id
          and sa.entity_1_entity_uuid = a.animal_id and sa.entity_1_type_id = 1000460004 and sa.active = 1000100001 and sa.start_date < now() and sa.end_date > now()
        left join main.entity_relationship sf on sf.entity_relationship_type_id = 1000210015 and sf.entity_1_entity_uuid = c.customer_id
          and sf.entity_2_entity_uuid = cc.care_calendar_id and sf.entity_1_type_id = 1000460001 and sf.active = 1000100001 and sf.start_date < now() and sf.end_date > now()
        where cc.active = 1000100001 and cc.entity_type_id = 1000460008       
      `

      const countQuery = `
        select count(*) as count from (
          ${innerQuery}
        ) outerTable
        ${whereClause}
      `
      const complaintResultCount = await mainDBConnections.manager.query(countQuery)
      const count = complaintResultCount[0].count

      const query = `
        select * from (
          ${innerQuery}
        ) outerTable
        ${whereClause}
        ${sortString}
        ${limitString}
      `
      const complaintResult = await mainDBConnections.manager.query(query)
      const complaintResult2 = postProcessRecords(undefined, complaintResult, {})

      const distinctVillageQuery = `
        select distinct village_id, village value, village text from (
          ${innerQuery}
        ) outerTable
      `
      // ${whereClause}
      const distinctVillageResult = await mainDBConnections.manager.query(distinctVillageQuery)
      const distinctVillageResult2 = postProcessRecords(undefined, distinctVillageResult, {})
      returnValue.village_filter_values = distinctVillageResult2

      returnValue.count = count
      returnValue.report = complaintResult2
      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async find(params) {
    try {
      const returnValue = {return_code: 0}
      const mainDBConnections = dbConnections().main
      const tableFilterData = {}
      returnValue.tableFilters = tableFilterData
      const distinctTalukQuery = `
        select distinct taluk_id, taluk_id as value, coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') as text
        from main.customer_classification cc
        inner join main.ref_sdtv_view rsv on rsv.village_id = cc.value_reference_id
        where cc.active = 1000100001 and cc.classifier_id = 2000000055
      `
      const distinctTalukFilterValuesResult = await mainDBConnections.manager.query(distinctTalukQuery)
      const distinctTalukFilterValuesResult2 = postProcessRecords(undefined, distinctTalukFilterValuesResult, {})
      tableFilterData.taluk_filter_values = distinctTalukFilterValuesResult2

      // const distinctVillageQuery = `
      //   select distinct village_id, coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') as value, coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') as text
      //   from main.customer_classification cc
      //   inner join main.ref_sdtv_view rsv on rsv.village_id = cc.value_reference_id
      //   where cc.active = 1000100001 and cc.classifier_id = 2000000055      
      // `
      // const distinctVillageFilterValuesResult = await mainDBConnections.manager.query(distinctVillageQuery)
      // const distinctVillageFilterValuesResult2 = postProcessRecords(undefined, distinctVillageFilterValuesResult, {})
      // returnValue.village_filter_values = distinctVillageFilterValuesResult2

      const staffQuery = `
        select s.staff_id as value, coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as text
        from main.staff s
        where s.active = 1000100001 and s.staff_type_id in (1000230001, 1000230005)
      `
      const staffFilterValuesResult = await mainDBConnections.manager.query(staffQuery)
      const staffFilterValuesResult2 = postProcessRecords(undefined, staffFilterValuesResult, {})
      tableFilterData.staff_filter_values = staffFilterValuesResult2


      return returnValue;
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class UpdateTaskStatus {
  async create /*or is it update*/(data, params) {
    try {
      const returnValue = {return_code: 0}
      console.log('oc MAS c 1, data = ', data)
      // create item for animal
      // copy item classification from animal
      // copy item document
      // create item listing linking to item
      const mainDBConnection = dbConnections().main
      const saveResult = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
        try {
          const taskRepo = transactionalEntityManager.getRepository('care_calendar')
          const taskEntity = mainDBConnection.entities['care_calendar']
          const valueToBeSet = { calendar_activity_status: data.task_status_id[0]}
          if (data.task_status_id[0] === 1000300050) {
            valueToBeSet.completion_date = new Date()
          }
          const updateResult = await taskRepo
            .createQueryBuilder()
            .update()
            .set(valueToBeSet)
            .where("care_calendar_id = :id", { id: data.care_calendar_id })
            .execute()
          
          const noteTableEntity = mainDBConnection.entities['note']
          const commentObject = {
            note_type_id: 1000440017,
            entity_1_type_id: 1000220003,
            note_time: new Date(),
            note: {ul: data.update_status_comments},
            entity_1_uuid: data.care_calendar_id
          }
          const notesInsertResults = await transactionalEntityManager.createQueryBuilder().insert().into(noteTableEntity).values(commentObject).execute()
        } catch (error) {
          console.log('oc MAS c 20, error')
          console.log('oc MAS c 20a, error = ', error)
          throw error
        }
      })
      console.log('oc MAS c 1a, saveResult = ', saveResult)
      returnValue.save_result = saveResult
      return returnValue
    } catch (error) {
      console.log('oc MAS c 10, error')
      console.log('oc MAS c 10a, error = ', error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

const convertToKeyMap = (array, key) => {
  const keyMap = {}
  array.map((object, index) => {
    keyMap[object[key]] = object
  })
  return keyMap
}

const generateFarmFarmPatchCropActions = (periodListData) => {
  const seasonFarmPatchCropMap = {}
  let allPossibleCropsWithDuplicates = []
  for (const toBeUpdatedPeriod of periodListData.toBeUpdatedPeriodsAndCrops) {
    console.log("gFFPCA u 10")
    const periodId = toBeUpdatedPeriod.period_id
    const assignedCropIds = toBeUpdatedPeriod.farmer_grown_crops
    allPossibleCropsWithDuplicates = allPossibleCropsWithDuplicates.concat(assignedCropIds)
    const dbCropIds = [...toBeUpdatedPeriod.farmer_grown_crops_in_db]
    const assignedFarmIds = toBeUpdatedPeriod.farms_grown_in
    const dbFarmIds = [...toBeUpdatedPeriod.farms_grown_in_in_db]
    // if no farm assigned, throw error
    const newFarmsForPeriod = lodashObject.difference(assignedFarmIds, dbFarmIds)
    const removedFarmsForPeriod = lodashObject.difference(dbFarmIds, assignedFarmIds)

    const newCropsForPeriod = lodashObject.difference(assignedCropIds, dbCropIds)
    const removedCropsForPeriod = lodashObject.difference(dbCropIds, assignedCropIds)
    const commonCropsForPeriod = lodashObject.intersection(dbCropIds, assignedCropIds)

    // no crop assigned for period new or past, no farm existed, nothing to do
    if (assignedCropIds.length === 0 && dbCropIds.length === 0 && dbFarmIds.length === 0) {
      // nothing to do
      console.log("gFFPCA u 11")
      continue
    }

    // new crops but no farm assigned
    if (assignedCropIds.length > 0) {
      console.log("gFFPCA u 12")
      if (assignedFarmIds.length === 0) {
        console.log("gFFPCA u 13")
        throw new Error("At least 1 farm needs to be assigned")
      }
    }

    // no change in farm, but change in crops
    console.log("gFFPCA u 20")
    if (newFarmsForPeriod.length === 0 && removedFarmsForPeriod.length === 0 
        && (newCropsForPeriod.length > 0 || removedCropsForPeriod.length > 0)) {
      console.log("gFFPCA u 21")
      let addFarmPatchObjects = []
      for (const farmId of assignedFarmIds) {
        const addFarmPatchObjectArray = newCropsForPeriod.map((cropId) => {
          return {
            farmForWhichFarmPatchHasToBeCreated: farmId,
            cropRelatedToPatch: cropId,
          }
        })
        addFarmPatchObjects = addFarmPatchObjects.concat(addFarmPatchObjectArray)
      }
      let removeFarmPatchObjects = []
      for (const farmId of assignedFarmIds) {
        const removeFarmPatchObjectArray = removedCropsForPeriod.map((cropId) => {
          return {
            farmForWhichFarmPatchHasToBeRemoved: farmId,
            cropRelatedToPatch: cropId,
          }
        })
        removeFarmPatchObjects = removeFarmPatchObjects.concat(removeFarmPatchObjectArray)
      }
      console.log("gFFPCA u 21a, addFarmPatchObjects = ", addFarmPatchObjects)
      console.log("gFFPCA u 21a, removeFarmPatchObjects = ", removeFarmPatchObjects)
      const periodObject = {
        addFarmPatches: addFarmPatchObjects,
        removeFarmPatches: removeFarmPatchObjects,
      }
      seasonFarmPatchCropMap[periodId] = periodObject
    }

    console.log("gFFPCA u 30")
    if (newFarmsForPeriod.length !== 0 || removedFarmsForPeriod.length !== 0) {
      // change in farm
      if (newCropsForPeriod.length === 0 && removedCropsForPeriod.length === 0) {
        // crops have not changed, farms have changed. we need to move the crop related farms from previous to new
        console.log("gFFPCA u 31")
        console.log("gFFPCA u 31a, newFarmsForPeriod = ", newFarmsForPeriod)
        console.log("gFFPCA u 31b, removedFarmsForPeriod = ", removedFarmsForPeriod)
        let moveCropObjects = []
        let removeFarmPatchObjects = []
        let addFarmPatchObjects = []
        if (newFarmsForPeriod.length === 1 && removedFarmsForPeriod.length === 1) {
          const moveCropObjectsToBeAdded = assignedCropIds.map((cropId) => {
            return {
              cropToBeMovedId: cropId,
              farmFromWherePatchIs: removedFarmsForPeriod[0],
              farmFromWherePatchShouldMoveTo: newFarmsForPeriod[0]
            }
          })
          moveCropObjects = moveCropObjects.concat(moveCropObjectsToBeAdded)
        } else {
          for (const cropId of assignedCropIds) {
            const removeFarmPatchObject = removedFarmsForPeriod.map((farmId) => {
              return {
                farmForWhichFarmPatchHasToBeRemoved: farmId,
                cropRelatedToPatch: cropId
              }
            })
            removeFarmPatchObjects = removeFarmPatchObjects.concat(removeFarmPatchObject)
          }
          for (const cropId of assignedCropIds) {
            const addFarmPatchObject = newFarmsForPeriod.map((farmId) => {
              return {
                farmForWhichFarmPatchHasToBeCreated: farmId,
                cropRelatedToPatch: cropId
              }
            })
            addFarmPatchObjects = addFarmPatchObjects.concat(addFarmPatchObject)
          }
        }
        console.log("gFFPCA u 32a, moveCropObjects = ", moveCropObjects)
        console.log("gFFPCA u 32b, removeFarmPatchObjects = ", removeFarmPatchObjects)
        console.log("gFFPCA u 32c, addFarmPatchObjects = ", addFarmPatchObjects)
        const periodObject = {
          addFarmPatches: addFarmPatchObjects,
          removeFarmPatches: removeFarmPatchObjects,
          moveCropsBetweenFarmPatch: moveCropObjects,
        }
        seasonFarmPatchCropMap[periodId] = periodObject
      }
    }

    console.log("gFFPCA u 40")
    if (newFarmsForPeriod.length !== 0 || removedFarmsForPeriod.length !== 0) {
      // change in farm
      if (newCropsForPeriod.length > 0 || removedCropsForPeriod.length > 0) {
        // also some crops have changed
        console.log("gFFPCA u 41")
        let moveCropObjects = []
        let addFarmPatchObjects = []
        let removeFarmPatchObjects = []
        if (newFarmsForPeriod.length === 1 && removedFarmsForPeriod.length === 1) {
          // move crops
          moveCropObjects = commonCropsForPeriod.map((cropId) => {
            return {
              cropToBeMovedId: cropId,
              farmFromWherePatchIs: removedFarmsForPeriod[0],
              farmFromWherePatchShouldMoveTo: newFarmsForPeriod[0]
            }
          })
          // add crops
          for (const cropId of newCropsForPeriod) {
            const addFarmPatchObject = newFarmsForPeriod.map((farmId) => {
              return {
                farmForWhichFarmPatchHasToBeCreated: farmId,
                cropRelatedToPatch: cropId
              }
            })
            addFarmPatchObjects = addFarmPatchObjects.concat(addFarmPatchObject)
          }

          // remove crops
          for (const cropId of removedCropsForPeriod) {
            const removeFarmPatchObject = removedFarmsForPeriod.map((farmId) => {
              return {
                farmForWhichFarmPatchHasToBeRemoved: farmId,
                cropRelatedToPatch: cropId
              }
            })
            removeFarmPatchObjects = removeFarmPatchObjects.concat(removeFarmPatchObject)
          }
        } else {
          // remove crops
          for (const cropId of dbCropIds) {
            const removeFarmPatchObject = dbFarmIds.map((farmId) => {
              return {
                farmForWhichFarmPatchHasToBeRemoved: farmId,
                cropRelatedToPatch: cropId,
              }
            })
            removeFarmPatchObjects = removeFarmPatchObjects.concat(removeFarmPatchObject)
          }

          // add crops
          for (const cropId of assignedCropIds) {
            const addFarmPatchObject = assignedFarmIds.map((farmId) => {
              return {
                farmForWhichFarmPatchHasToBeCreated: farmId,
                cropRelatedToPatch: cropId
              }
            })
            addFarmPatchObjects = addFarmPatchObjects.concat(addFarmPatchObject)
          }
        }
        const periodObject = {
          addFarmPatches: addFarmPatchObjects,
          removeFarmPatches: removeFarmPatchObjects,
          moveCropsBetweenFarmPatch: moveCropObjects,
        }
        console.log("gFFPCA u 41a, moveCropObjects = ", moveCropObjects)
        console.log("gFFPCA u 41b, addFarmPatchObjects = ", addFarmPatchObjects)
        console.log("gFFPCA u 41c, removeFarmPatchObjects = ", removeFarmPatchObjects)
        seasonFarmPatchCropMap[periodId] = periodObject
      }
    }

    // in case of update, first check if farm was already there
    // if not, it means that we need to create
    // create 1 farm patch for each of the crops farm patch in the farm for that period as the period classifier
    // also, in the farm patch classification, assign the crop as the primary crop

    // if farm existed in the past, but new farm has changed
    // check if crop is the same
    // create a farm patch for the season
    // move asset classification from previous to new
    // inactivate farm patch in old

    // if new farm is added to an existing, of one removed from multiple, above logic

    // if farm is same
    // if crop has changed, delete previous crop farm patch
    // add new crop farm patch
    // if multiple crops are there, same logic as above

    // net net, for each season
    // identify farm patches where crop needs to be moved to another
    //    identify old farm and crop
    //    create new asset
    //    copy classification from old asset
    //    delete old asset and asset classification
    // identify farm patches to be created with crop - asset and asset classification
    // identify farm patches to be deleted - asset and asset classification
  }
  for (const toBeInsertedPeriod of periodListData.toBeInsertedPeriodsAndCrops) {
    // add patch for each crop (together with period classification)
    console.log("gFFPCA i 1")
    const periodId = toBeInsertedPeriod.period_id
    const assignedCropIds = toBeInsertedPeriod.farmer_grown_crops
    allPossibleCropsWithDuplicates = allPossibleCropsWithDuplicates.concat(assignedCropIds)
    const dbCropIds = [...toBeInsertedPeriod.farmer_grown_crops_in_db]
    const dbCropClassificationUUIDs = [
      ...toBeInsertedPeriod.farmer_grown_crops_in_db_classification_uuid_array,
    ]
    const assignedFarmIds = toBeInsertedPeriod.farms_grown_in
    const dbFarmIds = [...toBeInsertedPeriod.farms_grown_in_in_db]
    let addFarmPatchObjects = []
    for (const farmId of assignedFarmIds) {
      const farmPatchObject = assignedCropIds.map((cropId) => {
        return {
          farmForWhichFarmPatchHasToBeCreated: farmId,
          cropRelatedToPatch: cropId,
        }
      })
      addFarmPatchObjects = addFarmPatchObjects.concat(farmPatchObject)
    }
    /* const addFarmPatchObjects = assignedFarmIds.map((farmId) => {
      return assignedCropIds.map((cropId) => {
        return {
          farmForWhichFarmPatchHasToBeCreated: farmId,
          cropRelatedToPatch: cropId,
        }
      })
    }) */

    // console.log("gFPFCA 1, addFarmObjects = ", addFarmObjects);

    const periodObject = {
      addFarmPatches: addFarmPatchObjects // { farms: assignedFarmIds },
    }
    seasonFarmPatchCropMap[periodId] = periodObject
  }
  const allPossibleCrops = lodashObject.uniq(allPossibleCropsWithDuplicates)
  return {seasonFarmPatchCropMap, allPossibleCrops}
}

const createFarmPatch = async (farmMap, periodMap, cropMap, transactionalEntityManager, assetTableEntity, farmId, periodId, cropId) => {
  const farmName = farmMap[farmId]
  const periodName = periodMap[periodId]
  const cropName = cropMap[cropId]
  const farmPatchName = farmName + ' ' + periodName + ' ' + cropName + ' Patch'
  const farmPatchAssetObject = {
    asset_type_id: 1001120002,
    asset_name_l10n: {ul: farmPatchName}
  }
  const assetInsertResults = await transactionalEntityManager.createQueryBuilder().insert().into(assetTableEntity).values(farmPatchAssetObject).execute()
  const createdFarmPatchId = assetInsertResults.identifiers[0].asset_id
  return createdFarmPatchId
}

const createFarmToFarmPatchRelationship = async (transactionalEntityManager, entityRelationshipTableEntity, farmId, periodId, farmPatchId) => {
  const farmToFarmPatchentityRelationshipObject = {
    entity_relationship_type_id: 1000210018,
    entity_1_type_id: 1000460012,
    entity_1_entity_uuid: farmId,
    entity_2_type_id: 1000460012,
    entity_2_entity_uuid: farmPatchId,
    qualifier_1_type_id: 1001140001,
    qualifier_1_entity_id: periodId,
  }
  const entityRelationshipInsertResults = await transactionalEntityManager.createQueryBuilder().insert().into(entityRelationshipTableEntity).values(farmToFarmPatchentityRelationshipObject).execute()
  const createdFarmToFarmPatchEntityRelationshipId = entityRelationshipInsertResults.identifiers[0].entity_relationship_id
  return createdFarmToFarmPatchEntityRelationshipId
}

const removeFarmPatch = async (assetTableRepo, assetClassificationTableRepo, entityRelationshipTableRepo, farmPatchId) => {
  const updateFarmToFarmPatchEntityRelationshipResult = await entityRelationshipTableRepo
    .createQueryBuilder()
    .update()
    .set({active: 1000100002})
    .where("entity_relationship_type_id = 1000210018 and active = 1000100001 and entity_2_type_id = 1000460012 and entity_2_entity_uuid = :id", { id: farmPatchId })
    .execute()  
  const updateAssetClassificationResult = await assetClassificationTableRepo
    .createQueryBuilder()
    .update()
    .set({active: 1000100002})
    .where("asset_id = :id", { id: farmPatchId })
    .execute()  
  const updateAssetResult = await assetTableRepo
    .createQueryBuilder()
    .update()
    .set({active: 1000100002})
    .where("asset_id = :id", { id: farmPatchId })
    .execute()  
}

class CropDetails {
  async create /*or is it update*/(data, params) {
    try {
      // throw new Error('ABC')
      const returnValue = { return_code: 0 }
      // console.log('oc r SAR c 1, data = ', data)

      const customerId = data.customer_id
      const classificationData = data.farmer_classification_data
      // const farmList = data.farm_list

      // const farmListData = farmList === undefined ? {toBeUpdatedFarms: [], toBeInsertedFarms: [], toBeDeletedFarms: []} : await extractInsertUpdateDeleteFarms(customerId, farmList)
      // return {anyChange, toBeInsertedList, toBeUpdatedListKeyMap, toBeDeletedKeyList}
      const farmListData = {
        toBeUpdatedFarms: data.farm_list_to_be_updated_key_map !== undefined
                            && data.farm_list_to_be_updated_key_map !== null
                            && typeof data.farm_list_to_be_updated_key_map === 'object'
                        ? Object.values(data.farm_list_to_be_updated_key_map): [],
        toBeInsertedFarms: data.farm_list_to_be_inserted_list !== undefined
                              && data.farm_list_to_be_inserted_list !== null
                              && Array.isArray(data.farm_list_to_be_inserted_list)
                        ? data.farm_list_to_be_inserted_list : [],
        toBeDeletedFarms: data.farm_list_to_be_deleted_key_list !== undefined
                              && data.farm_list_to_be_deleted_key_list !== null
                              && Array.isArray(data.farm_list_to_be_deleted_key_list)
                        ? data.farm_list_to_be_deleted_key_list : []
      }

      const periodListData = {
        toBeUpdatedPeriodsAndCrops: data.period_list_to_be_updated_key_map !== undefined
                            && data.period_list_to_be_updated_key_map !== null
                            && typeof data.period_list_to_be_updated_key_map === 'object'
                        ? Object.values(data.period_list_to_be_updated_key_map): [],
        toBeInsertedPeriodsAndCrops: data.period_list_to_be_inserted_list !== undefined
                              && data.period_list_to_be_inserted_list !== null
                              && Array.isArray(data.period_list_to_be_inserted_list)
                        ? data.period_list_to_be_inserted_list : [],
        toBeDeletedPeriodsAndCrops: data.period_list_to_be_deleted_key_list !== undefined
                              && data.period_list_to_be_deleted_key_list !== null
                              && Array.isArray(data.period_list_to_be_deleted_key_list)
                        ? data.period_list_to_be_deleted_key_list : []
      }

      const farmPatchAcreageData = {
        toBeUpdatedFarmPatchAcreage: data.farm_patch_acreage_to_be_updated_key_map !== undefined
                            && data.farm_patch_acreage_to_be_updated_key_map !== null
                            && typeof data.farm_patch_acreage_to_be_updated_key_map === 'object'
                        ? Object.values(data.farm_patch_acreage_to_be_updated_key_map): [],
        toBeInsertedFarmPatchAcreage: data.farm_patch_acreage_to_be_inserted_list !== undefined
                              && data.farm_patch_acreage_to_be_inserted_list !== null
                              && Array.isArray(data.farm_patch_acreage_to_be_inserted_list)
                        ? data.farm_patch_acreage_to_be_inserted_list : [],
        toBeDeletedFarmPatchAcreage: data.farm_patch_acreage_to_be_deleted_key_list !== undefined
                              && data.farm_patch_acreage_to_be_deleted_key_list !== null
                              && Array.isArray(data.farm_patch_acreage_to_be_deleted_key_list)
                        ? data.farm_patch_acreage_to_be_deleted_key_list : []
      }

      const mainDBConnection = dbConnections().main

      // const seasonFarmPatchCropMap = {}
      const {seasonFarmPatchCropMap, allPossibleCrops} = generateFarmFarmPatchCropActions(periodListData)
      const allRelevantPeriods = Object.keys(seasonFarmPatchCropMap)
      const periodRepo = mainDBConnection.manager.getRepository('ref_period')
      const cropRepo = mainDBConnection.manager.getRepository('ref_crop')

      const relevantPeriodMap = {}
      if (allRelevantPeriods.length > 0) {
        const relevantPeriodResultUnprocessed = await periodRepo
          .createQueryBuilder()
          .select('period_id, period_name_l10n')
          // .from('care_calendar')
          .where('period_id IN (:...periodIds)', { periodIds: allRelevantPeriods })
          .execute()
        const relevantPeriodResult = postProcessRecords(undefined, relevantPeriodResultUnprocessed, {json_columns:['period_name_l10n']})

        
        for (const relevantPeriod of relevantPeriodResult) {
          const returnedArray = assignL10NObjectOrObjectArrayToItSelf([relevantPeriod], 'period_name_l10n')
          relevantPeriodMap[relevantPeriod.period_id] = returnedArray[0]['period_name_l10n']
        }
      }
      
      const relevantCropMap = {}
      if (allPossibleCrops.length > 0) {
        const relevantCropResultUnprocessed = await cropRepo
          .createQueryBuilder()
          .select('crop_id, crop_name_l10n')
          .where('crop_id IN (:...cropIds)', { cropIds: allPossibleCrops })
          .execute()
        const relevantCropResult = postProcessRecords(undefined, relevantCropResultUnprocessed, {json_columns:['crop_name_l10n']})

        
        for (const relevantCrop of relevantCropResult) {
          const returnArray = assignL10NObjectOrObjectArrayToItSelf([relevantCrop], 'crop_name_l10n')
          relevantCropMap[relevantCrop.crop_id] = returnArray[0]['crop_name_l10n']
        }
      }
      
      const getCurrentFarmsOfCustomerQuery = `
        select f.asset_id, f.asset_name_l10n
        from main.entity_relationship c2f
        inner join main.asset f on f.asset_id = c2f.entity_2_entity_uuid and f.asset_type_id = 1001120001
          and f.active = 1000100001
        where c2f.entity_relationship_type_id = 1000210017 and c2f.active = 1000100001
          and c2f.entity_1_type_id = 1000460001 and c2f.entity_2_type_id = 1000460012
          and c2f.entity_1_entity_uuid = '${customerId}'
      `
      const getCurrentFarmsOfCustomerQueryResultUnprocessed = await mainDBConnection.manager.query(getCurrentFarmsOfCustomerQuery)
      const getCurrentFarmsOfCustomerQueryResult = postProcessRecords(undefined, getCurrentFarmsOfCustomerQueryResultUnprocessed, {json_columns:['asset_name_l10n']})
      const customerFarmMap = {}
      for (const customerFarm of getCurrentFarmsOfCustomerQueryResult) {
        const returnArray = assignL10NObjectOrObjectArrayToItSelf([customerFarm], 'asset_name_l10n')
        customerFarmMap[customerFarm.asset_id] = returnArray[0]['asset_name_l10n']
      }

      const saveResult = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
        try {
          const updateClassificationDataResult = await saveClassificationData(transactionalEntityManager, 'CUSTOMER_CLASSIFICATION', customerId, classificationData)
          returnValue.classificationDataUpdateResult = updateClassificationDataResult

          const assetRepo = transactionalEntityManager.getRepository('asset')
          const entityRelationshipRepo = transactionalEntityManager.getRepository('entity_relationship')
          const assetClassificationRepo = transactionalEntityManager.getRepository('asset_classification')
          const assetTableEntity = mainDBConnection.entities['asset']
          const entityRelationshipEntity = mainDBConnection.entities['entity_relationship']

          for (const toBeUpdatedFarm of farmListData.toBeUpdatedFarms) {
            const toBeUpdatedFarmModified = {...toBeUpdatedFarm}
            const assetId = toBeUpdatedFarmModified['asset_id']
            const assetName = toBeUpdatedFarmModified['asset_name_l10n']
            delete toBeUpdatedFarmModified['asset_id']
            delete toBeUpdatedFarmModified['asset_name_l10n']
            const updateAssetClassificationDataResult = await saveClassificationData(transactionalEntityManager, 'ASSET_CLASSIFICATION', assetId, toBeUpdatedFarmModified)
            const updateResult = await assetRepo
              .createQueryBuilder()
              .update()
              .set({asset_name_l10n: assetName})
              .where("asset_id = :id", { id: assetId })
              .execute()
          }
          for (const toBeInsertedFarmInList of farmListData.toBeInsertedFarms) {
            let toBeInsertedFarmClassification = {...toBeInsertedFarmInList}
            const {asset_name_l10n, ...rest} = toBeInsertedFarmClassification
            const assetObject = {
              asset_name_l10n: asset_name_l10n,
              asset_type_id: 1001120001
            }
            const assetInsertResults = await transactionalEntityManager.createQueryBuilder().insert().into(assetTableEntity).values(assetObject).execute()
            const createdAssetId = assetInsertResults.identifiers[0].asset_id
            delete toBeInsertedFarmClassification['asset_name_l10n']
            const entityRelationshipObject = {
              entity_relationship_type_id: 1000210017,
              entity_1_type_id: 1000460001,
              entity_1_entity_uuid: customerId,
              entity_2_type_id: 1000460012,
              entity_2_entity_uuid: createdAssetId
            }
            const entityRelationshipInsertResults = await transactionalEntityManager.createQueryBuilder().insert().into(entityRelationshipEntity).values(entityRelationshipObject).execute()
            const updateAssetClassificationDataResult = await saveClassificationData(transactionalEntityManager, 'ASSET_CLASSIFICATION', createdAssetId, toBeInsertedFarmClassification)
          }
          if (farmListData.toBeDeletedFarms.length > 0) {
            const updateAssetResult = await assetRepo
              .createQueryBuilder()
              .update()
              .set({active: 1000100002})
              .where("asset_id IN (:...id)", { id: farmListData.toBeDeletedFarms })
              .execute()
            const updateERResult = await entityRelationshipRepo
              .createQueryBuilder()
              .update()
              .set({active: 1000100002})
              .where(" \
                entity_relationship_type_id = 1000210017 and entity_1_type_id = 1000460001 \
                and entity_2_type_id = 1000460012 and entity_2_entity_uuid IN (:...id) \
                    ", { id: farmListData.toBeDeletedFarms })
              .execute()
          }

          // const customerFeatureRepo = transactionalEntityManager.getRepository('customer_feature')
          // const customerFeatureClassificationRepo = transactionalEntityManager.getRepository('customer_feature_classification')
          // const customerFeatureTableEntity = mainDBConnection.entities['customer_feature']
          // const customerFeatureClassificationTableEntity = mainDBConnection.entities['customer_feature_classification']

          for (const periodId of Object.keys(seasonFarmPatchCropMap)) {
            const periodRelatedActions = seasonFarmPatchCropMap[periodId]
            if (periodRelatedActions.addFarmPatches && Array.isArray(periodRelatedActions.addFarmPatches) && periodRelatedActions.addFarmPatches.length > 0) {
              // add farms
              for (const addFarmPatchObject of periodRelatedActions.addFarmPatches) {
                // create asset
                const createdFarmPatchId = await createFarmPatch(customerFarmMap, relevantPeriodMap,
                  relevantCropMap, transactionalEntityManager, assetTableEntity,
                  addFarmPatchObject.farmForWhichFarmPatchHasToBeCreated, periodId, addFarmPatchObject.cropRelatedToPatch)

                // create relationship to farm
                await createFarmToFarmPatchRelationship(transactionalEntityManager, entityRelationshipEntity, addFarmPatchObject.farmForWhichFarmPatchHasToBeCreated, periodId, createdFarmPatchId)

                // add crop as classification data
                const updateAssetClassificationDataResult = await saveClassificationData(transactionalEntityManager, 'ASSET_CLASSIFICATION', createdFarmPatchId, {primary_crop: addFarmPatchObject.cropRelatedToPatch})
                
              }
            }
            if (periodRelatedActions.moveCropsBetweenFarmPatch && Array.isArray(periodRelatedActions.moveCropsBetweenFarmPatch) && periodRelatedActions.moveCropsBetweenFarmPatch.length > 0) {
              for (const moveCropBetweenFarmPatchObject of periodRelatedActions.moveCropsBetweenFarmPatch) {
                // cropToBeMovedId: cropId,
                // farmFromWherePatchIs: removedFarmsForPeriod[0],
                // farmFromWherePatchShouldMoveTo: newFarmsForPeriod[0]
                // create new patch
                const createdFarmPatchId = await createFarmPatch(customerFarmMap, relevantPeriodMap,
                  relevantCropMap, transactionalEntityManager, assetTableEntity,
                  moveCropBetweenFarmPatchObject.farmFromWherePatchShouldMoveTo, periodId, moveCropBetweenFarmPatchObject.cropToBeMovedId)

                await createFarmToFarmPatchRelationship(transactionalEntityManager, entityRelationshipEntity, moveCropBetweenFarmPatchObject.farmFromWherePatchShouldMoveTo, periodId, createdFarmPatchId)

                // move crop from previous patch
                // for existing farm, take farm patch of crop
                // and for that farm patch, copy classification data to 
                const existingFarmPatchForFarmSeasonCropQuery = `
                  select fp.asset_id as existing_farm_patch
                  from main.asset rf
                  inner join main.entity_relationship f2p on f2p.entity_relationship_type_id = 1000210018 and f2p.active = 1000100001
                    and f2p.entity_1_type_id = 1000460012 and f2p.entity_2_type_id = 1000460012 and f2p.entity_1_entity_uuid = rf.asset_id
                    and f2p.qualifier_1_type_id = 1001140001 and f2p.qualifier_1_entity_id = ${periodId}
                  inner join main.asset fp on fp.active = 1000100001 and fp.asset_type_id = 1001120002 and fp.asset_id = f2p.entity_2_entity_uuid
                  inner join main.asset_classification fpc on fpc.asset_id = fp.asset_id and fpc.active = 1000100001
                    and fpc.classifier_id = 2000000266 and fpc.value_reference_id = ${moveCropBetweenFarmPatchObject.cropToBeMovedId}
                  where rf.asset_id = '${moveCropBetweenFarmPatchObject.farmFromWherePatchIs}'
                  and rf.active = 1000100001 and rf.asset_type_id = 1001120001
                `
                const existingFarmPatchForFarmSeasonCropQueryResultUnprocessed = await transactionalEntityManager.query(existingFarmPatchForFarmSeasonCropQuery)
                const existingFarmPatchForFarmSeasonCropQueryResult = postProcessRecords(undefined, existingFarmPatchForFarmSeasonCropQueryResultUnprocessed, {json_columns:[]})
                if (existingFarmPatchForFarmSeasonCropQueryResult.length === 0) {
                  throw new Error('Suggestion to move, no farm to move from')
                }
                if (existingFarmPatchForFarmSeasonCropQueryResult.length > 1) {
                  throw new Error('More than 1 farm patch for farm, season, crop, data issue')
                }

                const existingFarmPatch = existingFarmPatchForFarmSeasonCropQueryResult[0].existing_farm_patch

                const copyClassificationFromOldPatchToNewPatchQuery = `
                  insert into main.asset_classification (asset_id, classifier_id, value_int,
                    value_reference_id, value_reference_uuid, value_string_256, value_string_2000,
                    value_double, value_date, value_json, value_l10n, value_string_256_encrypted,
                    value_string_2000_encrypted)                    
                  select '${createdFarmPatchId}', classifier_id, value_int, value_reference_id, value_reference_uuid, value_string_256, value_string_2000, value_double, value_date, value_json, value_l10n, value_string_256_encrypted, value_string_2000_encrypted
                  from main.asset_classification ofpac
                  where ofpac.asset_id = '${existingFarmPatch}' and ofpac.active = 1000100001
                `
                const copyClassificationFromOldPatchToNewPatchQueryResults = await transactionalEntityManager.query(copyClassificationFromOldPatchToNewPatchQuery)


                // remove previous patch
                await removeFarmPatch(assetRepo, assetClassificationRepo, entityRelationshipRepo, existingFarmPatch)
              }
            }
            if (periodRelatedActions.removeFarmPatches && Array.isArray(periodRelatedActions.removeFarmPatches) && periodRelatedActions.removeFarmPatches.length > 0) {
              // remove farms
              for (const removeFarmPatchObject of periodRelatedActions.removeFarmPatches) {
                const existingFarmPatchForFarmSeasonCropQuery = `
                  select fp.asset_id as existing_farm_patch
                  from main.asset rf
                  inner join main.entity_relationship f2p on f2p.entity_relationship_type_id = 1000210018 and f2p.active = 1000100001
                    and f2p.entity_1_type_id = 1000460012 and f2p.entity_2_type_id = 1000460012 and f2p.entity_1_entity_uuid = rf.asset_id
                    and f2p.qualifier_1_type_id = 1001140001 and f2p.qualifier_1_entity_id = ${periodId}
                  inner join main.asset fp on fp.active = 1000100001 and fp.asset_type_id = 1001120002 and fp.asset_id = f2p.entity_2_entity_uuid
                  inner join main.asset_classification fpc on fpc.asset_id = fp.asset_id and fpc.active = 1000100001
                    and fpc.classifier_id = 2000000266 and fpc.value_reference_id = ${removeFarmPatchObject.cropRelatedToPatch}
                  where rf.asset_id = '${removeFarmPatchObject.farmForWhichFarmPatchHasToBeRemoved}'
                  and rf.active = 1000100001 and rf.asset_type_id = 1001120001
                `
                const existingFarmPatchForFarmSeasonCropQueryResultUnprocessed = await transactionalEntityManager.query(existingFarmPatchForFarmSeasonCropQuery)
                const existingFarmPatchForFarmSeasonCropQueryResult = postProcessRecords(undefined, existingFarmPatchForFarmSeasonCropQueryResultUnprocessed, {json_columns:[]})
                if (existingFarmPatchForFarmSeasonCropQueryResult.length === 0) {
                  throw new Error('Suggestion to move, no farm to move from')
                }
                if (existingFarmPatchForFarmSeasonCropQueryResult.length > 1) {
                  throw new Error('More than 1 farm patch for farm, season, crop, data issue')
                }

                const existingFarmPatch = existingFarmPatchForFarmSeasonCropQueryResult[0].existing_farm_patch
                // remove previous patch
                await removeFarmPatch(assetRepo, assetClassificationRepo, entityRelationshipRepo, existingFarmPatch)
                
              }
            }
          }

          for (const toBeUpdatedFarmPatchAcreage of farmPatchAcreageData.toBeUpdatedFarmPatchAcreage) {
            const assetClassificationObject = {
              acres_of_land: toBeUpdatedFarmPatchAcreage['crop_acres_in_farm_period'],
            }
            const updateAssetClassificationDataResult = await saveClassificationData(transactionalEntityManager, 'ASSET_CLASSIFICATION', toBeUpdatedFarmPatchAcreage['farm_patch_id'], assetClassificationObject)
          }
          for (const toBeInsertedFarmPatchAcreage of farmPatchAcreageData.toBeInsertedFarmPatchAcreage) {
            const assetClassificationObject = {
              acres_of_land: toBeInsertedFarmPatchAcreage[crop_acres_in_farm_period],
            }
            const updateAssetClassificationDataResult = await saveClassificationData(transactionalEntityManager, 'ASSET_CLASSIFICATION', toBeInsertedFarmPatchAcreage[farm_patch_id], assetClassificationObject)
          }
          
        } catch (error) {
          console.log('oc UASS c 20, error')
          console.log('oc UASS c 20a, error = ', error)
          throw error
        }
      })
      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async get(id, params) {
    try {
      const returnValue = {return_code: 0}
      const data = {}
      returnValue.data = data

      const mainDBConnections = dbConnections().main
      const numberOfCustomerFarmsQuery = `
        select count(er.entity_2_entity_uuid) as farm_count
        from main.entity_relationship er
        where er.entity_relationship_type_id = 1000210005
        and er.active = 1000100001
        and er.entity_1_entity_uuid = '${id}'
      `
      const numberOfCustomerFarmsQueryResultUnprocessed = await mainDBConnections.manager.query(numberOfCustomerFarmsQuery)
      const numberOfCustomerFarmsQueryResult = postProcessRecords(undefined, numberOfCustomerFarmsQueryResultUnprocessed, {})
      const numberOfFarms = parseInt(numberOfCustomerFarmsQueryResult[0].farm_count)
      console.log('oc r GAD c 2, numberOfFarms = ', numberOfFarms)

      const farmDetailsQuery = `
        select a.asset_id, a.asset_name_l10n, fs_ac.value_double as acres_of_land
        from main.entity_relationship er
        inner join main.asset a on a.active = 1000100001 and a.asset_type_id = 1001120001
          and a.asset_id = er.entity_2_entity_uuid
        left join main.asset_classification fs_ac on fs_ac.active = 1000100001 and fs_ac.classifier_id = 2000000258
          and fs_ac.asset_id = a.asset_id
        where er.entity_relationship_type_id = 1000210017
        and er.entity_1_type_id = 1000460001
        and er.entity_2_type_id = 1000460012
        and er.active = 1000100001
        and er.entity_1_entity_uuid = '${id}'
      `
      const farmDetailsQueryResultUnprocessed = await mainDBConnections.manager.query(farmDetailsQuery)
      const farmDetailsQueryResult = postProcessRecords(undefined, farmDetailsQueryResultUnprocessed, {json_columns:['asset_name_l10n'], double_columns: ['acres_of_land']})
      data.farms = farmDetailsQueryResult
      let farmList = lodashObject.cloneDeep(farmDetailsQueryResult)
      farmList = assignL10NObjectOrObjectArrayToItSelf(farmList, 'asset_name_l10n')
      const firstFarmAsArray = farmList.length > 0 ? [farmList[0].asset_id] : []

      const classificationData = await loadClassificationData('CUSTOMER_CLASSIFICATION', id, ['acres_of_land', 'crops_grown'])
      console.log('oc r GAD c 2, classificationData = ', classificationData)
      data.basic_classification_data = classificationData

      const recentPeriodsAndTheirCropsQuery = `
        select ap.period_id, ap.period_name_l10n, ap.start_date, ap.end_date, rc.crop_id, rc.crop_name_l10n,
          pfp.farm_asset_id, pfp.farm_patch_asset_id, pfp.growing_crop_id
        from (
          select rp.period_type_id, rp.period_id, rp.period_name_l10n, rp.start_date, rp.end_date
          from main.customer_classification cc
          inner join main.ref_crop rc on rc.crop_id = cc.value_reference_id and rc.active = 1000100001
          inner join lateral jsonb_array_elements_text(rc.crop_information->'applicable_period_types') as apt on true
          inner join main.ref_period rp ON apt::integer = rp.period_type_id
            and rp.active = 1000100001 and rp.start_date > now() - interval '2 years' and rp.start_date < now()
          where cc.active = 1000100001 and cc.classifier_id = 2000000257 and cc.customer_id = '${id}'
          group by period_id
        ) ap
        inner join (
            select rc.crop_id, jsonb_array_elements_text(rc.crop_information->'applicable_period_types')::integer as period_type_id, rc.crop_name_l10n
            from main.ref_crop rc
            inner join main.customer_classification cc2 on cc2.active = 1000100001
              and cc2.classifier_id = 2000000257 and cc2.customer_id = '${id}'
              and rc.crop_id = cc2.value_reference_id
        ) rc on rc.period_type_id = ap.period_type_id
        left join (
          select f2p.qualifier_1_entity_id as period_id, f.asset_id farm_asset_id,
            f.asset_name_l10n farm_name, fp.asset_id farm_patch_asset_id,
            fp.asset_name_l10n farm_patch_name, fpc.value_reference_id as growing_crop_id
          from main.entity_relationship c2f
          inner join main.asset f on f.asset_id = c2f.entity_2_entity_uuid and f.active = 1000100001 and f.asset_type_id = 1001120001
          inner join main.entity_relationship f2p on f2p.entity_relationship_type_id = 1000210018 and f2p.active = 1000100001 and f2p.qualifier_1_type_id = 1001140001
            and f2p.entity_1_entity_uuid = c2f.entity_2_entity_uuid
          inner join main.asset fp on fp.asset_id = f2p.entity_2_entity_uuid and fp.active = 1000100001 and fp.asset_type_id = 1001120002
          inner join main.asset_classification fpc on fpc.asset_id = fp.asset_id and fpc.active = 1000100001 and fpc.classifier_id = 2000000266
          where c2f.active = 1000100001 and c2f.entity_relationship_type_id = 1000210017 and c2f.entity_1_entity_uuid = '${id}'
        ) pfp on pfp.period_id = ap.period_id and pfp.growing_crop_id = rc.crop_id
        order by ap.start_date desc, ap.period_id
      `

      const recentPeriodsAndTheirCropsQueryResultUnprocessed = await mainDBConnections.manager.query(recentPeriodsAndTheirCropsQuery)
      const recentPeriodsAndTheirCropsQueryResult = postProcessRecords(undefined, recentPeriodsAndTheirCropsQueryResultUnprocessed, {})
      let lastPeriodId = -1
      const periodAndPossibleCrops = []
      let lastPeriodAndCropsObject
      for (const recentPeriodAndCrop of recentPeriodsAndTheirCropsQueryResult) {
        if (recentPeriodAndCrop.period_id !== lastPeriodId) {
          if (lastPeriodAndCropsObject !== undefined) {
            if (lastPeriodAndCropsObject.farms_grown_in.length === 0) {
              lastPeriodAndCropsObject.farms_grown_in = firstFarmAsArray
            }
          }
          lastPeriodId = recentPeriodAndCrop.period_id
          lastPeriodAndCropsObject = {
            period_id: recentPeriodAndCrop.period_id,
            period_name_l10n: recentPeriodAndCrop.period_name_l10n,
            period_start_date: recentPeriodAndCrop.start_date,
            period_end_date: recentPeriodAndCrop.end_date,
            possible_farmer_crops: [],
            farmer_period_feature_uuid: recentPeriodAndCrop.period_customer_feature_id !== null ? recentPeriodAndCrop.period_customer_feature_id : undefined,
            farmer_period_classification_uuid: recentPeriodAndCrop.period_feature_classification_id !== null ? recentPeriodAndCrop.period_feature_classification_id : undefined,
            farmer_period_id: recentPeriodAndCrop.growing_period_id !== null ? recentPeriodAndCrop.growing_period_id : undefined,
            farmer_grown_crops_in_db: [],
            farmer_grown_crops_in_db_classification_uuid_array: [],
            farmer_grown_crops: [],
            farmer_grown_crops_classification_uuid_array: [],
            farms_grown_in_in_db: [],
            farms_grown_in: [],
            farms_grown_in_list: farmList,
          }
          periodAndPossibleCrops.push(lastPeriodAndCropsObject)
        }
        lastPeriodAndCropsObject.possible_farmer_crops.push(recentPeriodAndCrop.crop_id)
        if (recentPeriodAndCrop.growing_crop_id !== null) {
          if (!lastPeriodAndCropsObject.farmer_grown_crops_in_db.includes(recentPeriodAndCrop.growing_crop_id)) {
            lastPeriodAndCropsObject.farmer_grown_crops_in_db.push(recentPeriodAndCrop.growing_crop_id)
            lastPeriodAndCropsObject.farmer_grown_crops_in_db_classification_uuid_array.push(recentPeriodAndCrop.crop_feature_classification_id)
            lastPeriodAndCropsObject.farmer_grown_crops.push(recentPeriodAndCrop.growing_crop_id)
            lastPeriodAndCropsObject.farmer_grown_crops_classification_uuid_array.push(recentPeriodAndCrop.crop_feature_classification_id)
          }
        }
        if (recentPeriodAndCrop.farm_asset_id !== undefined && recentPeriodAndCrop.farm_asset_id !== null) {
          if (!lastPeriodAndCropsObject.farms_grown_in_in_db.includes(recentPeriodAndCrop.farm_asset_id)) {
            lastPeriodAndCropsObject.farms_grown_in_in_db.push(recentPeriodAndCrop.farm_asset_id)
            lastPeriodAndCropsObject.farms_grown_in.push(recentPeriodAndCrop.farm_asset_id)
          }
        }
      }
      if (lastPeriodAndCropsObject !== undefined) {
        if (lastPeriodAndCropsObject.farms_grown_in.length === 0) {
          lastPeriodAndCropsObject.farms_grown_in = firstFarmAsArray
        }
      }
      data.periods_and_crops = periodAndPossibleCrops

      const seasonFarmAndLevelAcreageQuery = `
        select f.asset_name_l10n as farm_asset_name_l10n, rp.period_name_l10n, rp.start_date period_start_date,
          rp.end_date period_end_date, rc.crop_name_l10n, f2p.entity_2_entity_uuid as farm_patch_id,
          fpa.asset_classification_id as farm_patch_acreage_asset_classification_id,
          fpa.value_double as crop_acres_in_farm_period
        from main.entity_relationship c2f
        inner join main.asset f on f.asset_id = c2f.entity_2_entity_uuid and f.asset_type_id = 1001120001
          and f.active = 1000100001
        inner join main.entity_relationship f2p on f2p.active = 1000100001
          and f2p.entity_1_type_id = 1000460012 and f2p.entity_2_type_id = 1000460012
          and f2p.entity_relationship_type_id = 1000210018 and f2p.entity_1_entity_uuid = f.asset_id
          and f2p.qualifier_1_type_id = 1001140001
        inner join main.ref_period rp on rp.period_id = f2p.qualifier_1_entity_id
          and rp.start_date > now() - interval '1 years' and rp.start_date < now()
        inner join main.asset_classification fpc on fpc.asset_id = f2p.entity_2_entity_uuid
          and fpc.active = 1000100001 and fpc.classifier_id = 2000000266
        inner join main.ref_crop rc on rc.crop_id = fpc.value_reference_id
        left join main.asset_classification fpa on fpa.asset_id = f2p.entity_2_entity_uuid
          and fpa.active = 1000100001 and fpa.classifier_id = 2000000258
        where c2f.active = 1000100001 and c2f.entity_relationship_type_id = 1000210017
        and c2f.entity_2_type_id = 1000460012 and c2f.entity_1_type_id = 1000460001 and c2f.entity_1_entity_uuid = '${id}'
      `
      const seasonFarmAndLevelAcreageQueryResultUnprocessed = await mainDBConnections.manager.query(seasonFarmAndLevelAcreageQuery)
      const seasonFarmAndLevelAcreageQueryResult = postProcessRecords(undefined, seasonFarmAndLevelAcreageQueryResultUnprocessed, {double_columns: ['crop_acres_in_period'], json_columns: ['period_name_l10n', 'crop_name_l10n']})

      data.season_farm_crop_acreage = seasonFarmAndLevelAcreageQueryResult

      return returnValue;
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

module.exports = { SellableAnimalsReportOld, 
  SnoozeForSellableAnimals, UnsnoozeForSellableAnimals, UpdateAnimalSellingStatus, MarkAnimalsSellable,
  SellableAnimalsReport, ItemDetailByItemListingId, PotentialBuyerFarmersForAnimalReport, SnoozeForBuyerFarmers, UpdateBuyingFarmerStatus, FarmersWhoMayWantToBuyAnimals,
  CreateOrderForAnimalSale, AnimalExchangeOrders, UpdateOrderFarmerStatus,
  ManureSnoozeFarmers, GenerateCattleExchangePrice, CreateCustomerServiceTask,
  CustomerServiceTask, UpdateTaskStatus, CropDetails
}