const { LibSmartFeed } = require('@krushal-it/back-end-lib')
const { getDBConnections } = require('@krushal-it/ah-orm')

class SmartFeed {
  async find (params) {
    try {
      console.log('SmartFeed find called with params: ', params)
      const smartFeedLib = new LibSmartFeed()
      return smartFeedLib.find(params)
    } catch (error) {
      return { return_code: -101, message: error.message }
    }
  }

  async create /* or is it update */(data, params) {
    try {
      console.log('SmartFeed create called with data: ', data, ' and params: ', params)
      const result = await getDBConnections().main.manager.transaction(async (transactionalEntityManager) => {
        const smartFeedLib = new LibSmartFeed()
        return smartFeedLib.create(data, params, transactionalEntityManager)
      })
      return result
    } catch (error) {
      return { return_code: -101, message: error.message }
    }
  }

  async get (id, params) {
    try {
      console.log('SmartFeed get called with id: ', id, ' and params: ', params)
      const smartFeedLib = new LibSmartFeed()
      return smartFeedLib.get(id, params)
    } catch (error) {
      return { return_code: -101, message: error.message }
    }
  }
}

module.exports = { SmartFeed }
