const { LibAssetFeature } = require('@krushal-it/back-end-lib')
const { getDBConnections } = require('@krushal-it/ah-orm')

class AssetFeature {
  async find (params) {
    try {
      console.log('AssetFeature find called with params: ', params)
      const assetLib = new LibAssetFeature()
      return assetLib.find(params)
    } catch (error) {
      return { return_code: -101, message: error.message }
    }
  }

  async create /* or is it update */(data, params) {
    try {
      console.log('AssetFeature create called with data: ', data, ' and params: ', params)
      const result = await getDBConnections().main.manager.transaction(async (transactionalEntityManager) => {
        const assetLib = new LibAssetFeature()
        return assetLib.create(data, params, transactionalEntityManager)
      })
      return result
    } catch (error) {
      return { return_code: -101, message: error.message }
    }
  }

  async get (id, params) {
    try {
      console.log('AssetFeature get called with id: ', id, ' and params: ', params)
      const result = await getDBConnections().main.manager.transaction(async (transactionalEntityManager) => {
        const assetLib = new LibAssetFeature()
        return assetLib.get(id, params, transactionalEntityManager)
      })
      return result
    } catch (error) {
      return { return_code: -101, message: error.message }
    }
  }

  async patch /* or is it update */(id, data, params) {
    try {
      console.log('AssetFeature patch called with id: ', id, ' data: ', data, ' and params: ', params)
      const assetLib = new LibAssetFeature()
      return assetLib.update(id, data, params)
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }

  async update /* or is it update */(id, data, params) {
    // PUT
    try {
      console.log('AssetFeature update called with id: ', id, ' data: ', data, ' and params: ', params)
      const assetLib = new LibAssetFeature()
      return assetLib.update(id, data, params)
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }

  async remove (id, params) {
    try {
      console.log('AssetFeature remove called with id: ', id, ' and params: ', params)
      const assetLib = new LibAssetFeature()
      return assetLib.remove(id, params)
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

module.exports = { AssetFeature }
