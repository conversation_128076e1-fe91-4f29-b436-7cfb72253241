const { deleteDocumentById, getDocumentById } = require("../../database_utils/queries/document/document.query");
const { getDocumentJSONConfig } = require("../../utils/helpers/document.helper");
const { uploadDocument, addDocumentEntry } = require("./controllers/document");
const { getDocumentsByEnityUUID } = require("@krushal-it/back-end-lib");
class BackendDocumentService {
  async create(data, params) {
    try {
      // TODO: check R/W authorizations between user and entity
      const { user_type, user_id } = params.headers.token;
      if (user_type == 1000220001 && data.entity_1_type !== "animal") {
        //if user is farmer override the requested changing ids
        data.entity_1_type = 1000220001;
        data.entity_1_id = user_id;
      }
      if (data.files) {
        // If files exists trigger S3 upload
        let res = await uploadDocument(data);
        return { result: true, data: res };
      } else {
        // No files, consider as file meta data entry request
        return { result: true, data: await addDocumentEntry(data) };
      }
    } catch (error) {
      console.error(error);
      return { result: false, message: error.message };
    }
  }
  async get(id, params) {
    try {
      const options = params.query
   
      if (params.query.uploaded_at) {
        const response = getDocumentsByEnityUUID(id, options)
        return response;
      }
      // TODO: check R/W authorizations between user and entity
      return { config: await getDocumentJSONConfig(), pipeStream: false };
    } catch (error) {
      console.error(error);
      return { result: false, message: error.message };
    }
  }
  async remove(id, params) {
    try {
      // TODO: check R/W authorizations between user and entity
      await deleteDocumentById(id);
      return { result: true, message: "Image deleted" };
    } catch (error) {
      console.error(error);
      return { result: false, message: error.message };
    }
  }
}


class LibMediaService {
  async get(id, params) {
    try {
      // TODO: check R/W authorizations between user and entity
      if (id == -1) {
        return {
          config: await getDocumentJSONConfig(),
          pipeStream: false,
        };
      }
      return await getDocumentById(id);
    } catch (error) {
      console.error(error);
      return {
        result: false,
        message: error.message,
      };
    }
  }
}

class LibMediaKeyService {
  async get(id, params) {
    try {
      return id
    } catch (error) {
      console.error(error);
      return {
        result: false,
        message: error.message,
      };
    }
  }

  async find(params) {
    try {
      return params.query.url
    } catch (error) {
      console.error(error);
      return { result: false, error };
    }
  }
}

module.exports = { BackendDocumentService, LibMediaService, LibMediaKeyService };