const { LibMediaService } = require("@krushal-it/back-end-lib");
class MediaService {
  mediaServiceHandler = new LibMediaService();
  async get(id, params) {
    try {
      let result = this.mediaServiceHandler.get(id, params);
      return result;
    } catch (error) {
      console.error(error);
      return {
        result: false,
        message: error.message,
      };
    }
  }
}

module.exports = MediaService;
