const S3Utils = require('../../../utils/common/aws/s3.utils')
const { handleFileEntries, addTodumped } = require("../../../utils/helpers/document.helper");

const uploadDocument = async (data, options = {}) => {
    let uploadResult = null;
    try {
        const { files, entity } = data;
        const uploader = new S3Utils();
        uploadResult = await uploader.upload(files, entity);
        return { doc_key: uploadResult.file[0] };
    } catch (error) {
        if (uploadResult) addTodumped(uploadResult, error.message);
        console.error(error);
        throw error;
    }
};

const addDocumentEntry = async (data) => {
    const { document_entries } = data;
    if (!Array.isArray(document_entries) && document_entries.length > 0)
        throw new Error("document_entries must be an array");
    let promiseArray = [];
    await global.datasource;
    for (let entryIndex = 0; entryIndex < document_entries.length; entryIndex++) {
        const { entity, url, entity_1_id, document_type, document_information ,entity_1_type_id , fileType, uploaded_at, document_information_json} = document_entries[entryIndex];
        const entryObject = { entity, url, entity_1_id, document_type, document_information ,entity_1_type_id ,fileType, uploaded_at, document_information_json};
        promiseArray.push(handleFileEntries(entryObject));
    }
    try {
        let uploaded = await Promise.all(promiseArray);
        let document_ids = [];

        for (let i = 0; i < uploaded.length; i++) {
            if (uploaded[i].identifiers && uploaded[i].identifiers.length > 0)
                document_ids.push(uploaded[i].identifiers[0].document_id);
            else document_ids.push("500");
        }
        return { document_ids };
    } catch (error) {
        console.error(error);
        return { result: false, message: "cannot add document entry" };
    }
};
module.exports = { uploadDocument, addDocumentEntry };