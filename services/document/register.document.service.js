const { DocumentList, Document, EntityDocuments, SaveDocuments, DocumentInformation, UpsertDocumentByLanguage } = require('./document.class')
const auth = require('../../middleware/auth')
const tokenInject = require('../../middleware/tokenInject.js')
const configureDocument = (app) => {
  // app.use(auth)
  app.use('/v2/document-list', new DocumentList())
  app.use('/v2/document', new Document())
  app.use('/v2/document/:documentId', new Document())
  app.use('/oc/get-documents', new EntityDocuments(), tokenInject)
  app.use('/oc/save-documents', new SaveDocuments(), tokenInject)
  app.use('/oc/document-information', new DocumentInformation(), tokenInject)
  app.use('/upsert-document-by-language', new UpsertDocumentByLanguage(), tokenInject)
}
module.exports = { configureDocument }
