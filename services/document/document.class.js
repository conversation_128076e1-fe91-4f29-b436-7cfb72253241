const { getDBConnections, dbConnections, postProcessRecords } = require('@krushal-it/ah-orm')
const { configurationJSON, KrushalError } = require('@krushal-it/common-core')
const { DocumentListLib, DocumentLib ,LibDocumentService} = require('@krushal-it/back-end-lib')

const { uploadDocument } = require('./controllers/document')
const DocumentHelper = require('../../utils/helpers/document.helper')
const S3Utils = require('../../utils/common/aws/s3.utils')
var fs = require('fs')

class File {
  async create(data, params) {
    try {
      // TODO: check R/W authorizations between user and entity
      if (data.files) {
        // Trigger  s3 upload
        let res = await uploadDocument(data)
        return { result: true, data: res }
      } else {
        // Trigger document entry
        throw Error('no file to upload')
      }
    } catch (error) {
      console.error(error)
      return { result: false, message: error.message }
    }
  }

  async get(id, params) {
    try {
      // TODO: check R/W authorizations between user and entity
      if (id == -1) {
        return {
          config: await DocumentHelper.getDocumentJSONConfig(),
          pipeStream: false,
        }
      }

      const mainDBConnection = dbConnections().main
      const mainSchemaAddition =
        configurationJSON().IS_NODE_SERVER &&
        configurationJSON().IS_NODE_SERVER === 1
          ? 'main.'
          : ''
      const documentQuery = `
        SELECT d.document_id, d.document_information->>'fileName' as "s3Key", d.document_information, d.document_information->>'s3Key' as "s3KeyOld"
        FROM ${mainSchemaAddition}document d
        WHERE d.document_id = '${id}'
      `

      const documentResults = await mainDBConnection.manager.query(
        documentQuery
      )
      if (documentResults.length > 0) {
        const documentDataReturned = documentResults[0].s3Key
        return documentDataReturned
      } else {
        throw new Error('Did not find any document for this id')
      }
    } catch (error) {
      console.error(error)
      return {
        result: false,
        message: error.message,
      }
    }
  }
}

class FileByKey {
  async find(params) {
    try {
      return params.query.key
    } catch (error) {
      console.error(error)
      return {
        result: false,
        message: error.message,
      }
    }
  }
}

class DocumentList {
  async create(data, params) {
    try {
      const documentListLib = new DocumentListLib()
      return documentListLib.create(data, params)
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class Document {
  async find(params) {
    return { return_code: -101, message: 'in find' }
  }

  async create /*or is it update*/(data, params) {    
    try {
      const documentLib = new DocumentLib()
      return documentLib.create(data, params)
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }

  async get(id, params) {   
    try {
      const documentLib = new DocumentLib()
      return documentLib.get(id, params)
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }

  async patch /* or is it update */(id, data, params) {
    try {     
      return {
        return_code: 0,
        document_id: -1,
        message: 'Document Updated Successfully',
      }
    } catch (error) {
      console.log(error)
      if (error.name === 'KrushalError') {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async update_old /* or is it update */(id, data, params) {
    //PUT
    try {
      return { return_code: -101, message: 'in update with id as ' + id }
      // update details
      // id, name, mobile number, DOB, aadhaar number, gender (reference to master)
      // return { return_code: 0, data: {user_device_id: 1} };
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
   
  async update(id) {
    try{
      const libDocumentService = new LibDocumentService()
      let data =await libDocumentService.inactiveDocument(id);
      return { data : data}
    }
    catch (error) {
      return { return_code: -101, message: error.message}
    }
  }
   
}

class EntityDocuments {
  async create /*or is it update*/(data, params) {
    try {
      const returnValue = {return_code: 0}
      const mainDBConnection = dbConnections().main
      const activeOnlyConstraint = ' and d.active = 1000100001 '      
      const latestOnlyQuery = `
        inner join (
          select max(d.document_id::text) max_created_at_document_id, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
          from main.document d
          inner join (
            select max(d.created_at) max_created_at, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
            from main.document d
            where 1 = 1 ${data.inActiveAlso ? '' : activeOnlyConstraint}
            group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id 
          ) mcad on d.entity_1_type_id = mcad.entity_1_type_id and d.entity_1_entity_uuid = mcad.entity_1_entity_uuid
            and d.document_type_id = mcad.document_type_id and d.created_at = mcad.max_created_at
          where 1 = 1 ${data.inActiveAlso ? '' : activeOnlyConstraint}
          group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
        ) mcadid on d.document_id::text = mcadid.max_created_at_document_id
      `
      let documentQuery
      if (data.document_query_information && data.document_query_information.entity_name && data.document_query_information.entity_name === 'animal' && Array.isArray(data.document_query_information.related_additional_entity_types)) {
        
        const animalId = data.base_entity_information.entity_uuid
        documentQuery = `
          select *
          from (
            select d.document_id, d.entity_1_entity_uuid as entity_uuid, d.document_type_id, d.active,
              coalesce(dt_rr.reference_name_l10n->>'en', dt_rr.reference_name_l10n->>'ul') as document_type,
              d.document_information,
              d.created_at document_upload_time
            from main.document d
            inner join main.ref_reference dt_rr on dt_rr.reference_id = d.document_type_id
            where 1 = 1 ${data.inActiveAlso ? '' : activeOnlyConstraint}
              and d.entity_1_type_id in (1000220002, 1000220003) and d.entity_1_entity_uuid = '${animalId}'

            union

            select d.document_id, ac.animal_id as entity_uuid, d.document_type_id, d.active,
              coalesce(dt_rr.reference_name_l10n->>'en', dt_rr.reference_name_l10n->>'ul') as document_type,
              d.document_information,
              d.created_at document_upload_time
            from main.document d
            inner join main.animal_classification ac on ac.active = 1000100001 and ac.animal_id = '${animalId}'
              and ac.animal_classification_id = d.entity_1_entity_uuid and d.entity_1_type_id in (1000220004)
            inner join main.ref_reference dt_rr on dt_rr.reference_id = d.document_type_id
          ) d
          ${data.latestOnly ? latestOnlyQuery : ''}
        `
      } else {
        const documentTableAlias = 'd'
        documentQuery = `
          select d.document_id, d.entity_1_type_id, d.entity_1_entity_uuid as entity_uuid, d.entity_1_entity_id as entity_id, d.document_type_id,
            coalesce(dt_rr.reference_name_l10n->>'en', dt_rr.reference_name_l10n->>'ul') as document_type,
            d.document_information,
            d.created_at document_upload_time
          from main.document d
          inner join main.ref_reference dt_rr on dt_rr.reference_id = d.document_type_id
          where 1 = 1 ${data.inActiveAlso ? '' : activeOnlyConstraint}
        `
        documentQuery = `
          select *
          from (
            ${documentQuery}
          ) d
          ${data.latestOnly ? latestOnlyQuery : ''}
          where 1 = 1
        `
        
        if (data.base_entity_information) {
          const columns = Object.keys(data.base_entity_information)
          for (let counter = 0 ; counter < columns.length ; counter++) {
            const columnValue = data.base_entity_information[columns[counter]]
            if (Array.isArray(columnValue) && columnValue.length > 0) {
              const needQuotes = (typeof columnValue[0] === 'string' || columnValue[0] instanceof String)
              let valueWithinIn = ''
              for (let innerCounter = 0 ; innerCounter < columnValue.length ; innerCounter++) {
                if (valueWithinIn !== '') {
                  valueWithinIn = valueWithinIn + ', '
                }
                if (needQuotes) {
                  valueWithinIn = valueWithinIn + "'" + columnValue[innerCounter] + "'"
                } else {
                  valueWithinIn = valueWithinIn + columnValue[innerCounter]
                }
              }
              documentQuery = documentQuery + ' and ' + documentTableAlias + '.' + columns[counter]
              documentQuery = documentQuery + ' in (' + valueWithinIn + ')'
            } else {
              const needQuotes = (typeof columnValue === 'string' || columnValue instanceof String)
              documentQuery = documentQuery + ' and ' + documentTableAlias + '.' + columns[counter]
              documentQuery = documentQuery + ' = ' + (needQuotes ? "'" : "") + columnValue + (needQuotes ? "'" : "") + ' '
            }
  
          }
        }
      }
      if (data.document_type_id_array && Array.isArray(data.document_type_id_array) && data.document_type_id_array.length > 0) {
        const documentTypeInString = data.document_type_id_array.join(', ')
        documentQuery = documentQuery + ' and d.document_type_id in (' + documentTypeInString + ') '
      }

      const documentQueryResult = await mainDBConnection.manager.query(documentQuery)
      returnValue.result = documentQueryResult
      return returnValue
    } catch (error) {
      console.log('oc UBFS c 10, error')
      console.log('oc UBFS c 10a, error = ', error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}
class SaveDocuments {
  async create /*or is it update*/(data, params) {
    try {
      const returnValue = {return_code: 0}
      // create order
      // copy order classification from item classification
      // copy order document from item document
      const mainDBConnection = dbConnections().main
      const saveResult = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
        try {
          const documentEntity = mainDBConnection.entities[data.document_entity_name]
          const insertDocumentResults = await transactionalEntityManager.createQueryBuilder().insert().into(documentEntity).values(data.documents).execute()
          returnValue.inserted_documents = insertDocumentResults
        } catch (error) {
          console.log('oc COFAS c 20, error')
          console.log('oc COFAS c 20a, error = ', error)
          throw error
        }
      })
      returnValue.save_result = saveResult
      return returnValue
    } catch (error) {
      console.log('oc COFAS c 10, error')
      console.log('oc COFAS c 10a, error = ', error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class DocumentInformation {
  async get(id, params) {
    try {
      const returnValue = {return_code: 0}
      const mainDBConnections = dbConnections().main
      const documentQuery = `
        select d.document_id, d.document_type_id, coalesce(dt_rr.reference_name_l10n->>'en', dt_rr.reference_name_l10n->>'ul') document_type, d.document_information,
          coalesce(d.document_name, coalesce(d.document_name_l10n->>'en', d.document_name_l10n->>'ul')) document_name,
          d.updated_at document_upload_time
        from main.document d
        inner join main.ref_reference dt_rr on dt_rr.reference_id = d.document_type_id
        where d.document_id = '${id}'
      `
      const documentInformationUnprocessedResult = await mainDBConnections.manager.query(documentQuery)
      const documentInformationResult = postProcessRecords(undefined, documentInformationUnprocessedResult, {})
      returnValue.result = documentInformationResult[0]

      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class UpsertDocumentByLanguage {
  async create /*or is it update*/(data, params) {
    try {
      const returnValue = {return_code: 0}
      // create order
      // copy order classification from item classification
      // copy order document from item document
      const mainDBConnection = dbConnections().main
      const saveResult = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
        try {
          // check if document entry exists
          // any of them have same language
          // if so, inactivate that document
          // create new entries and push it into array
          const documentTableEntity = mainDBConnection.entities['document']
          const documentRepo = transactionalEntityManager.getRepository('document')
          const documentIdsToInactive = []
          const documentsToInsertObjectArray = []
          for (const individualDocument of data) {
            const languageToInsert = individualDocument['language']
            const existingActiveDocumentsAcrossLanguagesUnprocessed = await mainDBConnection.manager
              .createQueryBuilder()
              .select(['document_id', 'document_information'])
              .from(documentTableEntity)
              // .from('care_calendar')
              .where('active = :activeId and document_type_id = :documentTypeId and entity_1_type_id = :entity1TypeId and entity_1_entity_uuid = :entity1UUID', { activeId: 1000100001, documentTypeId: individualDocument['document_type_id'], entity1TypeId: individualDocument['entity_1_type_id'], entity1UUID: individualDocument['entity_1_entity_uuid'] })
              .execute()
            const existingActiveDocumentsAcrossLanguages = postProcessRecords(undefined, existingActiveDocumentsAcrossLanguagesUnprocessed, {json_columns: ['document_information']})
            const matchingDocumentRowsForLanguage = existingActiveDocumentsAcrossLanguages.filter(object => {
              let returnValue = false
              if (object['document_information'] && object['document_information']['language'] && object['document_information']['language'] === languageToInsert) {
                returnValue = true
              }
              return returnValue
            })
            for (const individualDocumentRow of matchingDocumentRowsForLanguage) {
              documentIdsToInactive.push(individualDocumentRow['document_id'])
            }
            const documentObject = {
              document_type_id: individualDocument['document_type_id'],
              entity_1_type_id: individualDocument['entity_1_type_id'],
              entity_1_entity_uuid: individualDocument['entity_1_entity_uuid'],
              document_information: individualDocument['document_information'],
            }
            documentsToInsertObjectArray.push(documentObject)
          }
          if (documentsToInsertObjectArray.length > 0) {
            const results = await transactionalEntityManager.createQueryBuilder().insert().into(documentTableEntity).values(documentsToInsertObjectArray).execute()
          }
          if (documentIdsToInactive.length > 0) {
            const inactivateDocumentsResult = await documentRepo
            .createQueryBuilder()
            .update()
            .set({active: 1000100002})
            .where('document_id IN (:...id)', { id: documentIdsToInactive })
            .execute()
          }
            

        } catch (error) {
          console.log('oc COFAS c 20, error')
          console.log('oc COFAS c 20a, error = ', error)
          throw error
        }
      })
      returnValue.save_result = saveResult
      return returnValue
    } catch (error) {
      console.log('oc COFAS c 10, error')
      console.log('oc COFAS c 10a, error = ', error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

module.exports = { DocumentList, Document, File, FileByKey, EntityDocuments, SaveDocuments, DocumentInformation, UpsertDocumentByLanguage }
