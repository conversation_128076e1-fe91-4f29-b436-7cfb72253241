const { BACK_OFFICE_PERSON } = require("@krushal-it/back-end-lib/utils/constant");
const { checkUsertype } = require("../../middleware/auth");
const tokenInject = require("../../middleware/tokenInject");
const { handleServerError } = require("../../middleware/server-error");
const { Farmer ,FarmerList} = require("./farmer.class")

const configureFarmerUserForPpu = (app) => {
    app.use("/ppu/farmer", checkUsertype(BACK_OFFICE_PERSON),new Farmer(), tokenInject, handleServerError);
    app.use("/ppu/farmerlist",new FarmerList(), tokenInject, handleServerError);
    app.use("/ppu/check_farmer_exist",new FarmerList(), tokenInject, handleServerError);


};

module.exports = configureFarmerUserForPpu;