const { BACK_OFFICE_PERSON } = require("@krushal-it/back-end-lib/utils/constant");
const { checkUsertype } = require("../../middleware/auth");
const tokenInject = require("../../middleware/tokenInject");
const { handleServerError } = require("../../middleware/server-error");
const { Finance ,GetFinance ,Khata ,KhataDashboad ,KhataListing} = require ('./finance.class.js')

const loadStaffFinanceServices = (app) => {
    app.use("/ppu/transaction_ledge", checkUsertype(BACK_OFFICE_PERSON), new Finance(), tokenInject, handleServerError);
    app.use("/ppu/get_transaction_ledge", new GetFinance(), tokenInject, handleServerError);

};

const loadKhataServices = (app) => {
    app.use("/khata/listing",new KhataListing(),tokenInject , handleServerError)
    app.use("/khata/dashboard",new KhataDashboad() ,tokenInject ,handleServerError)
    app.use("/khata/:object", new Khata() ,tokenInject ,handleServerError);
}

module.exports = { loadStaffFinanceServices ,loadKhataServices} ;