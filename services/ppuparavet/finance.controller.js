const { dbConnections } = require("@krushal-it/ah-orm");
const { GeneralError } = require("@feathersjs/errors");
const { getCompiledQuery } = require("@krushal-it/back-end-lib");

const createFinanceTransaction = async (data, params) => {
    try {
        let { staff_id , amount  , transaction_date ,transaction_description } = data;
        let transaction_ledger = await dbConnections().main.repos['transaction_ledger'].insert({
            staff_uuid :staff_id ,account_id: "KRUSHAL", amount , transaction_type :********** ,transaction_category:**********, transaction_date ,transaction_description
        });
        return transaction_ledger
    }
    catch (error) {
        console.error(error);
        if (error instanceof GeneralError) {
            return { data: -1, result: false, error: error.message, status: error.data.statusCode };
        }
        return { data: -1 };
    }
};

const getStaffFinanceService = async (staff_uuid,data = null) => {
	try {
		let templateParameters = {
			staff_uuid	
	   }
		if (data && data.search_by_FarmerName_SRNO) {
			data.search_by_FarmerName_SRNO = '%' + data.search_by_FarmerName_SRNO + '%'
		}
		if ( data && typeof data.transaction_type === 'int') {
			data.transaction_type = [data.transaction_type]
		}

		if (data) {
			templateParameters = {...templateParameters , ...data}
		}

		let query = getCompiledQuery('transaction-listing', templateParameters)
		const queryBuilder = await dbConnections().main.manager.createQueryBuilder().from(`(${query})`, 'transaction-listing').setParameters(templateParameters)
		let count = await queryBuilder.select('COUNT(*) as count').execute()
		count = parseInt(count[0].count)
		let total_page = 1
		if (data && !isNaN(data.page_number) && !isNaN(data.page_limit)) {
			let offset = data.page_limit * (data.page_number - 1)
			if (offset < 0) return { message: 'Invalid page number' }
			queryBuilder.skip(offset).take(data.page_limit)
			total_page = parseInt(count / data.page_limit)
			total_page += count % data.page_limit === 0 ? 0 : 1
		}
		const result = await queryBuilder.select('*').orderBy('"transaction-listing".transaction_date', 'DESC').execute()
		return { result, total_page: total_page }
	} catch (error) {
		console.error(error);
        if (error instanceof GeneralError) {
            return { data: -1, result: false, error: error.message, status: error.data.statusCode };
        }
        return { data: -1 };
	}

}



module.exports = { createFinanceTransaction ,getStaffFinanceService};



