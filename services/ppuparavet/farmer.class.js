const { createCustomer ,getCustomerByStaffId ,searchByPhone} = require("./farmer.controller");

class Farmer {
  async create(data, params) {
    try {
      let result = await createCustomer(data, params);
      return result;
    } catch (error) {
      console.error(error);
      return { result: false, error: error.message };
    }
  }
//get customer by staff_id
  async get(staff_id) {
    try{
      let result = await getCustomerByStaffId(staff_id)
      return result;
    } catch (error) {
      return { result: false, error: error.message };
    }
  }
}

class FarmerList{
  async create(data, params) {
    try {
      let {staff_id} = params.query
      let result = await getCustomerByStaffId(staff_id ,data);
      return result;
    } catch (error) {
      console.error(error);
      return { result: false, error: error.message };
    }
  }
  async find(params){
    try{
      let {mobile_number ,country_code ,staff_id} = params.query
      let result = await searchByPhone(mobile_number ,country_code,staff_id);
      return result
    } catch (error) {
      return { result: false, error: error.message };
    }

  }
}


module.exports = { Farmer ,FarmerList};

