const { createFinanceTransaction ,getStaffFinanceService } = require("./finance.controller");
const { creteNewKhata ,getKhataDashboard , getKhataListing} = require("@krushal-it/back-end-lib/services/finance/khata.controller")

class Finance {
  async create(data, params) {
    try {
      let result = await createFinanceTransaction(data, params);
      return result;
    } catch (error) {
      console.error(error);
      return { result: false, error: error.message };
    }
  }

}

class GetFinance {
  async create(data, params) {
    try {
      let {staff_id} = params.query
      let result = await getStaffFinanceService(staff_id, data);
      return result;
    } catch (error) {
      console.error(error);
      return { result: false, error: error.message };
    }
  }
  async get(id) {
    try{
      let {result} = await getStaffFinanceService(id);
      let balance =  this.getbalance(result)
      return { balance };
    } catch (error){

    }
  }
  getbalance(data){
     let debited= data.reduce(function(sum, record){
      if(record.transaction_type ===1000790002){
        return sum + parseInt(record.amount); 
      }
      return sum
      }, 0);
      let credited= data.reduce(function(sum, record){
        if(record.transaction_type ===1000790001){
          return sum + parseInt(record.amount); 
        }
        return sum
        }, 0);

      return credited - debited
   
  }
}

class Khata {
  async create(data, params) {
    try {
      let result = await creteNewKhata(data, params);
      return result;
    } catch (error) {
      console.error(error);
      return { result: false, error: error.message };
    }
  }
}

class KhataDashboad {
  async find(params) {
    try{
      let {staff_id}= params.query
      let result = await getKhataDashboard(staff_id); 
      return result
    } catch (error) {
      console.error(error);
      return { result: false, error: error.message };
    }

  }
}

class KhataListing {
  async create(data,params) {
    try {
      let result = await getKhataListing(data);
      return result
    }
    catch(error) {
      throw new Error("failed while getting data")
    }
  }
}

module.exports = { Finance ,GetFinance ,Khata ,KhataDashboad ,KhataListing};

