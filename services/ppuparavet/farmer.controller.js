const { dbConnections } = require("@krushal-it/ah-orm")
const { ACTIVE, FARMER_TYPE_CALENDAR, PAY_PER_USE_PARAVET, PPU_PARAVET_TO_FARMER } = require("@krushal-it/back-end-lib/utils/constant")
const { GeneralError } = require("@feathersjs/errors")
const defaultCountryCodeForMobileNumber = "+91"
const { generateVisualId, saveClassificationData ,getFarmerInfoByMobileNumber ,checkFreelanceCustomerRelationAlreadyExist} = require("@krushal-it/back-end-lib")
const { getCompiledQuery } = require("@krushal-it/back-end-lib")

const createCustomer = async (data, params) => {
  try {
    let { farmer_state_1, farmer_district_1, farmer_taluk_1, farmer_village_1, farmer_pan_1, farmer_address_1, customer_name_l10n, mobile_number, staff_id, country_code, aadhaar_1, dob_1, age_1 } = data
    //check existing data
    let { state_id, district_id, taluka_id } = {
      state_id: farmer_state_1[0],
      district_id: farmer_district_1[0],
      taluka_id: farmer_taluk_1[0]
    }
    if (typeof staff_name_l10n === "string") {
      staff_name_l10n = { ul: staff_name_l10n }
    }

    if (!country_code) {
      mobile_number = defaultCountryCodeForMobileNumber.concat(mobile_number)
    }

    return await dbConnections().main.manager.transaction(async transaction => {
      let entity_relationship = {
        entity_relationship_type_id: 1000210010,
        entity_1_type_id: 1000460003,
        entity_1_entity_uuid: staff_id,
        entity_2_type_id: 1000460001
      }

      let customer_visual_id = await generateVisualId(state_id, district_id, taluka_id)
      if (!customer_visual_id) throw new Error("Could not generate visual id, need state_id, district_id and taluka_id.")

      let constructedFarmerInsert = {
        customer_type_id: 1000220007,
        customer_visual_id: customer_visual_id,
        customer_name_l10n: { ul: customer_name_l10n },
        mobile_number
      }

      let customer = await transaction.getRepository("customer").insert(constructedFarmerInsert)
      let customer_id = customer.raw[0].customer_id
      entity_relationship["entity_2_entity_uuid"] = customer_id
      await transaction.getRepository("entity_relationship").insert(entity_relationship)
      await saveClassificationData(transaction, "CUSTOMER_CLASSIFICATION", customer_id, { farmer_state_1, farmer_district_1,farmer_taluka_1: farmer_taluk_1, farmer_village_1, farmer_pan_1, farmer_address_1, aadhaar_1, dob_1, age_1 })
      return { customer_id: customer_id }
    })
  } catch (error) {
    console.error(error)
    if (error instanceof GeneralError) {
      return { data: -1, result: false, error: error.message, status: error.data.statusCode }
    }
    return { data: -1 }
  }
}

const getCustomerByStaffId = async (staff_id, data = null) => {
  let templateParameters = {
    staff_id
  }
  if (data && data.f_farmer_name) {
    templateParameters.f_farmer_name = "%" + data.f_farmer_name + "%"
  }
  if (data && data.f_village_id_array && data.f_village_id_array.length) {
    templateParameters.f_village_id_array = data.f_village_id_array
  }

  let query = getCompiledQuery("ppu-customer-listing", templateParameters)
  const queryBuilder = await dbConnections()
    .main.manager.createQueryBuilder()
    .from(`(${query})`, "ppu-customer-listing")
    .setParameters(templateParameters)
  let count = await queryBuilder.select("COUNT(*) as count").execute()
  count = parseInt(count[0].count)
  let total_page = 1
  if (data && !isNaN(data.page_number) && !isNaN(data.page_limit)) {
    let offset = data.page_limit * (data.page_number - 1)
    if (offset < 0) return { message: "Invalid page number" }
    queryBuilder.skip(offset).take(data.page_limit)
    total_page = parseInt(count / data.page_limit)
    total_page += count % data.page_limit === 0 ? 0 : 1
  }
  const result = await queryBuilder.select("*").execute()
  return { result, total_page: total_page }
}

const searchByPhone = async (mobile_number, country_code ,staff_id) => {
  try {
    if (!country_code) {
      mobile_number = defaultCountryCodeForMobileNumber.concat(mobile_number)
    }
    let existingCustomer = await getFarmerInfoByMobileNumber(mobile_number)

    if (existingCustomer.length > 0) {
      let checkRelationExist =await  checkFreelanceCustomerRelationAlreadyExist(staff_id,existingCustomer[0].customer_id);
      if (checkRelationExist.length) {
        return { farmer_exist: true, farmer_enrolled: true , customer: existingCustomer }
      }
      return { farmer_exist: true, customer: existingCustomer }
    }
    return { farmer_exist: false }
  } catch (error) {
    return { data: -1, error }
  }
}

const enrollFarmerToFreelanceParavet = async data => {
  const { farmer_id, paravet_id } = data
  if (farmer_id && paravet_id) {
  } else {
    throw new Error("Did not find farmer_id or paravet_id.")
  }

  const entity_relationship = {
    entity_relationship_type_id: PPU_PARAVET_TO_FARMER,
    entity_1_type_id: PAY_PER_USE_PARAVET,
    entity_1_entity_uuid: paravet_id,
    entity_2_type_id: FARMER_TYPE_CALENDAR,
    entity_2_entity_uuid: farmer_id
  }

  try {
    await dbConnections()
      .main.manager.createQueryBuilder()
      .insert()
      .into("entity_relationship")
      .values(entity_relationship)
      .execute()
    return true
  } catch (error) {
    return false
  }
}
module.exports = { createCustomer, getCustomerByStaffId, searchByPhone, enrollFarmerToFreelanceParavet }
