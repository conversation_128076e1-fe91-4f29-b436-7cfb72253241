
const moment = require("moment");
const { postProcessRecords, dbConnections } = require("@krushal-it/ah-orm");
const { configurationJSON, assignL10NObjectOrObjectArrayToItSelf } = require("@krushal-it/common-core");
const { loadClassificationData, saveClassificationData } = require('@krushal-it/back-end-lib')
const { adjustStartDatesOfEntityStatusByCategoryAndEntities, reportQueryClassificationJoinConfiguration, generateSelectJoinClauseForClassification, generateWhereClauseForReportInnerQuery: generateWhereClause, generateLimitStringForReportInnerQuery: generateLimitString, generateSortStringForReportInnerQuery: generateSortString, identifyMandatoryColumnsForReportQuery} = require('../../utils/query/query.helper');
const { update } = require("lodash");
const lodashObject = require("lodash");

class MedicineReport {
  async create /*or is it update*/(data, params) {
    try {
      const returnValue = { return_code: 0 }
      const mainDBConnections = dbConnections().main
      const limitString = generateLimitString(data)
      const sortString = generateSortString(data) 
      const whereClause = generateWhereClause(data) 

      const selectedColumns = data.selectedColumns

      const mandatoryColumnsForSearch = identifyMandatoryColumnsForReportQuery(data.filters, data.sorting, data.globalSearch, data.searchConfiguration)

      const [selectClauseForClassification, joinClauseForClassification] = generateSelectJoinClauseForClassification(reportQueryClassificationJoinConfiguration, 'MEDICINE', undefined, selectedColumns)

      const innerQuery = `
        select rm.medicine_id, coalesce(medicine_name_l10n->>'en', medicine_name_l10n->>'ul') medicine_name,
          ${(selectClauseForClassification !== undefined && selectClauseForClassification !== '') ? selectClauseForClassification + ', ': ''}
          case when rm.active = 1000100001 then 'Active' else 'Not Active' end as medicine_active,
          rm.active as medicine_active_id
        from main.ref_medicine rm
        ${joinClauseForClassification}
        where 1 = 1
      `
      const countQuery = `
        select count(*) as count from (
          ${innerQuery}
        ) outerTable
        ${whereClause}
      `
      const reportResultCount = await mainDBConnections.manager.query(countQuery)
      const count = parseInt(reportResultCount[0].count)

      const query = `
        select * from (
          ${innerQuery}
        ) outerTable
        ${whereClause}
        ${sortString}
        ${limitString}
      `

      const reportResultUnprocessed = await mainDBConnections.manager.query(query)
      const reportResult = postProcessRecords(undefined, reportResultUnprocessed, {})

      returnValue.count = count
      returnValue.report = reportResult
      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async find(params) {
    try {
      const returnValue = {return_code: 0}    
      return returnValue;
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class Medicine {
  async create /*or is it update*/(data, params) {
    try {
      const returnValue = { return_code: 0 }
      const mainDBConnection = dbConnections().main

      const medicineId = data.medicineId
      const saveResult = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
        const medicineRepo = transactionalEntityManager.getRepository('ref_medicine')
        const objectToBeSet = {}
        if (data.medicine_name_l10n) {
          objectToBeSet.medicine_name_l10n = data.medicine_name_l10n
        }
        if (data.medicine_active_id) {
          objectToBeSet.active = data.medicine_active_id[0]
        }
        if (Object.keys(objectToBeSet).length > 0) {
          const updateAssetResult = await medicineRepo
                .createQueryBuilder()
                .update()
                .set(objectToBeSet)
                .where("medicine_id = :id ", { id: medicineId })
                .execute()
        }
        const updateClassificationDataResult = await saveClassificationData(transactionalEntityManager, 'MEDICINE_CLASSIFICATION', medicineId, data.classificationData)
        returnValue.classificationDataUpdateResult = updateClassificationDataResult
      })
      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async get(id, params) {
    try {
      const returnValue = {return_code: 0}    
      const mainDBConnection = dbConnections().main

      const medicineRepo = mainDBConnection.manager.getRepository('ref_medicine')

      const medicineResultUnprocessed = await medicineRepo
        .createQueryBuilder()
        .select('medicine_id, medicine_name_l10n, active as medicine_active_id')
        .where('medicine_id = :medicineId', { medicineId: id })
        .execute()
      const medicineResult = postProcessRecords(undefined, medicineResultUnprocessed, {json_columns:['medicine_name_l10n']})
      const classificationData = await loadClassificationData('MEDICINE_CLASSIFICATION', id, [
        'medicine_mrp',
        'medicine_unit',
        'medicine_form',
        'medicine_pack_size',
        'medicine_used_by_flag',
        'medicine_zoho_item_id',
      ])
      const result = {medicine_id: medicineResult[0].medicine_id, medicine_active_id: [medicineResult[0].medicine_active_id], medicine_name_l10n: medicineResult[0].medicine_name_l10n, ...classificationData}
      returnValue.result = result

      return returnValue;
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
  
  async find(params) {
    try {
      const returnValue = {return_code: 0}
      
      return returnValue;
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

module.exports = { MedicineReport, Medicine
}