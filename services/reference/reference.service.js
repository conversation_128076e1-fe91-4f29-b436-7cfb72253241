const { LibReferenceService } = require("@krushal-it/back-end-lib");
//TODO: Form validation
class ReferenceService {
  referenceServiceHandler = new LibReferenceService();
  async find(params) {
    try {
      let result = await this.referenceServiceHandler.find(params);
      return result;
    } catch (error) {
      console.error(error);
      return error;
    }
  }
}

module.exports = ReferenceService;
