const {
  MedicineReport,
  Medicine
} = require('./reference.class.js')
const tokenInject = require('../../middleware/tokenInject.js')

const configureReference = (app) => {
  app.use('/r/medicine-list', new MedicineReport(), tokenInject)
  app.use('/r/medicine', new Medicine(), tokenInject)
  app.use('/r/medicine/:medicineId', new Medicine(), tokenInject)
  // app.use('/report/set-client-delta', new SetClientData())
}
module.exports = { configureReference }
