const {insertFCM} =require('../../database_utils/notification/notificatio.query')
const { dbConnections } = require("@krushal-it/ah-orm");
class FCMService {
  async create(data, params) {
    try{
        const {user_id,user_type} = params.headers.token
        const {fcm_token} = data;
        await dbConnections()
        .main.manager.createQueryBuilder()
        .delete()
        .from("main.user_device")
        .where("entity_uuid = :user_id", { user_id })
        .execute();
        let result = await insertFCM({entity_uuid:user_id,fcm_id:fcm_token,entity_type_id:user_type})
        return {status:true,result:result}
 
    }
    catch (error) {
      errorLog("error in NotificationService", params, { message: error.message });
      return { result: false, error: error.message };
    }
  }
  async update(id, data, params) {
    const {user_id} = data
    try {
      let updateFCM =await dbConnections()
      .main.manager.createQueryBuilder()
      .update("main.user_device")
      .set({ active: 1000100002 })
      .where(`entity_uuid = :user_id`, { user_id:user_id })
      .execute();
      return updateFCM
     
    } catch (error) {
      errorLog("error in NotificationService", params, { message: error.message });
      return { result: false, error: error.message };
    }
  }
}

module.exports = { FCMService };
