
const { messaging } = require('../../utils/common/auth/firebase_admin');
const { sendNotifications, getFCMTokenByUserIds, getCustomerFCMBYFilter, getStaffFCMtByFilter } = require('../../database_utils/notification/notificatio.query');
const { dbConnections } = require('@krushal-it/ah-orm');

class NotificationService {
  async create(data, params) {
    try {
      const messages = await handleSendNotification(data);
      return {successCount:messages};
    } catch (error) {
      console.error(error);
      return { result: false, error: error.message };
    }
  }

}
const handleSendNotification = async (data) => {
  const { user_ids, filters, notification } = data;
  const { title, body, action,redirect, type } = notification;
  const fcmIDs = [];
  if (user_ids) {
    const result = await getFCMTokenByUserIds(user_ids);
    fcmIDs.push(...result.map(value => value.fcm_id).filter(token => token!==null));
  } else {
    const { village_ids, user_types } = filters;
    const fcmCustomerQuery = getCustomerFCMBYFilter();
    const fcmStaffQuery = getStaffFCMtByFilter();
    const fcmCusQueryBuilder = dbConnections().main.manager.createQueryBuilder().from(`(${fcmCustomerQuery})`).setParameters({ village_ids, user_types });
    const fcmStaffQueryBuilder = dbConnections().main.manager.createQueryBuilder().from(`(${fcmStaffQuery})`).setParameters({ village_ids, user_types });
    const cusRes = await fcmCusQueryBuilder.execute();
    const staffRes = await fcmStaffQueryBuilder.execute();
    const res = [...cusRes, ...staffRes];
    fcmIDs.push(...res.map(value => value.fcm_token).filter(token => token!==null));
  }

  if (fcmIDs.length > 0) {
    const res = await sendNotifications({ title, body, action, redirect, registrationTokens: fcmIDs, type })
    return res;
  }
  return { message: "No valid ids found" };
};
module.exports = { NotificationService };