const {
  getDBConnections,
  dbConnections,
  postProcessRecords,
  preProcessRecords,
} = require('@krushal-it/ah-orm')
const {
  getUserInfoBasedOnHeaderInformation,
} = require('../../utils/common/auth/auth')
const { configurationJSON, KrushalError } = require('@krushal-it/common-core')
const { DistrictListLib, TalukListLib, VillageListLib, GeoArraysLib } = require('@krushal-it/back-end-lib')


class StateList {
  async create (data, params) {
    try {
      const {
        entity_type_id: entityTypeId,
        entity_uuid: entityId,
        user_device_id: userDeviceId,
      } = await getUserInfoBasedOnHeaderInformation(params.headers)
      const returnValue = { return_code: 0 }

      const mainSchemaAddition =
      configurationJSON().IS_NODE_SERVER &&
      configurationJSON().IS_NODE_SERVER === 1
        ? 'main.'
        : ''


      const geoQuerySelect = 
        `
          select rs.state_id, rd.state_name_l10n
        `
      const geoQueryAfterSelect = 
      `
        from ${mainSchemaAddition}ref_state rs
      `

      const finalGeoQuery = geoQuerySelect + geoQueryAfterSelect

      const geoResults = await dbConnections.main.manager.query(finalGeoQuery)

      returnValue.list_data = geoResults
      return returnValue

    } catch (error) {
      console.log(error)
      if (error.name === 'KrushalError') {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async find(params) {
    try {
      const {
        entity_type_id: entityTypeId,
        entity_uuid: entityId,
        user_device_id: userDeviceId,
      } = await getUserInfoBasedOnHeaderInformation(params.headers)
      const returnValue = { return_code: 0 }

      const dbConnections = getDBConnections()

      const mainSchemaAddition =
      configurationJSON().IS_NODE_SERVER &&
      configurationJSON().IS_NODE_SERVER === 1
        ? 'main.'
        : ''

      const geoQuerySelect = 
        `
          select rs.state_id, rs.state_name_l10n
        `
      const geoQueryAfterSelect = 
      `
        from ${mainSchemaAddition}ref_state rs
      `

      const finalGeoQuery = geoQuerySelect + geoQueryAfterSelect

      const geoResultsUnprocessed = await dbConnections.main.manager.query(finalGeoQuery)
      const geoResults = postProcessRecords(undefined, geoResultsUnprocessed, {json_columns: ['state_name_l10n']})
      returnValue.list_data = geoResults
      return returnValue

    } catch (error) {
      console.log(error)
      if (error.name === 'KrushalError') {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class StateListV2 {
  async find(params) {
    try {
      const {
        entity_type_id: entityTypeId,
        entity_uuid: entityId,
        user_device_id: userDeviceId,
      } = await getUserInfoBasedOnHeaderInformation(params.headers)
      const returnValue = { return_code: 0 }

      const dbConnections = getDBConnections()

      const mainSchemaAddition =
      configurationJSON().IS_NODE_SERVER &&
      configurationJSON().IS_NODE_SERVER === 1
        ? 'main.'
        : ''

      const geoQuerySelect = 
        `
          select s_rg.geography_id, s_rg.geography_id as state_id,
            s_rg.geography_name_l10n, s_rg.geography_name_l10n as state_name_l10n
        `
      const geoQueryAfterSelect = 
      `
        from ${mainSchemaAddition}ref_geography s_rg
        where s_rg.geography_type_id = 1000320001
      `

      const finalGeoQuery = geoQuerySelect + geoQueryAfterSelect

      const geoResultsUnprocessed = await dbConnections.main.manager.query(finalGeoQuery)
      const geoResults = postProcessRecords(undefined, geoResultsUnprocessed, {json_columns: ['geography_name_l10n', 'state_name_l10n']})
      returnValue.list_data = geoResults
      return returnValue

    } catch (error) {
      console.log(error)
      if (error.name === 'KrushalError') {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class DistrictList {
  async create /*or is it update*/(data, params) {
    /*try {
      const {
        entity_type_id: entityTypeId,
        entity_uuid: entityId,
        user_device_id: userDeviceId,
      } = await getUserInfoBasedOnHeaderInformation(params.headers)
      const returnValue = { return_code: 0 }

      const { query } = params
      const {stateId, includeState} = query

      const dbConnections = getDBConnections()

      const mainSchemaAddition =
      configurationJSON().IS_NODE_SERVER &&
      configurationJSON().IS_NODE_SERVER === 1
        ? 'main.'
        : ''

      let geoQuerySelect = 
        `
          select rd.district_id, rd.district_name_l10n
        `
      let geoQueryAfterSelect = 
      `
        from ${mainSchemaAddition}ref_district rd, ${mainSchemaAddition}ref_state rs
        where rd.state_id = rs.state_id
      `
      if (includeState) {
        geoQuerySelect = geoQuerySelect + ' , rs.state_name_l10n '
      }

      if (stateId) {
        geoQueryAfterSelect = geoQueryAfterSelect + ` and rd.state_id = ${stateId}`
      }

      const finalGeoQuery = geoQuerySelect + geoQueryAfterSelect

      const geoResults = await dbConnections.main.manager.query(finalGeoQuery)

      returnValue.list_data = geoResults
      return returnValue

    } catch (error) {
      console.log(error)
      if (error.name === 'KrushalError') {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }*/
    try {
      const districtListLib = new DistrictListLib()
      return districtListLib.create(data, params)
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class DistrictListV2 {
  async create /*or is it update*/(data, params) {
    const returnValue = { return_code: 0 }

    const { query } = params
    const { stateId, includeState, farmerVillageId } = query

    const dbConnections = getDBConnections()

    const mainSchemaAddition =
    configurationJSON().IS_NODE_SERVER &&
    configurationJSON().IS_NODE_SERVER === 1
      ? 'main.'
      : ''

    let geoQuerySelect = 
      `
        select d_rg.geography_id, d_rg.geography_id as district_id,
          d_rg.geography_name_l10n, d_rg.geography_name_l10n as district_name_l10n
      `
    let geoQueryAfterSelect = 
      `
        from ${mainSchemaAddition}ref_geography d_rg
        where d_rg.geography_type_id = 1000320002
      `
    
    if (includeState) {
      geoQuerySelect = geoQuerySelect + ' , s_rg.geography_name_l10n as state_name_l10n '
    }

    if (stateId) {
      // geoQueryAfterSelect = geoQueryAfterSelect + ` and s_rg.geography_id = ${stateId}`
      geoQueryAfterSelect = 
      `
        from ${mainSchemaAddition}ref_geography s_rg
        inner join ${mainSchemaAddition}entity_relationship s2d_er on s2d_er.entity_relationship_type_id = 1000210029
          and s2d_er.entity_1_type_id = 1000460015 and s2d_er.entity_2_type_id = 1000460015
          and s2d_er.entity_1_entity_id = s_rg.geography_id
        inner join ${mainSchemaAddition}ref_geography d_rg on d_rg.geography_type_id = 1000320002
          and d_rg.geography_id = s2d_er.entity_2_entity_id
        where s_rg.geography_type_id = 1000320001 and s_rg.geography_id = ${stateId}
      `
    } else if (farmerVillageId) {
      // get all districts where state id is same as state id of given village
      geoQueryAfterSelect = 
        `
          from ${mainSchemaAddition}ref_sdtv_view_2 rsv
          inner join ${mainSchemaAddition}entity_relationship s2d_er on s2d_er.entity_relationship_type_id = 1000210029
            and s2d_er.entity_1_type_id = 1000460015 and s2d_er.entity_2_type_id = 1000460015
            and s2d_er.entity_1_entity_id = rsv.state_id
          inner join ${mainSchemaAddition}ref_geography d_rg on d_rg.geography_type_id = 1000320002
            and d_rg.geography_id = s2d_er.entity_2_entity_id
          inner join ${mainSchemaAddition}ref_geography s_rg on s_rg.geography_type_id = 1000320001
            and s_rg.geography_id = s2d_er.entity_1_entity_id
          where rsv.village_id = ${farmerVillageId}
        `
    }

    const finalGeoQuery = geoQuerySelect + geoQueryAfterSelect

    const geoResults = await dbConnections.main.manager.query(finalGeoQuery)
    postProcessRecords(undefined, geoResults, {json_columns: ['geography_name_l10n', 'district_name_l10n', 'state_name_l10n']})

    returnValue.list_data = geoResults
    return returnValue
  }
}

class TalukList {
  async create /*or is it update*/(data, params) {
    /*try {
      const {
        entity_type_id: entityTypeId,
        entity_uuid: entityId,
        user_device_id: userDeviceId,
      } = await getUserInfoBasedOnHeaderInformation(params.headers)
      const returnValue = { return_code: 0 }

      const { query } = params
      const { districtId } = query

      const dbConnections = getDBConnections()

      const mainSchemaAddition =
      configurationJSON().IS_NODE_SERVER &&
      configurationJSON().IS_NODE_SERVER === 1
        ? 'main.'
        : ''

      let geoQuerySelect = 
        `
          select rt.taluk_id, rt.taluk_name_l10n
        `
      let geoQueryAfterSelect = 
      `
        from ${mainSchemaAddition}ref_taluk rt
        where 1 = 1
      `
      if (districtId) {
        geoQueryAfterSelect = geoQueryAfterSelect + ` and rt.district_id = ${districtId}`
      }

      const finalGeoQuery = geoQuerySelect + geoQueryAfterSelect

      const geoResults = await dbConnections.main.manager.query(finalGeoQuery)

      returnValue.list_data = geoResults
      return returnValue

    } catch (error) {
      console.log(error)
      if (error.name === 'KrushalError') {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }*/
    try {
      const talukListLib = new TalukListLib()
      return talukListLib.create(data, params)
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }  
  }
}

class TalukListV2 {
  async create (data, params) {
    try {
      const returnValue = { return_code: 0 }

      const { query } = params
      const { districtId, farmerVillageId } = query

      const dbConnections = getDBConnections()

      const mainSchemaAddition =
      configurationJSON().IS_NODE_SERVER &&
      configurationJSON().IS_NODE_SERVER === 1
        ? 'main.'
        : ''

      let geoQuery = `
        select t_rg.geography_id, t_rg.geography_id as taluk_id, t_rg.geography_name_l10n,
          t_rg.geography_name_l10n as taluk_name_l10n
        from ${mainSchemaAddition}ref_geography t_rg
        where t_rg.geography_type_id = 1000320003
      `
      if (districtId) {
        geoQuery = 
          `
            select t_rg.geography_id, t_rg.geography_id as taluk_id, t_rg.geography_name_l10n,
              t_rg.geography_name_l10n as taluk_name_l10n
            from ${mainSchemaAddition}ref_geography t_rg
            inner join ${mainSchemaAddition}entity_relationship t2d_er on t2d_er.entity_relationship_type_id = 1000210030
              and t2d_er.entity_1_type_id = 1000460015 and t2d_er.entity_2_type_id = 1000460015
              and t2d_er.entity_2_entity_id = t_rg.geography_id
              and t2d_er.entity_1_entity_id = ${districtId}
            where t_rg.geography_type_id = 1000320003
          `
      } else if (farmerVillageId) {
        // get all taluks where district id is same as district id of given village
        geoQuery = `
          select t_rg.geography_id, t_rg.geography_id as taluk_id, t_rg.geography_name_l10n,
            t_rg.geography_name_l10n as taluk_name_l10n
          from ${mainSchemaAddition}ref_sdtv_view_2 rsv
          inner join ${mainSchemaAddition}entity_relationship d2t_er on d2t_er.entity_relationship_type_id = 1000210030
            and d2t_er.entity_1_type_id = 1000460015 and d2t_er.entity_2_type_id = 1000460015
            and d2t_er.entity_1_entity_id = rsv.district_id
          inner join ${mainSchemaAddition}ref_geography t_rg on t_rg.geography_type_id = 1000320003
            and t_rg.geography_id = d2t_er.entity_2_entity_id
          where rsv.village_id = ${farmerVillageId}
        `
      }
      

      const geoResults = await dbConnections.main.manager.query(geoQuery)
      postProcessRecords(undefined, geoResults, {json_columns: ['geography_name_l10n', 'taluk_name_l10n', 'district_name_l10n']})

      returnValue.list_data = geoResults
      return returnValue
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }  
  }
}

class VillageList {
  async create /*or is it update*/(data, params) {
    /*
    try {
      const {
        entity_type_id: entityTypeId,
        entity_uuid: entityId,
        user_device_id: userDeviceId,
      } = await getUserInfoBasedOnHeaderInformation(params.headers)
      const returnValue = { return_code: 0 }

      const { query } = params
      const { talukId } = query

      const dbConnections = getDBConnections()

      const mainSchemaAddition =
      configurationJSON().IS_NODE_SERVER &&
      configurationJSON().IS_NODE_SERVER === 1
        ? 'main.'
        : ''

      let geoQuerySelect = 
        `
          select rv.village_id, rv.village_name_l10n
        `
      let geoQueryAfterSelect = 
      `
        from ${mainSchemaAddition}ref_village rv
        where 1 = 1
      `
      if (talukId) {
        geoQueryAfterSelect = geoQueryAfterSelect + ` and rv.taluk_id = ${talukId}`
      }

      const finalGeoQuery = geoQuerySelect + geoQueryAfterSelect

      const geoResults = await dbConnections.main.manager.query(finalGeoQuery)

      returnValue.list_data = geoResults
      return returnValue

    } catch (error) {
      console.log(error)
      if (error.name === 'KrushalError') {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
    */
    try {
      const villageListLib = new VillageListLib()
      return villageListLib.create(data, params)
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class VillageListV2 {
  async create (data, params) {
    try {
      const returnValue = { return_code: 0 }

      const { query } = params
      const { talukId, farmerVillageId } = query

      const dbConnections = getDBConnections()

      const mainSchemaAddition =
      configurationJSON().IS_NODE_SERVER &&
      configurationJSON().IS_NODE_SERVER === 1
        ? 'main.'
        : ''

      let geoQuery = `
        select v_rg.geography_id, v_rg.geography_id as village_id, v_rg.geography_name_l10n,
          v_rg.geography_name_l10n as village_name_l10n
        from ${mainSchemaAddition}ref_geography t_rg
        where v_rg.geography_type_id = 1000320004
      `
      if (talukId) {
        geoQuery = 
          `
            select v_rg.geography_id, v_rg.geography_id as village_id, v_rg.geography_name_l10n,
              v_rg.geography_name_l10n as village_name_l10n
            from ${mainSchemaAddition}entity_relationship t2v_er 
            inner join ${mainSchemaAddition}ref_geography v_rg on v_rg.geography_type_id = 1000320004
              and t2v_er.entity_2_entity_id = v_rg.geography_id
            where t2v_er.entity_relationship_type_id = 1000210031
              and t2v_er.entity_1_type_id = 1000460015 and t2v_er.entity_2_type_id = 1000460015
              and t2v_er.entity_1_entity_id = ${talukId}
          `
      } else if (farmerVillageId) {
        // get all village where taluk id is same as taluk id of given village
        geoQuery = `
          select v_rg.geography_id, v_rg.geography_id as village_id, v_rg.geography_name_l10n,
            v_rg.geography_name_l10n as village_name_l10n
          from ${mainSchemaAddition}ref_sdtv_view_2 rsv
          inner join ${mainSchemaAddition}entity_relationship t2v_er on t2v_er.entity_relationship_type_id = 1000210031
            and t2v_er.entity_1_type_id = 1000460015 and t2v_er.entity_2_type_id = 1000460015
            and t2v_er.entity_1_entity_id = rsv.taluk_id
          inner join ${mainSchemaAddition}ref_geography v_rg on v_rg.geography_type_id = 1000320004
            and v_rg.geography_id = t2v_er.entity_2_entity_id
          where rsv.village_id = ${farmerVillageId}
        `
      }

      const geoResults = await dbConnections.main.manager.query(geoQuery)
      postProcessRecords(undefined, geoResults, {json_columns: ['village_name_l10n']})

      returnValue.list_data = geoResults
      return returnValue
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

const getGeoListingForParticularLevel = async (parentGeographyIdArray, parentToChildEntityRelationshipTypeId, parentGeographyTypeId, childGeographyTypeId, ) => {
  try {
    const dbConnections = getDBConnections()

    const mainSchemaAddition =
    configurationJSON().IS_NODE_SERVER &&
    configurationJSON().IS_NODE_SERVER === 1
      ? 'main.'
      : ''

    let geoQuery
    if (parentGeographyIdArray === undefined) {
      geoQuery = `
        select p_rg.geography_id, p_rg.geography_name_l10n
        from ${mainSchemaAddition}ref_geography p_rg
        where p_rg.geography_type_id = ${parentGeographyTypeId}
      `
    } else {
      const parentGeographyIdArrayAsString = parentGeographyIdArray.join()
      geoQuery = `
        select c_rg.geography_id, c_rg.geography_name_l10n
        from ${mainSchemaAddition}ref_geography p_rg
        inner join ${mainSchemaAddition}entity_relationship p2c_er on p2c_er.entity_relationship_type_id = ${parentToChildEntityRelationshipTypeId}
        and p2c_er.entity_1_type_id = 1000460015 and p2c_er.entity_2_type_id = 1000460015
        and p2c_er.entity_1_entity_id = p_rg.geography_id
        inner join ${mainSchemaAddition}ref_geography c_rg on c_rg.geography_type_id = ${childGeographyTypeId} and c_rg.geography_id = p2c_er.entity_2_entity_id
        where p_rg.geography_type_id = ${parentGeographyTypeId}
        and p_rg.geography_id in (${parentGeographyIdArrayAsString}) 
      `
    }
    const geoResultsUnprocessed = await dbConnections.main.manager.query(geoQuery)
    const geoResults = postProcessRecords(undefined, geoResultsUnprocessed, {json_columns: ['geography_name_l10n']})
    return geoResults
  } catch (error) {
    console.log('error', error)
  }
}
class GeographyList {
  async create (data, params) {
    try {
      const returnValue = { return_code: 0 }

      const { parentGeographyIdArray, parentToChildEntityRelationshipTypeId, parentGeographyTypeId, childGeographyTypeId } = data
      const geoResults = await getGeoListingForParticularLevel(parentGeographyIdArray, parentToChildEntityRelationshipTypeId, parentGeographyTypeId, childGeographyTypeId)
      
      returnValue.list_data = geoResults
      return returnValue
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class VillageGeographies {
  async create (data, params) {
    try {
      const returnValue = { return_code: 0 }

      const { village_id_array } = data

      const villageIdArrayAsString = village_id_array.join()

      const dbConnections = getDBConnections()

      const mainSchemaAddition =
      configurationJSON().IS_NODE_SERVER &&
      configurationJSON().IS_NODE_SERVER === 1
        ? 'main.'
        : ''

      const sdtvQuery = `
        select *
        from ${mainSchemaAddition}ref_sdtv_view_2 rsv
        where rsv.village_id in (${villageIdArrayAsString})
      `
      
      const sdtvQueryResultUnprocessed = await dbConnections.main.manager.query(sdtvQuery)
      const sdtvQueryResult = postProcessRecords(undefined, sdtvQueryResultUnprocessed, {json_columns: ['geography_name_l10n']})
      const stateIdAsArray = [sdtvQueryResult[0].state_id]
      const districtIdAsArray = [sdtvQueryResult[0].district_id]
      const talukIdAsArray = [sdtvQueryResult[0].taluk_id]

      const stateList = await getGeoListingForParticularLevel(undefined, undefined, 1000320001)
      const districtList = await getGeoListingForParticularLevel(stateIdAsArray, 1000210029, 1000320001, 1000320002)
      const talukList = await getGeoListingForParticularLevel(districtIdAsArray, 1000210030, 1000320002, 1000320003)
      const villageList = await getGeoListingForParticularLevel(talukIdAsArray, 1000210031, 1000320003, 1000320004)

      returnValue.state_id = stateIdAsArray
      returnValue.district_id = districtIdAsArray
      returnValue.taluk_id = talukIdAsArray
      returnValue.state_list = stateList
      returnValue.district_list = districtList
      returnValue.taluk_list = talukList
      returnValue.village_list = villageList

      return returnValue
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class GeoJSONData {
  async create (data, params) {
    try {
      const returnValue = { return_code: 0 }

      const { parentGeographyIdArray, parentToChildEntityRelationshipTypeId, parentGeographyTypeId, childGeographyTypeId } = data

      const dbConnections = getDBConnections()

      const mainSchemaAddition =
      configurationJSON().IS_NODE_SERVER &&
      configurationJSON().IS_NODE_SERVER === 1
        ? 'main.'
        : ''

      const parentGeographyIdArrayAsString = parentGeographyIdArray.join()

      const villageGeoJSONQuery = `
        select ng_rg.geography_id, ng_rg.geography_geojson, ng_rg.geography_name_l10n as next_gen_geography_name_l10n,
          cg_rg.geography_name_l10n as current_gen_geography_name_l10n,
          rsv.district_name_l10n, rsv.state_name_l10n
        from ${mainSchemaAddition}ref_geography cg_rg
        inner join ${mainSchemaAddition}entity_relationship p2c_er on p2c_er.entity_relationship_type_id = ${parentToChildEntityRelationshipTypeId}
          and p2c_er.entity_1_type_id = 1000460015 and p2c_er.entity_2_type_id = 1000460015
          and p2c_er.entity_1_entity_id = cg_rg.geography_id
        inner join ${mainSchemaAddition}ref_geography ng_rg on ng_rg.geography_type_id = ${childGeographyTypeId} and ng_rg.geography_id = p2c_er.entity_2_entity_id
          and ng_rg.geography_geojson is not NULL
        inner join ${mainSchemaAddition}ref_sdtv_view_2 rsv on rsv.village_id = ng_rg.geography_id
        where cg_rg.geography_type_id = ${parentGeographyTypeId}
          and cg_rg.geography_id in (${parentGeographyIdArrayAsString})
      `
      const villageGeoJSONQueryResultsUnprocessed = await dbConnections.main.manager.query(villageGeoJSONQuery)
      const villageGeoJSONQueryResults = postProcessRecords(undefined, villageGeoJSONQueryResultsUnprocessed, {json_columns: ['geography_name_l10n']})

      returnValue.village_geojson_array = villageGeoJSONQueryResults

      const talukGeoJSONQuery = `
        select sg_rg.geography_id, sg_rg.geography_geojson, cg_rg.geography_name_l10n as current_gen_geography_name_l10n,
          sg_rg.geography_name_l10n as sibling_gen_geography_name_l10n
        from ${mainSchemaAddition}ref_geography cg_rg
        inner join ${mainSchemaAddition}entity_relationship t2d_er on t2d_er.entity_relationship_type_id = 1000210030
          and t2d_er.entity_1_type_id = 1000460015 and t2d_er.entity_2_type_id = 1000460015
          and t2d_er.entity_2_entity_id = cg_rg.geography_id
        inner join ${mainSchemaAddition}entity_relationship d2t_er on d2t_er.entity_relationship_type_id = 1000210030
          and d2t_er.entity_1_type_id = 1000460015 and d2t_er.entity_2_type_id = 1000460015
          and d2t_er.entity_1_entity_id = t2d_er.entity_1_entity_id
        inner join ${mainSchemaAddition}ref_geography sg_rg on sg_rg.geography_type_id = ${parentGeographyTypeId} and sg_rg.geography_id = d2t_er.entity_2_entity_id
          and sg_rg.geography_geojson is not NULL
        where cg_rg.geography_type_id = ${parentGeographyTypeId}
          and cg_rg.geography_id in (${parentGeographyIdArrayAsString})
      `
      const talukGeoJSONQueryResultsUnprocessed = await dbConnections.main.manager.query(talukGeoJSONQuery)
      const talukGeoJSONQueryResults = postProcessRecords(undefined, talukGeoJSONQueryResultsUnprocessed, {json_columns: ['geography_name_l10n']})

      const districtGeoJSONQuery = `
        select scg_rg.geography_id, scg_rg.geography_name_l10n, scg_rg.geography_geojson,
          cg_rg.geography_name_l10n as current_gen_geography_name_l10n,
          scg_rg.geography_name_l10n as second_cousin_gen_geography_name_l10n
        from ${mainSchemaAddition}ref_geography cg_rg
        inner join ${mainSchemaAddition}entity_relationship t2d_er on t2d_er.entity_relationship_type_id = 1000210030
          and t2d_er.entity_1_type_id = 1000460015 and t2d_er.entity_2_type_id = 1000460015
          and t2d_er.entity_2_entity_id = cg_rg.geography_id
        inner join ${mainSchemaAddition}entity_relationship d2s_er on d2s_er.entity_relationship_type_id = 1000210029
          and d2s_er.entity_1_type_id = 1000460015 and d2s_er.entity_2_type_id = 1000460015
          and d2s_er.entity_2_entity_id = t2d_er.entity_1_entity_id
        inner join ${mainSchemaAddition}entity_relationship s2d_er on s2d_er.entity_relationship_type_id = 1000210029
          and s2d_er.entity_1_type_id = 1000460015 and s2d_er.entity_2_type_id = 1000460015
          and s2d_er.entity_1_entity_id = d2s_er.entity_1_entity_id
        inner join ${mainSchemaAddition}ref_geography scg_rg on scg_rg.geography_type_id = 1000320002 and scg_rg.geography_id = s2d_er.entity_2_entity_id
          and scg_rg.geography_geojson is not NULL
        where cg_rg.geography_type_id = ${parentGeographyTypeId}
          and cg_rg.geography_id in (${parentGeographyIdArrayAsString})
      `
      const districtGeoJSONQueryResultsUnprocessed = await dbConnections.main.manager.query(districtGeoJSONQuery)
      const districtGeoJSONQueryResults = postProcessRecords(undefined, districtGeoJSONQueryResultsUnprocessed, {json_columns: ['geography_name_l10n']})

      returnValue.village_geojson_array = villageGeoJSONQueryResults
      returnValue.taluk_geojson_array = talukGeoJSONQueryResults
      returnValue.district_geojson_array = districtGeoJSONQueryResults

      return returnValue
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class GeoArrays {
  async create /*or is it update*/(data, params) {
    /*
    try {
      const customerId = params.query.customerId
      const returnValue = { return_code: 0 }

      const dbConnections = getDBConnections()

      const mainSchemaAddition =
      configurationJSON().IS_NODE_SERVER &&
      configurationJSON().IS_NODE_SERVER === 1
        ? 'main.'
        : ''

      if (customerId === -1 || customerId === '-1') {
        // get all districts of maharashtra
        const stateId = 1026
        let geoQuery = 
          `
            select rd.district_id, rd.district_name_l10n
            from ${mainSchemaAddition}ref_district rd, ${mainSchemaAddition}ref_state rs
            where rd.state_id = rs.state_id
            and rd.state_id = ${stateId}
          `
        const districtResults = await dbConnections.main.manager.query(geoQuery)
        returnValue.district_list = districtResults
        returnValue.taluk_list = []
        returnValue.village_list = []
      } else {
        // get customer village
        const customerVillageQuery = 
          `
            select value_reference_id
            from ${mainSchemaAddition}customer_classification cc 
            where cc.customer_id = '${customerId}'
            and cc.classifier_id = 2000000055
          `
        const customerVillageResults = await dbConnections.main.manager.query(customerVillageQuery)
        const customerVillageId = (customerVillageResults.length > 0) ? customerVillageResults[0].value_reference_id : undefined
        // get districts (state districts?) of that village
        // get taluks of that village
        // get villages of that village
        if (customerVillageId) {
          let geoQuery = 
            `
              select rd.district_id, rd.district_name_l10n
              from ${mainSchemaAddition}ref_district rd, ${mainSchemaAddition}ref_state rs, ${mainSchemaAddition}ref_sdtv_view rsv
              where rd.state_id = rs.state_id and 
              rd.state_id = rsv.state_id and rsv.village_id = ${customerVillageId}
                `
          const districtResults = await dbConnections.main.manager.query(geoQuery)
          returnValue.district_list = districtResults
          geoQuery = 
            `
              select rt.taluk_id, rt.taluk_name_l10n
              from ${mainSchemaAddition}ref_taluk rt, ${mainSchemaAddition}ref_sdtv_view rsv
              where 1 = 1 and rt.district_id = rsv.district_id and rsv.village_id = ${customerVillageId}
            `
          const talukResults = await dbConnections.main.manager.query(geoQuery)
          returnValue.taluk_list = talukResults
          geoQuery = 
            `
              select rv.village_id, rv.village_name_l10n
              from ${mainSchemaAddition}ref_village rv, ${mainSchemaAddition}ref_sdtv_view rsv
              where 1 = 1
              and rv.taluk_id = rsv.taluk_id and rsv.village_id = ${customerVillageId}
            `
          const villageResults = await dbConnections.main.manager.query(geoQuery)
          returnValue.village_list = villageResults
        }

      }
      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === 'KrushalError') {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
    */    
    try {
      const geoArraysLib = new GeoArraysLib()
      return geoArraysLib.create(data, params)
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class GeoData {
  async create (data, params) {
    try {
      const returnValue = {return_code: 0}
      const mainDBConnection = dbConnections().main

      const mainSchemaAddition =
      configurationJSON().IS_NODE_SERVER &&
      configurationJSON().IS_NODE_SERVER === 1
        ? 'main.'
        : ''

      let villageIdArray = data.villageIdArray && Array.isArray(data.villageIdArray) ? data.villageIdArray : []
      let talukIdArray = data.talukIdArray && Array.isArray(data.talukIdArray) ? data.talukIdArray : []
      let districtIdArray = data.districtIdArray && Array.isArray(data.districtIdArray) ? data.districtIdArray : []
      let stateIdArray = data.stateIdArray && Array.isArray(data.stateIdArray) ? data.stateIdArray : []

      if (villageIdArray.length > 0) {
        // get state ids, district ids, taluk ids of villages
        const geographyDetailsQuery = `
          select *
          from ${mainSchemaAddition}ref_sdtv_view rsv
        `
        const geographyDetailsQueryResultUnprocessed = await mainDBConnection.manager
            .createQueryBuilder()
            .from(`(${geographyDetailsQuery})`, 'rsv')
            .where('village_id in (:...villageIds)', {villageIds: villageIdArray})
            .execute()
        const geographyDetailsQueryResult = postProcessRecords(undefined, geographyDetailsQueryResultUnprocessed, {json_columns:['village_name_l10n', 'taluk_name_l10n', 'district_name_l10n', 'state_name_l10n']})
        talukIdArray = geographyDetailsQueryResult.map(result => {
          return result['taluk_id']
        })
        districtIdArray = geographyDetailsQueryResult.map(result => {
          return result['district_id']
        })
        stateIdArray = geographyDetailsQueryResult.map(result => {
          return result['state_id']
        })
        const stateEntity = mainDBConnection.entities['ref_state']
        const stateListUnprocessed = await mainDBConnection.manager
            .createQueryBuilder()
            .select(['state_id', 'state_name_l10n'])
            .from(stateEntity)
            // .from('care_calendar')
            // .where('village_id = :id)', { id: villageId })
            .execute()
        const stateList = postProcessRecords(undefined, stateListUnprocessed, {json_columns:['state_name_l10n']})

        const districtEntity = mainDBConnection.entities['ref_district']
        const districtListUnprocessed = await mainDBConnection.manager
            .createQueryBuilder()
            .select(['district_id', 'district_name_l10n'])
            .from(districtEntity)
            // .from('care_calendar')
            .where('state_id in (:...id)', { id: stateIdArray })
            .execute()
        const districtList = postProcessRecords(undefined, districtListUnprocessed, {json_columns:['district_name_l10n']})

        const talukEntity = mainDBConnection.entities['ref_taluk']
        const talukListUnprocessed = await mainDBConnection.manager
            .createQueryBuilder()
            .select(['taluk_id', 'taluk_name_l10n'])
            .from(talukEntity)
            // .from('care_calendar')
            .where('district_id in (:...id)', { id: districtIdArray })
            .execute()
        const talukList = postProcessRecords(undefined, talukListUnprocessed, {json_columns:['taluk_name_l10n']})

        const villageEntity = mainDBConnection.entities['ref_village']
        const villageListUnprocessed = await mainDBConnection.manager
            .createQueryBuilder()
            .select(['village_id', 'village_name_l10n'])
            .from(villageEntity)
            // .from('care_calendar')
            .where('taluk_id in (:...id)', { id: talukIdArray })
            .execute()
        const villageList = postProcessRecords(undefined, villageListUnprocessed, {json_columns:['village_name_l10n']})
    
        returnValue.selectedVillageIdArray = villageIdArray
        returnValue.selectedTalukIdArray = talukIdArray
        returnValue.selectedDistrictIdArray = districtIdArray
        returnValue.selectedStateIdArray = stateIdArray
        returnValue.stateArray = stateList
        returnValue.districtArray = districtList
        returnValue.talukArray = talukList
        returnValue.talukArray = talukList
        returnValue.villageArray = villageList
        
        // get all states
        // get all districts of states ids
        // get all taluks of district ids
        // get all villages of taluk ids
      } else if (talukIdArray.length > 0) {
        const geographyDetailsQuery = `
          select rt.taluk_name_l10n, rt.taluk_id, rd.district_name_l10n, rd.district_id,
            rs.state_name_l10n, rs.state_id
          from ${mainSchemaAddition}ref_taluk rt
          inner join ${mainSchemaAddition}ref_district rd on rt.district_id = rd.district_id
          inner join ${mainSchemaAddition}ref_state rs on rd.state_id = rs.state_id
        `

        const geographyDetailsQueryResultUnprocessed = await mainDBConnection.manager
            .createQueryBuilder()
            .from(`(${geographyDetailsQuery})`, 'rt')
            .where('taluk_id in (:...talukIds)', {talukIds: talukIdArray})
            .execute()
        const geographyDetailsQueryResult = postProcessRecords(undefined, geographyDetailsQueryResultUnprocessed, {json_columns:['taluk_name_l10n', 'district_name_l10n', 'state_name_l10n']})
        districtIdArray = geographyDetailsQueryResult.map(result => {
          return result['district_id']
        })
        stateIdArray = geographyDetailsQueryResult.map(result => {
          return result['state_id']
        })
        const stateEntity = mainDBConnection.entities['ref_state']
        const stateListUnprocessed = await mainDBConnection.manager
            .createQueryBuilder()
            .select(['state_id', 'state_name_l10n'])
            .from(stateEntity)
            // .from('care_calendar')
            // .where('village_id = :id)', { id: villageId })
            .execute()
        const stateList = postProcessRecords(undefined, stateListUnprocessed, {json_columns:['state_name_l10n']})

        const districtEntity = mainDBConnection.entities['ref_district']
        const districtListUnprocessed = await mainDBConnection.manager
            .createQueryBuilder()
            .select(['district_id', 'district_name_l10n'])
            .from(districtEntity)
            // .from('care_calendar')
            .where('state_id in (:...id)', { id: stateIdArray })
            .execute()
        const districtList = postProcessRecords(undefined, districtListUnprocessed, {json_columns:['district_name_l10n']})

        const talukEntity = mainDBConnection.entities['ref_taluk']
        const talukListUnprocessed = await mainDBConnection.manager
            .createQueryBuilder()
            .select(['taluk_id', 'taluk_name_l10n'])
            .from(talukEntity)
            // .from('care_calendar')
            .where('district_id in (:...id)', { id: districtIdArray })
            .execute()
        const talukList = postProcessRecords(undefined, talukListUnprocessed, {json_columns:['taluk_name_l10n']})

        const villageEntity = mainDBConnection.entities['ref_village']
        const villageListUnprocessed = await mainDBConnection.manager
            .createQueryBuilder()
            .select(['village_id', 'village_name_l10n'])
            .from(villageEntity)
            // .from('care_calendar')
            .where('taluk_id in (:...id)', { id: talukIdArray })
            .execute()
        const villageList = postProcessRecords(undefined, villageListUnprocessed, {json_columns:['village_name_l10n']})
    
        returnValue.selectedTalukIdArray = talukIdArray
        returnValue.selectedDistrictIdArray = districtIdArray
        returnValue.selectedStateIdArray = stateIdArray
        returnValue.stateArray = stateList
        returnValue.districtArray = districtList
        returnValue.talukArray = talukList
        returnValue.talukArray = talukList
        returnValue.villageArray = villageList
      } else if (districtIdArray.length > 0) {
        const geographyDetailsQuery = `
          select rd.district_name_l10n, rd.district_id, rs.state_name_l10n, rs.state_id
          from ${mainSchemaAddition}ref_district rd
          inner join ${mainSchemaAddition}ref_state rs on rd.state_id = rs.state_id
        `

        const geographyDetailsQueryResultUnprocessed = await mainDBConnection.manager
            .createQueryBuilder()
            .from(`(${geographyDetailsQuery})`, 'rt')
            .where('district_id in (:...districtIds)', {districtIds: districtIdArray})
            .execute()
        const geographyDetailsQueryResult = postProcessRecords(undefined, geographyDetailsQueryResultUnprocessed, {json_columns:['district_name_l10n', 'state_name_l10n']})
        stateIdArray = geographyDetailsQueryResult.map(result => {
          return result['state_id']
        })
        const stateEntity = mainDBConnection.entities['ref_state']
        const stateListUnprocessed = await mainDBConnection.manager
            .createQueryBuilder()
            .select(['state_id', 'state_name_l10n'])
            .from(stateEntity)
            // .from('care_calendar')
            // .where('village_id = :id)', { id: villageId })
            .execute()
        const stateList = postProcessRecords(undefined, stateListUnprocessed, {json_columns:['state_name_l10n']})

        const districtEntity = mainDBConnection.entities['ref_district']
        const districtListUnprocessed = await mainDBConnection.manager
            .createQueryBuilder()
            .select(['district_id', 'district_name_l10n'])
            .from(districtEntity)
            // .from('care_calendar')
            .where('state_id in (:...id)', { id: stateIdArray })
            .execute()
        const districtList = postProcessRecords(undefined, districtListUnprocessed, {json_columns:['district_name_l10n']})

        const talukEntity = mainDBConnection.entities['ref_taluk']
        const talukListUnprocessed = await mainDBConnection.manager
            .createQueryBuilder()
            .select(['taluk_id', 'taluk_name_l10n'])
            .from(talukEntity)
            // .from('care_calendar')
            .where('district_id in (:...id)', { id: districtIdArray })
            .execute()
        const talukList = postProcessRecords(undefined, talukListUnprocessed, {json_columns:['taluk_name_l10n']})

        returnValue.selectedDistrictIdArray = districtIdArray
        returnValue.selectedStateIdArray = stateIdArray
        returnValue.stateArray = stateList
        returnValue.districtArray = districtList
        returnValue.talukArray = talukList
        returnValue.talukArray = talukList
      } else if (stateIdArray.length > 0) {
        
        const stateEntity = mainDBConnection.entities['ref_state']
        const stateListUnprocessed = await mainDBConnection.manager
            .createQueryBuilder()
            .select(['state_id', 'state_name_l10n'])
            .from(stateEntity)
            // .from('care_calendar')
            .where('state_id in (:...id)', { id: stateIdArray })
            .execute()
        const stateList = postProcessRecords(undefined, stateListUnprocessed, {json_columns:['state_name_l10n']})

        const districtEntity = mainDBConnection.entities['ref_district']
        const districtListUnprocessed = await mainDBConnection.manager
            .createQueryBuilder()
            .select(['district_id', 'district_name_l10n'])
            .from(districtEntity)
            // .from('care_calendar')
            .where('state_id in (:...id)', { id: stateIdArray })
            .execute()
        const districtList = postProcessRecords(undefined, districtListUnprocessed, {json_columns:['district_name_l10n']})

        returnValue.selectedStateIdArray = stateIdArray
        returnValue.stateArray = stateList
        returnValue.districtArray = districtList
      } else {  
        const stateEntity = mainDBConnection.entities['ref_state']
        const stateListUnprocessed = await mainDBConnection.manager
            .createQueryBuilder()
            .select(['state_id', 'state_name_l10n'])
            .from(stateEntity)
            // .from('care_calendar')
            // .where('state_id in (:...id)', { id: stateIdArray })
            .execute()
        const stateList = postProcessRecords(undefined, stateListUnprocessed, {json_columns:['state_name_l10n']})
        returnValue.stateArray = stateList
      }
      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === 'KrushalError') {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async find(params) {
    try {
      const {
        entity_type_id: entityTypeId,
        entity_uuid: entityId,
        user_device_id: userDeviceId,
      } = await getUserInfoBasedOnHeaderInformation(params.headers)
      const returnValue = { return_code: 0 }
      const { query } = params
      const { villageId } = query

      const mainDBConnection = dbConnections().main

      const mainSchemaAddition =
      configurationJSON().IS_NODE_SERVER &&
      configurationJSON().IS_NODE_SERVER === 1
        ? 'main.'
        : ''

      const sdtvQuery = `
        select village_id, taluk_id, district_id, state_id
        from ${mainSchemaAddition}ref_sdtv_view rsv
        where rsv.village_id = ${villageId}
      `

      const sdtvQueryResultUnprocessed = await mainDBConnection.manager.query(sdtvQuery)
      const sdtvQueryResult = postProcessRecords(undefined, sdtvQueryResultUnprocessed, {json_columns:[]})
      const sdtvRow = sdtvQueryResult[0]

      const stateEntity = mainDBConnection.entities['ref_state']
      const stateListUnprocessed = await mainDBConnection.manager
          .createQueryBuilder()
          .select(['state_id', 'state_name_l10n'])
          .from(stateEntity)
          // .from('care_calendar')
          // .where('village_id = :id)', { id: villageId })
          .execute()
      const stateList = postProcessRecords(undefined, stateListUnprocessed, {json_columns:['state_name_l10n']})
  
      const districtEntity = mainDBConnection.entities['ref_district']
      const districtListUnprocessed = await mainDBConnection.manager
          .createQueryBuilder()
          .select(['district_id', 'district_name_l10n'])
          .from(districtEntity)
          // .from('care_calendar')
          .where('state_id = :id', { id: sdtvRow.state_id })
          .execute()
      const districtList = postProcessRecords(undefined, districtListUnprocessed, {json_columns:['district_name_l10n']})

      const talukEntity = mainDBConnection.entities['ref_taluk']
      const talukListUnprocessed = await mainDBConnection.manager
          .createQueryBuilder()
          .select(['taluk_id', 'taluk_name_l10n'])
          .from(talukEntity)
          // .from('care_calendar')
          .where('district_id = :id', { id: sdtvRow.district_id })
          .execute()
      const talukList = postProcessRecords(undefined, talukListUnprocessed, {json_columns:['taluk_name_l10n']})

      const villageEntity = mainDBConnection.entities['ref_village']
      const villageListUnprocessed = await mainDBConnection.manager
          .createQueryBuilder()
          .select(['village_id', 'village_name_l10n'])
          .from(villageEntity)
          // .from('care_calendar')
          .where('taluk_id = :id', { id: sdtvRow.taluk_id })
          .execute()
      const villageList = postProcessRecords(undefined, villageListUnprocessed, {json_columns:['village_name_l10n']})

      returnValue.state_id = [sdtvRow.state_id]
      returnValue.district_id = [sdtvRow.district_id]
      returnValue.taluk_id = [sdtvRow.taluk_id]
      returnValue.village_id = [sdtvRow.village_id]
      returnValue.state_list = stateList
      returnValue.district_list = districtList
      returnValue.taluk_list = talukList
      returnValue.village_list = villageList

      return returnValue

    } catch (error) {
      console.log(error)
      if (error.name === 'KrushalError') {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

module.exports = { StateList, StateListV2, DistrictList, DistrictListV2, TalukList, TalukListV2, VillageList, VillageListV2, GeographyList, VillageGeographies, GeoJSONData, GeoArrays, GeoData }
