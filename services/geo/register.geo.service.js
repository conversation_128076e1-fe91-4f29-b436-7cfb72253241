const {
  StateList,
  StateListV2,
  DistrictList,
  DistrictListV2,
  TalukList,
  TalukListV2,
  VillageList,
  VillageListV2,
  GeographyList,
  GeoJSONData,
  GeoArrays,
  VillageGeographies,
  GeoData
} = require('./geo.class')
const auth = require('../../middleware/auth')
const configureGeo = (app) => {
  // app.use(auth)
  app.use('/geo/states', new StateList())
  app.use('/v2/geo/states', new StateListV2())
  app.use('/geo/districts', new DistrictList())
  app.use('/v2/geo/districts', new DistrictListV2())
  app.use('/geo/taluks', new TalukList())
  app.use('/v2/geo/taluks', new TalukListV2())
  app.use('/geo/villages', new VillageList())
  app.use('/v2/geo/villages', new VillageListV2())
  app.use('/v2/geo', new GeographyList())
  app.use('/v2/geos-for-village', new VillageGeographies())
  app.use('/v2/geo-json', new GeoJSONData())
  app.use('/geo/get-geo-arrays', new GeoArrays())
  app.use('/v2/geo/get-geo-data', new GeoData())
}
module.exports = { configureGeo }
