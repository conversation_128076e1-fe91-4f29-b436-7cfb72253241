const { LibInventoryLedgerService } = require("@krushal-it/back-end-lib");
const uuid = require("uuid");
const errors = require("@feathersjs/errors");
const { responseStructure } = require("../common/responsestructure");

class InventoryLedgerService {
    inventoryLedgerHandler = new LibInventoryLedgerService()

    async create(data,params){
      try{
        let result = await this.inventoryLedgerHandler.getInventoryLedger(data,params);
        return responseStructure(result);
      } catch (error) {
        console.error(error);
        return { result: false, error };
      }
    } 

    async find(params){
        try {
        let result = await this.inventoryLedgerHandler.getInventoryKitList(params);
        return responseStructure(result);
      } catch (error) {
        console.error(error);
        return { result: false, error };
      }
    }
}

class MedicineInventoryService {
  inventoryLedgerHandler = new LibInventoryLedgerService()
  async create(data,params){
      try {
      let result = await this.inventoryLedgerHandler.getMedicineInventory(data,params);
      return responseStructure(result);
    } catch (error) {
      console.error(error);
      return { result: false, error };
    }
  }
}

module.exports = { InventoryLedgerService, MedicineInventoryService };