const { BadRequest, GeneralError } = require("@feathersjs/errors")
const { observation<PERSON>anager, observationServiceInstance } = require("@krushal-it/back-end-lib")

module.exports = servicesV3 = app => {

  app.use("v3/tasks/:care_calendar_id/observations/all", {
    
    async find(params) {
      const { care_calendar_id } = params.route
      try {
        response = await observationServiceInstance.getObservations(care_calendar_id)
        return response
      } catch (error) {
        return new BadRequest(error).toJSON()
      }
    }
  })

  app.use("v3/tasks/:care_calendar_id/observations", {
    async create(data, params) {
      const { care_calendar_id } = params.route
      try {
        response = await observationManager.save(care_calendar_id, data, params.headers.token)
        return response
      } catch (error) {
        return new BadRequest(error).toJSON()
      }
    },

    async find(params) {
      const { care_calendar_id } = params.route
      const { question_ids } = params.query
      try {
        response = await observationManager.get(care_calendar_id, JSON.parse(question_ids))
        return response
      } catch (error) {
        return new BadRequest(error).toJSON()
      }
    }
  })
}
