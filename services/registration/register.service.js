const { BadRequest } = require("@feathersjs/errors");
const { ENTITY_TYPE_ANIMAL, ENTITY_TYPE_CUSTOMER } = require("../../utils/common/namespace/krushal.namespace");
const { LibUpsertCustomer, LibUpsertAnimalService } = require("@krushal-it/back-end-lib");
//TODO: Form validation
class RegisterService {
  customerRegistrationServiceHandler = new LibUpsertCustomer();
  animalRegistrationServiceHandler = new LibUpsertAnimalService();
  async create(data, params) {
    const { entityType } = params.route;
    try {
      switch (entityType) {
        case ENTITY_TYPE_CUSTOMER: {
          let result = await this.customerRegistrationServiceHandler.create(data, params);
          return result;
        }
        case ENTITY_TYPE_ANIMAL: {
          let result = await this.animalRegistrationServiceHandler.create(data, params);
          return result;
        }
        default:
          throw new BadRequest("Invalid entity type");
      }
    } catch (error) {
      console.error(error);
      if (error.code) return error;
    }
  }
}

module.exports = RegisterService;
