const {LibLocationService} = require("@krushal-it/back-end-lib")
class LocationService{
  locationServiceHandler = new LibLocationService()
  async find(params){
        try {
          let result = await this.locationServiceHandler.find(params);
          return result;
        } catch (error) {
            console.error(error);
            return {result:false,message:error.message};
        }
    }
}

module.exports = LocationService;