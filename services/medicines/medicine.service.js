const { LibMedicineService } = require("@krushal-it/back-end-lib");
const uuid = require("uuid");
const errors = require("@feathersjs/errors");
const { responseStructure } = require("../common/responsestructure");

class MedicineService{
    medicineServiceHandler = new LibMedicineService();
    async find(params){
      try {
        let result = await this.medicineServiceHandler.getMedicines(params);
        return responseStructure(result)
      } catch (error) {
        console.error(error);
        return { result: false, error };
      }
    }
  }
  
class MedQtyService{	
    medicineServiceHandler = new LibMedicineService();
    async create(data,params){
        try {
        let result = await this.medicineServiceHandler.getMedQtyDetails(data,params);
        return responseStructure(result)
        } catch (error) {
        console.error(error);
        return { result: false, error };
        }
    }
}

module.exports = { MedicineService, MedQtyService }
