//const staff_queries=require("../../../database_utils/queries/staff");
const { UsersLogin } = require("@krushal-it/back-end-lib")
const response = require("../../v2/utils/responseTemplate")
const { getDBConnections, preProcessRecords, dbConnections } = require('@krushal-it/ah-orm')
const { loadClassificationData } = require('@krushal-it/back-end-lib')

class ValidatePhone {
  async find(params) {
    try {
      const dbConnections = getDBConnections()
      const phoneNumberAsString = String(params.query.phoneNumber)
      //for staff only
      // const queryToCheckUsersForPhoneNumber = "select * from main.staff where mobile_number = '+91" + phoneNumberAsString + "'"

      //First check in customer DB and then based on the result check staff DB
      let queryToCheckUsersForPhoneNumber = "select * from main.customer where mobile_number = '+91" + phoneNumberAsString + "'"
      let type = "farmer"
      let phoneMatchingResults = await dbConnections.main.manager.query(queryToCheckUsersForPhoneNumber)

      if (phoneMatchingResults.length === 0) {
        queryToCheckUsersForPhoneNumber = "select * from main.staff where mobile_number = '+91" + phoneNumberAsString + "'"
        type = "staff"
        phoneMatchingResults = await dbConnections.main.manager.query(queryToCheckUsersForPhoneNumber)
      }

      if (phoneMatchingResults.length > 0) {
        return { return_code: 0, message: "Mobile exists in the system", type: type }
      } else {
        return {
          return_code: -1,
          message: "Mobile number does not exist in the system",
          type: null
        }
      }
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message, type: null }
    }
  }
}

class LoginMobileUser {
  // do a check on phone number again
  // if instanceId + entity type + entity id match, update uid and other details. else insert
  // to identify entity type + entity id, search staff and customer database for phone number
  // return device id

  async create(data, params) {
    try {
      // console.log('login user, data = ', data)
      const dbConnections = getDBConnections()

      const phoneNumberAsString = String(data.phoneNumber)
      /*const queryToCheckUsersForPhoneNumber =
        'select * from main.staff where mobile_number = "' +
        phoneNumberAsString +
        '"'
      const phoneMatchingResults = await dbConnections.main.manager.query(
        queryToCheckUsersForPhoneNumber
      )*/
      /* const phoneMatchingResults = await dbConnections.main.repos.staff
        .createQueryBuilder('staff')
        .where(' mobile_number = :mobileNumber', {
          mobileNumber: '+91' + phoneNumberAsString,
        })
        .getMany() */
      const queryToCheckUsersForPhoneNumber =
        "\
                                              select s.staff_id, s.staff_type_id, sc.value_reference_id, \
                                                sccom.value_reference_id as compress_db_sync, \
                                                sfm.value_json as staff_feature_map,\
                                                s.mobile_number, s.staff_name_l10n, rr_st.reference_name_l10n as staff_type_l10n, \
                                                sd.document_information \
                                              from main.staff s \
                                              inner join main.ref_reference rr_st on s.staff_type_id = rr_st.reference_id \
                                              left join main.staff_classification sc on sc.staff_id = s.staff_id and sc.classifier_id = 2000000137 and sc.active = 1000100001 \
                                              left join main.staff_classification sccom on sccom.staff_id = s.staff_id and sccom.classifier_id = 2000000206 \
                                              left join main.staff_classification sfm on sfm.staff_id = s.staff_id and sfm.classifier_id = 2000000425 \
                                              left join main.document sd on sd.document_type_id = 1000260004 and sd.entity_1_entity_uuid = s.staff_id \
                                              where s.mobile_number = '+91" +
        phoneNumberAsString +
        "' and s.active = 1000100001"
      const phoneMatchingResults = await dbConnections.main.manager.query(queryToCheckUsersForPhoneNumber)

      if (phoneMatchingResults.length > 0) {
        // identify entity type and entity
        console.log("u LMU, phoneMatchingResults = ", phoneMatchingResults)
        const entityType = 1000460003 // 1000430001
        const staffId = phoneMatchingResults[0].staff_id
        const staffTypeId = phoneMatchingResults[0].staff_type_id
        // const offlineStaffTypeIds = [1000230001, 1000230004, 1000230005, 1000230003]
        const offlineStaffTypeIds = [1000230001, 1000230004, 1000230003]
        let offlineUser = offlineStaffTypeIds.includes(staffTypeId)
        if (offlineUser && phoneMatchingResults[0].value_reference_id && phoneMatchingResults[0].value_reference_id === 1000105001) {
          offlineUser = false
        }
        let compress_db_sync = false
        if (phoneMatchingResults[0].compress_db_sync && phoneMatchingResults[0].compress_db_sync === 1000105001) {
          compress_db_sync = true
        }
        
        let staff_feature_map = null
        if(phoneMatchingResults[0].staff_feature_map){
          staff_feature_map= phoneMatchingResults[0].staff_feature_map
        }

        const userDeviceRepo = dbConnections.main.repos.user_device
        const userDeviceEntity = dbConnections.main.entities.user_device
        const matchingUserDevices = await userDeviceRepo.find({
          where: {
            entity_type_id: entityType,
            entity_uuid: staffId,
            instance_id: data.instance_id,
            active: 1000100001
          }
        })


        if (matchingUserDevices.length > 0) {
          const userDeviceId = matchingUserDevices[0].user_device_id

          try {
            console.log("updating device information", data.device_information)
            await dbConnections.main.manager.transaction(async transaction => {
              transaction.getRepository("user_device").update(
                {
                  user_device_id: userDeviceId
                },
                {
                  device_information: data.device_information
                }
              )
            })
          } catch (error) {
            console.log("error in updating device information", error)
          }

          const userDeviceResult = {
            return_code: 1,
            message: "Re-using existing device id",
            user_device_id: userDeviceId,
            entity_type_id: 1000460003, //1000430001,
            offline_user: offlineUser,
            staff_type_id: staffTypeId,
            user_id: staffId,
            entity_uuid: staffId,
            staff_name_l10n: phoneMatchingResults[0].staff_name_l10n,
            staff_type: phoneMatchingResults[0].staff_type_l10n,
            staff_mobile_number: phoneMatchingResults[0].mobile_number,
            staff_document_information: phoneMatchingResults[0].document_information,
            compress_db_sync: compress_db_sync,
            staff_feature_map: staff_feature_map
          }
          console.log("s u u LMU 1, userDeviceResult = ", userDeviceResult)
          return userDeviceResult
        } else {
          const userDeviceCreationResult = await dbConnections.main.manager.transaction(async transactionalEntityManager => {
            const userDeviceData = {
              entity_type_id: entityType,
              entity_uuid: staffId,
              uid: data.uid,
              instance_id: data.instance_id,
              device_id: data.device_id,
              device_information: data.device_information
            }
            const userDeviceEntity = dbConnections.main.entities["user_device"]
            const preProcessedUserDeviceData = preProcessRecords(userDeviceEntity, userDeviceData, userDeviceEntity.additionalAttributes)
            const createdUserDeviceRecord = await transactionalEntityManager
              .createQueryBuilder()
              .insert()
              .into(userDeviceEntity)
              .values(preProcessedUserDeviceData)
              .execute()

            // const userDeviceRecord = await userDeviceRepo.save([userDeviceData])
            console.log("u LMU c 1, userDeviceRecord = ", createdUserDeviceRecord)
            const insertedUserDeviceId = createdUserDeviceRecord.identifiers[0].user_device_id
            const userDeviceResult = {
              return_code: 1,
              message: "created a new device id",
              user_device_id: insertedUserDeviceId,
              entity_type_id: 1000460003, //1000430001,
              staff_type_id: staffTypeId,
              offline_user: offlineUser,
              entity_uuid: staffId,
              staff_name_l10n: phoneMatchingResults[0].staff_name_l10n,
              staff_type: phoneMatchingResults[0].staff_type_l10n,
              staff_mobile_number: phoneMatchingResults[0].mobile_number,
              staff_document_information: phoneMatchingResults[0].document_information,
              compress_db_sync:compress_db_sync,
              staff_feature_map: staff_feature_map

            }
            console.log("s u u LMU 2, userDeviceResult = ", userDeviceResult)
            return userDeviceResult
          })
          return userDeviceCreationResult
        }
      } else {
        return {
          return_code: -1,
          message: "Mobile number does not exist in the system"
        }
      }
      // return { return_code: 0, data: {user_device_id: 1} };
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class FCMToken {
  // update based on user device id
  async create(data, params) {
    try {
      const dbConnections = getDBConnections()

      const userDeviceIdAsString = params.headers.userdeviceid || params.headers.userDeviceId
      const userDeviceId = parseInt(userDeviceIdAsString)

      const fcmToken = data.fcmToken

      const userDeviceTransactionResult = await dbConnections.main.manager.transaction(async transactionalEntityManager => {
        const userDeviceUpdateResult = await transactionalEntityManager
          .getRepository("user_device")
          .createQueryBuilder()
          .update()
          .set({ fcm_id: fcmToken })
          .where("user_device_id = :id", { id: userDeviceId })
          .execute()
      })

      return { return_code: 0 }
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class LoginFromMobile {
  async create(data, params) {
    try {
      const usersLogin = new UsersLogin()
      const loginDetails = await usersLogin.create(data, params)
      return loginDetails
    } catch (error) {
      return response(-101, 400, error.message, {})
    }
  }
}
class GetUserAppGroups {
  // do a check on phone number again
  // if instanceId + entity type + entity id match, update uid and other details. else insert
  // to identify entity type + entity id, search staff and customer database for phone number
  // return device id

  async create(data, params) {
    try {
      // console.log('login user, data = ', data)
      const mainDBConnection = dbConnections().main

      const phoneNumberAsString = data.firebaseUser.phoneNumber
      const emailAdString = data.firebaseUser.email
      const queryToCheckUserBasedOnAuthenticationData = `
        select s.staff_id, s.staff_type_id,
          s.mobile_number, s.staff_name_l10n, s.email_address
        from main.staff s
        where s.active = 1000100001
        ${phoneNumberAsString !== undefined && phoneNumberAsString !== null && phoneNumberAsString !== '' ? " and s.mobile_number = '" + phoneNumberAsString + "'" : ""}
        ${emailAdString !== undefined && emailAdString !== null && emailAdString !== '' ? " and s.email_address = '" + emailAdString + "'" : ""}
      `

      const matchingStaffResults = await mainDBConnection.manager.query(queryToCheckUserBasedOnAuthenticationData)

      if (matchingStaffResults.length === 1) {
        // identify entity type and entity
        console.log('u LMU, matchingStaffResults = ', matchingStaffResults)
        const staffId = matchingStaffResults[0]['staff_id']
        const returnValue = { return_code: 0 }
        const classificationData = await loadClassificationData('STAFF_CLASSIFICATION', staffId, ['staff_app_access_groups_1'])
        console.log('oc r GAD c 2, classificationData = ', classificationData)
        returnValue.app_access_groups = classificationData.staff_app_access_groups_1
        return returnValue
      } else {
        if (matchingStaffResults.length > 1) {
          return {
            return_code: -2,
            message: 'More than 1 entry for this user in the system',
          }
        } else {
          return {
            return_code: -1,
            message: 'Mobile number or email does not exist in the system',
          }
        }
      }
      // return { return_code: 0, data: {user_device_id: 1} };
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

module.exports = { ValidatePhone, LoginMobileUser, FCMToken, LoginFromMobile, GetUserAppGroups }
