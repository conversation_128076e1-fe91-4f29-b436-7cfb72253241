const { ValidatePhone, LoginMobileUser, FCMToken, GetUserAppGroups } = require('./user.class')
// const auth = require("../../middleware/auth");
const configureUser = (app) => {
  // app.use(auth) // not necessary at all for this
  app.use('/validate-phone', new ValidatePhone())
  app.use('/login-mobile-user', new LoginMobileUser())
  app.use('/set-fcm-token', new FCMToken())
  app.use('/get-user-app-groups', new GetUserAppGroups())
}
module.exports = { configureUser }
