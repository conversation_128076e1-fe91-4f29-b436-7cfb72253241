
const moment = require("moment");
const { postProcessRecords, dbConnections } = require("@krushal-it/ah-orm");
const { configurationJSON, assignL10NObjectOrObjectArrayToItSelf } = require("@krushal-it/common-core");
const { loadClassificationData, saveClassificationData } = require('@krushal-it/back-end-lib')
const { adjustStartDatesOfEntityStatusByCategoryAndEntities, reportQueryClassificationJoinConfiguration, generateSelectJoinClauseForClassification, generateWhereClauseForReportInnerQuery: generateWhereClause, generateLimitStringForReportInnerQuery: generateLimitString, generateSortStringForReportInnerQuery: generateSortString, identifyMandatoryColumnsForReportQuery} = require('../../utils/query/query.helper');
const { update } = require("lodash");
const lodashObject = require("lodash");

class ZohoCallback {
  async create /*or is it update*/(data, params) {
    try {
      const returnValue = {return_code: 0}
      console.log('s i ZH c 1, data = ', data, ', params = ', params)
      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async find(params) {
    try {
      const returnValue = {return_code: 0}
      console.log('s i ZH f 1, params = ', params)
      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async get(id, params) {
    try {
      const returnValue = {return_code: 0}
      console.log('s i ZH f 1, id = ', id, ', params = ', params)
      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

module.exports = { ZohoCallback
}