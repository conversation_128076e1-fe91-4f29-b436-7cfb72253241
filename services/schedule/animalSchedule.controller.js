const { dbConnections, preProcessRecords, postProcessRecords } = require("@krushal-it/ah-orm")
const { error } = require("console")
const { schedule } = require("node-cron")
const { configuration } = require("../../utils/constants/translator.constant")
const moment = require("moment")
const Ajv = require("ajv")

const convertArrayToCommaSeparatedConcatedString = stringArray => {
  let commaSeparatedConcatenatedString = ""
  for (const string of stringArray) {
    if (commaSeparatedConcatenatedString !== "") {
      commaSeparatedConcatenatedString = commaSeparatedConcatenatedString + ", "
    }
    commaSeparatedConcatenatedString = commaSeparatedConcatenatedString + "'" + string + "'"
  }
  return commaSeparatedConcatenatedString
}

const validateIncommingPayload = data => {
  const ajv = new Ajv()
  const mainSchema = {
    type: "object",
    properties: {
      activity: {
        type: "object",
        properties: {
          activity_id: { type: "integer" }
        },
        required: ["activity_id"]
      },
      schedule: {
        type: "object",
        properties: {
          displayString: { type: "string" },
          startDate: { type: "string" },
          startTime: { type: "string" },
          frequency: {
            type: "object",
            properties: {
              numberOfRepetitions: { type: "integer" },
              type: { type: "string" },
              weekDaysRepetition: { type: "array", items: { type: "integer" } }
            },
            required: ["numberOfRepetitions", "type", "weekDaysRepetition"]
          },
          endingCondition: {
            type: "object",
            properties: {
              type: { type: "string" },
              endTime: { type: "string" },
              endDate: { type: "string" },
              endingOccurrencesNumber: { type: "integer" }
            },
            required: ["type", "endTime", "endDate"]
          },
          isAllDay: { type: "boolean" }
        },
        required: ["startDate", "frequency", "endingCondition", "isAllDay"]
      },
      appliesTo: {
        type: "object",
        properties: {
          customer: {
            type: "object",
            properties: {
              customer_id: { type: "array", items: { type: "string" } }
            },
            required: ["customer_id"]
          },
          geography: {
            type: "object",
            oneOf: [
              {
                required: ["taluka_ids"]
              },
              {
                required: ["state_ids"]
              },
              {
                required: ["village_ids"]
              },
              {
                required: ["district_ids"]
              }
            ]
          }
        },
        oneOf: [
          {
            required: ["customer"]
          },
          {
            required: ["geography"]
          }
        ]
      },
      staff_type_list: {
        type: "array",
        items: { type: "integer" }
      }
    },
    required: ["activity", "schedule", "appliesTo"]
  }

  const validate = ajv.compile(mainSchema)
  let main = validate(data)
  return main
}

const getVillageBySdtvView = async (sdtv_ids, sdt_type, transactionEntityManager) => {
  let village_id = []
  // get animal ids by famer id
  let getanimalFromCustomer = await transactionEntityManager.query(`
    SELECT rv.village_id from main.ref_sdtv_view as sdtvview left join main.ref_village  as rv on rv.village_id=sdtvview.village_id and rv.active = 1000100001
    where sdtvview.${sdt_type} in (${convertArrayToCommaSeparatedConcatedString(sdtv_ids)}) 
      `)
  village_id = getanimalFromCustomer.map(village => village.village_id)
  return village_id
}

const getVillageByGeo = async (geography, transactionEntityManager) => {
  let { taluka_ids, state_ids, district_ids } = geography
  if (taluka_ids) {
    return await getVillageBySdtvView(taluka_ids, "taluk_id", transactionEntityManager)
  }
  if (district_ids) {
    return await getVillageBySdtvView(district_ids, "district_id", transactionEntityManager)
  }
  if (state_ids) {
    return await getVillageBySdtvView(state_ids, "state_id", transactionEntityManager)
  }
}

const getAnimalIdBycustomerId = async (customer_ids, transactionEntityManager) => {
  let animal_ids = []
  if (!customer_ids.length) throw new Error("No Customer  found for this filter")

  // get animal ids by famer id
  let getanimalFromCustomer = await transactionEntityManager.query(`
    SELECT a.animal_id from main.customer as c left join  main.entity_relationship as er on er.entity_1_entity_uuid= c.customer_id and er.active = 1000100001 and entity_relationship_type_id = 1000210004 
    left join main.animal as a on a.animal_id = er.entity_2_entity_uuid and a.active = 1000100001
    where c.customer_id in (${convertArrayToCommaSeparatedConcatedString(customer_ids)}) 
    `)
  animal_ids = getanimalFromCustomer.map(animal => animal.animal_id)
  animal_ids = animal_ids.filter(animal => animal != null)

  return animal_ids
}

const getCustomerIdByVillageId = async (village_ids, transactionEntityManager) => {
  let customer_ids = []
  if (!village_ids.length) throw new Error("No village Id found for this filter")
  let getcustomerFromVillage = await transactionEntityManager.query(`
    SELECT c.customer_id from main.customer_classification as cuc left join main.customer as c on c.customer_id = cuc.customer_id AND
    c.active = 1000100001 where cuc.classifier_id=2000000055 and value_reference_id in (${convertArrayToCommaSeparatedConcatedString(village_ids)})
    `)
  customer_ids = getcustomerFromVillage.map(customer => customer.customer_id)
  customer_ids = customer_ids.filter(customer => customer != null)
  return customer_ids
}

const getCustomerIds = async (geography, transactionEntityManager) => {
  if (geography.village_ids) {
    return await getCustomerIdByVillageId(geography.village_ids, transactionEntityManager)
  }
  let village_id_array = await getVillageByGeo(geography, transactionEntityManager)
  return await getCustomerIdByVillageId(village_id_array, transactionEntityManager)
}

const getCustomerIdArray = async (appliesTo, transactionEntityManager) => {
  let { geography, customer } = appliesTo
  if (customer) {
    return customer.customer_id
  }
  return await getCustomerIds(geography, transactionEntityManager)
}

const getAnimalIdsArray = async (appliesTo, transactionEntityManager) => {
  // let { geography , customer_ids } = appliesTo

  let customer_ids_array = await getCustomerIdArray(appliesTo, transactionEntityManager)
  return await getAnimalIdBycustomerId(customer_ids_array, transactionEntityManager)
}

const getActivityDateArray = schedule => {
  const { startDate, frequency, endingCondition } = schedule
  // const {numberOfRepetitions } = frequency;

  const formattedStartDate = moment(startDate, "DD/MM/YYYY").toISOString()
  const formattedStart = moment(startDate, "DD/MM/YYYY")
  const formattedEnd = moment(endingCondition?.endDate, "DD/MM/YYYY")
  const diffInDays = formattedEnd.diff(formattedStart, "days")

  let dateObj = new Date(formattedStartDate)
  let listItems = []
  let key = -1
  let week = 0
  let noOfDays = diffInDays + 1 || endingCondition?.endingOccurrencesNumber || 31
  for (let index = 0; index < noOfDays; index++) {
    const formatted = moment(dateObj)
    const currentWeek = formatted.week()

    if (week !== currentWeek) {
      week = currentWeek
      key++
    }
    listItems.push({
      date: formatted.format("DD MMM YYYY"),
      day: formatted.day(),
      week: formatted.week(),
      key: key,
      formatted: { formattedDate: formatted.format("YYYY-MM-DD"), gmtDate: formatted }
    })
    dateObj.setDate(dateObj.getDate() + 1)
  }
  let matchedDateWithFrequency = []

  if (listItems?.length > 0) {
    listItems?.forEach((item, index) => {
      if (frequency?.type === "daily" && index % frequency?.numberOfRepetitions === 0) {
        // For "daily" events, include every numberOfRepetitions days
        matchedDateWithFrequency.push(item.formatted)
      } else if (frequency?.type === "weekly" && item.key % frequency?.numberOfRepetitions === 0 && frequency?.weekDaysRepetition?.includes(item?.day)) {
        // For "weekly" events, include every numberOfRepetitions weeks on specified weekdays
        matchedDateWithFrequency.push(item.formatted)
      } else if (frequency?.type === "monthly" && item?.date === moment(startDate, "DD/MM/YYYY").format("DD MMM YYYY")) {
        // Handle "monthly" events
        matchedDateWithFrequency.push(item.formatted)
      } else if (frequency?.type === "none") {
        // Handle "none" events
        // matchedDateWithFrequency.push(item.formatted);
      }
    })
  }
  return matchedDateWithFrequency
}

const createTask = async (activityDates , animalIds , activity_id , params , transactionEntityManager) => {
    try {
        let entryToTable = []
        const { user_id } = params.headers.token
        const currentDate = new Date();
        const formattedDateTimeInGMT = currentDate.toISOString();
        let count = 0
        for(let activityDate of activityDates) {
            for(let animalId of animalIds) {
                let constructTask = {
                    entity_uuid: animalId,
                    entity_type_id: 1000460002,
                    activity_id,
                    activity_date: activityDate.formattedDate,
                    calendar_activity_status: 1000300001,
                    visit_schedule_time: activityDate.gmtDate,
                    visit_creation_time: formattedDateTimeInGMT,
                    activity_created_by: user_id
                }
                entryToTable.push(constructTask)
                count += 1
            }
        }
        await transactionEntityManager.save("care_calendar",entryToTable)
        return { count }
    } catch (error) {
        throw error
    }
}

const scheduleAnimalTask = async (data, params) => {
  try {
    // validate incoming payload
    let payloadValid = validateIncommingPayload(data)
    // return payloadValid
    if (!payloadValid) throw new Error("payload is invalid")
    let activityDates = getActivityDateArray(data.schedule)
    // return activityDates

    return await dbConnections().main.manager.transaction(async transactionEntityManager => {
      let animalIds = await getAnimalIdsArray(data.appliesTo, transactionEntityManager)
      let saveToDb = await createTask(activityDates, animalIds, data.activity.activity_id, params, transactionEntityManager)
      return saveToDb
    })
    // get schedule Activity dates
    // activity date
  } catch (error) {
    return error
  }
}

async function doBatchInsert(transactionalEntityManager, data, batchSize) {
  for (let i = 0; i < data.length; i += batchSize) {
    const batch = data.slice(i, i + batchSize)
    await transactionalEntityManager.insert("care_calendar", batch)
  }
}

module.exports = { scheduleAnimalTask }
