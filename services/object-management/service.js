const { GeneralError } = require("@feathersjs/errors")
const { getFile, handleFileUpload } = require("./controller")
const { getUniqueObjectKeyWithExt, getNanoId } = require("@krushal-it/back-end-lib")
module.exports = {
  download: {
    async find(params) {
      try {
        const res = await getFile(params)
        return res
      } catch (error) {
        return new GeneralError(error).toJSON()
      }
    }
  },

  upload: {
    async create(data) {
      try {
        const res = await handleFileUpload(data)
        return res
      } catch (error) {
        new GeneralError(error).toJSON()
      }
    }
  },

  nanoid: {
    async find(params) {
      const { extension } = params.query
      try {
        if (extension) {
          return getUniqueObjectKeyWithExt(extension)
        }
        return getNanoId()
      } catch (error) {
        new GeneralError(error).toJSON()
      }
    }
  }
}
