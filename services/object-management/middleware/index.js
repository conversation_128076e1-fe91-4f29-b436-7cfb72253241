const multer = require("multer")
const {
  object_management: {
    file_object_key: { NAME, MAX_COUNT },
    LIMIT_SIZE
  }
} = require("@krushal-it/back-end-lib/ENUMS")

const storage = require("./multer/storageConfig")
const filter = require("./multer/filter")
const fieldsConfig = [{ name: NAME, maxCount: MAX_COUNT }]
const limitConfig = { fileSize: LIMIT_SIZE }

module.exports = {
  multiPartFormDataHandler: multer({ storage: storage, fileFilter: filter, limits: limitConfig }).fields(fieldsConfig),
  copyFileDataToBody: function copyFileDataToBody(req, res, next) {
    req.body.files = req.files
    return next()
  },
  pipeStreamForGet: function pipeStreamForGet(req, res, next) {
    if (res.data.pipe) {
      res.data.pipe(res)
    } else {
      next()
    }
  }
}
