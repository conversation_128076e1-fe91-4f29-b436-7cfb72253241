const fs = require("fs")
const mkdirp = require("mkdirp")
const os = require("os")
const _path = require("path")

class Storage {
  constructor(opts) {
    this.getDestination =
      opts.destination ||
      function getDestination(req, file, cb) {
        cb(null, "/dev/null")
      }
  }
  _handleFile(req, file, cb) {
    this.getDestination(req, file, function(err, path) {
      if (err) return cb(err)
      const tempDir = _path.join(os.tmpdir(), path)
      mkdirp.sync(tempDir)

      const tempFilePath = _path.join(tempDir, file.originalname)

      const outStream = fs.createWriteStream(tempFilePath)

      file.stream.pipe(outStream)
      outStream.on("error", cb)
      outStream.on("finish", function() {
        cb(null, {
          path: tempFilePath,
          size: outStream.bytesWritten
        })
      })
    })
  }
  _removeFile(req, file, cb) {
    const path = file.path

    delete file.destination
    delete file.filename
    delete file.path

    fs.unlink(path, cb)
  }
}

module.exports = function diskStorage(opts) {
  return new Storage(opts)
}
