const {
  object_management: { allowed_file_types },
  aws_s3_entities
} = require("@krushal-it/back-end-lib/ENUMS")

function fileFilter(req, file, cb) {
  filterArray.forEach(filter => filter(req, file, cb))
  return cb(null, true)
}

const filterArray = [fileTypeFilter]

function fileTypeFilter(req, file, cb) {
  const allowedFile = new Set(allowed_file_types)
  const fileType = file.mimetype.split("/")[1]

  if (allowedFile.has(fileType) === false) {
    return cb(new Error("Invalid file-type!"))
  }
}

module.exports = fileFilter
