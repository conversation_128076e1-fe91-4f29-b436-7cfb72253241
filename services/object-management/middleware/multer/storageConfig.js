const multer = require("multer")
const { configurationJSO<PERSON>, createHeaders } = require("@krushal-it/common-core")
const { object_management } = require("@krushal-it/back-end-lib/ENUMS")
const diskStorage = require("./StorageEngine/diskStorage")

const BUCKET_ROOT = configurationJSON().AWS.AWS_BUCKET_ROOT_LOCATION

const {
  file_object_key: { NAME },
  storage
} = object_management

const diskConfig = {
  destination: function _destination(req, file, cb) {
    const location = BUCKET_ROOT + "/" + `${req.body.entity}`
    cb(null, location)
  },

  filename: function _filename(req, file, cb) {
    cb(null, file.originalname)
  }
}

const storeType = {
  disk: diskStorage(diskConfig),
  memory: multer.memoryStorage()
}

const MULTERCONFIG = configurationJSON().MULTERCONFIG

if (MULTERCONFIG && MULTERCONFIG.STORAGE === storage.MEMORY) {
  module.exports = storeType.memory
} else {
  module.exports = storeType.disk
}
