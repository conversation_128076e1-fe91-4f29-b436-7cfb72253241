const fs = require("fs")
const { configurationJSON } = require("@krushal-it/common-core")
const { uploadFile, downloadFile } = require("../../utils/common/aws/s3.utilsV2")
const {
  object_management: { storage }
} = require("@krushal-it/back-end-lib/ENUMS")
const { getUniqueObjectKeyWithExt, verifyEntity } = require("@krushal-it/back-end-lib")

const BUCKET_NAME = configurationJSON().AWS.AWS_BUCKET_NAME
const BUCKET_ROOT = configurationJSON().AWS.AWS_BUCKET_ROOT_LOCATION
const s3bucket = entity => BUCKET_NAME + `/${BUCKET_ROOT}/${entity}`

module.exports = {
  getFile: async function getFile(params) {
    try {
      const { key } = params.query
      if (!key) {
        throw new Error("Missing key in parameters!")
      }
      return await downloadFile({ key, bucketName: BUCKET_NAME })
    } catch (error) {
      throw new Error(error.message)
    }
  },
  handleFileUpload: async function handleFileUpload(data) {
    let uploadResult = null
    try {
      const { files, entity } = data
      const verifiedEntity = verifyEntity(entity)

      uploadResult = await _performMultiFilesUpload(files, verifiedEntity)
      return uploadResult
    } catch (error) {
      throw error
    }
  }
}

async function _performMultiFilesUpload(files, entity) {
  const successful = {}
  const unsuccessful = {}
  const toMemory = configurationJSON().MULTERCONFIG.STORAGE === storage.MEMORY

  for (const key in files) {
    files_in_feild_name = files[key]

    successful[key] = []
    unsuccessful[key] = []

    for (let index = 0; index < files_in_feild_name.length; index++) {
      const file = files_in_feild_name[index]
      try {
        const [contentType, extention] = file.mimetype.split("/")

        if (!file.originalname) file.originalname = getUniqueObjectKeyWithExt(extention)
        const payload = {
          buffer: toMemory ? file.buffer : fs.createReadStream(file.path),
          fileName: file.originalname,
          entity,
          contentType: contentType,
          bucket: s3bucket(entity)
        }

        const file_acc_key = await uploadFile(payload)
        successful[key].push({ filename: file.originalname, key: file_acc_key })
      } catch (error) {
        unsuccessful[key] = push({ filename: file.originalname, error: error.message })
      }
    }
  }
  return { successful, unsuccessful }
}
