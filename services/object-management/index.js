const { pipeStreamForGet, multiPartFormDataHandler, copyFileDataToBody } = require("./middleware/index")
const service = require("./service")

function objectManagement(app) {
  app.use("v3/object-management/file/upload", multiPartFormDataHandler, copyFileDataToBody, service.upload)
  app.use("v3/object-management/file/download", service.download, pipeStreamForGet)
}

module.exports = objectManagement
