const lodashObject = require("lodash");

const { dbConnections, preProcessRecords, postProcessRecords, getDBConnections } = require('@krushal-it/ah-orm')
const { configurationJSON, KrushalError } = require('@krushal-it/common-core')
const { AnimalListLib, AnimalLib } = require('@krushal-it/back-end-lib')
const { loadClassificationData, saveClassificationData } = require('@krushal-it/back-end-lib')
const { adjustStartDatesOfEntityStatusByCategoryAndEntities, reportQueryClassificationJoinConfiguration, generateSelectJoinClauseForClassification, generateWhereClauseForReportInnerQuery: generateWhereClause, generateLimitStringForReportInnerQuery: generateLimitString, generateSortStringForReportInnerQuery: generateSortString, identifyMandatoryColumnsForReportQuery, extractFieldsNeedingFilterData, extractFieldsNeedingPageFilterData } = require('../../utils/query/query.helper');


class AnimalList {
  async create(data, params) {
    try {
      const animalListLib = new AnimalListLib()
      return animalListLib.create(data, params)
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class Animal {
  async find(params) {
    return { return_code: -101, message: 'in find' }
  }

  async create /*or is it update*/(data, params) {
    try {
      const animalLib = new AnimalLib()
      return animalLib.create(data, params)
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }

  async get(id, params) {
    try {
      const animalLib = new AnimalLib()
      return animalLib.get(id, params)
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }

  async patch /* or is it update */(id, data, params) {
    try {
      const animalLib = new AnimalLib()
      return animalLib.patch(id, data, params)
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }

  async update /* or is it update */(id, data, params) {
    //PUT
    try {
      console.log('customer update')
      return { return_code: -101, message: 'in update with id as ' + id }
      // update details
      // id, name, mobile number, DOB, aadhaar number, gender (reference to master)
      // return { return_code: 0, data: {user_device_id: 1} };
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

const snoozedStatusRelatedColumnColumnConfiguration = [
  `
    case when sa.entity_relationship_id is not null or sf.entity_relationship_id is not null then 'Snoozed' else 'Not Snoozed' end as snooze_status,
  `,
  `
    left join main.entity_relationship sa on sa.entity_relationship_type_id = 1000210005 and sa.entity_1_entity_uuid = a.animal_id
      and sa.active = 1000100001 and sa.start_date < now() and sa.end_date > now()
    left join main.entity_relationship sf on sf.entity_relationship_type_id = 1000210006 and sf.entity_1_entity_uuid = c.customer_id
      and sf.active = 1000100001 and sf.start_date < now() and sf.end_date > now()
  `
]

const customerRelatedColumnsQueryConfiguration = [
  `
    c.customer_id, coalesce(c.customer_name_l10n->>'en', c.customer_name_l10n->>'ul') customer_name, c.mobile_number,
  `,
  `
    inner join main.entity_relationship er on er.active = 1000100001 and er.entity_relationship_type_id = 1000210004
      and er.entity_2_entity_uuid = a.animal_id
    inner join main.customer c on c.active = 1000100001 and c.customer_id = er.entity_1_entity_uuid /* and c.customer_type_id in (1000220001) */
  `
]

const geographyRelatedColumnsQueryConfiguration = [
  `
    rsv.village_id, coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') village_name,
    rsv.taluk_id, coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') taluk_name,
  `,
  `
    inner join main.customer_classification v_cc on v_cc.active = 1000100001 and c.customer_id = v_cc.customer_id
      and v_cc.classifier_id = 2000000055
    inner join main.ref_sdtv_view rsv on rsv.village_id = v_cc.value_reference_id
  `
]

const bdmRelatedColumnsQueryConfiguration = [
  `
    s_g.bdm_uuid, s_g.bdm, s_g.bdm_mobile,
  `,
  `
    left join (
      select s.staff_id as bdm_uuid, coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') bdm,
        s.mobile_number bdm_mobile, eg.geography_id
      from main.entity_geography eg
      inner join main.staff s on s.staff_id = eg.entity_uuid and s.active = 1000100001
      where eg.entity_type_id in (1000230001, 1000230005) and eg.active = 1000100001
        and eg.geography_type_id = 1000320004
    ) s_g on s_g.geography_id = rsv.village_id
  `
]

const paravetRelatedColumnsQueryConfiguration = [
  `
    fpv.paravet_uuid, fpv.paravet, fpv.paravet_mobile,
  `,
  `
    left join (
      select gcc.customer_id, pg.paravet, pg.paravet_mobile, pg.paravet_uuid
      from main.customer_classification gcc
      left join (
        select coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as paravet, s.mobile_number as paravet_mobile, pg.geography_id, s.staff_id as paravet_uuid
        from main.staff s, (
          select eg.geography_id, (min(eg.entity_uuid::text))::uuid as staff_id
          from main.entity_geography eg
          where eg.active = 1000100001 and eg.geography_type_id = 1000320004
          and eg.entity_type_id in (1000230004)
          group by eg.geography_id
        ) pg
        where s.staff_id = pg.staff_id and s.active = 1000100001
      ) pg on pg.geography_id = gcc.value_reference_id
      where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
    ) fpv on fpv.customer_id = c.customer_id
  `
]

const locationRecordedStatusRelatedColumnsConfiguration = [
  `
    case when dfl_cc.value_json is not null then 'Location Recorded' else 'Location Not Recorded' end as farmer_location_recorded_status, dfl_cc.updated_at as location_updated_timestamp,
  `,
  `
    left join main.customer_classification dfl_cc on dfl_cc.active = 1000100001 and dfl_cc.classifier_id = 2000000004 and dfl_cc.customer_id = c.customer_id
  `  
]

const cowScoreAttributeCountColumnQueryConfiguration = [
  `
    coalesce(a_a_c.cow_score_attribute_count, 0) cow_score_attribute_count,
  `,
  `
    left join (
      select count(animal_classification_id) as cow_score_attribute_count, animal_id
      from main.animal_classification ac
      where ac.active = 1000100001  and ac.classifier_id in (2000000197, 2000000198, 2000000199, 2000000200, 2000000201, 2000000202, 2000000203, 2000000204, 2000000205, 2000000210, 2000000211, 2000000212, 2000000213, 2000000214, 2000000215)
      group by ac.animal_id
    ) a_a_c on a_a_c.animal_id = a.animal_id
  `
]

const cowScoreImageCountColumnQueryConfiguration = [
  `
    coalesce(a_d_c.cow_score_image_count, 0) cow_score_image_count,
  `,
  `
    left join (
      select count(document_id) as cow_score_image_count, animal_id
      from (
        select ac.animal_id, d.*
        from main.document d
        inner join main.animal_classification ac on ac.animal_classification_id = d.entity_1_entity_uuid
        where d.entity_1_type_id = 1000220004 and d.active = 1000100001 and d.document_type_id in (1000260016)
        union
        select d.entity_1_entity_uuid as animal_id, d.*
        from main.document d
        where d.active = 1000100001 and (
          d.entity_1_type_id in (1000220002, 1000220003) and d.document_type_id in (1000260002, 1000260016, 1000260001, 1000260012)
        )
      ) a_d_c
      group by a_d_c.animal_id
    ) a_d_c on a_d_c.animal_id = a.animal_id
  `
]

const pregnancyMonthsRelatedColumnColumnConfiguration = [
  `
    case when (pm_ac.value_double > 0.5 and (EXTRACT(YEAR FROM age(now(), pm_ac.updated_at)) * 12 + EXTRACT(MONTH FROM age(now(), pm_ac.updated_at)) + pm_ac.value_double) < 9) then (EXTRACT(YEAR FROM age(now(), pm_ac.updated_at)) * 12 + EXTRACT(MONTH FROM age(now(), pm_ac.updated_at)) + pm_ac.value_double) else 0 end as number_of_months_pregnant,
  `,
  `
    left join main.animal_classification pm_ac on pm_ac.active = 1000100001 and pm_ac.classifier_id = 2000000067 and pm_ac.animal_id = a.animal_id
  `
]

const sellingAnimalStatusLeftJoinRelatedColumnColumnConfiguration = [
  `
    coalesce(sas_rr.reference_id, 1000490000) as selling_animal_current_status_id,
    coalesce(coalesce(sas_rr.reference_name_l10n->>'en', sas_rr.reference_name_l10n->>'ul'), 'Not Spoken To') as selling_animal_current_status,
  `,
  `
    left join main.entity_status es on es.status_category_id = 10004900 and es.entity_1_type_id = 1000220002
      and es.entity_1_entity_uuid = a.animal_id and es.start_date < now() and es.end_date > now()
    left join main.ref_reference sas_rr on es.status_id = sas_rr.reference_id
  `
]

const sellingAnimalStatusInnerJoinRelatedColumnColumnConfiguration = [
  `
    coalesce(sas_rr.reference_id, 1000490000) as selling_animal_current_status_id,
    coalesce(coalesce(sas_rr.reference_name_l10n->>'en', sas_rr.reference_name_l10n->>'ul'), 'Not Spoken To') as selling_animal_current_status,
  `,
  `
    inner join main.entity_status es on es.status_category_id = 10004900 and es.entity_1_type_id = 1000220002
      and es.entity_1_entity_uuid = a.animal_id and es.start_date < now() and es.end_date > now()
      and es.status_id not in (1000490001)
    left join main.ref_reference sas_rr on es.status_id = sas_rr.reference_id
  `
]

const sellingPriceColumnConfiguration = [
  `
    coalesce((spr_ac.value_json->>'sellingPriceUpperRange')::numeric::int, 0) maximum_selling_price,
    coalesce((spr_ac.value_json->>'sellingPriceLowerRange')::numeric::int, 0) minimum_selling_price,
  `,
  `
    left join main.animal_classification spr_ac on spr_ac.active = 1000100001
      and spr_ac.classifier_id = 2000000226 and spr_ac.animal_id = a.animal_id
      and spr_ac.value_json is not null
  `
]

const createAnimalInnerQuery = ({includeMonthsPregnantColumnInQuery, includeCowAttributeCountInQuery,
  includeCowImageCountInQuery, includeLocationRecordedColumnsInQuery, includeSnoozeStatusColumnInQuery,
  includeParavetRelatedColumnsInQuery, includeBDMRelatedColumnsInQuery, includeGeographyRelatedColumnsInQuery,
  includeCustomerRelatedColumnsInQuery, includeSellingPricesColumnsInQuery,
  restrictedIds, searchWithinRestrictedIds,
  ignoreInitialContactStatusAnimalsInQuery, includeAnimalStatusColumnsInQuery,
  selectClauseForClassification, joinClauseForClassification,
  noLocationRecordedDefault, noLocationRecordedInnerQuery,
  customerId}) => {

  const innerQuery = `
    select a.animal_id,
      ${includeCustomerRelatedColumnsInQuery ? customerRelatedColumnsQueryConfiguration[0] : ''}
      ${includeGeographyRelatedColumnsInQuery ? geographyRelatedColumnsQueryConfiguration[0] : ''}
      ${includeBDMRelatedColumnsInQuery ? bdmRelatedColumnsQueryConfiguration[0] : ''}
      ${includeParavetRelatedColumnsInQuery ? paravetRelatedColumnsQueryConfiguration[0] : ''}
      ${includeLocationRecordedColumnsInQuery ? locationRecordedStatusRelatedColumnsConfiguration[0] : ''}
      ${includeSnoozeStatusColumnInQuery ? snoozedStatusRelatedColumnColumnConfiguration[0] : ''}
      ${includeAnimalStatusColumnsInQuery ? (ignoreInitialContactStatusAnimalsInQuery ? sellingAnimalStatusInnerJoinRelatedColumnColumnConfiguration[0] : sellingAnimalStatusLeftJoinRelatedColumnColumnConfiguration[0]) : ''}
      ${includeCowAttributeCountInQuery ? cowScoreAttributeCountColumnQueryConfiguration[0] : ''}
      ${includeCowImageCountInQuery ? cowScoreImageCountColumnQueryConfiguration[0] : ''}
      ${includeMonthsPregnantColumnInQuery ? pregnancyMonthsRelatedColumnColumnConfiguration[0] : ''}
      ${includeSellingPricesColumnsInQuery ? sellingPriceColumnConfiguration[0] : ''}
      ${(selectClauseForClassification !== undefined && selectClauseForClassification !== '') ? selectClauseForClassification + ', ': ''}
      a.active as animal_active_id,
      case when a.active = 1000100001 then 'Active' else 'Not Active' end as animal_active
    from main.animal a
    ${includeCustomerRelatedColumnsInQuery ? customerRelatedColumnsQueryConfiguration[1] + ((customerId !== undefined) ? ` and c.customer_id = '${customerId}'` : '') : ''}
    ${includeGeographyRelatedColumnsInQuery ? geographyRelatedColumnsQueryConfiguration[1] : ''}
    ${includeBDMRelatedColumnsInQuery ? bdmRelatedColumnsQueryConfiguration[1] : ''}
    ${includeParavetRelatedColumnsInQuery ? paravetRelatedColumnsQueryConfiguration[1] : ''}
    ${includeLocationRecordedColumnsInQuery ? locationRecordedStatusRelatedColumnsConfiguration[1] : ''}
    ${includeSnoozeStatusColumnInQuery ? snoozedStatusRelatedColumnColumnConfiguration[1] : ''}
    ${includeAnimalStatusColumnsInQuery ? (ignoreInitialContactStatusAnimalsInQuery ? sellingAnimalStatusInnerJoinRelatedColumnColumnConfiguration[1] : sellingAnimalStatusLeftJoinRelatedColumnColumnConfiguration[1]) : ''}
    ${includeCowAttributeCountInQuery ? cowScoreAttributeCountColumnQueryConfiguration[1] : ''}
    ${includeCowImageCountInQuery ? cowScoreImageCountColumnQueryConfiguration[1] : ''}
    ${includeMonthsPregnantColumnInQuery ? pregnancyMonthsRelatedColumnColumnConfiguration[1] : ''}
    ${includeSellingPricesColumnsInQuery ? sellingPriceColumnConfiguration[1] : ''}
    ${joinClauseForClassification}
    where a.active = 1000100001 
    ${restrictedIds ? searchWithinRestrictedIds : ''}
    ${noLocationRecordedDefault ? noLocationRecordedInnerQuery : ''}
  `
  return innerQuery
}

class AnimalListV3 {
  async create (data, params) {
    try {
      const returnValue = { return_code: 0 }
      const mainDBConnection = dbConnections().main

      let jsonColumns = []
      let doubleColumns = []

      const customerId = (params && params.query && params.query.cId) ? params.query.cId : undefined

      const limitString = generateLimitString(data)
      const sortString = generateSortString(data) 
      const whereClause = generateWhereClause(data) 
      let searchWithinRestrictedIds = ``

      const excludeBDM = (data.excludeBDM === true) ? true : false
      const ignoreInitialContactStatusAnimals = (data.animal_selling_intent_already_expressed === true) ? true : false

      // identify all columns needed
      const selectedColumns = data.selectedColumns
      // identify mandatory columns needed
      const mandatoryColumnsForSearch = identifyMandatoryColumnsForReportQuery(data.filters, data.sorting, data.globalSearch, data.searchConfiguration)

      let selectClauseForClassification
      let joinClauseForClassification
      let returnedJSONColumns
      let returnedDoubleColumns
      [selectClauseForClassification, joinClauseForClassification, returnedJSONColumns, returnedDoubleColumns] = generateSelectJoinClauseForClassification(reportQueryClassificationJoinConfiguration, 'ANIMAL', undefined, mandatoryColumnsForSearch)
      jsonColumns = [...jsonColumns, ...returnedJSONColumns]
      doubleColumns = [...doubleColumns, ...returnedDoubleColumns]

      let includeSellingPricesColumnsInQuery = false
      let includeMonthsPregnantColumnInQuery = false
      let includeCowAttributeCountInQuery = false
      let includeCowImageCountInQuery = false
      let includeLocationRecordedColumnsInQuery = false
      let includeAnimalStatusColumnsInQuery = false
      let includeSnoozeStatusColumnInQuery = false
      let includeParavetRelatedColumnsInQuery = false
      let includeBDMRelatedColumnsInQuery = false
      let includeGeographyRelatedColumnsInQuery = false
      let includeCustomerRelatedColumnsInQuery = false

      let restrictedIds = false

      if (mandatoryColumnsForSearch.includes('minimum_selling_price') || mandatoryColumnsForSearch.includes('maximum_selling_price')) {
        includeSellingPricesColumnsInQuery = true
      }
      if (mandatoryColumnsForSearch.includes('number_of_months_pregnant')) {
        includeMonthsPregnantColumnInQuery = true
      }
      if (mandatoryColumnsForSearch.includes('cow_score_attribute_count')) {
        includeCowAttributeCountInQuery = true
      }
      if (mandatoryColumnsForSearch.includes('cow_score_image_count')) {
        includeCowImageCountInQuery = true
      }
      if (mandatoryColumnsForSearch.includes('selling_animal_current_status') || mandatoryColumnsForSearch.includes('selling_animal_current_status_id')) {
        includeAnimalStatusColumnsInQuery = true
      }
      if ((mandatoryColumnsForSearch.includes('bdm') || mandatoryColumnsForSearch.includes('bdm_uuid')) && !excludeBDM) {
        includeBDMRelatedColumnsInQuery = true
      }
      if (mandatoryColumnsForSearch.includes('paravet') || mandatoryColumnsForSearch.includes('paravet_uuid')) {
        includeParavetRelatedColumnsInQuery = true
      }
      if (mandatoryColumnsForSearch.includes('village') || mandatoryColumnsForSearch.includes('taluk')
          || mandatoryColumnsForSearch.includes('village_name') || mandatoryColumnsForSearch.includes('taluk_name')
          || includeBDMRelatedColumnsInQuery || includeParavetRelatedColumnsInQuery) {
        includeGeographyRelatedColumnsInQuery = true
      }
      if (mandatoryColumnsForSearch.includes('farmer_location_recorded_status') || mandatoryColumnsForSearch.includes('location_updated_timestamp')) {
        includeLocationRecordedColumnsInQuery = true
      }
      if (mandatoryColumnsForSearch.includes('snooze_status')) {
        includeSnoozeStatusColumnInQuery = true
      }
      if (mandatoryColumnsForSearch.includes('customer_id')
          || mandatoryColumnsForSearch.includes('customer_name')
          || mandatoryColumnsForSearch.includes('mobile_number') || includeGeographyRelatedColumnsInQuery || includeLocationRecordedColumnsInQuery
          || includeSnoozeStatusColumnInQuery
          || customerId !== undefined) {
        includeCustomerRelatedColumnsInQuery = true
      }

      let includeWhereClauseInDetailedColumnsQuery = false

      let innerQuery = createAnimalInnerQuery({includeMonthsPregnantColumnInQuery, includeCowAttributeCountInQuery,
        includeCowImageCountInQuery, includeLocationRecordedColumnsInQuery, includeSnoozeStatusColumnInQuery,
        includeParavetRelatedColumnsInQuery, includeBDMRelatedColumnsInQuery, includeGeographyRelatedColumnsInQuery,
        includeCustomerRelatedColumnsInQuery, includeSellingPricesColumnsInQuery,
        restrictedIds, searchWithinRestrictedIds,
        ignoreInitialContactStatusAnimals, includeAnimalStatusColumnsInQuery, 
        selectClauseForClassification, joinClauseForClassification,
        customerId})

      const countQuery = `
        select count(*) as count from (
          ${innerQuery}
        ) outerTable
        ${whereClause}
      `
      const reportResultCount = await mainDBConnection.manager.query(countQuery)
      const count = parseInt(reportResultCount[0].count)

      if (count === 0) {
        includeWhereClauseInDetailedColumnsQuery = true
      }

      let query = `
          select * 
          from (
            ${innerQuery}
          ) outerTable
          ${whereClause}
          ${sortString}
          ${limitString}
        `

      const reportResultUnprocessed = await mainDBConnection.manager.query(query)
      const reportResult = postProcessRecords(undefined, reportResultUnprocessed, {})

      const primaryKeyToRowMap = {}
      for (const reportRow of reportResult) {
        primaryKeyToRowMap[reportRow['animal_id']] = reportRow
      }
      const primaryKeysToBeQueried = Object.keys(primaryKeyToRowMap)

      let inPrimaryKeysIdString = ""
      for (const primaryKey of primaryKeysToBeQueried) {
        if (inPrimaryKeysIdString !== "") {
          inPrimaryKeysIdString = inPrimaryKeysIdString + ", "
        }
        inPrimaryKeysIdString = inPrimaryKeysIdString + "'" + primaryKey + "'"
      }
      searchWithinRestrictedIds = (inPrimaryKeysIdString !== '') ? ` and a.animal_id in (${inPrimaryKeysIdString})` : ''

      const filtersFieldToReturnKeyMap = extractFieldsNeedingFilterData(data.searchConfiguration, data.selectedColumns)
      const fieldsNeedingFilterData = Object.keys(filtersFieldToReturnKeyMap)

      if (fieldsNeedingFilterData.includes('village') || fieldsNeedingFilterData.includes('village_name')) {
        includeGeographyRelatedColumnsInQuery = true
      }
      if (includeGeographyRelatedColumnsInQuery) {
        includeCustomerRelatedColumnsInQuery = true  
      }

      const mandatoryColumnsPlusColumnsNeededForFilteringWithoutUniq = [...mandatoryColumnsForSearch, ...fieldsNeedingFilterData]
      const mandatoryColumnsPlusColumnsNeededForFiltering = lodashObject.uniq(mandatoryColumnsPlusColumnsNeededForFilteringWithoutUniq)
      const returnValue0 = generateSelectJoinClauseForClassification(reportQueryClassificationJoinConfiguration, 'ANIMAL', undefined, mandatoryColumnsPlusColumnsNeededForFiltering)
      console.log('s a ALV3 1, returnValue = ', returnValue)
      selectClauseForClassification = returnValue0[0]
      joinClauseForClassification = returnValue0[1]
      returnedJSONColumns = returnValue0[2]
      returnedDoubleColumns = returnValue0[3]
      jsonColumns = [...jsonColumns, ...returnedJSONColumns]
      doubleColumns = [...doubleColumns, ...returnedDoubleColumns]
      /* includeSellingPricesColumnsInQuery = false
      includeMonthsPregnantColumnInQuery = false
      includeCowAttributeCountInQuery = false
      includeCowImageCountInQuery = false
      includeLocationRecordedColumnsInQuery = false
      includeAnimalStatusColumnsInQuery = false
      includeSnoozeStatusColumnInQuery = false
      includeParavetRelatedColumnsInQuery = false
      includeBDMRelatedColumnsInQuery = false
      includeGeographyRelatedColumnsInQuery = false
      includeCustomerRelatedColumnsInQuery = false */

      innerQuery = createAnimalInnerQuery({includeMonthsPregnantColumnInQuery, includeCowAttributeCountInQuery,
        includeCowImageCountInQuery, includeLocationRecordedColumnsInQuery, includeSnoozeStatusColumnInQuery,
        includeParavetRelatedColumnsInQuery, includeBDMRelatedColumnsInQuery, includeGeographyRelatedColumnsInQuery,
        includeCustomerRelatedColumnsInQuery, includeSellingPricesColumnsInQuery,
        restrictedIds, searchWithinRestrictedIds,
        ignoreInitialContactStatusAnimals, includeAnimalStatusColumnsInQuery, 
        selectClauseForClassification, joinClauseForClassification,
        customerId})

      if (fieldsNeedingFilterData.includes('village') || fieldsNeedingFilterData.includes('village_name')) {
        const villageNameFilterKey = fieldsNeedingFilterData.includes('village_name') ? 'village_name_filter_values' : undefined
        const villageFilterKey = fieldsNeedingFilterData.includes('village') ? 'village_filter_values' : undefined

          const distinctVillageQuery = `
            select distinct village_id, village_name value, village_name text from (
              ${innerQuery}
            ) outerTable
            ${whereClause}
          `
        const distinctVillageResultUnprocessed = await mainDBConnection.manager.query(distinctVillageQuery)
        const distinctVillageResult = postProcessRecords(undefined, distinctVillageResultUnprocessed, {})

        if (fieldsNeedingFilterData.includes('village_name')) {
          returnValue[filtersFieldToReturnKeyMap['village_name']] = distinctVillageResult
        }
        if (fieldsNeedingFilterData.includes('village')) {
          returnValue[filtersFieldToReturnKeyMap['village']] = distinctVillageResult
        }
      }

      includeSellingPricesColumnsInQuery = false
      includeMonthsPregnantColumnInQuery = false
      includeCowAttributeCountInQuery = false
      includeCowImageCountInQuery = false
      includeLocationRecordedColumnsInQuery = false
      includeAnimalStatusColumnsInQuery = false
      includeSnoozeStatusColumnInQuery = false
      includeParavetRelatedColumnsInQuery = false
      includeBDMRelatedColumnsInQuery = !excludeBDM
      includeGeographyRelatedColumnsInQuery = false
      includeCustomerRelatedColumnsInQuery = false
      
      restrictedIds = true

      // [selectClauseForClassification, joinClauseForClassification] = generateSelectJoinClauseForClassification(reportQueryClassificationJoinConfiguration, 'ANIMAL', undefined, selectedColumns)
      const returnValue1 = generateSelectJoinClauseForClassification(reportQueryClassificationJoinConfiguration, 'ANIMAL', undefined, selectedColumns)
      console.log('s a ALV3 1, returnValue = ', returnValue)
      selectClauseForClassification = returnValue1[0]
      joinClauseForClassification = returnValue1[1]
      returnedJSONColumns = returnValue1[2]
      returnedDoubleColumns = returnValue1[3]
      jsonColumns = [...jsonColumns, ...returnedJSONColumns]
      doubleColumns = [...doubleColumns, ...returnedDoubleColumns]


      if (selectedColumns.includes('minimum_selling_price') || selectedColumns.includes('maximum_selling_price')) {
        includeSellingPricesColumnsInQuery = true
      }
      if (selectedColumns.includes('number_of_months_pregnant')) {
        includeMonthsPregnantColumnInQuery = true
      }
      if ((selectedColumns.includes('bdm') || selectedColumns.includes('bdm_uuid')) && !excludeBDM) {
        includeBDMRelatedColumnsInQuery = true
      }
      if (selectedColumns.includes('paravet') || selectedColumns.includes('paravet_uuid')) {
        includeParavetRelatedColumnsInQuery = true
      }
      if (selectedColumns.includes('village') || selectedColumns.includes('taluk')
          || selectedColumns.includes('village_name') || selectedColumns.includes('taluk_name')
          || includeBDMRelatedColumnsInQuery || includeParavetRelatedColumnsInQuery) {
        includeGeographyRelatedColumnsInQuery = true
      }
      if (selectedColumns.includes('farmer_location_recorded_status') || selectedColumns.includes('location_updated_timestamp')) {
        includeLocationRecordedColumnsInQuery = true
      }
      if (selectedColumns.includes('snooze_status')) {
        includeSnoozeStatusColumnInQuery = true
      }
      if (selectedColumns.includes('customer_id')
          || selectedColumns.includes('customer_name')
          || selectedColumns.includes('mobile_number') || includeGeographyRelatedColumnsInQuery || includeLocationRecordedColumnsInQuery
          || includeSnoozeStatusColumnInQuery
          || customerId !== undefined) {
        includeCustomerRelatedColumnsInQuery = true
      }

      if (selectedColumns.includes('cow_score_attribute_count')) {
        includeCowAttributeCountInQuery = true
      }
      if (selectedColumns.includes('cow_score_image_count')) {
        includeCowImageCountInQuery = true
      }
      if (selectedColumns.includes('selling_animal_current_status') || selectedColumns.includes('selling_animal_current_status_id')) {
        includeAnimalStatusColumnsInQuery = true
      }

      innerQuery = createAnimalInnerQuery({includeMonthsPregnantColumnInQuery, includeCowAttributeCountInQuery,
        includeCowImageCountInQuery, includeLocationRecordedColumnsInQuery, includeSnoozeStatusColumnInQuery,
        includeParavetRelatedColumnsInQuery, includeBDMRelatedColumnsInQuery, includeGeographyRelatedColumnsInQuery,
        includeCustomerRelatedColumnsInQuery, includeSellingPricesColumnsInQuery,
        restrictedIds, searchWithinRestrictedIds,
        ignoreInitialContactStatusAnimals, includeAnimalStatusColumnsInQuery,
        selectClauseForClassification, joinClauseForClassification,
        customerId})

      query = `
        select * from (
          ${innerQuery}
        ) outerTable
        ${includeWhereClauseInDetailedColumnsQuery ? whereClause : ''}
        ${sortString}
      `

      const reportResultWithSelectedColumnsUnprocessed = await mainDBConnection.manager.query(query)
      const reportResultWithSelectedColumns = postProcessRecords(undefined, reportResultWithSelectedColumnsUnprocessed, {json_columns: jsonColumns, double_columns: doubleColumns})

      returnValue.count = count
      returnValue.report = reportResultWithSelectedColumns

      return returnValue
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class AnimalListFilter {
  async create (data, params) {
    try {
      const returnValue = { return_code: 0 }
      const tableFilterData = {}
      returnValue.tableFilters = tableFilterData
      const mainDBConnection = dbConnections().main

      // const limitString = generateLimitString(data)
      // const sortString = generateSortString(data) 
      // const whereClause = generateWhereClause(data) 

      // const excludeBDM = (data.excludeBDM === true) ? true : false
      // const restrictToLocationNotRecorded = (data.restrictToLocationNotRecorded === true) ? true : false

      // identify all columns needed
      const selectedColumns = data.selectedColumns
      // identify mandatory columns needed
      // const mandatoryColumnsForSearch = identifyMandatoryColumnsForReportQuery(data.filters, data.sorting, data.globalSearch, data.searchConfiguration)

      // let selectClauseForClassification
      // let joinClauseForClassification
      // [selectClauseForClassification, joinClauseForClassification] = generateSelectJoinClauseForClassification(reportQueryClassificationJoinConfiguration, 'CUSTOMER', undefined, mandatoryColumnsForSearch)

      // let includeBDMInQuery = false
      // let includeParavetInQuery = false
      // let includeGeographyInQuery = false
      // let includeFarmerCountInVillageInQuery = false
      // let includeFarmerCountOfLocationNotRecordedInVillageInQuery = false
      // let noLocationRecordedDefault = restrictToLocationNotRecorded
      // let restrictedIds = false

      // if ((mandatoryColumnsForSearch.includes('bdm') || mandatoryColumnsForSearch.includes('bdm_uuid')) && !excludeBDM) {
      //   includeBDMInQuery = true
      // }
      // if (mandatoryColumnsForSearch.includes('paravet') || mandatoryColumnsForSearch.includes('paravet_uuid')) {
      //   includeParavetInQuery = true
      // }
      // if (mandatoryColumnsForSearch.includes('village') || mandatoryColumnsForSearch.includes('taluk') || includeBDMInQuery || includeParavetInQuery) {
      //   includeGeographyInQuery = true
      // }
      // if (mandatoryColumnsForSearch.includes('farmer_count_in_village')) {
      //   includeFarmerCountInVillageInQuery = true
      // }
      // if (mandatoryColumnsForSearch.includes('farmer_count_in_village_with_no_location')) {
      //   includeFarmerCountOfLocationNotRecordedInVillageInQuery = true
      // }

      // let includeWhereClauseInDetailedColumnsQuery = false

      // let innerQuery = createCustomerInnerQuery({includeGeographyInQuery,
      //   includeBDMInQuery, includeParavetInQuery, includeFarmerCountInVillageInQuery,
      //   includeFarmerCountOfLocationNotRecordedInVillageInQuery,
      //   restrictedIds, searchWithinRestrictedIds,
      //   selectClauseForClassification, joinClauseForClassification,
      //   noLocationRecordedDefault, noLocationRecordedInnerQuery})

      // const countQuery = `
      //   select count(*) as count from (
      //     ${innerQuery}
      //   ) outerTable
      //   ${whereClause}
      // `
      // const reportResultCount = await mainDBConnection.manager.query(countQuery)
      // const count = parseInt(reportResultCount[0].count)

      // if (count === 0) {
      //   includeWhereClauseInDetailedColumnsQuery = true
      // }
      
      // let query = `
      //     select * 
      //     from (
      //       ${innerQuery}
      //     ) outerTable
      //     ${whereClause}
      //     ${sortString}
      //     ${limitString}
      //   `

      // const reportResultUnprocessed = await mainDBConnection.manager.query(query)
      // const reportResult = postProcessRecords(undefined, reportResultUnprocessed, {})

      // const primaryKeyToRowMap = {}
      // for (const reportRow of reportResult) {
      //   primaryKeyToRowMap[reportRow['customer_id']] = reportRow
      // }
      // const primaryKeysToBeQueried = Object.keys(primaryKeyToRowMap)

      // let inPrimaryKeysIdString = ""
      // for (const primaryKey of primaryKeysToBeQueried) {
      //   if (inPrimaryKeysIdString !== "") {
      //     inPrimaryKeysIdString = inPrimaryKeysIdString + ", "
      //   }
      //   inPrimaryKeysIdString = inPrimaryKeysIdString + "'" + primaryKey + "'"
      // }
      // searchWithinRestrictedIds = (inPrimaryKeysIdString !== '') ? ` and c.customer_id in (${inPrimaryKeysIdString})` : ''

      const pageFiltersFieldToReturnKeyMap = extractFieldsNeedingPageFilterData(
        data.searchConfiguration.columnConfiguration,
        Object.keys(data.searchConfiguration.columnConfiguration) !== undefined && Array.isArray(Object.keys(data.searchConfiguration.columnConfiguration))
          ? Object.keys(data.searchConfiguration.columnConfiguration)
          : []
      )
      const fieldsNeedingPageLoadFilterData = Object.keys(pageFiltersFieldToReturnKeyMap)

      if (fieldsNeedingPageLoadFilterData.includes('taluk') || fieldsNeedingPageLoadFilterData.includes('taluk_name')) {
        const talukAsFieldNeedingPageLoadFilterData = fieldsNeedingPageLoadFilterData.includes('taluk')
        const talukNameAsFieldNeedingPageLoadFilterData = fieldsNeedingPageLoadFilterData.includes('taluk_name')
        const distinctTalukQuery = `
          select distinct taluk_id, taluk_id as value, coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') as text
          from main.customer_classification cc
          inner join main.customer c on c.customer_id = cc.customer_id and c.active = 1000100001 and c.customer_type_id in (1000220001)
          inner join main.ref_sdtv_view rsv on rsv.village_id = cc.value_reference_id
          where cc.active = 1000100001 and cc.classifier_id = 2000000055
        `
        const distinctTalukFilterValuesResultUnprocessed = await mainDBConnection.manager.query(distinctTalukQuery)
        const distinctTalukFilterValuesResult = postProcessRecords(undefined, distinctTalukFilterValuesResultUnprocessed, {})
        if (talukAsFieldNeedingPageLoadFilterData) {
          tableFilterData[pageFiltersFieldToReturnKeyMap['taluk']] = distinctTalukFilterValuesResult
        }
        if (talukNameAsFieldNeedingPageLoadFilterData) {
          tableFilterData[pageFiltersFieldToReturnKeyMap['taluk_name']] = distinctTalukFilterValuesResult
        }
      }

      if (fieldsNeedingPageLoadFilterData.includes('bdm')) {
        const staffQuery = `
          select s.staff_id as value, coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as text
          from main.staff s
          where s.active = 1000100001 and s.staff_type_id in (1000230001, 1000230005)
        `
        const staffFilterValuesResultUnprocessed = await mainDBConnection.manager.query(staffQuery)
        const staffFilterValuesResult = postProcessRecords(undefined, staffFilterValuesResultUnprocessed, {})
        tableFilterData[pageFiltersFieldToReturnKeyMap['bdm']] = staffFilterValuesResult
      }
      
      return returnValue
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class GetAnimalDetails {
  async get(id, params) {
    try {
      const returnValue = {return_code: 0}
      console.log('oc r GAD g 1, id = ', id, ', params = ', params)
      const mainDBConnections = dbConnections().main
      const animalQuery = `
        select a.animal_id, a.animal_visual_id, et_ac.value_string_256 animal_ear_tag,
          concat(coalesce(b_rr.reference_name_l10n->>'en', b_rr.reference_name_l10n->>'ul'),'-',
            coalesce(at_rr.reference_name_l10n->>'en', at_rr.reference_name_l10n->>'ul')) animal_breed_and_type,
          c.customer_id, c.customer_type_id,
          concat(coalesce(c.customer_name_l10n->>'en', c.customer_name_l10n->>'ul'), '-', c.mobile_number) customer_name_and_mobile,
          concat(coalesce(gcc_v.district_name, gcc_t.district_name), '-', coalesce(gcc_v.taluk_name, gcc_t.taluk_name), '-', gcc_v.village_name) district_taluk_village,
          concat(fbdm.bdm, '-', fbdm.bdm_mobile) bdm_and_mobile, concat(fbdm.paravet, '-', fbdm.paravet_mobile) paravet_and_mobile,
          ct_d.document_id as customer_thumbnail_document_id, at_d.document_id as animal_thumbnail_document_id,
          aet_d.document_id as animal_eartag_document_id,
          dfl_cc.value_json dairy_farm_location_position
        from main.animal a
        left join main.animal_classification et_ac on et_ac.active = 1000100001 and et_ac.animal_id = a.animal_id and et_ac.classifier_id = 2000000034
        left join main.animal_classification b_ac on b_ac.active = 1000100001 and b_ac.animal_id = a.animal_id and b_ac.classifier_id = 2000000036
        left join main.ref_reference b_rr on b_rr.reference_id = b_ac.value_reference_id
        left join main.animal_classification at_ac on at_ac.active = 1000100001 and at_ac.animal_id = a.animal_id and at_ac.classifier_id = 2000000035
        left join main.ref_reference at_rr on at_rr.reference_id = at_ac.value_reference_id
        inner join main.entity_relationship f2a on f2a.active = 1000100001 and f2a.entity_relationship_type_id = 1000210004 and f2a.entity_2_entity_uuid = a.animal_id
        inner join main.customer c on c.customer_id = f2a.entity_1_entity_uuid
          /* and c.active = 1000100001 and c.customer_type_id in (1000220001) */
        left join (
          select gcc.customer_id,
            coalesce(rsv.district_name_l10n->>'en', rsv.district_name_l10n->>'ul') as district_name,
            coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') as taluk_name,
            coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') as village_name,
            rsv.district_id, rsv.village_id, rsv.taluk_id
          from main.customer_classification gcc
          inner join main.ref_sdtv_view_2 rsv on rsv.village_id = gcc.value_reference_id
          where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
        ) gcc_v on gcc_v.customer_id = c.customer_id
        left join (
          select gcc.customer_id,
            coalesce(d_rg.geography_name_l10n->>'en', d_rg.geography_name_l10n->>'ul') as district_name,
            coalesce(t_rg.geography_name_l10n->>'en', t_rg.geography_name_l10n->>'ul') taluk_name,
            d_rg.geography_id as district_id, t_rg.geography_id as taluk_id
          from main.customer_classification gcc
          inner join main.ref_geography t_rg on t_rg.geography_type_id = 1000320003
            and t_rg.geography_id = gcc.value_reference_id
          inner join main.entity_relationship t2d on t2d.active = 1000100001
            and t2d.entity_relationship_type_id = 1000210030
            and t2d.entity_1_type_id = 1000460015 and t2d.entity_2_type_id = 1000460015
            and t2d.entity_2_entity_id = t_rg.geography_id
          inner join main.ref_geography d_rg on d_rg.geography_type_id = 1000320002
            and d_rg.geography_id = t2d.entity_1_entity_id
          where gcc.classifier_id = 2000000054 and gcc.active = 1000100001
        ) gcc_t on gcc_t.customer_id = c.customer_id
        left join (
          select gcc.customer_id, sg.bdm, sg.bdm_mobile, sg.bdm_uuid, pg.paravet, pg.paravet_mobile, pg.paravet_uuid
          from main.customer_classification gcc
          left join (
            select coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as bdm, s.mobile_number as bdm_mobile, sg.geography_id, s.staff_id bdm_uuid
            from main.staff s, (
              select eg.geography_id, (min(eg.entity_uuid::text))::uuid as staff_id
              from main.entity_geography eg
              where eg.active = 1000100001 and eg.geography_type_id = 1000320004
              and eg.entity_type_id in (1000230001, 1000230005)
              group by eg.geography_id
            ) sg
            where s.staff_id = sg.staff_id and s.active = 1000100001
          ) sg on sg.geography_id = gcc.value_reference_id
          left join (
            select coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as paravet, s.mobile_number as paravet_mobile, pg.geography_id, s.staff_id as paravet_uuid
            from main.staff s, (
              select eg.geography_id, (min(eg.entity_uuid::text))::uuid as staff_id
              from main.entity_geography eg
              where eg.active = 1000100001 and eg.geography_type_id = 1000320004
              and eg.entity_type_id in (1000230004)
              group by eg.geography_id
            ) pg
            where s.staff_id = pg.staff_id and s.active = 1000100001
          ) pg on pg.geography_id = gcc.value_reference_id
          where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
        ) fbdm on fbdm.customer_id = c.customer_id
        left join main.customer_classification dfl_cc on dfl_cc.active = 1000100001
          and dfl_cc.customer_id = c.customer_id and dfl_cc.classifier_id = 2000000004
        left join (
          select d.document_id, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id,
            coalesce(dt_rr.reference_name_l10n->>'en', dt_rr.reference_name_l10n->>'ul') as document_type,
            d.document_information,
            d.created_at document_upload_time
          from main.document d
          inner join main.ref_reference dt_rr on dt_rr.reference_id = d.document_type_id
          left join (
            select max(d.document_id::text) max_created_at_document_id, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
            from main.document d
            inner join (
              select max(d.created_at) max_created_at, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
              from main.document d
              where 1 = 1 and d.active = 1000100001
              group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id 
            ) mcad on d.entity_1_type_id = mcad.entity_1_type_id and d.entity_1_entity_uuid = mcad.entity_1_entity_uuid
              and d.document_type_id = mcad.document_type_id and d.created_at = mcad.max_created_at
            where 1 = 1  and d.active = 1000100001
            group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
          ) mcadid on d.document_id::text = mcadid.max_created_at_document_id
        ) ct_d on ct_d.entity_1_type_id = 1000220001 and ct_d.entity_1_entity_uuid = c.customer_id and ct_d.document_type_id = 1000260003
        left join (
          select d.document_id, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id,
            coalesce(dt_rr.reference_name_l10n->>'en', dt_rr.reference_name_l10n->>'ul') as document_type,
            d.document_information,
            d.created_at document_upload_time
          from main.document d
          inner join main.ref_reference dt_rr on dt_rr.reference_id = d.document_type_id
          left join (
            select max(d.document_id::text) max_created_at_document_id, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
            from main.document d
            inner join (
              select max(d.created_at) max_created_at, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
              from main.document d
              where 1 = 1 and d.active = 1000100001
              group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id 
            ) mcad on d.entity_1_type_id = mcad.entity_1_type_id and d.entity_1_entity_uuid = mcad.entity_1_entity_uuid
              and d.document_type_id = mcad.document_type_id and d.created_at = mcad.max_created_at
            where 1 = 1  and d.active = 1000100001
            group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
          ) mcadid on d.document_id::text = mcadid.max_created_at_document_id
        ) at_d on at_d.entity_1_type_id = 1000220002 and at_d.entity_1_entity_uuid = a.animal_id and at_d.document_type_id = 1000260001
        left join (
          select d.document_id, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id,
            coalesce(dt_rr.reference_name_l10n->>'en', dt_rr.reference_name_l10n->>'ul') as document_type,
            d.document_information,
            d.created_at document_upload_time
          from main.document d
          inner join main.ref_reference dt_rr on dt_rr.reference_id = d.document_type_id
          left join (
            select max(d.document_id::text) max_created_at_document_id, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
            from main.document d
            inner join (
              select max(d.created_at) max_created_at, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
              from main.document d
              where 1 = 1 and d.active = 1000100001
              group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id 
            ) mcad on d.entity_1_type_id = mcad.entity_1_type_id and d.entity_1_entity_uuid = mcad.entity_1_entity_uuid
              and d.document_type_id = mcad.document_type_id and d.created_at = mcad.max_created_at
            where 1 = 1  and d.active = 1000100001
            group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
          ) mcadid on d.document_id::text = mcadid.max_created_at_document_id
        ) aet_d on aet_d.entity_1_type_id = 1000220002 and aet_d.entity_1_entity_uuid = a.animal_id and aet_d.document_type_id = 1000260012
        where a.animal_id = '${id}'
      `
      console.log('oc r GAD g 2, animalQuery = ', animalQuery)
      const animalBasicDetailsUnProcessedResult = await mainDBConnections.manager.query(animalQuery)
      const animalBasicDetailsResult = postProcessRecords(undefined, animalBasicDetailsUnProcessedResult, {})
      console.log('oc r GAD g 3, animalBasicDetailsResult = ', animalBasicDetailsResult)
      returnValue.result = animalBasicDetailsResult[0]

      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async create (data, params) {
    try {
      console.log('oc r GAD c 1, data = ', data)
      const returnValue = {return_code: 0}
      const classificationData = await loadClassificationData('ANIMAL_CLASSIFICATION', data.animalId, data.classifierArray)
      console.log('oc r GAD c 2, classificationData = ', classificationData)
      returnValue.data = classificationData
      return returnValue
    } catch (error) {
      console.log('oc r GAD c 10, error')
      console.log('oc r GAD c 10a, error = ', error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class SetAnimalDetails {
  async create (data, params) {
    try {
      console.log('oc r SFD c 1')
      const returnValue = {return_code: 0}
      const mainDBConnection = dbConnections().main
      const animalTableEntity = mainDBConnection.entities['animal']
      const animalClassificationTableEntity = mainDBConnection.entities['animal_classification']
      const entityRelationshipTableEntity = mainDBConnection.entities['entity_relationship']
      const {customer_id, customer_type_id, animal_type, ear_tag_1, ...classificationData } = data.classificationData
      if (ear_tag_1 !== undefined) {
        classificationData['ear_tag_1'] = ear_tag_1
      }
      if (animal_type !== undefined) {
        classificationData['animal_type'] = animal_type
      }
      let animalId = data.animalId
      if (animalId === undefined) {
        // this is creating a new customer scenario
        if (customer_type_id !== 1000220009) {
          returnValue.return_code = -202
          returnValue.message = 'This API can create only SR customer'
          return returnValue
        }
        // check if customer already exists based on mobile number
        const numberOfActiveAnimalsWithEarTag = await mainDBConnection.manager
          .createQueryBuilder()
          // .select(['customer_id'])
          .from(animalTableEntity, 'a')
          .innerJoinAndSelect(
            animalClassificationTableEntity,
            'ac',
            'ac.active = :activeId and ac.classifier_id = :earTagClassifierId and ac.animal_id = a.animal_id and ac.value_string_256 = :animalEarTag',
            { activeId: 1000100001, earTagClassifierId: 2000000034, animalEarTag: ear_tag_1 },
          )
          // .where('active = :activeId and classifier_id = :earTagClassifierId and value_string_256 = :animalEarTag', { activeId: 1000100001, earTagClassifierId: 2000000034, animalEarTag: ear_tag_1 })
          .where('a.active = :activeId', { activeId: 1000100001 })
          .getCount()

        if (numberOfActiveAnimalsWithEarTag > 0) {
          return { return_code: -203, message: "Another Animal in the system has the same ear tag" };
        }
      }
      const saveResult = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
        try {
          if (animalId === undefined) {
            // create farmer
            // get the customer id from the execution result. set it into return value
            const visualIdGenerationQuery = `
              select concat(c.customer_visual_id, '/A', a.id) as animal_visual_id
              from 
              (
                select coalesce(lpad((max(trim(leading '0' from replace(a.animal_visual_id, concat(c.customer_visual_id, '/A'), ''))::int)+1)::text, 3, '0'), '001') id
                from main.customer c 
                inner join main.entity_relationship er on er.entity_relationship_type_id = 1000210004 and er.entity_1_entity_uuid = c.customer_id 
                inner join main.animal a on a.animal_id = er.entity_2_entity_uuid 
                where c.customer_id = '${customer_id}'
              ) a, main.customer c
              where c.customer_id = '${customer_id}'
            `
            const animalVisualIdQueryResponse = await transactionalEntityManager.query(visualIdGenerationQuery)
            const animalVisualId = animalVisualIdQueryResponse[0].animal_visual_id


            const preProcessedAnimalData = preProcessRecords(animalTableEntity, {animal_visual_id: animalVisualId, animal_type: animal_type[0] }, animalTableEntity.additionalAttributes)
            const createdAnimal = await transactionalEntityManager.createQueryBuilder().insert().into(animalTableEntity).values(preProcessedAnimalData).execute()
            animalId = createdAnimal.identifiers[0].animal_id
            // postUpsertProcessing('main', 'customer', { insertedPrimaryKeys: [customerId] })
            returnValue.animalId = animalId
            
            const entityRelationshipObject = {
              entity_relationship_type_id: 1000210004,
              entity_1_entity_uuid: customer_id,
              entity_2_entity_uuid: animalId,
              entity_1_type_id: 1000460001,
              entity_2_type_id: 1000460002,
            }
            const createdEntityRelationship = await transactionalEntityManager.createQueryBuilder().insert().into(entityRelationshipTableEntity).values(entityRelationshipObject).execute()
          }
          const updateClassificationDataResult = await saveClassificationData(transactionalEntityManager, 'ANIMAL_CLASSIFICATION', animalId, classificationData);
          returnValue.classificationDataUpdateResult = updateClassificationDataResult
        } catch (error) {
          console.log('oc UASS c 20, error')
          console.log('oc UASS c 20a, error = ', error)
          throw error
        }
      })
      return returnValue
    } catch (error) {
      console.log('oc r SFD c 10, error')
      console.log('oc r SFD c 10a, error = ', error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

module.exports = { AnimalList, Animal,
  AnimalListV3, AnimalListFilter, GetAnimalDetails, SetAnimalDetails }
