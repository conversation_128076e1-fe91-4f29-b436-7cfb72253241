const { LibAnimalService, LibAnimalListFarmTaskService, LibUpdateAnimalActiveStatus , LibAnimalHealthRecord,LibAnimalMilking,LibHealthScore} = require("@krushal-it/back-end-lib");
const uuid = require("uuid");
const errors = require("@feathersjs/errors");

class AnimalService {
  animalServiceHandler = new LibAnimalService();
  hs = new LibHealthScore()
  async find(params) {
    try {
      let result = await this.animalServiceHandler.find(params);
      return result;
    } catch (error) {
      console.error(error);
      return { result: false, error };
    }
  }

  async get(id, params) {
    let newAnimalData = await this.hs.loadAnimalClassiferDataWithDocuments(params.headers, id ,true)
    let oldAnimalData = await this.animalServiceHandler.get(id, params);
    let animalHealthScoreHistory = await this.hs.animalScoreHistory(id); 
    //get animal healthscore history
    return {
      ...newAnimalData,
      ...oldAnimalData,
      animalHealthScoreHistory
    }
  }

  async create(data, params) {

    if (!data.animal_id || !uuid.validate(data.animal_id)) {
      throw new errors.BadRequest('animal_id is mandatory');
    }

    let response = await this.animalServiceHandler.getAnimalDetails(data, params);

    if (response) {
      return response;
    } else {
      throw new errors.NotFound('data not found')
    }

  }

}
class AnimalListForFarm {
  animalListServiceHandler = new LibAnimalListFarmTaskService();
  async create(data, params) {
    try {
      // throw new Error('not supported')
      return await this.animalListServiceHandler.create(data, params);
    } catch (error) {
      console.error(error);
      return error.message;
    }
  }
}
class UpdateAnimalActiveStatus {
  animalInactiveHandler = new LibUpdateAnimalActiveStatus();
  async create(data,params) {
    try {
      return await this.animalInactiveHandler.create(data,params);
    } catch (error) {
      console.error(error);
      return error.message;
    }
  }

}
class AnimalHealthRecord {
   animalHealthRecordHandler = new LibAnimalHealthRecord();
   async get(id,params) {
    return await this.animalHealthRecordHandler.getAnimalHeathRecord(id)
  }  
}

class AnimalTaskHistoryBySrId {
  animalMedication = new LibAnimalHealthRecord();
  async get(SrNo , params) {
    return await this.animalMedication.getMedicationBySrNo(SrNo);
  } 
}

class AnimalMilking {
  animalMilking = new LibAnimalMilking();
  async create(data,params) {
    const response = await this.animalMilking.createOrUpdateMilkLog(data,params)
    return response;
  }
  async get(id,params) {

    const { user_id } = params.headers.token
    if(id==="farmer"){
      
      const response = await this.animalMilking.getAnimalMilikingByFarmerId(user_id,params)
      return response
    }
    if(id==="animal"){
      const response = await this.animalMilking.getAnimalMilikingByAnimalId(params)
      return response
    }
  }
}

module.exports = { AnimalService, AnimalListForFarm, UpdateAnimalActiveStatus , AnimalHealthRecord ,AnimalTaskHistoryBySrId ,AnimalMilking};