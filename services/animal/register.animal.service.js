const { AnimalList, Animal,
  AnimalListV3, AnimalListFilter, GetAnimalDetails, SetAnimalDetails } = require('./animal.class')
const auth = require('../../middleware/auth')
const tokenInject = require('../../middleware/tokenInject.js')

const configureAnimal = (app) => {
  // app.use(auth)
  app.use('/v2/animal-list', new AnimalList())
  // app.use('/v2/animal', new Animal())
  // app.use('/v2/animal/:animalId', new Animal())
  app.use('/v3/animal-list', new AnimalListV3(), tokenInject)
  app.use('/v3/animal-list-filter', new AnimalListFilter(), tokenInject)
  app.use('/v3/get-animal-details', new GetAnimalDetails(), tokenInject)
  app.use('/v3/set-animal-details', new SetAnimalDetails(), tokenInject)
}
module.exports = { configureAnimal }
