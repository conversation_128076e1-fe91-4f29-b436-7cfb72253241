const { <PERSON><PERSON><PERSON><PERSON>, GeneralError, BadRequest } = require('@feathersjs/errors')

function hooks(app) {
    app.service('/customer').hooks({
        before: {
            find: [async (context) => { 
                throw new BadRequest('Invalid params', {
                    farmer_id: 'is missing'
                  })
            }]
          },
    })
}


module.exports = {
    hooks
}