const { rm } = require("fs/promises")
const { hooks } = require("./hooks")
const { BadRequest, GeneralError, errors } = require("@feathersjs/errors")
const { errorLog } = require("@krushal-it/mobile-or-server-lib")
const { CENTRALIZED_VET, PPU_PARAVET } = require("../../utils/common/namespace/krushal.namespace")
const { PARAVET } = require("@krushal-it/back-end-lib/utils/constant")
const { getServiceDocumentData, loadClassificationData, LibActivityService } = require("@krushal-it/back-end-lib")
const { ActivityPrescriptionServiceV2, ActivityDiagnosisServiceV2, ActivityObservationServiceV2, AdditionalMedicineServciceV2, ActivitySummaryServiceV2 } = require("../activity/activity.service")
const tokenInject = require("../../middleware/tokenInject")
const kurshal_image = require("../../utils/image")
const { CattleV2, getTasks, getAnimalsByFarmerID, getActivityByIdV2, getParavetCard, getVetCard, getRoutePlanV2, getParavetPreventiveDashboard, ActivityFormControl, updateActivity_V2 } = require("@krushal-it/back-end-lib")
const { createTaskv2 } = require("@krushal-it/back-end-lib/services/activity_routeplan/controller")
const { MIN_BALANCE } = require("../../utils/common/namespace/krushal.namespace")
const { FREELANCER_TRAINING_VIDEOS, ACTIVE } = require("@krushal-it/back-end-lib/utils/constant")
const { GetFinance } = require("../ppuparavet/finance.class.js")
const { enrollFarmerToFreelanceParavet } = require("../ppuparavet/farmer.controller.js")
const { dbConnections } = require("@krushal-it/ah-orm")
const { handleServerError } = require("../../middleware/server-error")
const { sendNotificationsByCustomerIdV2, getFCMofUserQuery, getFCMforFreelancers } = require("../../database_utils/notification/notificatio.query")
const { classifiers, record_statuses } = require("@krushal-it/back-end-lib/ENUMS")
const { getServiceDocumentHTML } = require("../../js-template/service-document.js")
const { pipeStreamForGet } = require("../object-management/middleware/index.js")
const { existsSync, writeFileSync, mkdirSync } = require("fs")
const { Readable } = require("stream")
const { uploadDocument, addDocumentEntry } = require("../document/controllers/document")
const S3Utils = require("../../utils/common/aws/s3.utils")

const {
  entity_type: { CUSTOMER, TASK },
  document_type: { FARM_MANAGEMENT_PDF, SERVICE_DOCUMENT }
} = require("@krushal-it/back-end-lib/ENUMS")
const { getPrescriptionHTML } = require("../../js-template/prescription-document.js")
const { costSummaryHTML } = require("../finance/farmer/service.js")
const ENUMS = require("@krushal-it/back-end-lib/ENUMS")
const { parse } = require("path")

const servicesV2 = app => {
  // fetches all tasks of a farmer or an animal (via a FARMER-ID or ANIMAL-ID)
  app.use("v2/tasks/prescription/:type", new ActivityPrescriptionServiceV2(), tokenInject)
  app.use("v2/tasks/observation", new ActivityObservationServiceV2(), tokenInject)
  app.use("v2/tasks/diagnosis", new ActivityDiagnosisServiceV2(), tokenInject)
  app.use("v2/task/addition_medicine", new AdditionalMedicineServciceV2(), tokenInject)
  app.use("v2/task/summary", new ActivitySummaryServiceV2(), tokenInject)
  app.use("v2/tasks/service-document-data", {
    async get(care_calendar_id) {
      try {
        const response = await getServiceDocumentData(care_calendar_id)
        return { ...response }
      } catch (error) {
        return new GeneralError(error)
      }
    }
  })
  app.use("v2/collected/amount", {
    async create(data) {
      const { care_calendar_id, collected_amount } = data
      if (!care_calendar_id) {
        throw new BadRequest("missing care-calendar-id!")
      }

      const task = await dbConnections()
        .main.manager.createQueryBuilder()
        .select("cc.calendar_activity_status", "calendar_activity_status")
        .from("care_calendar", "cc")
        .where("cc.care_calendar_id = :care_calendar_id", { care_calendar_id })
        .getRawOne()

      if (task.calendar_activity_status === ENUMS.staff_activity_statuses.VISIT_COMPLETED) {
        switch (collected_amount) {
          case undefined:
            const actual_amount = await dbConnections()
              .main.manager.createQueryBuilder()
              .select(["collectedServiceAmount.value_double AS service_amount", "collectedMedicineAmount.value_double AS medicine_amount", "paymentDetails.value_json as payment_detail"])
              .from("care_calendar", "cc") // Main table
              .leftJoin("care_calendar_classification", "collectedServiceAmount", "collectedServiceAmount.care_calendar_id = cc.care_calendar_id AND collectedServiceAmount.classifier_id = :collectedServiceAmount and collectedServiceAmount.active= :active")
              .leftJoin("care_calendar_classification", "collectedMedicineAmount", "collectedMedicineAmount.care_calendar_id = cc.care_calendar_id AND collectedMedicineAmount.classifier_id = :collectedMedicineAmount and collectedMedicineAmount.active = :active")
              .leftJoin("care_calendar_classification", "paymentDetails", "paymentDetails.care_calendar_id = cc.care_calendar_id AND paymentDetails.classifier_id = :paymentDetails AND paymentDetails.active = :active")
              .where("cc.care_calendar_id = :careCalendarId")
              .setParameters({
                collectedServiceAmount: ENUMS.classifiers.TASK_SERVICE_COST_PAYMENT_DETAILS,
                collectedMedicineAmount: ENUMS.classifiers.TASK_MEDICINE_COST_PAYMENT_DETAILS,
                paymentDetails: ENUMS.classifiers.TASK_PAYMENT_DETAILS,
                careCalendarId: care_calendar_id,
                active: ENUMS.record_statuses.ACTIVE
              })
              .getRawOne()

            const current_amount = await dbConnections()
              .main.manager.createQueryBuilder()
              .select(["cc.value_json AS collected_amount", "cc.active AS active"])
              .from("care_calendar_classification", "cc")
              .where("cc.classifier_id = :collected_amount and cc.care_calendar_id = :care_calendar_id and cc.active = :active")
              .setParameters({
                care_calendar_id,
                collected_amount: 2000000482,
                active: ENUMS.record_statuses.ACTIVE
              })
              .getRawOne()

            return {
              service: {
                actual_amount: parseFloat(actual_amount.service_amount),
                actual_mechanism: actual_amount.payment_detail.service_cost.payment_type,
                current_amount: current_amount?.collected_amount.service.current_amount,
                current_mechanism: current_amount?.collected_amount.service.current_mechanism
              },
              medicine: {
                current_amount: current_amount?.collected_amount.medicine.current_amount,
                actual_amount: parseFloat(actual_amount.medicine_amount),
                current_mechanism: current_amount?.collected_amount.medicine.current_mechanism,
                actual_mechanism: actual_amount.payment_detail.medicine_cost.payment_type
              }
            }

          default:
            const result = await dbConnections().main.manager.transaction(async transactionalEntityManager => {
              const updateResult = await transactionalEntityManager
                .createQueryBuilder()
                .update("care_calendar_classification")
                .set({ active: ENUMS.record_statuses.INACTIVE })
                .where("care_calendar_id = :care_calendar_id AND classifier_id = :classifier_id", {
                  care_calendar_id,
                  classifier_id: 2000000482
                })
                .execute()
              let medicine
              const insertResult = await transactionalEntityManager
                .createQueryBuilder()
                .insert()
                .into("care_calendar_classification")
                .values({
                  care_calendar_id: care_calendar_id,
                  classifier_id: 2000000482,
                  value_json: collected_amount
                })
                .execute()

              return { success: true }
            })
            return result
        }
      }

      throw new BadRequest("invalid task status")
    }
  })
  app.use(
    "v2/tasks/service-document/pdf",
    {
      async get(care_calendar_id) {
        try {
          if (existsSync("./.cache")) await rm("./.cache", { recursive: true })
          const response = await getServiceDocumentData(care_calendar_id)
          const htmlString = getServiceDocumentHTML({ data: response, userLanguage: "en", krushalLogo: kurshal_image })
          const browser = global.browser
          const page = await browser.newPage()
          await page.setViewport({ width: 2560, height: 1600 })
          await page.emulateMediaType("print")
          await page.setContent(htmlString, { waitUntil: "networkidle0" })

          const pdfBuffer = await page.pdf({
            format: "A4",
            preferCSSPageSize: true,
            printBackground: true,
            displayHeaderFooter: true,
            footerTemplate: "<div></div>",
            emulateMediaType: "print"
          })

          await page.close()
          const timeStamp = new Date().getTime()
          const fileName = (response.task.ticket_no || "") + "-" + timeStamp + ".pdf"
          const path = `./.cache/${fileName}`
          if (!existsSync("./.cache")) mkdirSync("./.cache")
          writeFileSync(path, pdfBuffer)

          const existsCheck = await dbConnections()
            .main.manager.createQueryBuilder()
            .select("*")
            .from("document", "d")
            .innerJoin("care_calendar", "cc", "cc.care_calendar_id = d.entity_1_entity_uuid")
            .where("cc.care_calendar_id = :care_calendar_id", { care_calendar_id })
            .andWhere("d.document_type_id = :document_type_id", { document_type_id: 1000260026 })
            .andWhere(
              `(
              d.document_information->>'key' IS NULL
              OR d.document_information->>'url' IS NULL
              OR d.document_information->>'uri' IS NULL
              )`
            )
            .getRawOne()

          if (existsCheck) {
            const s3 = new S3Utils()
            const key = await s3.s3Uploader({ originalname: fileName, path }, "ah", "task", "pdf")
            const result = await dbConnections()
              .main.manager.createQueryBuilder()
              .update("main.document")
              .set({
                document_information: {
                  key: key,
                  url: key,
                  uri: key,
                  ...existsCheck.document_information
                }
              })
              .where("entity_1_entity_uuid = :care_calendar_id", { care_calendar_id })
              .execute()
          }

          return Readable.from(pdfBuffer)
        } catch (error) {
          return new GeneralError(error)
        }
      }
    },
    pipeStreamForGet
  )
  app.use(
    "v2/tasks/rx/pdf",
    {
      async find(params) {
        try {
          if (existsSync("./.cache")) await rm("./.cache", { recursive: true })
          const s3 = new S3Utils()
          const activity = new LibActivityService()
          const { language, farmer_id, calendar_id } = params.query
          const _params = {
            ...params,
            route: {
              object: "by-id"
            }
          }

          const farmer_summary = await costSummaryHTML.get(farmer_id, params)
          const total_savings_html_strip = farmer_summary.total_savings
          const farmer_cost_summary_html = farmer_summary.htmlString

          const activityData = await activity.find(_params)
          const { vet_signature_doc_info } = activityData
          const vetSignBuffer = vet_signature_doc_info && vet_signature_doc_info.url ? await s3.getS3ObjectAsBase64(vet_signature_doc_info.url) : ""
          const rxHTML = getPrescriptionHTML(activityData, language, vetSignBuffer, total_savings_html_strip)
          const finalHTML = farmer_cost_summary_html ? rxHTML.concat(farmer_cost_summary_html) : rxHTML

          const browser = global.browser
          const page = await browser.newPage()
          await page.setViewport({ width: 2560, height: 1600 })
          await page.emulateMediaType("print")
          await page.setContent(finalHTML, { waitUntil: "networkidle0" })

          const pdfBuffer = await page.pdf({
            format: "A4",
            preferCSSPageSize: true,
            printBackground: true,
            displayHeaderFooter: true,
            footerTemplate: "<div></div>",
            emulateMediaType: "print"
          })

          await page.close()
          const timeStamp = new Date().getTime()
          const fileName = (calendar_id || "") + "-" + timeStamp + ".pdf"
          const path = `./.cache/${fileName}`
          if (!existsSync("./.cache")) mkdirSync("./.cache")
          writeFileSync(path, pdfBuffer)

          const existsCheck = await dbConnections()
            .main.manager.createQueryBuilder()
            .select("*")
            .from("document", "d")
            .innerJoin("care_calendar", "cc", "cc.care_calendar_id = d.entity_1_entity_uuid")
            .where("cc.care_calendar_id = :calendar_id", { calendar_id })
            .andWhere(
              `(
              d.document_type_id = 1000260015 AND
              d.active  = ${record_statuses.ACTIVE}
              )`
            )
            .getRawOne()

          if (existsCheck) {
            const result = await dbConnections()
              .main.manager.createQueryBuilder()
              .update("main.document")
              .set({
                active: record_statuses.INACTIVE
              })
              .where("document_id = :document_id", { document_id: existsCheck.document_id })
              .execute()
          }
          const key = await s3.s3Uploader({ originalname: fileName, path }, "ah", "task", "application/pdf")
          const result = await dbConnections()
            .main.manager.createQueryBuilder()
            .insert()
            .into("main.document")
            .values([
              {
                entity_1_type_id: 1000220003,
                entity_1_entity_uuid: calendar_id,
                document_type_id: 1000260015,
                document_name: "activity_prescription_pdf",
                document_information: {
                  key: key,
                  url: key,
                  uri: key,
                  fileName,
                  type: "application/pdf",
                  contentType: "application/pdf"
                }
              }
            ])
            .execute()
          return Readable.from(pdfBuffer)
        } catch (error) {
          return new GeneralError(error)
        }
      }
    },
    pipeStreamForGet
  )
  app.use(
    "v2/tasks",
    {
      async find(params) {
        try {
          const { farmer_id, animal_id } = params.query
          if (farmer_id == undefined && animal_id == undefined) {
            throw new Error("parameter is missing.")
          }
          const options = { farmer_id, animal_id }

          const tasks = await getTasks(options)

          return {
            result: tasks
          }
        } catch (error) {
          errorLog("error in FIND: 'v2/tasks'", params, { message: error.message })
          return new BadRequest(error).toJSON()
        }
      },

      async create(data, params) {
        if (data.staff_id) {
          let finance = new GetFinance()
          let balance = await finance.get(data.staff_id)
          if (balance.balance < MIN_BALANCE) {
            return new BadRequest({ message: "cannot add SR , Balance is low" })
          }
        }
        let response = await createTaskv2(data, params.headers.token)

        response.fcms = await sendNotificationsByCustomerIdV2({ calendar_id: response.id })
        return response
      }
    },
    tokenInject,
    handleServerError
  )

  // fetches farmer's animal list ( via a FARMER-ID)
  // change to v2/customer/:customer_id/animals/:animal_id
  app.use(
    "v2/customers/:customer/animals",
    {
      async find(params) {
        try {
          const { customer } = params.route
          const options = { customer_id: customer }
          const animals = await getAnimalsByFarmerID(options)
          return {
            result: animals
          }
        } catch (error) {
          errorLog("error in GET: 'v2/animal'", params, { message: error.message })
          return new GeneralError(error).toJSON()
        }
      }
    },
    tokenInject,
    handleServerError
  )

  //fetches all the information of the task ( via a CARE-CALENDAR-ID )
  app.use(
    "v2/activity",
    {
      async get(id, params) {
        try {
          const options = { care_calender_id: id }
          const activity = await getActivityByIdV2(options)
          return {
            result: activity
          }
        } catch (error) {
          errorLog("error in GET:'v2/activity'", params, { message: error.message })
          return new GeneralError(error).toJSON()
        }
      }
    },
    tokenInject,
    handleServerError
  )

  app.use(
    "v2/paravet-card",
    {
      async get(id, params) {
        try {
          const options = { paravet_id: id }
          const card = await getParavetCard(options)
          return {
            result: card
          }
        } catch (error) {
          errorLog("error in GET: 'v2/paravet-card'", params, { message: error.message })
          return new GeneralError(error).toJSON()
        }
      }
    },
    tokenInject,
    handleServerError
  )

  app.use(
    "v2/vet-stats",
    {
      async create(data, params) {
        try {
          const { user_id, user_type } = params.headers.token
          let options = {
            user_id: user_id,
            all_stats: true,
            stats_by_user: true,
            stats_by_staff_type: false,
            number_of_days: 15,
            IS_POSTGRES: 1
          }

          let paravet_card = undefined
          let dashboard = undefined

          const { summary_type } = data

          switch (summary_type) {
            case "all":
              dashboard = await getVetCard(options)
              paravet_card = await getVetCard({
                ...options,
                all_stats: false,
                stats_by_user: false,
                stats_by_staff_type: true,
                staff_type: PPU_PARAVET
              })

              return {
                vet_dashboard: dashboard,
                paravets: paravet_card
              }
            case "by_staff":
              options = { ...options, all_stats: false, stats_by_staff_type: true, staff_type: PPU_PARAVET }
              paravet_card = await getVetCard(options)
              return {
                paravets: paravet_card
              }
            default:
              dashboard = await getVetCard(options)
              return {
                vet_dashboard: dashboard
              }
          }
        } catch (error) {
          errorLog("error in GET: 'v2/vet-card'", params, { message: error.message })
          return new GeneralError(error).toJSON()
        }
      }
    },
    tokenInject,
    handleServerError
  )

  app.use(
    "v2/route-plan",
    {
      async create(data, params) {
        try {
          const { user_id, user_type } = params.headers.token
          let result = await getRoutePlanV2(data, user_id, user_type)

          return { result }
        } catch (error) {
          errorLog("error in CREATE: 'V2/route-plan'", params, { message: error.message })
          return new GeneralError(error).toJSON()
        }
      }
    },
    tokenInject,
    handleServerError
  )

  app.use(
    "v2/animal/enroll",
    {
      //may need to change some logic in this api
      async create(data) {
        try {
          const cattle = new CattleV2()

          //association of freelancer_paravet with animal

          return await cattle.enrollCattle(data)
        } catch (error) {
          return new GeneralError(error).toJSON()
        }
      }
    },
    tokenInject,
    handleServerError
  )

  app.use(
    "v2/farmer/enroll",
    {
      async create(data) {
        try {
          let result = await enrollFarmerToFreelanceParavet(data)
          if (result) {
            return result
          } else {
            return new GeneralError("Could not enroll farmer.")
          }
        } catch (error) {
          return new BadRequest(error)
        }
      }
    },
    tokenInject,
    handleServerError
  )

  app.use(
    "v2/freelancer/stats",
    {
      async create(data) {
        const stats = await getParavetPreventiveDashboard(data)
        const response = {
          preventive: {
            open: stats[0].open
          },

          filters: {
            activity_category: 1000270001,
            start_date: stats[0].start_date,
            end_date: stats[0].end_date
          }
        }
        return response
      }
    },
    tokenInject,
    handleServerError
  )

  app.use(
    "v2/freelancer-training",
    {
      async create(data, params) {
        try {
          const { page_number, page_limit } = data
          const result = await dbConnections()
            .main.manager.createQueryBuilder()
            .select("document_information")
            .from("main.document")
            .where("document_type_id = :document_type_id and active= :active", { document_type_id: FREELANCER_TRAINING_VIDEOS, active: ACTIVE })
            .offset((page_number - 1) * page_limit)
            .limit(page_limit)
            .execute()

          return result
        } catch (error) {
          errorLog("error in CREATE: 'V2/route-plan'", params, { message: error.message })
          return new GeneralError(error).toJSON()
        }
      }
    },
    tokenInject,
    handleServerError
  )

  app.use(
    "v2/animal",
    {
      async create(data, params) {
        try {
          const cattle = new CattleV2()
          let result = await cattle.registerCattleV2(data)
          return result
        } catch (error) {
          errorLog("error in CREATE: 'v2/animal'", data, { message: error.message })
          return new GeneralError(error, {
            statusCode: 400
          }).toJSON()
        }
      }
    },
    tokenInject,
    handleServerError
  )

  app.use(
    "v2/animals/animal-classification",
    {
      async get(animal_id) {
        try {
          const query = `
          SELECT
          REF_REFERENCE.reference_id,
          REF_REFERENCE.reference_name,
          AC.animal_classification_id,
          DOCUMENT.document_id,
          DOCUMENT.document_information,
          DOCUMENT.updated_at
        FROM
          MAIN.REF_REFERENCE
          LEFT JOIN MAIN.ANIMAL ON ANIMAL.ANIMAL_ID = :animal_id
          AND ANIMAL.ACTIVE = ${record_statuses.ACTIVE}
          LEFT JOIN MAIN.ANIMAL_CLASSIFICATION AS AC ON AC.ANIMAL_ID = ANIMAL.ANIMAL_ID
          AND AC.CLASSIFIER_ID = REF_REFERENCE.REFERENCE_ID
          AND AC.ACTIVE = ${record_statuses.ACTIVE}
          LEFT JOIN MAIN.DOCUMENT ON DOCUMENT.ENTITY_1_TYPE_ID = 1000220004
          AND DOCUMENT.ENTITY_1_ENTITY_UUID = AC.ANIMAL_CLASSIFICATION_ID
        WHERE
          REFERENCE_ID IN (:...classifier_ids)
          `
          const classifier_ids = [classifiers.BODY_SCORE_OBSERVATION_1, classifiers.BODY_SCORE_OBSERVATION_3, classifiers.BODY_SCORE_OBSERVATION_4, classifiers.UDDER_CLEFT, classifiers.UDDER_DEPTH, classifiers.UDDER_BALANCE, classifiers.UDDER_TEAT_PLACEMENT, classifiers.UDDER_TEAT_LENGTH, classifiers.ANIMAL_FOREIGN_BODY_PRESENCE_AREA, classifiers.ANIMAL_LOCOMOTION_SCORE, classifiers.HORNS_EXIST, classifiers.SPREAD_OF_PATCHES, classifiers.STRETCHABILITY_OF_UDDER, classifiers.VISIBILITY_OF_VEINS, classifiers.ADDITIONAL_TEAT, classifiers.GAP_IN_TEETH]
          const data = await dbConnections()
            .main.manager.createQueryBuilder()
            .from(`(${query})`)
            .setParameters({ animal_id, classifier_ids })
            .execute()

          const obj = {}
          let info = {
            animal_classification_id: null,
            documents: [],
            classifier_name: null
          }
          for (let index = 0; index < data.length; index++) {
            const element = data[index]
            const document = {
              document_information: element.document_information,
              document_id: element.document_id,
              updated_at: element.updated_at
            }

            if (obj[element.reference_id]) {
              obj[element.reference_id].documents.push(document)
            } else {
              obj[element.reference_id] = info
              info.documents.push(document)
              info.classifier_name = element.reference_name
              info.animal_classification_id = element.animal_classification_id
            }

            info = {
              animal_classification_id: null,
              documents: [],
              classifier_name: null
            }
          }

          return obj
        } catch (error) {
          return new GeneralError(error, {
            statusCode: 400
          }).toJSON()
        }
      }
    },
    tokenInject,
    handleServerError
  )

  app.use(
    "v2/tasks/activity/save-form-data/:activity_id",
    {
      async create(data, params) {
        try {
          const { activity_id } = params.route
          const { token } = params.headers
          return await ActivityFormControl(activity_id).update(data, token)
        } catch (err) {
          return new GeneralError(err).toJSON()
        }
      }
    },
    tokenInject,
    handleServerError
  )
  app.use(
    "v2/tasks/activity/get-form-data/",
    {
      async get(id, params) {
        try {
          const { care_calendar } = params.query
          const { token } = params.headers
          const activityFormControl = ActivityFormControl(id)
          return await activityFormControl.get(care_calendar)
        } catch (err) {
          return new GeneralError(err).toJSON()
        }
      },

      async create(data, params) {
        try {
          const { token } = params.headers
          const activity_id = data.activity_id
          const activityFormControl = ActivityFormControl(activity_id)
          return await activityFormControl.get(data, token)
        } catch (err) {
          return new GeneralError(err).toJSON()
        }
      }
    },
    tokenInject,
    handleServerError
  )
  app.use(
    "v2/tasks/activity/update/",
    {
      async create(data, params) {
        try {
          const { token } = params.headers
          return await updateActivity_V2(data, token)
        } catch (err) {
          return new GeneralError(err).toJSON()
        }
      }
    },
    tokenInject,
    handleServerError
  )
}

module.exports = { servicesV2 }
