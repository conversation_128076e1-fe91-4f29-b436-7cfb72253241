const { ReloadReferenceData, AllAnimalsFeedRecommendations, HealthScore, AnimalClassiferDataWithDocuments, ClassificationsData, AnimalFeedsData } = require('./animalFeeds.class')

const { SmartRationDocument, SmartRationReportList, PerCustomerLatestSmartRationReport, SetAssetDetails } = require('./smart-ration.class')
const auth = require('../../middleware/auth')
const tokenInject = require('../../middleware/tokenInject')

const configureAnimalFeeds = (app) => {
  // app.use(auth)
  // app.use('/animalFeeds/calculateConcentrates', new CalculateConcentrates())
  // app.use('/animalFeeds/getConcentrates', new CalculateDailyFeeds())
  // app.use('/animalFeeds/calculateAllAnimalFeeds', new CalculateAllAnimalFeeds())
  app.use('/animalFeeds/reloadReferenceData', new ReloadReferenceData(), tokenInject)
  app.use('/animalFeeds/allAnimalsFeedRecommendations', new AllAnimalsFeedRecommendations(), tokenInject)
  app.use('/animalFeeds/healthScore', new HealthScore(), tokenInject)
  app.use('/animalFeeds/animalClassiferDataWithDocuments', new AnimalClassiferDataWithDocuments(), tokenInject)
  app.use('/animalFeeds/classificationsData', new ClassificationsData(), tokenInject)
  app.use('/animalFeeds/animalFeedsData', new AnimalFeedsData(), tokenInject)
  app.use('/v2/sr/generate-smart-ration-report-as-pdf', new SmartRationDocument(), tokenInject)
  app.use('/smart-ration-list', new SmartRationReportList(), tokenInject)
  app.use('/per-customer-latest-smart-ration-list', new PerCustomerLatestSmartRationReport(), tokenInject)
  app.use('/set-report-attributes', new SetAssetDetails(), tokenInject)
  app.route('/animalFeeds/calculateMonthlyFeeds').post(new AllAnimalsFeedRecommendations().routeCall(), tokenInject)
  app.route('/animalFeeds/calculateActualNutrients').post(new AllAnimalsFeedRecommendations().routeCall(), tokenInject)
  app.route('/animalFeeds/saveSmartFeedsData').post(new AllAnimalsFeedRecommendations().routeCall(), tokenInject)
  app.route('/animalFeeds/generateCsvFile').post(new AllAnimalsFeedRecommendations().routeCall(), tokenInject)
  app.route('/animalFeeds/solveForAnimal').post(new AllAnimalsFeedRecommendations().routeCall(), tokenInject)
  app.route('/animalFeeds/normalizeAnimalData').post(new AllAnimalsFeedRecommendations().routeCall(), tokenInject)
  app.route('/animalFeeds/calculateNutrientRequirements').post(new AllAnimalsFeedRecommendations().routeCall(), tokenInject)
  app.route('/animalFeeds/test').post(new AllAnimalsFeedRecommendations().routeCall(), tokenInject)
}
module.exports = { configureAnimalFeeds }
