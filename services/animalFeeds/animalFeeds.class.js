/* eslint-disable no-unused-vars */
/* eslint-disable eqeqeq */
/* eslint-disable camelcase */
const { LibHealthScore, saveClassifications, LibAnimalFeeds, LibSmartFeed } = require("@krushal-it/back-end-lib")
const { extractBasedOnLanguage } = require("@krushal-it/common-core")
const moment = require("moment")

const _ = require("lodash")
// const lpsolve = require('lp_solve')
const solver = require("javascript-lp-solver/src/solver")
const { References2Lib } = require("@krushal-it/back-end-lib")
const fs = require("fs")
const { Parser } = require("json2csv")
let feedMasterData = require("./feedMaster.js")
const { normalize } = require("path")
const { nextTick } = require("process")
const { de, sw } = require("translate-google/languages.js")
const { dbConnections } = require("@krushal-it/ah-orm")
feedMasterData = convertBooleanAndNumericValuesDeep(feedMasterData.feedMaster)
// let [foliageReferenceData, concentratesReferenceData, otherNutrientReferenceData] = [[], [], []]

//  [
//   '_readableState',   '_events',
//   '_eventsCount',     '_maxListeners',
//   'socket',           'httpVersionMajor',
//   'httpVersionMinor', 'httpVersion',
//   'complete',         'rawHeaders',
//   'rawTrailers',      'joinDuplicateHeaders',
//   'aborted',          'upgrade',
//   'url',              'method',
//   'statusCode',       'statusMessage',
//   'client',           '_consuming',
//   '_dumped',          'next',
//   'baseUrl',          'originalUrl',
//   '_parsedUrl',       'params',
//   'query',            'res',
//   'body',             '_body',
//   'length',           'feathers',
//   'route'
// ]
// switch (func) {
//   case 'calculateMonthlyFeeds':
//     this.calculateMonthlyFeeds(req.body).then((result) => {
//       res.send({ return_code: 0, result })
//     }).catch((error) => {
//       console.log('error', error)
//       res.send({ return_code: -1, message: error.message })
//     })
//     break
//   case 'test':
//     res.send({ return_code: 0, result: 'test' })
//     break
//   default :
//     res.send({ return_code: -1, message: 'No matching function found' })
// }

// if (func === 'test') {
//   res.send({ return_code: 0, result: 'test' })
// }
// console.log('called with function', func)
// if (routeFuncs[func]) {
//   routeFuncs[func](req.body).then((result) => {
//     res.send({ return_code: 0, result })
//   }).catch((error) => {
//     console.log('error', error)
//     res.send({ return_code: -1, message: error.message })
//   })
// }

const loadFeedReferenceData = async () => {
  // const category_id_array = [10001300, 10013000]
  try {
    const referenceLib = new References2Lib()
    let data = await referenceLib.create({ category_id_array: [10001300] })
    let foliageReferenceData = convertBooleanAndNumericValuesDeep(data.data[0].list_data_l10)
    foliageReferenceData = foliageReferenceData.filter(foliage => foliage?.reference_information?.feed_properties?.used === true)
    foliageReferenceData.forEach(foliage => {
      const properties = foliage.reference_information.feed_properties || {}
      Object.keys(properties).forEach(propKey => {
        foliage[propKey] = properties[propKey]
      })
    })
    // removed with used = false
    foliageReferenceData = foliageReferenceData.filter(foliage => foliage.used === true)
    // console.log('animalFeedClass.loadFoliages foliageReferenceData', foliageReferenceData)
    data = await referenceLib.create({ category_id_array: [10013000] })
    let concentratesReferenceData = convertBooleanAndNumericValuesDeep(data.data[0].list_data_l10)
    concentratesReferenceData = concentratesReferenceData.filter(concentrate => concentrate?.reference_information?.feed_properties?.used === true)
    concentratesReferenceData.forEach(concentrate => {
      const properties = concentrate.reference_information.feed_properties || {}
      Object.keys(properties).forEach(propKey => {
        concentrate[propKey] = properties[propKey]
      })
    })
    // Any additional cleanup on foliages and concentrates
    // removed with used = false
    concentratesReferenceData = concentratesReferenceData.filter(concentrate => concentrate.used === true && concentrate.type === "readymade")
    // console.log('animalFeedClass.loadConcentrates concentratesReferenceData', concentratesReferenceData)
    data = await referenceLib.create({ category_id_array: [10013005] })
    let otherNutrientReferenceData = convertBooleanAndNumericValuesDeep(data.data[0].list_data_l10)
    otherNutrientReferenceData = otherNutrientReferenceData.filter(otherNutrient => otherNutrient?.reference_information?.feed_properties?.used === true)
    otherNutrientReferenceData.forEach(otherNutrient => {
      const properties = otherNutrient.reference_information.feed_properties || {}
      Object.keys(properties).forEach(propKey => {
        otherNutrient[propKey] = properties[propKey]
      })
    })
    // Any additional cleanup on foliages and concentrates
    // removed with used = false
    otherNutrientReferenceData = otherNutrientReferenceData.filter(otherNutrient => otherNutrient.used === true)
    return { foliageReferenceData, concentratesReferenceData, otherNutrientReferenceData }
  } catch (error) {
    console.log("animalFeedClass.loadReferences error", error)
  }
}
const loadRefData = async ({ foliageReferenceData, concentratesReferenceData, otherNutrientReferenceData }) => {
  if (!foliageReferenceData || !concentratesReferenceData || !otherNutrientReferenceData || !foliageReferenceData?.length || !concentratesReferenceData?.length || !otherNutrientReferenceData?.length) {
    const refData = await loadFeedReferenceData()
    foliageReferenceData = refData.foliageReferenceData
    concentratesReferenceData = refData.concentratesReferenceData
    otherNutrientReferenceData = refData.otherNutrientReferenceData
  }
  return { foliageReferenceData, concentratesReferenceData, otherNutrientReferenceData }
}

class AllAnimalsFeedRecommendations {
  routeCall() {
    const routeFuncs = {
      calculateMonthlyFeeds: this.calculateMonthlyFeeds,
      calculateActualNutrients: this.calculateActualNutrients,
      saveSmartFeedsData: this.saveSmartFeedsData,
      generateCsvFile: this.generateCsvFile,
      solveForAnimal: this.solveForAnimal,
      normalizeAnimalData: this.normalizeAnimalData,
      calculateNutrientRequirements: this.calculateNutrientRequirements
    }
    return async (req, res) => {
      console.log("animalFeeds/allAnimalsFeedRecommendations/*", Object.keys(req))
      console.log("animalFeeds/allAnimalsFeedRecommendations/*", req.body)
      const func = req.route.path.replace("/animalFeeds/", "")
      try {
        console.log("animalFeeds/allAnimalsFeedRecommendations/", func)
        if (!routeFuncs[func]) {
          throw new Error("No matching function found")
        }
        const data = await routeFuncs[func](req.body)
        res.send({ return_code: 0, data })
      } catch (error) {
        console.log("error", error)
        res.send({ return_code: -1, data: {}, message: error.message })
      }
    }
  }

  calculateNutrientRequirements({ animal }) {
    animal = this.normalizeAnimalData({ animal })
    try {
      let matchingDmConsts = feedMasterData.cow_bw_dcp_tdn_req.dm.animals.filter(dmConst => {
        return animal.number_of_months_pregnant_1 === dmConst.number_of_months_pregnant_1 && animal.number_of_months_since_last_calving === dmConst.number_of_months_since_last_calving
      })
      if (!matchingDmConsts || matchingDmConsts.length === 0) {
        matchingDmConsts = feedMasterData.cow_bw_dcp_tdn_req.dm.animals
      }
      // console.log('animalFeedClass.AnimalFeedRec:2 matchingDmConst', matchingDmConsts)
      let dm_min = (matchingDmConsts[0].minPercent * animal.animal_weight_1) / 100
      let dm_max = (matchingDmConsts[0].maxPercent * animal.animal_weight_1) / 100
      // add lactation allowance
      if (animal.milking && animal.animal_average_lpd_1 > 0) {
        dm_min = dm_min * 1.15
        dm_max = dm_max * 1.15
      }

      // const dm = (dm_min + dm_max) / 2
      let matchingDcpMaint = null
      matchingDcpMaint = feedMasterData.cow_bw_dcp_tdn_req.dcptdnMaint.filter(dcpMaint => {
        return animal.animal_weight_1 >= dcpMaint.animal_weight_1.min && animal.animal_weight_1 <= dcpMaint.animal_weight_1.max
      })
      if (!matchingDcpMaint) {
        matchingDcpMaint = [feedMasterData.cow_bw_dcp_tdn_req.dcptdnMaint[0]]
        //    return { return_code: -1, message: 'No matching DCP constant found' }
      }
      // console.log('animalFeedClass.AnimalFeedRec:3 matchingDcpMaint', matchingDcpMaint)
      const [dcpMaint, tdnMaint] = [matchingDcpMaint[0].dcp, matchingDcpMaint[0].tdn]
      let [dcpLpd, tdnLpd] = [0, 0]
      if (animal.milking) {
        let matchingDcpLpd = null
        matchingDcpLpd = feedMasterData.dcpTdnLactationLPD.filter(dcpLpd => {
          return animal.animal_milk_fat_1 >= dcpLpd.animal_milk_fat_1.min && animal.animal_milk_fat_1 <= dcpLpd.animal_milk_fat_1.max
        })
        if (!matchingDcpLpd) {
          matchingDcpLpd = feedMasterData.dcpTdnLactationLPD[0]
          //  return { return_code: -1, message: 'No matching DCP constant found' }
        }
        // console.log('animalFeedClass.AnimalFeedRec:4 matchingDcpLpd', matchingDcpLpd)
        dcpLpd = matchingDcpLpd[0].dcp * animal.animal_average_lpd_1
        tdnLpd = matchingDcpLpd[0].tdn * animal.animal_average_lpd_1
      }
      // add pregnancy allowance
      let [dcpPreg, tdnPreg] = [0, 0]
      if (animal.number_of_months_pregnant_1) {
        let matchingDcpPreg = null
        matchingDcpPreg = feedMasterData.dcpTdnPregnancy.filter(dcpPreg => {
          return animal.number_of_months_pregnant_1 >= dcpPreg.number_of_months_pregnant_1.min && animal.number_of_months_pregnant_1 <= dcpPreg.number_of_months_pregnant_1.max
        })
        if (!matchingDcpPreg) {
          matchingDcpPreg = feedMasterData.dcpTdnPregnancy[0]
          //  return { return_code: -1, message: 'No matching DCP constant found' }
        }
        // console.log('animalFeedClass.AnimalFeedRec:4 matchingDcpPreg', matchingDcpPreg)
        dcpPreg = matchingDcpPreg[0].dcp
        tdnPreg = matchingDcpPreg[0].tdn
      }

      const [dcp, tdn] = [dcpMaint + dcpLpd + dcpPreg, tdnMaint + tdnLpd + tdnPreg]
      const nutrients = { dm_min, dm_max, dcp, tdn, dcpMaint, dcpLpd, dcpPreg, tdnMaint, tdnLpd, tdnPreg }
      return { nutrients, solution: { code: 0, message: "Nutrients calculated" } }
    } catch (error) {
      console.log("animalFeedClass.AnimalFeedRec:5 error", error)
      // no nutrients for this animal do not compute feeds for this animal
      const nutrients = { dm_min: 0, dm_max: 0, dcpMaint: 0, dcpLpd: 0, dcpPreg: 0, dcp: 0, tdnMaint: 0, tdnLpd: 0, tdnPreg: 0, tdn: 0 }
      // animal.solution = { code: -1, message: error.message }
      return { nutrients, solution: { code: -1, message: error.message } }
    }
  }

  normalizeAnimalData({ animal }) {
    try {
      animal.number_of_months_pregnant_1 = animal.number_of_months_pregnant_1 ? animal.number_of_months_pregnant_1 : 0
      animal.number_of_months_pregnant_1 = animal.number_of_months_pregnant_1 > 9 ? 9 : animal.number_of_months_pregnant_1
      // if no calving months then set it to 0 as of date to today
      console.log("normalizing animal.months_since_last_calving_1", animal?.months_since_last_calving_1, "animal.month_since_calving_as_of_date_1", animal?.month_since_calving_as_of_date_1)
      if (animal?.months_since_last_calving_1) {
        if (animal?.month_since_calving_as_of_date_1) {
          // update number_of_months_since_last_calving  to months_since_last_calving_1 + date difference of month_since_calving_as_of_date_1 and today
          // also update months_since_last_calving_as_of_date_1 to today so that it is not recalculated
          animal.months_since_last_calving_1 = animal.months_since_last_calving_1 + moment().diff(moment(animal.month_since_calving_as_of_date_1), "months")
          animal.month_since_calving_as_of_date_1 = new Date()
        } else {
          // since as of date is not there so set it to today and months_since_last_calving_1 to as is
          animal.month_since_calving_as_of_date_1 = new Date()
        }
      } else {
        animal.months_since_last_calving_1 = 0
        animal.month_since_calving_as_of_date_1 = new Date()
      }
      animal.months_since_last_calving_1 = animal.months_since_last_calving_1 > 9 ? 9 : animal.months_since_last_calving_1 < 0 ? 0 : animal.months_since_last_calving_1
      // this is normalized to 0 to 9 months and this wont be recalculated
      animal.number_of_months_since_last_calving = animal.months_since_last_calving_1
      console.log("normalizing animal.number_of_months_since_last_calving", animal.number_of_months_since_last_calving, "animal.month_since_calving_as_of_date_1", animal.month_since_calving_as_of_date_1)
      animal.animal_milk_fat_1 = animal.animal_average_lpd_1 > 0 ? animal.animal_milk_fat_1 : 0
      animal.animal_milk_fat_1 = animal.animal_milk_fat_1 || 0
      animal.animal_weight_1 = animal.animal_weight_1 > 800 ? 800 : animal.animal_weight_1
      animal.animal_milk_fat_1 = animal.animal_milk_fat_1 > 7 ? 7 : animal.animal_milk_fat_1
      if (isNaN(animal.animal_milk_fat_1) || animal.animal_milk_fat_1 === null || animal.animal_milk_fat_1 === undefined || animal.animal_milk_fat_1 === "" || animal.animal_milk_fat_1 < 2 || animal.animal_milk_fat_1 > 6) {
        animal.animal_milk_fat_1 = 3.5
      }
      animal.milking = animal.animal_average_lpd_1 > 0
      animal.pregnancy = animal.number_of_months_pregnant_1 > 0
      let highYeilder = animal.animal_average_lpd_1 > 20
      if (animal.animal_weight_1 < 475) {
        highYeilder = animal.animal_average_lpd_1 >= 15
      }
      animal.yeilder = highYeilder ? "highYeilder" : "lowYeilder"
      return animal
    } catch (error) {
      console.log("animalFeedClass.normalizeAnimalData error", error)
      return animal
    }
  }

  async calculateMonthlyFeeds({ animalDailyFeeds, foliageReferenceData = null, concentratesReferenceData = null, otherNutrientReferenceData = null }) {
    const monthlyFeeds = { foliages: [], concentrates: [], otherNutrients: [] }
    const refData = await loadRefData({ foliageReferenceData, concentratesReferenceData, otherNutrientReferenceData })
    foliageReferenceData = refData.foliageReferenceData
    concentratesReferenceData = refData.concentratesReferenceData
    otherNutrientReferenceData = refData.otherNutrientReferenceData
    let monthlyCost = 0
    for (const [animal_id, animalFeeds] of Object.entries(animalDailyFeeds)) {
      if (animalFeeds.solution.code !== 0) {
        continue
      }
      const { foliagesPerDay, concentratesPerDay, otherNutrientsPerDay } = animalFeeds
      foliagesPerDay.forEach(foliage => {
        foliage = _.cloneDeep(foliage)
        foliage.quantity = foliage.quantity * 30
        const monthlyFoliage = monthlyFeeds.foliages.find(mFoliage => mFoliage.reference_id === foliage.reference_id)
        monthlyFoliage ? (monthlyFoliage.quantity = monthlyFoliage.quantity + foliage.quantity) : monthlyFeeds.foliages.push(foliage)
      })
      concentratesPerDay.forEach(concentrate => {
        concentrate = _.cloneDeep(concentrate)
        concentrate.quantity = concentrate.quantity * 30
        const monthlyConcentrate = monthlyFeeds.concentrates.find(mConcentrate => mConcentrate.reference_id === concentrate.reference_id)
        monthlyConcentrate ? (monthlyConcentrate.quantity = monthlyConcentrate.quantity + concentrate.quantity) : monthlyFeeds.concentrates.push(concentrate)
      })
      otherNutrientsPerDay.forEach(otherNutrient => {
        otherNutrient = _.cloneDeep(otherNutrient)
        otherNutrient.quantity = otherNutrient.quantity * 30
        const monthlyOtherNutrient = monthlyFeeds.otherNutrients.find(monthlyOtherNutrient => monthlyOtherNutrient.reference_id === otherNutrient.reference_id)
        monthlyOtherNutrient ? (monthlyOtherNutrient.quantity = monthlyOtherNutrient.quantity + otherNutrient.quantity) : monthlyFeeds.otherNutrients.push(otherNutrient)
      })
      for (const foliage of monthlyFeeds.foliages) {
        // find matching foliage from reference data
        const refFoliage = foliageReferenceData.find(refFoliage => refFoliage.reference_id === foliage.reference_id)
        monthlyCost = monthlyCost + refFoliage.pricePkg * foliage.quantity
      }
      for (const concentrate of monthlyFeeds.concentrates) {
        // find matching concentrate from reference data
        const refConcentrate = concentratesReferenceData.find(refConcentrate => refConcentrate.reference_id === concentrate.reference_id)
        monthlyCost = monthlyCost + refConcentrate.pricePkg * concentrate.quantity
      }
      for (const otherNutrient of monthlyFeeds.otherNutrients) {
        // find matching otherNutrient from reference data
        const refOtherNutrient = otherNutrientReferenceData.find(refOtherNutrient => refOtherNutrient.reference_id === otherNutrient.reference_id)
        monthlyCost = monthlyCost + refOtherNutrient.pricePkg * otherNutrient.quantity
      }
    }
    return { monthlyFeeds, monthlyCost }
  }

  async find(params) {
    console.log("animalFeeds/allAnimalsFeedRecommendations GET")
    return { return_code: 0 }
  }

  async create(data, params) {
    console.log("animalFeedClass.AnimalFeedRec:1 animalFeeds/allAnimalsFeedRecommendations")
    const { allAnimalsData, generateCsv, saveFeeds, useOnlyFarmerFeeds, user_device_id } = data
    console.log("animalFeedClass.AnimalFeedRec:1 called with generateCsv, saveFeeds, useOnlyFarmerFeeds, user_device_id,  customer_id ", generateCsv, saveFeeds, useOnlyFarmerFeeds, user_device_id, allAnimalsData.data.customer.customer_id)

    const returnResults = { animalDailyFeeds: {}, monthlyFeeds: {}, monthlyCost: 0, allMissedConstraints: [], user_device_id, missedConstraints: [] } // minearlMixturePricePkg, saltPricePkg, bypassFatPricePkg,monthlyOtherNutrients: [], monthlyFoliages: [], monthlyConcentrates: [], monthlyMineralMixtures: 0, monthlySalt: 0, monthlyBypassFat: 0,
    returnResults.monthlyFeeds = { foliages: [], concentrates: [], otherNutrients: [], return_code: -1, message: "Unknown server error" }
    for (const [animal_id, animal] of Object.entries(allAnimalsData.data.animals)) {
      returnResults.animalDailyFeeds[animal.animal_id] = {
        solution: { code: -1, message: "No feeds available" },
        totalFeedsData: { dm_min: 0, dm_max: 0, dcpMaint: 0, dcpLpd: 0, dcpPreg: 0, dcp: 0, tdnMaint: 0, tdnLpd: 0, tdnPreg: 0, tdn: 0 }
      }
    }
    try {
      let _customer_id = allAnimalsData.data.customer.customer_id
      let solvedAnimals = 0
      let { foliageReferenceData, concentratesReferenceData, otherNutrientReferenceData } = await loadFeedReferenceData()

      foliageReferenceData = _.cloneDeep(foliageReferenceData)
      concentratesReferenceData = _.cloneDeep(concentratesReferenceData)
      otherNutrientReferenceData = _.cloneDeep(otherNutrientReferenceData)

      // const minearlMixturePricePkg = feedMasterData.minearlMixturePricePkg
      // const saltPricePkg = feedMasterData.saltPricePkg
      // const bypassFatPricePkg = feedMasterData.bypassFatPricePkg

      // remove non readymade concentrates
      const farmer_foliage_available_1 = allAnimalsData.data.customer.farmer_foliage_available_1 || []
      const farmer_concentrates_available_1 = allAnimalsData.data.customer.farmer_concentrates_available_1 || []
      if (useOnlyFarmerFeeds) {
        foliageReferenceData = foliageReferenceData.filter(foliage => farmer_foliage_available_1.includes(foliage.reference_id))
        concentratesReferenceData = concentratesReferenceData.filter(concentrate => farmer_concentrates_available_1.includes(concentrate.reference_id))
        // check if foliage and concentrates are available after filtering else return
        if (foliageReferenceData.length === 0 || concentratesReferenceData.length === 0) {
          if (saveFeeds) {
            const saveResults = await this.saveSmartFeedsData({
              customer_id: allAnimalsData.data.customer.customer_id,
              feed_generation_type: useOnlyFarmerFeeds ? 1001500002 : 1001500001,
              smartFeedsData: returnResults,
              feedMasterData
            })
            console.log("animalFeedClass.AnimalFeedRec:7 saveResults", saveResults)
            for (const animalId of Object.keys(returnResults.animalDailyFeeds)) {
              if (Object.keys(saveResults?.animalFeedAssetIds).includes(animalId)) {
                returnResults.animalDailyFeeds[animalId].feedAssetId = saveResults?.animalFeedAssetIds[animalId]
              }
            }
            returnResults.monthlyFeeds.feedAssetId = saveResults?.monthlyFeedAssetId
          }
          returnResults.return_code = -1
          returnResults.message = "No usable foliages or concentrates available"
          return returnResults
        }
      }
      // reduce pricePKgUsed of folige and concentrates by 30 % if farmer has them available
      for (const foliage of foliageReferenceData) {
        const matchedFoliage = farmer_foliage_available_1.find(farmerFoliageReferenceId => farmerFoliageReferenceId === foliage.reference_id)
        if (matchedFoliage) {
          foliage.pricePKgUsed = foliage.pricePkg * 0.7
          // console.log('animalFeedClass.AnimalFeedRec:1 matchedFoliage', foliage.reference_name_l10n, 'reduced price by 30%')
        } else {
          foliage.pricePKgUsed = foliage.pricePkg
        }
      }
      for (const concentrate of concentratesReferenceData) {
        const matchedConcentrate = farmer_concentrates_available_1.find(farmerConcentrateReferenceId => farmerConcentrateReferenceId === concentrate.reference_id)
        if (matchedConcentrate) {
          // console.log('animalFeedClass.AnimalFeedRec:1 matchedConcentrate', concentrate.reference_name_l10n, 'reduced price by 30%')
          concentrate.pricePKgUsed = concentrate.pricePkg * 0.7
        } else {
          concentrate.pricePKgUsed = concentrate.pricePkg
        }
      }
      // no price reduction for other nutrients
      for (const otherNutrient of otherNutrientReferenceData) {
        otherNutrient.pricePKgUsed = otherNutrient.pricePkg
      }
      for (let [animal_id, animal] of Object.entries(allAnimalsData.data.animals)) {
        animal = this.normalizeAnimalData({ animal })
        const result = this.calculateNutrientRequirements({ animal })
        animal.solution = result.solution
        animal.nutrients = result.nutrients
        Object.entries(result.nutrients).forEach(([key, value]) => {
          animal[key] = value
        })
        allAnimalsData.data.animals[animal.animal_id] = animal
      }
      let allMissedConstraints = []
      foliageReferenceData.forEach(foliage => {
        foliage.greenVote = 0
        foliage.dryVote = 0
        foliage.concentrateVote = 0
        foliage.quantity = 0
      })
      concentratesReferenceData.forEach(concentrate => {
        concentrate.concentrateVote = 0
        concentrate.greenVote = 0
        concentrate.dryVote = 0
        concentrate.quantity = 0
      })

      for (const [animal_id, animal] of Object.entries(allAnimalsData.data.animals)) {
        try {
          const result = this.solveForAnimal({
            animal,
            feedMasterData,
            foliageReferenceData,
            concentratesReferenceData,
            otherNutrientReferenceData,
            useFoliages: [],
            useConcentrares: [],
            removedConstraint: allMissedConstraints,
            constraintOptimization: true,
            solutionWithNoOptionalConstraint: false
          })
          const { solution, foliagesPerDay, concentratesPerDay, otherNutrientsPerDay, missedConstraints, totalFeedsData, projectedLPD } = result // mineralMixturePerDay, saltPerDay, bypassFatPerDay,
          console.log("animalFeedClass.AnimalFeedRec:6 solution ", solution.code, "for animal", animal.animal_id)

          if (solution.code === 0) {
            solvedAnimals++
            animal.bestGreenFoliages = _.sortBy(
              foliagesPerDay.filter(foliage => foliage.green == true),
              ["quantity"]
            )
              .reverse()
              .slice(0, 3)
            animal.bestDryFoliages = _.sortBy(
              foliagesPerDay.filter(foliage => foliage.dry == true),
              ["quantity"]
            )
              .reverse()
              .slice(0, 2)
            animal.bestConcentrates = _.sortBy(concentratesPerDay, ["quantity"])
              .reverse()
              .slice(0, 2)
            allMissedConstraints = [...allMissedConstraints, ...missedConstraints]
            animal.bestGreenFoliages.forEach((foliage, index) => {
              if (index > 1) return
              const refFoliage = foliageReferenceData.find(refFoliage => refFoliage.reference_id === foliage.reference_id)
              refFoliage.greenVote = refFoliage.greenVote ? refFoliage.greenVote + 1 : 1
              refFoliage.quantity = refFoliage.quantity ? refFoliage.quantity + foliage.quantity : foliage.quantity
            })
            animal.bestDryFoliages.forEach((foliage, index) => {
              if (index > 0) return
              const refFoliage = foliageReferenceData.find(refFoliage => refFoliage.reference_id === foliage.reference_id)
              refFoliage.dryVote = refFoliage.dryVote ? refFoliage.dryVote + 1 : 1
              refFoliage.quantity = refFoliage.quantity ? refFoliage.quantity + foliage.quantity : foliage.quantity
            })
            animal.bestConcentrates.forEach((concentrate, index) => {
              if (index > 0) return
              const refConcentrate = concentratesReferenceData.find(refConcentrate => refConcentrate.reference_id === concentrate.reference_id)
              refConcentrate.concentrateVote = refConcentrate.concentrateVote ? refConcentrate.concentrateVote + 1 : 1
              refConcentrate.quantity = refConcentrate.quantity ? refConcentrate.quantity + concentrate.quantity : concentrate.quantity
            })
          } else {
            animal.bestGreenFoliages = []
            animal.bestDryFoliages = []
            animal.bestConcentrates = []
          }
        } catch (error) {
          console.log("animalFeedClass.AnimalFeedRec:12 error", error, "for animal ", animal.animal_id)
          animal.bestGreenFoliages = []
          animal.bestDryFoliages = []
          animal.bestConcentrates = []
        }
      }
      console.log("animalFeedClass.AnimalFeedRec:1 It solvedAnimals", solvedAnimals, "no solutions for ", Object.keys(allAnimalsData.data.animals).length - solvedAnimals, "animals")
      // now take a vote of all the foliages and concentrates and take the top 2 green foliages, top 1 dry foliage, and top 1 concentrate
      const bestGreenFoliages = _.sortBy(
        foliageReferenceData.filter(foliage => foliage.green),
        ["greenVote", "quantity"]
      )
        .reverse()
        .slice(0, 2)
      const bestDryFoliages = _.sortBy(
        foliageReferenceData.filter(foliage => foliage.dry),
        ["dryVote", "quantity"]
      )
        .reverse()
        .slice(0, 1)
      const bestConcentrates = _.sortBy(concentratesReferenceData, ["concentrateVote", "quantity"])
        .reverse()
        .slice(0, 1)
      console.log("animalFeedClass.AnimalFeedRec:6 bestGreenFoliages ", bestGreenFoliages.length, "bestDryFoliages", bestDryFoliages.length, "bestConcentrates", bestConcentrates.length)
      // take all the missed constraints and remove duplicates and solve again with fixed foliages and concentrates and removed constraints
      // allMissedConstraints = _.uniq(allMissedConstraints)
      // now run once with above foliages and concentrates to find optimal missed constraints
      allMissedConstraints = _.uniq(allMissedConstraints)

      let newAllMissedConstraints = [],
        _totalCurrentLPD = 0,
        _totalProjectedLPD = 0

      for (const [animal_id, animal] of Object.entries(allAnimalsData.data.animals)) {
        if (animal.nutrients.dm_min === 0) continue // no nutrients for this animal do not compute feeds for this animal
        try {
          const result = this.solveForAnimal({
            animal,
            feedMasterData,
            foliageReferenceData,
            concentratesReferenceData,
            otherNutrientReferenceData,
            useFoliages: [...bestGreenFoliages, ...bestDryFoliages],
            useConcentrares: bestConcentrates,
            removedConstraint: allMissedConstraints,
            constraintOptimization: true,
            solutionWithNoOptionalConstraint: false
          })
          const { solution, foliagesPerDay, concentratesPerDay, otherNutrientsPerDay, missedConstraints, totalFeedsData, projectedLPD } = result // mineralMixturePerDay, saltPerDay, bypassFatPerDay,
          console.log("animalFeedClass.AnimalFeedRec:6 solution ", solution.code, "for animal", animal.animal_id)
          if (solution.code === 0) {
            solvedAnimals++
            newAllMissedConstraints = [...newAllMissedConstraints, ...missedConstraints]
          }
        } catch (error) {
          console.log("animalFeedClass.AnimalFeedRec:12 error", error, "for animal ", animal.animal_id)
        }
      }
      console.log("animalFeedClass.AnimalFeedRec:2nd It solvedAnimals", solvedAnimals, "no solutions for ", Object.keys(allAnimalsData.data.animals).length - solvedAnimals, "animals")
      newAllMissedConstraints = _.uniq([...allMissedConstraints, ...newAllMissedConstraints])
      console.log("animalFeedClass.AnimalFeedRec:6 newAllMissedConstraints ", newAllMissedConstraints)
      solvedAnimals = 0
      // if (bestGreenFoliages.length > 0) {
      //   // make green1 cost 50% less This is to try and gravitate to first green solution, occassionally extra green is added
      //   console.log('best green foliage 1 pricePKgUsed before', bestGreenFoliages[0].pricePKgUsed)
      //   foliageReferenceData.forEach((foliage) => { if (foliage.reference_id == bestGreenFoliages[0].reference_id) foliage.pricePKgUsed = foliage.pricePKgUsed * 0.5 })
      //   console.log('best green foliage 1 pricePKgUsed after', bestGreenFoliages[0].pricePKgUsed)
      // }
      for (const [animal_id, animal] of Object.entries(allAnimalsData.data.animals)) {
        try {
          // make green1 cost 50% less This is to try and gravitate to first green solution, occassionally extra green is added

          const result = this.solveForAnimal({
            animal,
            feedMasterData,
            foliageReferenceData,
            concentratesReferenceData,
            otherNutrientReferenceData,
            useFoliages: [...bestGreenFoliages, ...bestDryFoliages],
            useConcentrares: bestConcentrates,
            removedConstraint: newAllMissedConstraints,
            constraintOptimization: false,
            solutionWithNoOptionalConstraint: true
          })
          const { solution, foliagesPerDay, concentratesPerDay, otherNutrientsPerDay, missedConstraints, totalFeedsData, projectedLPD } = result // mineralMixturePerDay, saltPerDay, bypassFatPerDay,
          console.log("animalFeedClass.AnimalFeedRec:6 solution ", solution.code, "for animal", animal.animal_id)
          if (solution.code === 0) {
            solvedAnimals++
            returnResults.animalDailyFeeds[animal.animal_id] = {
              solution,
              foliagesPerDay,
              concentratesPerDay,
              otherNutrientsPerDay,
              projectedLPD,
              totalFeedsData,
              missedConstraints,
              subOptimalSoultion: false
            } // mineralMixturePerDay, saltPerDay, bypassFatPerDay,
            returnResults.allMissedConstraints = newAllMissedConstraints
            returnResults.missedConstraints = missedConstraints
          } else {
            // since no solution for the animal add  only nutrient data
            const { dm_min, dm_max, dcp, tdn, dcpMaint, dcpLpd, dcpPreg, tdnMaint, tdnLpd, tdnPreg } = animal.nutrients
            returnResults.animalDailyFeeds[animal.animal_id] = {
              solution: { code: -1, message: "No solution" },
              totalFeedsData: { dm_min, dm_max, dcpMaint, dcpLpd, dcpPreg, dcp, tdnMaint, tdnLpd, tdnPreg, tdn }
            }
          }
        } catch (error) {
          console.log("animalFeedClass.AnimalFeedRec:12 error", error, "for animal ", animal.animal_id)
          const { dm_min, dm_max, dcp, tdn, dcpMaint, dcpLpd, dcpPreg, tdnMaint, tdnLpd, tdnPreg } = animal.nutrients
          returnResults.animalDailyFeeds[animal.animal_id] = {
            solution: { code: -1, message: error.message },
            totalFeedsData: { dm_min, dm_max, dcpMaint, dcpLpd, dcpPreg, dcp, tdnMaint, tdnLpd, tdnPreg, tdn }
          }
        }
      }
      const { monthlyFeeds, monthlyCost } = await this.calculateMonthlyFeeds({
        animalDailyFeeds: returnResults.animalDailyFeeds,
        foliageReferenceData,
        concentratesReferenceData,
        otherNutrientReferenceData
      })

      for (const key in returnResults.animalDailyFeeds) {
        const dailyFeed = returnResults.animalDailyFeeds[key]
        if (dailyFeed.projectedLPD) {
          _totalProjectedLPD += dailyFeed.projectedLPD
          _totalCurrentLPD += allAnimalsData.data.animals[key].animal_average_lpd_1
        }
      }

      returnResults.monthlyFeeds = monthlyFeeds
      returnResults.monthlyCost = monthlyCost
      returnResults.totalProjectedLPD = parseFloat(_totalProjectedLPD.toFixed(2))
      returnResults.totalCurrentLPD = parseFloat(_totalCurrentLPD.toFixed(2))
      returnResults.costOfRationPerLitreOfProjectedMilk = monthlyCost ? (monthlyCost / _totalProjectedLPD / 30).toFixed(2) : 0
      console.log("animalFeedClass.AnimalFeedRec:3rd It solvedAnimals", solvedAnimals, "no solutions for ", Object.keys(allAnimalsData.data.animals).length - solvedAnimals, "animals")
      // save the smart feeds data
      await saveFeedToCustomerClassification(monthlyFeeds, _customer_id, solvedAnimals)
      if (saveFeeds) {
        const saveResults = await this.saveSmartFeedsData({
          customer_id: allAnimalsData.data.customer.customer_id,
          feed_generation_type: useOnlyFarmerFeeds ? 1001500002 : 1001500001,
          smartFeedsData: returnResults,
          feedMasterData
        })
        console.log("animalFeedClass.AnimalFeedRec:7 saveResults", saveResults)
        for (const animalId of Object.keys(returnResults.animalDailyFeeds)) {
          if (Object.keys(saveResults?.animalFeedAssetIds).includes(animalId)) {
            returnResults.animalDailyFeeds[animalId].feedAssetId = saveResults?.animalFeedAssetIds[animalId]
          }
        }
        returnResults.monthlyFeeds.feedAssetId = saveResults?.monthlyFeedAssetId
      }
      if (generateCsv) {
        const csvResults = await this.generateCsvFile({ allAnimalsData, animalDailyFeeds: returnResults.animalDailyFeeds })
        console.log("animalFeedClass.AnimalFeedRec:7 csvResults", csvResults)
      }
      returnResults.return_code = 0
      returnResults.message = "Success"
      return returnResults
    } catch (error) {
      console.log(error)
      if (saveFeeds) {
        const saveResults = await this.saveSmartFeedsData({
          customer_id: allAnimalsData.data.customer.customer_id,
          feed_generation_type: useOnlyFarmerFeeds ? 1001500002 : 1001500001,
          smartFeedsData: returnResults,
          feedMasterData
        })
        console.log("animalFeedClass.AnimalFeedRec:7 saveResults", saveResults)
        for (const animalId of Object.keys(returnResults.animalDailyFeeds)) {
          if (Object.keys(saveResults?.animalFeedAssetIds).includes(animalId)) {
            returnResults.animalDailyFeeds[animalId].feedAssetId = saveResults?.animalFeedAssetIds[animalId]
          }
        }
        returnResults.monthlyFeeds.feedAssetId = saveResults?.monthlyFeedAssetId
      }
      returnResults.return_code = -1
      returnResults.message = error.message
    }
  }

  async calculateActualNutrients({ foliagesPerDay, concentratesPerDay, otherNutrientsPerDay, foliageReferenceData, concentratesReferenceData, otherNutrientReferenceData }) {
    const actualNutrients = {}
    const totalFodders = {}
    const refData = await loadRefData({ foliageReferenceData, concentratesReferenceData, otherNutrientReferenceData })
    foliageReferenceData = refData.foliageReferenceData
    concentratesReferenceData = refData.concentratesReferenceData
    otherNutrientReferenceData = refData.otherNutrientReferenceData
    foliagesPerDay.forEach(foliage => {
      const matchedFoliage = foliageReferenceData.find(referenceFoliage => referenceFoliage.reference_id === foliage.reference_id)
      if (matchedFoliage) {
        foliage.dm = matchedFoliage.dm
        foliage.dcp = matchedFoliage.dcp
        foliage.tdn = matchedFoliage.tdn
        foliage.pricePkg = matchedFoliage.pricePkg
      }
    })
    concentratesPerDay.forEach(concentrate => {
      const matchedConcentrate = concentratesReferenceData.find(referenceConcentrate => referenceConcentrate.reference_id === concentrate.reference_id)
      if (matchedConcentrate) {
        concentrate.dm = matchedConcentrate.dm
        concentrate.dcp = matchedConcentrate.dcp
        concentrate.tdn = matchedConcentrate.tdn
        concentrate.pricePkg = matchedConcentrate.pricePkg
      }
    })
    otherNutrientsPerDay.forEach(otherNutrient => {
      const matchedOtherNutrient = otherNutrientReferenceData.find(referenceOtherNutrient => referenceOtherNutrient.reference_id === otherNutrient.reference_id)
      if (matchedOtherNutrient) {
        otherNutrient.pricePkg = matchedOtherNutrient.pricePkg
      }
    })
    actualNutrients.dm = _.sumBy(foliagesPerDay, foliage => foliage.quantity * foliage.dm * 0.01) + _.sumBy(concentratesPerDay, concentrate => concentrate.quantity * concentrate.dm * 0.01)
    actualNutrients.dcp = _.sumBy(foliagesPerDay, foliage => foliage.quantity * foliage.dcp * 0.01) + _.sumBy(concentratesPerDay, concentrate => concentrate.quantity * concentrate.dcp * 0.01)
    actualNutrients.tdn = _.sumBy(foliagesPerDay, foliage => foliage.quantity * foliage.tdn * 0.01) + _.sumBy(concentratesPerDay, concentrate => concentrate.quantity * concentrate.tdn * 0.01)
    totalFodders.foliages = _.sumBy(foliagesPerDay, foliage => foliage.quantity)
    totalFodders.concentrates = _.sumBy(concentratesPerDay, concentrate => concentrate.quantity)
    totalFodders.green = _.sumBy(foliagesPerDay, foliage => (foliage.green ? foliage.quantity : 0))
    totalFodders.dry = _.sumBy(foliagesPerDay, foliage => (foliage.dry ? foliage.quantity : 0))
    totalFodders.total = totalFodders.foliages + totalFodders.concentrates
    totalFodders.cost = _.sumBy(foliagesPerDay, foliage => foliage.quantity * foliage.pricePkg) + _.sumBy(concentratesPerDay, concentrate => concentrate.quantity * concentrate.pricePkg) + _.sumBy(otherNutrientsPerDay, otherNutrient => otherNutrient.quantity * otherNutrient.pricePkg)
    return { actualNutrients, totalFodders }
  }

  async saveSmartFeedsData({ customer_id, smartFeedsData, feed_generation_type }) {
    const { monthlyFeeds, animalDailyFeeds, user_device_id } = smartFeedsData // monthlyMineralMixtures, monthlySalt, monthlyBypassFat,
    const refData = await loadRefData({ foliageReferenceData: null, concentratesReferenceData: null, otherNutrientReferenceData: null })
    const foliageReferenceData = refData.foliageReferenceData
    const concentratesReferenceData = refData.concentratesReferenceData
    const otherNutrientReferenceData = refData.otherNutrientReferenceData
    // save monthly data
    const monthSmartFeedLib = new LibSmartFeed()
    let monthlyResult = null
    try {
      let monthlySolutionFeasible
      if (monthlyFeeds && monthlyFeeds?.foliages && monthlyFeeds?.concentrates && monthlyFeeds?.otherNutrients && monthlyFeeds.foliages.length + monthlyFeeds.concentrates.length + monthlyFeeds.otherNutrients.length > 0) {
        monthlySolutionFeasible = 1000105001
      } else {
        monthlySolutionFeasible = 1000105002
      }
      const smartFeedData = {
        asset_type_id: 1001120003,
        smart_ration_feasible_solution: monthlySolutionFeasible,
        asset_name_l10n: `${customer_id} - 1001120004 - ${feed_generation_type}`,
        parents: [
          {
            entity_1_type_id: 1000460001,
            entity_1_entity_uuid: customer_id, // 'ba19b26a-0529-11ee-9670-3fb01029cc05',
            entity_relationship_type_id: 1000210027
          }
        ],
        feed_generation_type
      }
      if (user_device_id) smartFeedData.user_device_id = user_device_id

      if (monthlySolutionFeasible === 1000105001) {
        smartFeedData.smart_ration_foliages = {}
        smartFeedData.smart_ration_concentrates = {}
        smartFeedData.smart_ration_other_nutrients = {}
        for (const monthlyFoliage of monthlyFeeds.foliages) {
          // calculate cost and price per kg
          const refFoliage = foliageReferenceData.find(refFoliage => refFoliage.reference_id === monthlyFoliage.reference_id)
          if (!refFoliage) {
            console.log("saveSmartFeedsData error", "no refFoliage found for", monthlyFoliage.reference_id)
            continue
          }
          if (!monthlyFoliage.quantity) {
            console.log("saveSmartFeedsData error", "no monthlyFoliage.quantity found for", monthlyFoliage.reference_id)
            continue
          }
          const smart_ration_price_per_kg = monthlyFoliage.pricePkg ? monthlyFoliage.pricePkg : refFoliage.pricePkg
          smartFeedData.smart_ration_foliages[monthlyFoliage.reference_id] = {
            smart_ration_weight: monthlyFoliage.quantity,
            smart_ration_cost: monthlyFoliage.quantity * smart_ration_price_per_kg,
            smart_ration_price_per_kg
          }
        }
        for (const monthlyConcentrate of monthlyFeeds.concentrates) {
          const refConcentrate = concentratesReferenceData.find(refConcentrate => refConcentrate.reference_id === monthlyConcentrate.reference_id)
          if (!refConcentrate) {
            console.log("saveSmartFeedsData error", "no refConcentrate found for", monthlyConcentrate.reference_id)
            continue
          }
          if (!monthlyConcentrate.quantity) {
            console.log("saveSmartFeedsData error", "no monthlyConcentrate.quantity found for", monthlyConcentrate.reference_id)
            continue
          }
          const smart_ration_price_per_kg = monthlyConcentrate.pricePkg ? monthlyConcentrate.pricePkg : refConcentrate.pricePkg

          smartFeedData.smart_ration_concentrates[monthlyConcentrate.reference_id] = {
            smart_ration_weight: monthlyConcentrate.quantity,
            smart_ration_cost: monthlyConcentrate.quantity * smart_ration_price_per_kg,
            smart_ration_price_per_kg
          }
        }
        for (const monthlyOtherNutrient of monthlyFeeds.otherNutrients) {
          const refOtherNutrient = otherNutrientReferenceData.find(refOtherNutrient => refOtherNutrient.reference_id === monthlyOtherNutrient.reference_id)
          if (!refOtherNutrient) {
            console.log("saveSmartFeedsData error", "no refOtherNutrient found for", monthlyOtherNutrient.reference_id)
            continue
          }
          if (!monthlyOtherNutrient.quantity) {
            console.log("saveSmartFeedsData error", "no monthlyOtherNutrient.quantity found for", monthlyOtherNutrient.reference_id)
            continue
          }
          const smart_ration_price_per_kg = monthlyOtherNutrient.pricePkg ? monthlyOtherNutrient.pricePkg : refOtherNutrient.pricePkg

          smartFeedData.smart_ration_other_nutrients[monthlyOtherNutrient.reference_id] = {
            smart_ration_weight: monthlyOtherNutrient.quantity,
            smart_ration_cost: monthlyOtherNutrient.quantity * smart_ration_price_per_kg,
            smart_ration_price_per_kg
          }
        }
      }

      monthlyResult = await monthSmartFeedLib.create(smartFeedData, {})
      // check if monthly got created properly
      if (monthlyResult.return_code !== 0) {
        return { monthlyResult, animalFeedSaveResult: [] }
      }
    } catch (error) {
      console.log("saveSmartFeedsData error", error)
      return { monthlyResult: null, animalFeedSaveResult: [] }
    }

    const animalFeedAssetIds = {}
    // now create per cow smart feed data
    for (const [animal_id, animalDailyFeed] of Object.entries(animalDailyFeeds)) {
      // if (animalDailyFeed.solution.code != 0) {
      // // there is no solution untill we decide what to store for no solution we will continue
      //   continue
      // }
      try {
        let animalSolutionFeasible
        if (animalDailyFeed.solution.code === 0 && animalDailyFeed?.foliagesPerDay && animalDailyFeed?.concentratesPerDay && animalDailyFeed?.otherNutrientsPerDay && animalDailyFeed.foliagesPerDay.length + animalDailyFeed.concentratesPerDay.length + animalDailyFeed.otherNutrientsPerDay.length > 0) {
          animalSolutionFeasible = 1000105001
        } else {
          animalSolutionFeasible = 1000105002
        }
        const smartFeedLib = new LibSmartFeed()
        const smartFeedData = {
          asset_type_id: 1001120004,
          smart_ration_feasible_solution: animalSolutionFeasible,
          asset_name_l10n: `${animal_id} - 1001120004 - ${feed_generation_type}`,
          parents: [
            {
              entity_1_type_id: 1000460002,
              entity_1_entity_uuid: animal_id, // 'ba19b26a-0529-11ee-9670-3fb01029cc05',
              entity_relationship_type_id: 1000210028
            },
            {
              entity_1_type_id: 1000460012,
              entity_1_entity_uuid: monthlyResult.data.asset_id, // 'ba19b26a-0529-11ee-9670-3fb01029cc05',
              entity_relationship_type_id: 1000210032
            }
          ],
          feed_generation_type
        }
        if (user_device_id) smartFeedData.user_device_id = user_device_id
        if (animalDailyFeed?.totalFeedsData?.dm_min) smartFeedData.dm_min = animalDailyFeed.totalFeedsData.dm_min
        if (animalDailyFeed?.totalFeedsData?.dm_max) smartFeedData.dm_max = animalDailyFeed.totalFeedsData.dm_max
        if (animalDailyFeed?.totalFeedsData?.dcp) smartFeedData.dcp = animalDailyFeed.totalFeedsData.dcp
        if (animalDailyFeed?.totalFeedsData?.tdn) smartFeedData.tdn = animalDailyFeed.totalFeedsData.tdn

        if (animalSolutionFeasible == 1000105001) {
          smartFeedData.smart_ration_foliages = {}
          smartFeedData.smart_ration_concentrates = {}
          smartFeedData.smart_ration_other_nutrients = {}
          for (const foliage of animalDailyFeed.foliagesPerDay) {
            const refFoliage = foliageReferenceData.find(refFoliage => refFoliage.reference_id === foliage.reference_id)
            if (!refFoliage) {
              console.log("saveSmartFeedsData error", "no refFoliage found for", foliage.reference_id)
              continue
            }
            if (!foliage.quantity) {
              console.log("saveSmartFeedsData error", "no foliage.quantity found for", foliage.reference_id)
              continue
            }
            const smart_ration_price_per_kg = foliage.pricePkg ? foliage.pricePkg : refFoliage.pricePkg
            const smart_ration_cost = foliage.quantity * smart_ration_price_per_kg
            smartFeedData.smart_ration_foliages[foliage.reference_id] = {
              smart_ration_weight: foliage.quantity,
              smart_ration_cost,
              smart_ration_price_per_kg
            }
          }
          for (const concentrate of animalDailyFeed.concentratesPerDay) {
            const refConcentrate = concentratesReferenceData.find(refConcentrate => refConcentrate.reference_id === concentrate.reference_id)
            if (!refConcentrate) {
              console.log("saveSmartFeedsData error", "no refConcentrate found for", concentrate.reference_id)
              continue
            }
            if (!concentrate.quantity) {
              console.log("saveSmartFeedsData error", "no concentrate.quantity found for", concentrate.reference_id)
              continue
            }
            const smart_ration_price_per_kg = concentrate.pricePkg ? concentrate.pricePkg : refConcentrate.pricePkg
            const smart_ration_cost = concentrate.quantity * smart_ration_price_per_kg
            smartFeedData.smart_ration_concentrates[concentrate.reference_id] = {
              smart_ration_weight: concentrate.quantity,
              smart_ration_cost,
              smart_ration_price_per_kg
            }
          }
          for (const otherNutrient of animalDailyFeed.otherNutrientsPerDay) {
            const refOtherNutrient = otherNutrientReferenceData.find(refOtherNutrient => refOtherNutrient.reference_id === otherNutrient.reference_id)
            if (!refOtherNutrient) {
              console.log("saveSmartFeedsData error", "no refOtherNutrient found for", otherNutrient.reference_id)
              continue
            }
            if (!otherNutrient.quantity) {
              console.log("saveSmartFeedsData error", "no otherNutrient.quantity found for", otherNutrient.reference_id)
              continue
            }
            const smart_ration_price_per_kg = otherNutrient.pricePkg ? otherNutrient.pricePkg : refOtherNutrient.pricePkg
            const smart_ration_cost = otherNutrient.quantity * smart_ration_price_per_kg

            smartFeedData.smart_ration_other_nutrients[otherNutrient.reference_id] = {
              smart_ration_weight: otherNutrient.quantity,
              smart_ration_cost,
              smart_ration_price_per_kg
            }
          }
        }
        const result = await smartFeedLib.create(smartFeedData, {})
        animalFeedAssetIds[animal_id] = result?.data?.asset_id
      } catch (error) {
        console.log("saveSmartFeedsData error", error)
        animalFeedAssetIds[animal_id] = null
      }
    }
    return { monthlyFeedAssetId: monthlyResult?.data?.asset_id, animalFeedAssetIds }
  }

  async loadSmartFeedsData({ customer_id, feed_generation_type, user_device_id }) {
    // const { monthlyFeeds, animalDailyFeeds, user_device_id } = smartFeedsData // monthlyMineralMixtures, monthlySalt, monthlyBypassFat,
    // save monthly data
    try {
      const libFeed = new LibSmartFeed()
      const monthlyResult = await libFeed.find({ query: { entity_1_type_id: 1000460001, entity_1_entity_uuid: customer_id, entity_relationship_type_id: 1000210027 } })
      const monthlyFeeds = monthlyResult.data
      // for this monthly feed get all the related animal feeds
      const animalResult = await libFeed.find({ query: { entity_1_type_id: 1000460012, entity_1_entity_uuid: monthlyFeeds.asset_id, entity_relationship_type_id: 1000210032 } })
      const animalDailyFeeds = animalResult.data
      return { monthlyFeeds, animalDailyFeeds }
    } catch (error) {
      console.log("loadSmartFeedsData error", error)
      throw error
    }
  }

  solveForAnimal({ animal, feedMasterData, foliageReferenceData, concentratesReferenceData, otherNutrientReferenceData, useFoliages = [], useConcentrares = [], removedConstraint = [], constraintOptimization = true, solutionWithNoOptionalConstraint = false }) {
    let returnResult = { solution: { code: -1, message: "no optimal solution available" } }
    // const { minearlMixturePricePkg, saltPricePkg, bypassFatPricePkg } = feedMasterData
    if (animal.nutrients.dm_min === 0) {
      return returnResult
    }

    const { dm_min, dm_max, dcp, tdn, dcpMaint, dcpLpd, dcpPreg, tdnMaint, tdnLpd, tdnPreg } = animal.nutrients
    foliageReferenceData = _.cloneDeep(foliageReferenceData)
    concentratesReferenceData = _.cloneDeep(concentratesReferenceData)
    if (useFoliages && useFoliages.length) {
      foliageReferenceData = _.cloneDeep(useFoliages)
    }
    if (useConcentrares && useConcentrares.length) {
      concentratesReferenceData = _.cloneDeep(useConcentrares)
    }

    try {
      const modelVars = {}
      const constraints = {
        dm_min_op: { min: feedMasterData.rules[animal.yeilder].dm_min_op * animal.dm_min },
        dm_max_op: { max: feedMasterData.rules[animal.yeilder].dm_max_op * animal.dm_max },
        dm_min_must: { min: feedMasterData.rules[animal.yeilder].dm_min_must * animal.dm_min },
        dm_max_must: { max: feedMasterData.rules[animal.yeilder].dm_max_must * animal.dm_max },

        dcp_min_op: { min: feedMasterData.rules[animal.yeilder].dcp_min_op * animal.dcp },
        dcp_max_op: { max: feedMasterData.rules[animal.yeilder].dcp_max_op * animal.dcp },
        dcp_min_must: { min: feedMasterData.rules[animal.yeilder].dcp_min_must * animal.dcp },
        dcp_max_must: { max: feedMasterData.rules[animal.yeilder].dcp_max_must * animal.dcp },

        tdn_min_op: { min: feedMasterData.rules[animal.yeilder].tdn_min_op * animal.tdn },
        tdn_max_op: { max: feedMasterData.rules[animal.yeilder].tdn_max_op * animal.tdn },
        tdn_min_must: { min: feedMasterData.rules[animal.yeilder].tdn_min_must * animal.tdn },
        tdn_max_must: { max: feedMasterData.rules[animal.yeilder].tdn_max_must * animal.tdn },

        dm_green_min_op: { min: feedMasterData.rules[animal.yeilder].dm_green_min_op * animal.dm_min },
        dm_green_max_op: { max: feedMasterData.rules[animal.yeilder].dm_green_max_op * animal.dm_max },
        dm_green_min_must: { min: feedMasterData.rules[animal.yeilder].dm_green_min_must * animal.dm_min },
        dm_green_max_must: { max: feedMasterData.rules[animal.yeilder].dm_green_max_must * animal.dm_max },

        dm_dry_min_op: { min: feedMasterData.rules[animal.yeilder].dm_dry_min_op * animal.dm_min },
        dm_dry_max_op: { max: feedMasterData.rules[animal.yeilder].dm_dry_max_op * animal.dm_max },
        dm_dry_min_must: { min: feedMasterData.rules[animal.yeilder].dm_dry_min_must * animal.dm_min },
        dm_dry_max_must: { max: feedMasterData.rules[animal.yeilder].dm_dry_max_must * animal.dm_max },

        qty_green_min_op: { min: feedMasterData.rules[animal.yeilder].qty_green_min_op },
        qty_green_max_op: { max: feedMasterData.rules[animal.yeilder].qty_green_max_op },
        qty_green_min_must: { min: feedMasterData.rules[animal.yeilder].qty_green_min_must },
        qty_green_max_must: { max: feedMasterData.rules[animal.yeilder].qty_green_max_must },
        qty_dry_min_op: { min: feedMasterData.rules[animal.yeilder].qty_dry_min_op },
        qty_dry_max_op: { max: feedMasterData.rules[animal.yeilder].qty_dry_max_op },
        qty_dry_min_must: { min: feedMasterData.rules[animal.yeilder].qty_dry_min_must },
        qty_dry_max_must: { max: feedMasterData.rules[animal.yeilder].qty_dry_max_must },
        qty_min_must: { min: feedMasterData.rules[animal.yeilder].qty_min_must },
        qty_max_must: { max: feedMasterData.rules[animal.yeilder].qty_max_must },
        qty_min_op: { min: feedMasterData.rules[animal.yeilder].qty_min_op },
        qty_max_op: { max: feedMasterData.rules[animal.yeilder].qty_max_op },
        qty_silage_max_op: { max: feedMasterData.rules[animal.yeilder].qty_silage_max_op },
        qty_silage_min_op: { min: feedMasterData.rules[animal.yeilder].qty_silage_min_op },
        qty_silage_max_must: { max: feedMasterData.rules[animal.yeilder].qty_silage_max_must },
        qty_silage_min_must: { min: feedMasterData.rules[animal.yeilder].qty_silage_min_must },

        qty_concentrate_min_op: { min: feedMasterData.rules[animal.yeilder].qty_concentrate_min_op * animal.animal_average_lpd_1 },
        qty_concentrate_max_op: { max: feedMasterData.rules[animal.yeilder].qty_concentrate_max_op * animal.animal_average_lpd_1 },
        qty_concentrate_min_must: { min: feedMasterData.rules[animal.yeilder].qty_concentrate_min_must * animal.animal_average_lpd_1 },
        qty_concentrate_max_must: { max: feedMasterData.rules[animal.yeilder].qty_concentrate_max_must * animal.animal_average_lpd_1 }
      }
      // remove invalid constraints with null or undefined values
      for (const [constraintKey, constraintValue] of Object.entries(constraints)) {
        const foundConstraint = constraintValue.min || constraintValue.max || constraintValue.equal
        if (!foundConstraint) {
          delete constraints[constraintKey]
          // console.log('animalFeedClass.AnimalFeedRec:6 constraintKey', constraintKey, 'constraintValue', constraintValue, 'deleted')
        }
      }
      // remove constraints which are asked to be removed
      if (removedConstraint && removedConstraint.length) {
        for (const constraintKey of removedConstraint) {
          delete constraints[constraintKey]
          // console.log('animalFeedClass.AnimalFeedRec:6 constraintKey', constraintKey, 'deleted')
        }
      }
      let foliagesPerDay = _.cloneDeep(foliageReferenceData)
      let concentratesPerDay = _.cloneDeep(concentratesReferenceData)
      const otherNutrientsPerDay = _.cloneDeep(otherNutrientReferenceData)
      for (const foliage of foliagesPerDay) {
        modelVars[`q_${foliage.type}_${foliage.reference_id}`] = { cost: foliage.pricePKgUsed }
        foliage.modelVar = `q_${foliage.type}_${foliage.reference_id}`
        foliage.quantity = 0
      }
      for (const concentrate of concentratesPerDay) {
        modelVars[`q_concentrate_${concentrate.reference_id}`] = { cost: concentrate.pricePKgUsed }
        concentrate.modelVar = `q_concentrate_${concentrate.reference_id}`
        concentrate.quantity = 0
        concentrate.type = "concentrate"
      }

      const ints = {}
      foliagesPerDay.forEach(foliage => {
        ints[foliage.modelVar] = 1
      })
      for (const varKey of Object.keys(modelVars)) {
        const matchedFodder = foliagesPerDay.find(foliage => varKey === foliage.modelVar) || concentratesPerDay.find(concentrate => varKey === concentrate.modelVar) || null
        if (!matchedFodder) {
          console.log("animalFeedClass.AnimalFeedRec:6 matchedFodder not found", matchedFodder)
          continue
        }
        for (const constraintKey of Object.keys(constraints)) {
          const constraintWords = constraintKey.split("_")
          const opt = constraintWords[constraintWords.length - 1]
          const minMax = constraintWords[constraintWords.length - 2]
          const qtyNutrient = constraintWords[0]
          const additionalConstraints = constraintWords.slice(1, -2)
          let useFodder = true
          additionalConstraints.forEach(additionalConstraint => {
            if (!matchedFodder[additionalConstraint] || !matchedFodder[additionalConstraint] === true) {
              useFodder = false
            }
          })
          if (!useFodder) {
            continue
          }
          if (qtyNutrient !== "qty") {
            modelVars[varKey][constraintKey] = matchedFodder[qtyNutrient] * 0.01
          } else {
            modelVars[varKey][constraintKey] = 1
          }
        }
      }
      const { saltPerDay, mineralMixturePerDay, bypassFatPerDay } = calculateSaltAndMineralMixture(animal)

      const projectedLPD = animal.animal_average_lpd_1 * 1.15

      let optionalConstraints = constraintOptimization ? _.cloneDeep(feedMasterData.optionalConstraints) : []
      optionalConstraints = ["", "", "", "", ...optionalConstraints]
      // const missedConstraints = []
      // for (const optionalConstraint of optionalConstraints) {
      for (let m = 0; m < optionalConstraints.length; m++) {
        // for (const fourthOptionalConstraint of optionalConstraints) {
        const fourthOptionalConstraint = optionalConstraints[m]
        for (let i = m; i < optionalConstraints.length; i++) {
          const thirdOptionalConstraint = optionalConstraints[i]
          // if thirdOptionalConstraint is not '' and not in constraints then continue, we have already take care of this
          if (!thirdOptionalConstraint == "" && !constraints[thirdOptionalConstraint]) {
            // console.log('animalFeedClass.AnimalFeedRec:6 thirdOptionalConstraint', thirdOptionalConstraint, 'not in constraints')
            continue
          }

          for (let j = i; j < optionalConstraints.length; j++) {
            const secondOptionalConstraint = optionalConstraints[j]
            // if secondOptionalConstraint is not '' and not in constraints then continue, we have already take care of this
            if (!secondOptionalConstraint == "" && !constraints[secondOptionalConstraint]) {
              // console.log('animalFeedClass.AnimalFeedRec:6 secondOptionalConstraint', secondOptionalConstraint, 'not in constraints')
              continue
            }

            // for (const secondOptionalConstraint of optionalConstraints) {
            for (let k = j; k < optionalConstraints.length; k++) {
              const firstOptionalConstraint = optionalConstraints[k]
              // if firstOptionalConstraint is not '' and not in constraints then continue, we have already take care of this
              if (!firstOptionalConstraint == "" && !constraints[firstOptionalConstraint]) {
                // console.log('animalFeedClass.AnimalFeedRec:6 firstOptionalConstraint', firstOptionalConstraint, 'not in constraints')
                continue
              }
              // for (const firstOptionalConstraint of optionalConstraints) {
              const constraintsToUse = _.cloneDeep(constraints)
              // remove first& second optional constraint

              try {
                constraintsToUse[firstOptionalConstraint] && delete constraintsToUse[firstOptionalConstraint]
                constraintsToUse[secondOptionalConstraint] && delete constraintsToUse[secondOptionalConstraint]
                constraintsToUse[thirdOptionalConstraint] && delete constraintsToUse[thirdOptionalConstraint]
                constraintsToUse[fourthOptionalConstraint] && delete constraintsToUse[fourthOptionalConstraint]
              } catch (error) {
                console.log("animalFeedClass.AnimalFeedRec:7 error", error)
              }
              const model = {
                optimize: "cost",
                opType: "min",
                constraints: constraintsToUse,
                variables: modelVars,
                ints,
                options: {
                  tolerance: 0.05
                }
              }
              // console.log('animalFeedClass.AnimalFeedRec:8 model', model, 'optionalConstraint removed', optionalConstraint)
              const results = solver.Solve(model)
              if (results.feasible === true) {
                // result is found no more iterations needed
                for (const varKey of Object.keys(modelVars)) {
                  if (!results[varKey] || results[varKey] <= 0) {
                    //     console.log('animalFeedClass.AnimalFeedRec:9 varKey', varKey, 'results[varKey]', results[varKey])
                    continue
                  }
                  const [d, fodderType, reference_id] = varKey.split("_")
                  const matchedFodder = foliagesPerDay.find(foliage => varKey === foliage.modelVar) || concentratesPerDay.find(concentrate => varKey === concentrate.modelVar) || null
                  if (matchedFodder) {
                    matchedFodder.quantity = results[varKey]
                    // console.log('animalFeedClass.AnimalFeedRec:10 matchedFodder', matchedFodder.reference_name_l10n, results[varKey])
                  }
                }
                foliagesPerDay = _.filter(_.clone(foliagesPerDay), foliage => foliage.quantity > 0)
                concentratesPerDay = _.filter(_.clone(concentratesPerDay), concentrate => concentrate.quantity > 0)
                // create otherNutrientsPerDay
                for (const otherNutrient of otherNutrientsPerDay) {
                  switch (parseInt(otherNutrient.reference_id)) {
                    case 1001300501: // 'mineral_mixture':
                      otherNutrient.quantity = mineralMixturePerDay
                      break
                    case 1001300502: // 'salt':
                      otherNutrient.quantity = saltPerDay
                      break
                    case 1001300503: // 'bypass_fat':
                      otherNutrient.quantity = bypassFatPerDay
                      break
                  }
                }

                const totalCost = _.sumBy(foliagesPerDay, foliage => foliage.quantity * foliage.pricePkg) + _.sumBy(concentratesPerDay, concentrate => concentrate.quantity * concentrate.pricePkg) + _.sumBy(otherNutrientsPerDay, otherNutrient => otherNutrient.quantity * otherNutrient.pricePkg)
                const totalDm = _.sumBy(foliagesPerDay, foliage => foliage.quantity * foliage.dm * 0.01) + _.sumBy(concentratesPerDay, concentrate => concentrate.quantity * concentrate.dm * 0.01)
                const totalDcp = _.sumBy(foliagesPerDay, foliage => foliage.quantity * foliage.dcp * 0.01) + _.sumBy(concentratesPerDay, concentrate => concentrate.quantity * concentrate.dcp * 0.01)
                const totalTdn = _.sumBy(foliagesPerDay, foliage => foliage.quantity * foliage.tdn * 0.01) + _.sumBy(concentratesPerDay, concentrate => concentrate.quantity * concentrate.tdn * 0.01)
                const totalFoliageQuantity = _.sumBy(foliagesPerDay, foliage => foliage.quantity)
                const totalDry = _.sumBy(foliagesPerDay, foliage => (foliage.dry ? foliage.quantity : 0))
                const totalGreen = _.sumBy(foliagesPerDay, foliage => (foliage.green ? foliage.quantity : 0))
                const totalConcentrateQuantity = _.sumBy(concentratesPerDay, concentrate => concentrate.quantity)
                const totalFodder = parseInt(parseFloat(totalFoliageQuantity) + parseFloat(totalConcentrateQuantity))
                const totalFeedsData = { dm_min, dm_max, totalDm, dcpMaint, dcpLpd, dcpPreg, dcp, totalDcp, tdnMaint, tdnLpd, tdnPreg, tdn, totalTdn, totalDry, totalGreen, totalFoliageQuantity, totalConcentrateQuantity, totalFodder, totalCost }
                const missedConstraints = [firstOptionalConstraint, secondOptionalConstraint, thirdOptionalConstraint, fourthOptionalConstraint].filter(constraint => constraint !== "")
                returnResult = { solution: { code: 0 }, foliagesPerDay, concentratesPerDay, otherNutrientsPerDay, mineralMixturePerDay, saltPerDay, bypassFatPerDay, projectedLPD, totalFeedsData, missedConstraints }
                return returnResult
              } else if (results.feasible == false) {
                // no result found continue removing optional constraints further
              }
            }
          }
        }
      }
      // If there is no solution and solutionWithNoOptionalConstraint = true // now loop through the optional constraints and remove them one by one and try to find a solution
      const missedConstraints = []
      if (returnResult.solution.code === -1 && solutionWithNoOptionalConstraint === true) {
        const constraintsToUse = _.cloneDeep(constraints)
        for (const optionalConstraint of optionalConstraints) {
          const model = {
            optimize: "cost",
            opType: "min",
            constraints: constraintsToUse,
            variables: modelVars,
            ints,
            options: {
              tolerance: 0.015
            }
          }
          // console.log('animalFeedClass.AnimalFeedRec:8 model', model, 'optionalConstraint removed', optionalConstraint)
          const results = solver.Solve(model)
          if (results.feasible === true) {
            // result is found no more iterations needed
            if (results.feasible === true) {
              // result is found no more iterations needed
              for (const varKey of Object.keys(modelVars)) {
                if (!results[varKey] || results[varKey] <= 0) {
                  //     console.log('animalFeedClass.AnimalFeedRec:9 varKey', varKey, 'results[varKey]', results[varKey])
                  continue
                }
                const [d, fodderType, reference_id] = varKey.split("_")
                const matchedFodder = foliagesPerDay.find(foliage => varKey === foliage.modelVar) || concentratesPerDay.find(concentrate => varKey === concentrate.modelVar) || null
                if (matchedFodder) {
                  matchedFodder.quantity = results[varKey]
                  // console.log('animalFeedClass.AnimalFeedRec:10 matchedFodder', matchedFodder.reference_name_l10n, results[varKey])
                }
              }
              foliagesPerDay = _.filter(_.clone(foliagesPerDay), foliage => foliage.quantity > 0)
              concentratesPerDay = _.filter(_.clone(concentratesPerDay), concentrate => concentrate.quantity > 0)
              // create otherNutrientsPerDay
              for (const otherNutrient of otherNutrientsPerDay) {
                switch (otherNutrient.reference_id) {
                  case 1001300501: // 'mineral_mixture':
                    otherNutrient.quantity = mineralMixturePerDay
                    break
                  case 1001300502: // 'salt':
                    otherNutrient.quantity = saltPerDay
                    break
                  case 1001300503: // 'bypass_fat':
                    otherNutrient.quantity = bypassFatPerDay
                    break
                }
              }

              // const mineralMixturePerDay = animal.number_of_months_pregnant_1 >= 7 ? animal.dm_max * 0.02 : animal.dm_max * 0.01
              // const saltPerDay = animal.dm_max * 0.01
              // compute total cost, dm, dcp, tdn
              // const totalCost = _.sumBy(foliagesPerDay, (foliage) => foliage.quantity * foliage.pricePkg) + _.sumBy(concentratesPerDay, (concentrate) => concentrate.quantity * concentrate.pricePkg) + (mineralMixturePerDay / 1000 * minearlMixturePricePkg) + (saltPerDay / 1000 * saltPricePkg) + (bypassFatPerDay / 1000 * bypassFatPricePkg)
              const totalCost = _.sumBy(foliagesPerDay, foliage => foliage.quantity * foliage.pricePkg) + _.sumBy(concentratesPerDay, concentrate => concentrate.quantity * concentrate.pricePkg) + _.sumBy(otherNutrientsPerDay, otherNutrient => otherNutrient.quantity * otherNutrient.pricePkg)
              const totalDm = _.sumBy(foliagesPerDay, foliage => foliage.quantity * foliage.dm * 0.01) + _.sumBy(concentratesPerDay, concentrate => concentrate.quantity * concentrate.dm * 0.01)
              const totalDcp = _.sumBy(foliagesPerDay, foliage => foliage.quantity * foliage.dcp * 0.01) + _.sumBy(concentratesPerDay, concentrate => concentrate.quantity * concentrate.dcp * 0.01)
              const totalTdn = _.sumBy(foliagesPerDay, foliage => foliage.quantity * foliage.tdn * 0.01) + _.sumBy(concentratesPerDay, concentrate => concentrate.quantity * concentrate.tdn * 0.01)
              const totalFoliageQuantity = _.sumBy(foliagesPerDay, foliage => foliage.quantity)
              const totalDry = _.sumBy(foliagesPerDay, foliage => (foliage.dry ? foliage.quantity : 0))
              const totalGreen = _.sumBy(foliagesPerDay, foliage => (foliage.green ? foliage.quantity : 0))
              const totalConcentrateQuantity = _.sumBy(concentratesPerDay, concentrate => concentrate.quantity)
              const totalFodder = parseInt(parseFloat(totalFoliageQuantity) + parseFloat(totalConcentrateQuantity))
              const totalFeedsData = { dm_min, dm_max, totalDm, dcpMaint, dcpLpd, dcpPreg, dcp, totalDcp, tdnMaint, tdnLpd, tdnPreg, tdn, totalTdn, totalDry, totalGreen, totalFoliageQuantity, totalConcentrateQuantity, totalFodder, totalCost }
              // foliagesPerDay.forEach((foliage) => { foliage.quantity = foliage.quantity })
              // concentratesPerDay.forEach((concentrate) => { concentrate.quantity = concentrate.quantity })
              returnResult = { solution: { code: 0 }, foliagesPerDay, concentratesPerDay, otherNutrientsPerDay, projectedLPD, totalFeedsData, missedConstraints } // mineralMixturePerDay, saltPerDay, bypassFatPerDay,
              return returnResult
            } else if (results.feasible == false) {
              // no result found continue removing optional constraints delete current optional constraint
              try {
                missedConstraints.push(optionalConstraint)
                delete model.constraints[optionalConstraint]
              } catch (error) {
                console.log("animalFeedClass.AnimalFeedRec:7 error", error)
              }
            }
          }
        }
      }
    } catch (error) {
      console.log("animalFeedClass.AnimalFeedRec:12 error", error, "for animal ", animal.animal_id)
      returnResult = { solution: { code: -1, message: error.message } }
    }
    return returnResult
  }

  async generateCsvFile({ allAnimalsData, animalDailyFeeds }) {
    try {
      const csvFields = ["animal_id", "animal_weight_1", "animal_average_lpd_1", "animal_milk_fat_1", "number_of_months_pregnant_1", "dm_min", "dm_max", "totalDm", "dcpMaint", "dcpLpd", "dcpPreg", "dcp", "totalDcp", "tdnMaint", "tdnLpd", "tdnPreg", "tdn", "totalTdn", "totalDry", "totalGreen", "totalFoliageQuantity", "totalConcentrateQuantity", "totalFodder", "totalCost", "foliages", "concentrates", "missed_constraints"]
      const csvParser = new Parser({ fields: csvFields })
      const csvInputData = []
      for (const [animal_id, animalDailyFeed] of Object.entries(animalDailyFeeds)) {
        const animal = allAnimalsData.data.animals.find(animal => animal.animal_id === animal_id)
        const animalDailyFeedData = animalDailyFeed.totalFeedsData
        let foliages = animalDailyFeed.foliagesPerDay
          ? animalDailyFeed.foliagesPerDay.map(foliage => {
              const foliageName = extractBasedOnLanguage(foliage.reference_name_l10n, "en") // typeof foliage.reference_name_l10n === 'string' ? foliage.reference_name_l10n : foliage.reference_name_l10n.en
              return `${foliageName} - ${foliage.quantity} Kg`
            })
          : []
        foliages = foliages.join("  ")
        let concentrates = animalDailyFeed.concentratesPerDay
          ? animalDailyFeed.concentratesPerDay.map(concentrate => {
              const concentrateName = extractBasedOnLanguage(concentrate.reference_name_l10n, "en") // typeof concentrate.reference_name_l10n === 'string' ? concentrate.reference_name_l10n : concentrate.reference_name_l10n.en
              return `${concentrateName} - ${concentrate.quantity} Kg`
            })
          : []
        concentrates = concentrates.join("  ")
        // console.log('animalFeedClass.AnimalFeedRec:13 animalDailyFeedData', animal_id, animalDailyFeedData, animal,
        // allAnimalsData.data.animals)
        const animalDailyFeedDataCsv = {
          animal_id,
          animal_weight_1: animal.animal_weight_1.toFixed(2),
          animal_average_lpd_1: animal.animal_average_lpd_1.toFixed(2),
          animal_milk_fat_1: animal.animal_milk_fat_1,
          number_of_months_pregnant_1: animal.number_of_months_pregnant_1,
          ...animalDailyFeedData,
          foliages,
          concentrates,
          missed_constraints: animalDailyFeed.missedConstraints ? animalDailyFeed.missedConstraints.join(" ") : ""
        }
        csvInputData.push(animalDailyFeedDataCsv)
      }
      const csvData = csvParser.parse(Object.values(csvInputData))
      fs.writeFile("./calculatedFeeds.csv", csvData, err => {
        if (err) {
          console.log("animalFeedClass.AnimalFeedRec:14 error writing csv", err)
        } else {
          console.log("animalFeedClass.AnimalFeedRec:14 csv written")
        }
      })
      return { csvData }
    } catch (error) {
      console.log("animalFeedClass.AnimalFeedRec:14 error", error)
      return { return_code: -1, message: error.message }
    }
  }
}

function calculateSaltAndMineralMixture(animal) {
  let mineralMixturePerDay // changes in calculation as per discussion with Dr. Honalikar on 12/05/2023
  if ((animal.days_since_last_calving == undefined || animal.days_since_last_calving == null) && animal.animal_average_lpd_1 > 0) {
    // conservative will treat late lactation
    mineralMixturePerDay = 50 / 1000
  } else if (!animal.animal_average_lpd_1) {
    // dry cow
    mineralMixturePerDay = 25 / 1000
  } else if (animal.days_since_last_calving >= 0 && animal.days_since_last_calving <= 120 && animal.animal_average_lpd_1 > 0) {
    // early lactation
    if (animal.animal_average_lpd_1 >= 20) {
      // high yielder
      mineralMixturePerDay = 200 / 1000
    } else {
      // low yielder
      mineralMixturePerDay = 150 / 1000
    }
  } else if (animal.days_since_last_calving > 120 && animal.days_since_last_calving <= 180 && animal.animal_average_lpd_1 > 0) {
    // mid lactation
    if (animal.animal_average_lpd_1 >= 20) {
      // high yielder
      mineralMixturePerDay = 150 / 1000
    } else {
      // low yielder
      mineralMixturePerDay = 100 / 1000
    }
  } else if (animal.days_since_last_calving > 180 && animal.animal_average_lpd_1 > 0) {
    // late lactation
    mineralMixturePerDay = 50 / 1000
  } else {
    mineralMixturePerDay = 25 / 1000
  }

  let saltPerDay
  if (animal.number_of_months_pregnant_1 == undefined || animal.number_of_months_pregnant_1 == null) {
    // not taking risk with this will make salt 0
    saltPerDay = 0
  } else if (animal.number_of_months_pregnant_1 == 7) {
    saltPerDay = animal.dm_max * 0.0005 // made it 0.001 as per discussion with Dr. Honalikar on 12/05/2023
  } else if (animal.number_of_months_pregnant_1 >= 8) {
    saltPerDay = 0
  } else if (animal.number_of_months_pregnant_1 < 7) {
    saltPerDay = animal.dm_max * 0.001 // made it 0.001 as per discussion with Dr. Honalikar on 12/05/2023
  }
  let bypassFatPerDay = 0
  if (animal.number_of_months_pregnant_1 >= 9) {
    // late pregnancy
    bypassFatPerDay = 50 / 1000
  } else if (animal.number_of_months_since_last_calving >= 1 && animal.number_of_months_since_last_calving <= 4 && animal.animal_average_lpd_1 > 0) {
    // early lactation
    bypassFatPerDay = (5 * animal.animal_average_lpd_1) / 1000
  }

  console.log("calculated saltPerDay & miniral Mixture, bypassFatPerDay", saltPerDay, mineralMixturePerDay, bypassFatPerDay)
  return { saltPerDay, mineralMixturePerDay, bypassFatPerDay }
}

function convertBooleanAndNumericValues(obj) {
  if (!_.isObject(obj)) {
    if (_.isString(obj)) {
      if (obj === "true") return true
      if (obj === "false") return false
      if (obj === "null") return null
      if (obj === "undefined") return undefined
      if (!isNaN(obj)) {
        const parsed = parseFloat(obj)
        if (Number.isInteger(parsed)) return parsed
        return parsed
      }
    }
    return obj
  }
  if (_.isArray(obj)) return _.map(obj, convertBooleanAndNumericValues)
  return _.mapValues(obj, value => convertBooleanAndNumericValues(value))
}

function convertBooleanAndNumericValuesDeep(obj) {
  return _.cloneDeep(convertBooleanAndNumericValues(obj))
}
class ReloadReferenceData {
  async get(id, params) {
    return { return_code: 0, data: "animalFeeds/reloadReferenceData GET" }
  }
}

class HealthScore {
  async get(id, params) {
    console.log("animalFeeds/healthScore GET", params)
    return { return_code: 0, data: "animalFeeds/healthScore GET" }
  }

  async find(params) {
    console.log("animalFeeds/healthScore GET", params)
    return { return_code: 0, data: "animalFeeds/healthScore GET" }
  }

  async create(data, params) {
    console.log("animalFeeds/healthScore POST", data, params)
    const hs = new LibHealthScore()
    const healthScore = await hs.create(data, { headers: params.headers })
    return { return_code: 0, data: healthScore }
  }
}

class AnimalClassiferDataWithDocuments {
  async get(id, params) {
    console.log("animalFeeds/animalClassiferDataWithDocuments GET", params)
    const hs = new LibHealthScore()
    const response = await hs.loadAnimalClassiferDataWithDocuments(params.headers, id)
    return { data: response }
  }
}

class ClassificationsData {
  async create(data, params) {
    console.log("animalFeeds/ClassificationsData POST", data, params)
    const response = await saveClassifications(params.headers, data)
    return { data: response }
  }
}

class AnimalFeedsData {
  async get(id, params) {
    console.log("animalFeeds/animalFeedsData GET", params)
    const hs = new LibAnimalFeeds()
    const response = await hs.get(params, id)
    return { data: response }
  }

  async find(params) {
    console.log("animalFeeds/animalFeedsData GET", params)
    const hs = new LibAnimalFeeds()
    const id = params.query.id
    console.log("animalFeeds/animalFeedsData GET id", id)
    const response = await hs.get(params, id)
    return { data: response }
  }

  async create(data, params) {
    console.log("animalFeeds/animalFeedsData POST", data, params)
    const hs = new LibAnimalFeeds()
    const response = await hs.create(params, data)
    return { data: response }
  }
}
module.exports = { ReloadReferenceData, AllAnimalsFeedRecommendations, AnimalClassiferDataWithDocuments, ClassificationsData, HealthScore, AnimalFeedsData }

async function saveFeedToCustomerClassification(monthlyFeeds, customer_id, solvedAnimals) {
  const classifier_id = **********
  const foliages = {}
  const concentrates = {}
  const otherNutrients = {}
  const all_feed = []
  const dry_green_concentrate = {
    _daily_silage: null,
    _daily_green: null,
    _daily_dry: null,
    _daily_mineral_mixture: null,
    _daily_compound_feed1: null,
    _daily_compound_feed2: null
  }

  for (const foliage of monthlyFeeds.foliages) {
    foliages[foliage.reference_id] = {
      reference_name_l10n: foliage.reference_name_l10n,
      quantity: foliage.quantity,
      ...foliage.reference_information.feed_properties
    }
    all_feed.push(foliages[foliage.reference_id])
  }

  for (const concentrate of monthlyFeeds.concentrates) {
    concentrates[concentrate.reference_id] = {
      reference_name_l10n: concentrate.reference_name_l10n,
      quantity: concentrate.quantity,
      ...concentrate.reference_information.feed_properties,
      is_compound_feed: concentrate.reference_information.is_compound_feed ? concentrate.reference_information.is_compound_feed : false
    }
    all_feed.push(concentrates[concentrate.reference_id])
  }

  for (const otherNutrient of monthlyFeeds.otherNutrients) {
    otherNutrients[otherNutrient.reference_id] = {
      reference_name_l10n: otherNutrient.reference_name_l10n,
      quantity: otherNutrient.quantity,
      ...otherNutrient.reference_information.feed_properties
    }
    all_feed.push(otherNutrients[otherNutrient.reference_id])
  }

  const _distillFeeds = distillFeeds(all_feed, solvedAnimals)
  const data = { ...foliages, ...concentrates, ...otherNutrients, ..._distillFeeds }

  if (!customer_id) {
    throw new Error("Customer_id is not defined")
  }

  const existing = await dbConnections()
    .main.manager.createQueryBuilder("customer_classification", "customer_classification")
    .select(["customer_classification.customer_classification_id", "customer_classification.classifier_id", "customer_classification.value_json"])
    .where("customer_classification.classifier_id = :classifier_id and customer_classification.customer_id = :customer_id", { classifier_id, customer_id })
    .getOne()

  if (!existing) {
    await dbConnections()
      .main.manager.createQueryBuilder()
      .insert()
      .into("customer_classification")
      .values({ value_json: data, classifier_id: classifier_id, customer_id: customer_id })
      .execute()
  } else {
    await dbConnections()
      .main.manager.createQueryBuilder()
      .update("customer_classification")
      .set({ value_json: data })
      .where("customer_classification.classifier_id = :classifier_id and customer_classification.customer_id = :customer_id", { classifier_id, customer_id })
      .execute()
  }
}

function distillFeeds(feeds, solvedAnimals) {
  const _monthly_silage = [],
    _monthly_green = [],
    _monthly_dry = [],
    _monthly_mineral_mixture = [],
    _monthly_compound_feed1 = [],
    _monthly_compound_feed2 = []

  for (const feed of feeds) {
    switch (feed.type) {
      case "green":
        switch (feed.silage) {
          case true:
            _monthly_silage.push(feed)
            break
          case false:
            _monthly_green.push(feed)
            break
        }
        break
      case "dry":
        _monthly_dry.push(feed)
        break
      case "concentrate":
        switch (feed.is_compound_feed) {
          case true:
            if (_monthly_compound_feed1.length === 0) _monthly_compound_feed1.push(feed)
            else _monthly_compound_feed2.push(feed)
            break
          default:
            _monthly_compound_feed1.push(feed)
        }
        break
      case "readymade":
        switch (feed.is_compound_feed) {
          case true:
            if (_monthly_compound_feed1.length === 0) _monthly_compound_feed1.push(feed)
            else _monthly_compound_feed2.push(feed)
            break
          default:
            _monthly_compound_feed1.push(feed)
        }
        break
      case "other":
        switch (feed.reference_name_l10n.en) {
          case "Mineral Mixture":
            _monthly_mineral_mixture.push(feed)
            break
          case "Bypass Fat":
            break
          case "Salt":
            break
        }
    }
  }

  const reductionParameter = function equa(prev, curr) {
    return curr.quantity + prev
  }

    const daily_silage = solvedAnimals ? _monthly_silage.reduce(reductionParameter, 0) / (solvedAnimals * 30) : 0
  const daily_dry = solvedAnimals ? _monthly_dry.reduce(reductionParameter, 0) / (solvedAnimals * 30) : 0
  const daily_green = solvedAnimals ? _monthly_green.reduce(reductionParameter, 0) / (solvedAnimals * 30) : 0
  const daily_mineral_mixture = solvedAnimals ? _monthly_mineral_mixture.reduce(reductionParameter, 0) / (solvedAnimals * 30) : 0
  const daily_compound_feed1 = solvedAnimals ? _monthly_compound_feed1.reduce(reductionParameter, 0) / (solvedAnimals * 30) : 0
  const daily_compound_feed2 = solvedAnimals ? _monthly_compound_feed2.reduce(reductionParameter, 0) / (solvedAnimals * 30) : 0


  return { daily_silage, daily_dry, daily_green, daily_mineral_mixture, daily_compound_feed1, daily_compound_feed2 }
}
