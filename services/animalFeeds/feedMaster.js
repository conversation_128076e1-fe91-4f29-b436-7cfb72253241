const feedMaster = {
  minearlMixturePricePkg: 150,
  saltPricePkg: 20,
  bypassFatPricePkg: 130,
  cow_bw_dcp_tdn_req: {
    dm: {
      animals: [
        { number_of_months_pregnant_1: 0, number_of_months_since_last_calving: 0, minPercent: 2.5, maxPercent: 3 },
        { number_of_months_pregnant_1: 0, number_of_months_since_last_calving: 1, minPercent: 3.25, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 0, number_of_months_since_last_calving: 2, minPercent: 3, maxPercent: 4.2 },
        { number_of_months_pregnant_1: 0, number_of_months_since_last_calving: 3, minPercent: 3, maxPercent: 4.2 },
        { number_of_months_pregnant_1: 0, number_of_months_since_last_calving: 4, minPercent: 3, maxPercent: 4.2 },
        { number_of_months_pregnant_1: 0, number_of_months_since_last_calving: 5, minPercent: 3, maxPercent: 4 },
        { number_of_months_pregnant_1: 0, number_of_months_since_last_calving: 6, minPercent: 3, maxPercent: 4 },
        { number_of_months_pregnant_1: 0, number_of_months_since_last_calving: 7, minPercent: 3, maxPercent: 4 },
        { number_of_months_pregnant_1: 0, number_of_months_since_last_calving: 8, minPercent: 3, maxPercent: 4 },
        { number_of_months_pregnant_1: 0, number_of_months_since_last_calving: 9, minPercent: 2.5, maxPercent: 3 },
        { number_of_months_pregnant_1: 1, number_of_months_since_last_calving: 0, minPercent: 2.5, maxPercent: 3 },
        { number_of_months_pregnant_1: 1, number_of_months_since_last_calving: 1, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 1, number_of_months_since_last_calving: 2, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 1, number_of_months_since_last_calving: 3, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 1, number_of_months_since_last_calving: 4, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 1, number_of_months_since_last_calving: 5, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 1, number_of_months_since_last_calving: 6, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 1, number_of_months_since_last_calving: 7, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 1, number_of_months_since_last_calving: 8, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 1, number_of_months_since_last_calving: 9, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 2, number_of_months_since_last_calving: 0, minPercent: 2.5, maxPercent: 3 },
        { number_of_months_pregnant_1: 2, number_of_months_since_last_calving: 1, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 2, number_of_months_since_last_calving: 2, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 2, number_of_months_since_last_calving: 3, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 2, number_of_months_since_last_calving: 4, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 2, number_of_months_since_last_calving: 5, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 2, number_of_months_since_last_calving: 6, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 2, number_of_months_since_last_calving: 7, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 2, number_of_months_since_last_calving: 8, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 2, number_of_months_since_last_calving: 9, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 3, number_of_months_since_last_calving: 0, minPercent: 2.5, maxPercent: 3 },
        { number_of_months_pregnant_1: 3, number_of_months_since_last_calving: 1, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 3, number_of_months_since_last_calving: 2, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 3, number_of_months_since_last_calving: 3, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 3, number_of_months_since_last_calving: 4, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 3, number_of_months_since_last_calving: 5, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 3, number_of_months_since_last_calving: 6, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 3, number_of_months_since_last_calving: 7, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 3, number_of_months_since_last_calving: 8, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 3, number_of_months_since_last_calving: 9, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 4, number_of_months_since_last_calving: 0, minPercent: 2.5, maxPercent: 3 },
        { number_of_months_pregnant_1: 4, number_of_months_since_last_calving: 1, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 4, number_of_months_since_last_calving: 2, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 4, number_of_months_since_last_calving: 3, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 4, number_of_months_since_last_calving: 4, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 4, number_of_months_since_last_calving: 5, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 4, number_of_months_since_last_calving: 6, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 4, number_of_months_since_last_calving: 7, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 4, number_of_months_since_last_calving: 8, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 4, number_of_months_since_last_calving: 9, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 5, number_of_months_since_last_calving: 0, minPercent: 2.5, maxPercent: 3 },
        { number_of_months_pregnant_1: 5, number_of_months_since_last_calving: 1, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 5, number_of_months_since_last_calving: 2, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 5, number_of_months_since_last_calving: 3, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 5, number_of_months_since_last_calving: 4, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 5, number_of_months_since_last_calving: 5, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 5, number_of_months_since_last_calving: 6, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 5, number_of_months_since_last_calving: 7, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 5, number_of_months_since_last_calving: 8, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 5, number_of_months_since_last_calving: 9, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 6, number_of_months_since_last_calving: 0, minPercent: 2.5, maxPercent: 3 },
        { number_of_months_pregnant_1: 6, number_of_months_since_last_calving: 1, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 6, number_of_months_since_last_calving: 2, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 6, number_of_months_since_last_calving: 3, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 6, number_of_months_since_last_calving: 4, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 6, number_of_months_since_last_calving: 5, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 6, number_of_months_since_last_calving: 6, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 6, number_of_months_since_last_calving: 7, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 6, number_of_months_since_last_calving: 8, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 6, number_of_months_since_last_calving: 9, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 7, number_of_months_since_last_calving: 0, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 7, number_of_months_since_last_calving: 1, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 7, number_of_months_since_last_calving: 2, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 7, number_of_months_since_last_calving: 3, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 7, number_of_months_since_last_calving: 4, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 7, number_of_months_since_last_calving: 5, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 7, number_of_months_since_last_calving: 6, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 7, number_of_months_since_last_calving: 7, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 7, number_of_months_since_last_calving: 8, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 7, number_of_months_since_last_calving: 9, minPercent: 2.5, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 8, number_of_months_since_last_calving: 0, minPercent: 3.25, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 8, number_of_months_since_last_calving: 1, minPercent: 3.25, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 8, number_of_months_since_last_calving: 2, minPercent: 3.25, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 8, number_of_months_since_last_calving: 3, minPercent: 3.25, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 8, number_of_months_since_last_calving: 4, minPercent: 3.25, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 8, number_of_months_since_last_calving: 5, minPercent: 3.25, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 8, number_of_months_since_last_calving: 6, minPercent: 3.25, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 8, number_of_months_since_last_calving: 7, minPercent: 3.25, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 8, number_of_months_since_last_calving: 8, minPercent: 3.25, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 8, number_of_months_since_last_calving: 9, minPercent: 3.25, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 9, number_of_months_since_last_calving: 0, minPercent: 3.25, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 9, number_of_months_since_last_calving: 1, minPercent: 3.25, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 9, number_of_months_since_last_calving: 2, minPercent: 3.25, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 9, number_of_months_since_last_calving: 3, minPercent: 3.25, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 9, number_of_months_since_last_calving: 4, minPercent: 3.25, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 9, number_of_months_since_last_calving: 5, minPercent: 3.25, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 9, number_of_months_since_last_calving: 6, minPercent: 3.25, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 9, number_of_months_since_last_calving: 7, minPercent: 3.25, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 9, number_of_months_since_last_calving: 8, minPercent: 3.25, maxPercent: 3.5 },
        { number_of_months_pregnant_1: 9, number_of_months_since_last_calving: 9, minPercent: 3.25, maxPercent: 3.5 }
      ]
    },
    dcptdnMaint: [
      {
        animal_weight_1: {
          min: 0,
          max: 100
        },
        dcp: 0.168,
        tdn: 2.02
      },
      {
        animal_weight_1: {
          min: 100,
          max: 250
        },
        dcp: 0.168,
        tdn: 2.02
      },
      {
        animal_weight_1: {
          min: 251,
          max: 300
        },
        dcp: 0.197,
        tdn: 2.36
      },
      {
        animal_weight_1: {
          min: 301,
          max: 350
        },
        dcp: 0.227,
        tdn: 2.7
      },
      {
        animal_weight_1: {
          min: 351,
          max: 400
        },
        dcp: 0.254,
        tdn: 3.03
      },
      {
        animal_weight_1: {
          min: 401,
          max: 450
        },
        dcp: 0.254,
        tdn: 3.03
      },
      {
        animal_weight_1: {
          min: 451,
          max: 500
        },
        dcp: 0.31,
        tdn: 3.7
      },
      {
        animal_weight_1: {
          min: 501,
          max: 550
        },
        dcp: 0.336,
        tdn: 4.015
      },
      {
        animal_weight_1: {
          min: 551,
          max: 600
        },
        dcp: 0.36,
        tdn: 4.32
      },
      {
        animal_weight_1: {
          min: 601,
          max: 650
        },
        dcp: 0.384,
        tdn: 4.615
      },
      {
        animal_weight_1: {
          min: 651,
          max: 700
        },
        dcp: 0.406,
        tdn: 4.9
      },
      {
        animal_weight_1: {
          min: 701,
          max: 750
        },
        dcp: 0.428,
        tdn: 5.175
      },
      {
        animal_weight_1: {
          min: 751,
          max: 800
        },
        dcp: 0.449,
        tdn: 5.44
      }
    ]
  },
  dcpTdnLactationLPD: [
    {
      animal_milk_fat_1: {
        min: 1,
        max: 3
      },
      dcp: 0.04,
      tdn: 0.27
    },
    {
      animal_milk_fat_1: {
        min: 3.001,
        max: 4
      },
      dcp: 0.045,
      tdn: 0.315
    },
    {
      animal_milk_fat_1: {
        min: 4.001,
        max: 5
      },
      dcp: 0.051,
      tdn: 0.37
    },
    {
      animal_milk_fat_1: {
        min: 5.001,
        max: 6
      },
      dcp: 0.057,
      tdn: 0.41
    },
    {
      animal_milk_fat_1: {
        min: 6.001,
        max: 7
      },
      dcp: 0.059,
      tdn: 0.46
    }
  ],
  dcpTdnPregnancy: [
    {
      number_of_months_pregnant_1: {
        min: 0,
        max: 3
      },
      dcp: 0,
      tdn: 0
    },
    {
      number_of_months_pregnant_1: {
        min: 3.01,
        max: 6
      },
      dcp: 0.14,
      tdn: 0.7
    },
    {
      number_of_months_pregnant_1: {
        min: 6.01,
        max: 99
      },
      trimester: 3,
      dcp: 0.14,
      tdn: 0.7
    }
  ],
  concentrates_mix_master: {
    cereal: {
      concentrate_type: 'cereal',
      min: 0.35,
      max: 0.75,
      optional: 'false'
    },
    cereal_by_product: {
      concentrate_type: 'cereal_by_product',
      min: 0.15,
      max: 0.4,
      optional: 'false'
    },
    deolled_cake: {
      concentrate_type: 'deolled_cake',
      min: 0.15,
      max: 0.3,
      optional: 'false'
    },
    salt: {
      concentrate_type: 'salt',
      min: 1,
      max: 1,
      optional: 'false',
      dcp: 0.063
    },
    mineral_mixture: {
      concentrate_type: 'mineral_mixture',
      min: 1,
      max: 2,
      optional: 'false'
    },
    chuni: {
      concentrate_type: 'chuni',
      min: 0,
      max: 0.2,
      optional: 'true'
    },
    sugarcane_molasses: {
      concentrate_type: 'sugarcane_molasses',
      min: 0,
      max: 0.12,
      optional: 'true'
    }
  },
  feedRules: {
    green: {
      min: 0.3,
      max: 0.8
    },
    dry: {
      min: 0.05,
      max: 0.5
    },
    concentrate: {
      min: 0,
      max: 0.4
    },
    dcp: {
      min: 1,
      max: 1.1
    },
    tdn: {
      min: 1,
      max: 1.2
    }
  },
  // "rules": {
  //   "lowYeilder": {
  //     "dcp_min_op": 1,
  //     "dcp_max_op": 1.1,
  //     "tdn_min_op": 1,
  //     "tdn_max_op": 1.2,
  //     "dm_green_min_op": 0.3,
  //     "dm_green_max_op": 0.8,
  //     "dm_dry_min_op": 0.05,
  //     "dm_dry_max_op": 0.5,
  //     "dm_concentrate_min_op": 0,
  //     "dm_concentrate_max_op": 0.4,
  //     "qty_max_must": 40,
  //     "qty_green_min_op": 10,
  //     "qty_green_max_op": 35,
  //     "qty_dry_min_op": 1,
  //     "qty_dry_max_op": 5,
  //     "qty_concentrate_min_op": 0,
  //     "qty_concentrate_max_op": 8,
  //     "qty_dry_max_must": 5,
  //     "qty_silage_max_must": 15,
  //     "dcp_min_must": 0.6,
  //     "dcp_max_must": 1.6,
  //     "tdn_min_must": 0.7,
  //     "tdn_max_must": 1.6,
  //     "qty_green_max_must": 50
  //   },
  //   "highYeilder": {
  //     "dcp_min_op": 1,
  //     "dcp_max_op": 1.1,
  //     "tdn_min_op": 1,
  //     "tdn_max_op": 1.2,
  //     "dm_green_min_op": 0.3,
  //     "dm_green_max_op": 0.8,
  //     "dm_dry_min_op": 0.05,
  //     "dm_dry_max_op": 0.5,
  //     "dm_concentrate_min_op": 0,
  //     "dm_concentrate_max_op": 0.4,
  //     "qty_max_must": 50,
  //     "qty_green_min_op": 10,
  //     "qty_green_max_op": 45,
  //     "qty_dry_min_op": 1,
  //     "qty_dry_max_op": 5,
  //     "qty_concentrate_min_op": 2,
  //     "qty_concentrate_max_op": 12,
  //     "qty_dry_max_must": 8,
  //     "qty_silage_max_must": 15,
  //     "dcp_min_must": 0.6,
  //     "dcp_max_must": 1.6,
  //     "tdn_min_must": 0.7,
  //     "tdn_max_must": 1.6,
  //     "qty_green_max_must": 50
  //   }
  // },
  // "optionalConstraints": [
  //   "dm_concentrate_max_op",
  //   "qty_green_max_op",
  //   "dcp_max_op",
  //   "tdn_max_op",
  //   "qty_dry_max_op",
  //   "qty_green_min_op",
  //   "qty_dry_min_op",
  //   "qty_concentrate_min_op",
  //   "dm_min_op",
  //   "dm_max_op",
  //   "dcp_min_op"
  // ]
  rules: {
    lowYeilder: {
      dm_min_op: 0.9,
      dm_max_op: 1.1,
      dm_min_must: 0.7,
      dm_max_must: 1.3,

      dcp_min_op: 1.0,
      dcp_max_op: 1.1,
      dcp_min_must: 0.8,
      dcp_max_must: 1.2,

      tdn_min_op: 1,
      tdn_max_op: 1.2,
      tdn_min_must: 0.9,
      tdn_max_must: 1.5,

      dm_green_min_op: 0.05,
      dm_green_max_op: 0.5,
      dm_green_min_must: 0.05,
      dm_green_max_must: 0.5,

      dm_dry_min_op: 0.3,
      dm_dry_max_op: 0.95,

      dm_dry_min_must: 0.3,
      dm_dry_max_must: 0.5,

      // dm_concentrate_min_op: 0, // now removed
      // dm_concentrate_max_op: 0.4,
      qty_min_must: 4,
      qty_max_must: 30,

      qty_green_min_op: 1,
      qty_green_max_op: 25,

      qty_green_min_must: 1,
      qty_green_max_must: 20,

      qty_dry_min_op: 2,
      qty_dry_max_op: 4,

      qty_dry_min_must: 1,
      qty_dry_max_must: 5,

      qty_concentrate_min_op: 0.28,
      qty_concentrate_max_op: 0.32,
      qty_concentrate_min_must: 0.23,
      qty_concentrate_max_must: 0.35,

      // qty_concentrate_min_must: 0,  // put in code 0.28/litre to 0.32/litre
      // qty_concentrate_max_must: 8,
      qty_silage_max_must: 12
    },
    highYeilder: {
      dm_min_op: 0.9,
      dm_max_op: 1.1,
      dm_min_must: 0.7,
      dm_max_must: 1.3,

      dcp_min_op: 1,
      dcp_max_op: 1.2,
      dcp_min_must: 0.8,
      dcp_max_must: 1.3,

      tdn_min_op: 1,
      tdn_max_op: 1.2,
      tdn_min_must: 0.9,
      tdn_max_must: 1.5,

      dm_green_min_op: 0.4,
      dm_green_max_op: 0.5,
      dm_green_min_must: 0.3,
      dm_green_max_must: 0.8,

      dm_dry_min_op: 0.2,
      dm_dry_max_op: 0.5,

      dm_dry_min_must: 0.05,
      dm_dry_max_must: 0.5,

      // dm_concentrate_min_op: 0, // now removed
      // dm_concentrate_max_op: 0.4,
      qty_min_must: 16,
      qty_max_must: 50,

      qty_green_min_op: 15,
      qty_green_max_op: 30,

      qty_green_min_must: 13,
      qty_green_max_must: 40,

      qty_dry_min_op: 2,
      qty_dry_max_op: 4,

      qty_dry_min_must: 1,
      qty_dry_max_must: 5,

      // qty_concentrate_min_must: 0,  // put in code 0.28/litre to 0.32/litre
      // qty_concentrate_max_must: 8,
      qty_concentrate_min_op: 0.28,
      qty_concentrate_max_op: 0.32,
      qty_concentrate_min_must: 0.23,
      qty_concentrate_max_must: 0.35,

      qty_silage_max_must: 15
    }
  },
  optionalConstraints: [
    'qty_dry_min_op', 'qty_dry_max_op',
    'qty_green_min_op', 'qty_green_max_op',
    'qty_concentrate_min_op', 'qty_concentrate_max_op',
    'dm_min_op', 'dm_max_op',
    'dm_green_min_op', 'dm_green_max_op',
    'dm_dry_min_op', 'dm_dry_max_op',
    'dm_concentrate_min_op', 'dm_concentrate_max_op',
    'tdn_min_op', 'tdn_max_op',
    'dcp_min_op', 'dcp_max_op'
  ]

}

module.exports = { feedMaster }
