/* eslint-disable eqeqeq */
/* eslint-disable camelcase */

const { dbConnections, postProcessRecords } = require('@krushal-it/ah-orm')
const { saveClassificationData } = require('@krushal-it/back-end-lib')
const { extractTemplateConfigurationForTemplateKey, downloadLatestTemplate, generateFinalDocumentFromTemplate, convertWordDocumentToPDFInStream, saveGeneratedDocumentIntoKey } = require('../../utils/helpers/template.helper')
const { adjustStartDatesOfEntityStatusByCategoryAndEntities, reportQueryClassificationJoinConfiguration, generateSelectJoinClauseForClassification, generateWhereClauseForReportInnerQuery: generateWhereClause, generateLimitStringForReportInnerQuery: generateLimitString, generateSortStringForReportInnerQuery: generateSortString, identifyMandatoryColumnsForReportQuery, extractFieldsNeedingFilterData, extractFieldsNeedingPageFilterData } = require('../../utils/query/query.helper');

class SmartRationDocument {
  async find (params) {
    return { return_code: 0, refData }
  }

  async create (data, params) {
    try {
      const returnValue = { return_code: 0 }
      /* const reportDataForLanguage = {
        unique_key_for_file: reportId,
        report_template_key: 'FARMER_LEVEL_FEED_RECOMMENDATION',
        // document_type_id: reportConfiguration['document_type_id'],
        language: individualLanguage,
        file_name: 'Smart Ration - ' + reportData['report_type_for_file_name'] + ' - ' + customerData['mobile_number'].slice(-10) + ' - ' + individualLanguage + '.pdf',
        file_location_on_s3: 'smartration',
        report_data: {
          ...customerDataForLanguage,
          report_type: reportType,
          report_date: reportData['feed_generation_date'],
          mfa: reportMFA,
          animals: reportAnimals,
        },
      } */

      console.log('sr SRD c 1')
      const uniqueKeyForFileToS3KeyMap = {}
      returnValue['unique_key_for_file_to_s3_key_map'] = uniqueKeyForFileToS3KeyMap
      for (const individualReportAndConfiguration of data) {
        const uniqueKeyForFile = individualReportAndConfiguration['unique_key_for_file']
        const reportTemplateKey = individualReportAndConfiguration['report_template_key']
        const reportLanguage = individualReportAndConfiguration['language']
        const reportData = individualReportAndConfiguration['report_data']
        const s3FileName = individualReportAndConfiguration['file_name']
        const fileLocationOnS3 = individualReportAndConfiguration['file_location_on_s3']
        const templateConfiguration = extractTemplateConfigurationForTemplateKey(reportTemplateKey)
        const folderIdInS3ToStoreFile = templateConfiguration['DOCX_TO_PDF_CONVERSION_FOLDER_ID']
        const latestSmartRationTemplate = await downloadLatestTemplate(reportTemplateKey, templateConfiguration, reportLanguage)
        // console.log('sr SRD c 2, latestSmartRationTemplate = ', latestSmartRationTemplate)
        // const dataForTemplate = {"customer_name":"Test khushi","customer_visual_id":"MHPUIND000016","mfa":[{"feed":"Green Maize","quantity":1260,"type":"Green","cost":1250},{"feed":"Dry Soyabean Straw","quantity":420,"type":"Dry","cost":1890},{"feed":"Sugarcane","quantity":60,"type":"Green","cost":270},{"feed":"Productivity Feeds Cattle Feed Premix","quantity":408.5,"type":"Concentrate","cost":30641},{"feed":"Mineral mixture","quantity":3.75,"type":"Mineral Mixture","cost":563},{"feed":"Salt","quantity":1.67,"type":"Salt","cost":33}],"animals":[{"sn":1,"tag":7578,"weight":456,"bs":3.6,"mypd":10.3,"mf":3.5,"pm":8,"dim":113,"pmypd":11.8,"cost":201.7,"doesNotHaveDfr":true,"hasDfr":false,"dfr":[]},{"sn":2,"tag":1233,"weight":543,"bs":4.3,"mypd":15.3,"mf":3.5,"pm":5,"dim":113,"pmypd":16.8,"cost":151.7,"doesNotHaveDfr":false,"hasDfr":true,"dfr":[{"feed":"Green Maize","quantity":18,"type":"Green","u":"kg","cost":36},{"feed":"Dry Soyabean Straw","quantity":5,"type":"Dry","u":"kg","cost":22.5},{"feed":"Productivity Feeds Cattle Feed Premix","quantity":4.5,"u":"kg","type":"Concentrate","cost":336},{"feed":"Mineral Mixture","quantity":50,"u":"gm","type":"Mineral Mixture","cost":8},{"feed":"Salt","quantity":23,"u":"gm","type":"Salt","cost":0.5}]}]}
        console.log('sr SRD c 3, dataForTemplate = ', reportData)
        const generatedDocumentAsBuffer = await generateFinalDocumentFromTemplate(latestSmartRationTemplate, reportData)
        // const folderId = configurationJSON().AWS.DOCX_TO_PDF_CONVERSION_FOLDER_ID // '1gY-3A29_Z1p23dPVBSXxnV1ezB0wxwh6'

        // console.log('sr SRD c 4, generatedDocumentAsBuffer = ', generatedDocumentAsBuffer)
        const pdfAsStream = await convertWordDocumentToPDFInStream(generatedDocumentAsBuffer, folderIdInS3ToStoreFile)
        // console.log('sr SRD c 5, pdfAsStream = ', pdfAsStream)
        const fileS3Key = await saveGeneratedDocumentIntoKey(pdfAsStream, s3FileName, fileLocationOnS3, 'application/pdf')
        console.log('sr SRD c 6, fileS3Key = ', fileS3Key)  
        uniqueKeyForFileToS3KeyMap[uniqueKeyForFile] = fileS3Key
      }
      return returnValue
    } catch (error) {
      console.log(error)
      throw error
    }
  }
}

class SmartRationReportList {
  async create (data, params) {
    try {
      const returnValue = { return_code: 0 }
      const mainDBConnection = dbConnections().main

      const limitString = generateLimitString(data)
      const sortString = generateSortString(data) 
      const whereClause = generateWhereClause(data) 

      const customerId = data['customer_id']

      const innerQuery = `
        select srrt_rr.reference_id as report_type_id, srr.asset_id as report_id, srr.created_at report_date,
          coalesce(srrt_rr.reference_name_l10n->>'en', srrt_rr.reference_name_l10n->>'ul') as report_type,
          srrac.number_of_animals_in_report, srracg.number_of_animals_with_sr_in_report,
          sr_d_en.document_id as en_document_id, sr_d_en.document_information as en_document_information,
          sr_d_mr.document_id as mr_document_id, sr_d_mr.document_information as mr_document_information,
          sr_d_ta.document_id as ta_document_id, sr_d_ta.document_information as ta_document_information,
          sr_d_te.document_id as te_document_id, sr_d_te.document_information as te_document_information
        from main.customer c 
        inner join main.entity_relationship c2srr on c2srr.entity_relationship_type_id = 1000210027 and c2srr.active = 1000100001
          and c2srr.entity_1_entity_uuid = c.customer_id and c2srr.entity_1_type_id = 1000460001 and c2srr.entity_2_type_id = 1000460012
        inner join main.asset srr on srr.asset_type_id = 1001120003 and srr.active = 1000100001 and srr.asset_id = c2srr.entity_2_entity_uuid 
        inner join main.asset_classification srr_ac on srr_ac.classifier_id = 2000000401 and srr_ac.active = 1000100001 and srr_ac.asset_id = srr.asset_id
        inner join main.ref_reference srrt_rr on srr_ac.value_reference_id = srrt_rr.reference_id
        left join (
          select fr2ar.entity_1_entity_uuid report_id, count(fr2ar.entity_2_entity_uuid) as number_of_animals_in_report/* */
          from main.entity_relationship fr2ar
          where fr2ar.active = 1000100001 and fr2ar.entity_relationship_type_id = 1000210032
          group by fr2ar.entity_1_entity_uuid
        ) srrac on srrac.report_id = srr.asset_id
        left join (
          select fr2ar.entity_1_entity_uuid report_id, count(fr2ar.entity_2_entity_uuid) as number_of_animals_with_sr_in_report
          from main.entity_relationship fr2ar
          inner join main.asset_classification ac on ac.active = 1000100001 and ac.classifier_id = 2000000419 and ac.value_reference_id = 1000105001
            and ac.asset_id = fr2ar.entity_2_entity_uuid
          where fr2ar.active = 1000100001 and fr2ar.entity_relationship_type_id = 1000210032
          group by fr2ar.entity_1_entity_uuid
        ) srracg on srracg.report_id = srr.asset_id
        left join main.document sr_d_en on sr_d_en.active = 1000100001 and sr_d_en.entity_1_type_id = 1000460012 and sr_d_en.entity_1_entity_uuid = srr.asset_id
          and sr_d_en.document_information->>'language' = 'en'
        left join main.document sr_d_mr on sr_d_mr.active = 1000100001 and sr_d_mr.entity_1_type_id = 1000460012 and sr_d_mr.entity_1_entity_uuid = srr.asset_id
          and sr_d_mr.document_information->>'language' = 'mr'
        left join main.document sr_d_ta on sr_d_ta.active = 1000100001 and sr_d_ta.entity_1_type_id = 1000460012 and sr_d_ta.entity_1_entity_uuid = srr.asset_id
          and sr_d_ta.document_information->>'language' = 'ta'
        left join main.document sr_d_te on sr_d_te.active = 1000100001 and sr_d_te.entity_1_type_id = 1000460012 and sr_d_te.entity_1_entity_uuid = srr.asset_id
          and sr_d_te.document_information->>'language' = 'te'
        where c.customer_id = '${customerId}'
      `
      const countQuery = `
        select count(*) as count from (
          ${innerQuery}
        ) outerTable
        ${whereClause}
      `
      const reportResultCount = await mainDBConnection.manager.query(countQuery)
      const count = parseInt(reportResultCount[0].count)

      let query = `
        select * 
        from (
          ${innerQuery}
        ) outerTable
        ${whereClause}
        ${sortString}
        ${limitString}
      `

      const reportResultUnprocessed = await mainDBConnection.manager.query(query)
      const reportResult = postProcessRecords(undefined, reportResultUnprocessed, {json_columns: ['document_information']})

      returnValue.count = count
      returnValue.report = reportResult

      return returnValue
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

const geographyRelatedJoin = [
  `
    coalesce(gcc_v.taluk, gcc_t.taluk) taluk, coalesce(gcc_v.taluk_id, gcc_t.taluk_id) taluk_id, gcc_v.village, gcc_v.village_id,
  `,
  `
    left join (
      select gcc.customer_id,
        coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') as taluk,
        coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') as village,
        rsv.village_id, rsv.taluk_id
      from main.customer_classification gcc, main.ref_sdtv_view_2 rsv
      where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
      and gcc.value_reference_id = rsv.village_id
    ) gcc_v on gcc_v.customer_id = c.customer_id
    left join (
      select gcc.customer_id,
        coalesce(t_rg.geography_name_l10n->>'en', t_rg.geography_name_l10n->>'ul') as taluk,
        t_rg.geography_id as taluk_id
      from main.customer_classification gcc
      inner join main.ref_geography t_rg on gcc.value_reference_id = t_rg.geography_id
        and t_rg.geography_type_id = 1000320003
      where gcc.classifier_id = 2000000054 and gcc.active = 1000100001
    ) gcc_t on gcc_t.customer_id = c.customer_id
  `
]

const numberOfActiveAnimalsRelatedColumnsQueryConfiguration = [
  `
    cac.active_animal_count,
  `,
  `
    left join (
      select f2a.entity_1_entity_uuid customer_id, count(f2a.entity_2_entity_uuid) active_animal_count
      from main.entity_relationship f2a
      inner join main.animal a on a.animal_id = f2a.entity_2_entity_uuid and a.active = 1000100001
      where f2a.active = 1000100001 and f2a.entity_relationship_type_id = 1000210004
      group by f2a.entity_1_entity_uuid
    ) cac on cac.customer_id = c.customer_id
  `
]

const smartRationAttributeLastUpdatedAtColumnConfiguration = [
  `
    greatest(srflu.smart_ration_attribute_last_updated_at, sralu.smart_ration_attribute_last_updated_at) as smart_ration_attribute_last_updated_at,
  `,
  `
    left join (
      select max(sr_ac.updated_at) smart_ration_attribute_last_updated_at, f2a.entity_1_entity_uuid as customer_id
      from main.animal_classification sr_ac
      inner join main.entity_relationship f2a on f2a.active = 1000100001 and f2a.entity_relationship_type_id = 1000210004
        and f2a.entity_2_entity_uuid = sr_ac.animal_id
      where sr_ac.active = 1000100001 and sr_ac.classifier_id in (2000000041, 2000000103, 2000000172, 2000000064, 2000000163, 2000000067, 2000000165)
      group by f2a.entity_1_entity_uuid
    ) sralu on sralu.customer_id = c.customer_id
    left join (
      select max(cc.updated_at) smart_ration_attribute_last_updated_at, cc.customer_id
      from main.customer_classification cc
      where cc.active = 1000100001 and cc.classifier_id in (2000000169, 2000000170)
      group by cc.customer_id
    ) srflu on srflu.customer_id = c.customer_id
  `
]

class PerCustomerLatestSmartRationReport {
  async create (data, params) {
    try {
      const returnValue = { return_code: 0 }
      const mainDBConnection = dbConnections().main

      const limitString = generateLimitString(data)
      const whereClause = generateWhereClause(data) 

      const selectedColumns = data.selectedColumns
      let includeGeographyInQuery = false
      let includeActiveAnimalCountInQuery = false
      let includeSmartRationAttributeLastUpdatedAtColumnsInQuery = false
      if (selectedColumns.includes('taluk') || selectedColumns.includes('village')) {
        includeGeographyInQuery = true
      }
      if (selectedColumns.includes('active_animal_count')) {
        includeActiveAnimalCountInQuery = true
      }
      if (selectedColumns.includes('smart_ration_attribute_last_updated_at')) {
        includeSmartRationAttributeLastUpdatedAtColumnsInQuery = true
      }

      const innerQuery = `
        WITH LatestReports AS (
          SELECT 
            c.customer_id, 
            srrd.asset_id AS srrd_id, 
            srrd.created_at AS srrd_creation_date,
            lsrt_ac.value_reference_id AS report_type_id,
            ROW_NUMBER() OVER (PARTITION BY c.customer_id, lsrt_ac.value_reference_id ORDER BY srrd.created_at DESC) AS rank_from_latest
          FROM main.asset srrd
          INNER JOIN main.entity_relationship f2r ON f2r.active = 1000100001 
            AND f2r.entity_relationship_type_id = 1000210027 
            AND f2r.entity_1_type_id = 1000460001 
            AND f2r.entity_2_type_id = 1000460012 
            AND f2r.entity_2_entity_uuid = srrd.asset_id
          INNER JOIN main.customer c ON c.customer_id = f2r.entity_1_entity_uuid 
          LEFT JOIN main.asset_classification lsrt_ac ON lsrt_ac.active = 1000100001 
            AND lsrt_ac.classifier_id = 2000000401
            AND lsrt_ac.asset_id = srrd.asset_id
          WHERE srrd.asset_type_id = 1001120003 
            AND srrd.active = 1000100001 
            AND c.active = 1000100001
        ),
        sgpf AS (
          SELECT 
            customer_id, 
            srrd_id AS sgpf_report_id,
            srrd_creation_date AS sgpf_creation_date
          FROM LatestReports
          WHERE report_type_id = 1001500001
            AND rank_from_latest = 1
        ),
        sgff AS (
          SELECT 
            customer_id, 
            srrd_id AS sgff_report_id,
            srrd_creation_date AS sgff_creation_date
          FROM LatestReports
          WHERE report_type_id = 1001500002
            AND rank_from_latest = 1
        ),
        mg AS (
          SELECT 
            customer_id, 
            srrd_id AS mg_report_id,
            srrd_creation_date AS mg_creation_date
          FROM LatestReports
          WHERE report_type_id = 1001500003
            AND rank_from_latest = 1
        )
        SELECT 
          c.customer_id,
          coalesce(c.customer_name_l10n->>'en', c.customer_name_l10n->>'ul') as customer_name,
          case when sgpf.sgpf_report_id is not null or sgff.sgff_report_id is not null or mg.mg_report_id is not null then 1000105001 else 1000105002 end as smart_ration_generated_or_not_id,
          case when sgpf.sgpf_report_id is not null or sgff.sgff_report_id is not null or mg.mg_report_id is not null then 'Yes' else 'No' end as smart_ration_generated_or_not,
          sgpf.sgpf_report_id as s_g_r_id,
          sgpf.sgpf_creation_date as last_system_generated_report_date,
          sgpf.sgpf_creation_date as s_g_r_date,
          sgsrtac.s_g_n_o_a_i_r, sgsrsrac.s_g_n_o_a_i_r_w_sr,
          sr_pf_d_en.document_information as s_g_r_d_i_en, sr_pf_d_mr.document_information as s_g_r_d_i_mr,
          sr_pf_d_ta.document_information as s_g_r_d_i_ta, sr_pf_d_te.document_information as s_g_r_d_i_te,
          sgff.sgff_report_id as s_g_f_f_r_id, sgff.sgff_creation_date as s_g_f_f_r_date,
          sgffsrtac.s_g_f_f_n_o_a_i_r, sgffsrsrac.s_g_f_f_n_o_a_i_r_w_sr,
          sr_ff_d_en.document_information as s_g_f_f_r_d_i_en, sr_ff_d_mr.document_information as s_g_f_f_r_d_i_mr,
          sr_ff_d_ta.document_information as s_g_f_f_r_d_i_ta, sr_ff_d_te.document_information as s_g_f_f_r_d_i_te,
          mg.mg_report_id as v_g_r_id,
          mg.mg_creation_date v_g_r_date,
          vgsrtac.v_g_n_o_a_i_r, vgsrsrac.v_g_n_o_a_i_r_w_sr,
          sr_mg_d_en.document_information as v_g_r_d_i_en, sr_mg_d_mr.document_information as v_g_r_d_i_mr,
          sr_mg_d_ta.document_information as v_g_r_d_i_ta, sr_mg_d_te.document_information as v_g_r_d_i_te,
          ${includeGeographyInQuery === true ? geographyRelatedJoin[0] : ''}
          ${includeActiveAnimalCountInQuery === true ? numberOfActiveAnimalsRelatedColumnsQueryConfiguration[0] : ''}
          ${includeSmartRationAttributeLastUpdatedAtColumnsInQuery === true ? smartRationAttributeLastUpdatedAtColumnConfiguration[0] : ''}
          c.mobile_number,
          c.active as customer_active_id,
          case when c.active = 1000100001 then 'Active' else 'Not Active' end as customer_active
        FROM main.customer c
        ${includeGeographyInQuery === true ? geographyRelatedJoin[1] : ''}
        ${includeActiveAnimalCountInQuery === true ? numberOfActiveAnimalsRelatedColumnsQueryConfiguration[1] : ''}
        ${includeSmartRationAttributeLastUpdatedAtColumnsInQuery === true ? smartRationAttributeLastUpdatedAtColumnConfiguration[1] : ''}
        LEFT JOIN sgpf ON sgpf.customer_id = c.customer_id
        LEFT JOIN sgff ON sgff.customer_id = c.customer_id
        LEFT JOIN mg ON mg.customer_id = c.customer_id
        left join (
          select sgsr.entity_1_entity_uuid as report_id, count(sgsr.entity_2_entity_uuid) as s_g_n_o_a_i_r
          from main.entity_relationship sgsr
          where sgsr.active = 1000100001 and sgsr.entity_relationship_type_id = 1000210032 and sgsr.entity_1_type_id = 1000460012
            and sgsr.entity_2_type_id = 1000460012
          group by sgsr.entity_1_entity_uuid
        ) sgsrtac on sgsrtac.report_id = sgpf.sgpf_report_id
        left join (
          select sgsr.entity_1_entity_uuid as report_id, count(sgsr.entity_2_entity_uuid) as s_g_n_o_a_i_r_w_sr
          from main.entity_relationship sgsr
          inner join main.asset_classification alsr_ac on alsr_ac.active = 1000100001
            and alsr_ac.asset_id = sgsr.entity_2_entity_uuid
            and alsr_ac.classifier_id = 2000000419 and alsr_ac.value_reference_id = 1000105001
          where sgsr.active = 1000100001 and sgsr.entity_relationship_type_id = 1000210032 and sgsr.entity_1_type_id = 1000460012
            and sgsr.entity_2_type_id = 1000460012
          group by sgsr.entity_1_entity_uuid
        ) sgsrsrac on sgsrsrac.report_id = sgpf.sgpf_report_id
        left join main.document sr_pf_d_en on sr_pf_d_en.active = 1000100001 and sr_pf_d_en.entity_1_type_id = 1000460012
          and sr_pf_d_en.entity_1_entity_uuid = sgpf.sgpf_report_id and sr_pf_d_en.document_information->>'language' = 'en'
        left join main.document sr_pf_d_mr on sr_pf_d_mr.active = 1000100001 and sr_pf_d_mr.entity_1_type_id = 1000460012
          and sr_pf_d_mr.entity_1_entity_uuid = sgpf.sgpf_report_id and sr_pf_d_mr.document_information->>'language' = 'mr'
        left join main.document sr_pf_d_ta on sr_pf_d_ta.active = 1000100001 and sr_pf_d_ta.entity_1_type_id = 1000460012
          and sr_pf_d_ta.entity_1_entity_uuid = sgpf.sgpf_report_id and sr_pf_d_ta.document_information->>'language' = 'ta'
        left join main.document sr_pf_d_te on sr_pf_d_te.active = 1000100001 and sr_pf_d_te.entity_1_type_id = 1000460012
          and sr_pf_d_te.entity_1_entity_uuid = sgpf.sgpf_report_id and sr_pf_d_te.document_information->>'language' = 'te'
        left join (
          select sgsr.entity_1_entity_uuid as report_id, count(sgsr.entity_2_entity_uuid) as s_g_f_f_n_o_a_i_r
          from main.entity_relationship sgsr
          where sgsr.active = 1000100001 and sgsr.entity_relationship_type_id = 1000210032 and sgsr.entity_1_type_id = 1000460012
            and sgsr.entity_2_type_id = 1000460012
          group by sgsr.entity_1_entity_uuid
        ) sgffsrtac on sgffsrtac.report_id = sgff.sgff_report_id
        left join (
          select sgsr.entity_1_entity_uuid as report_id, count(sgsr.entity_2_entity_uuid) as s_g_f_f_n_o_a_i_r_w_sr
          from main.entity_relationship sgsr
          inner join main.asset_classification alsr_ac on alsr_ac.active = 1000100001
            and alsr_ac.asset_id = sgsr.entity_2_entity_uuid
            and alsr_ac.classifier_id = 2000000419 and alsr_ac.value_reference_id = 1000105001
          where sgsr.active = 1000100001 and sgsr.entity_relationship_type_id = 1000210032 and sgsr.entity_1_type_id = 1000460012
            and sgsr.entity_2_type_id = 1000460012
          group by sgsr.entity_1_entity_uuid
        ) sgffsrsrac on sgffsrsrac.report_id = sgff.sgff_report_id
        left join main.document sr_ff_d_en on sr_ff_d_en.active = 1000100001 and sr_ff_d_en.entity_1_type_id = 1000460012
          and sr_ff_d_en.entity_1_entity_uuid = sgff.sgff_report_id and sr_ff_d_en.document_information->>'language' = 'en'
        left join main.document sr_ff_d_mr on sr_ff_d_mr.active = 1000100001 and sr_ff_d_mr.entity_1_type_id = 1000460012
          and sr_ff_d_mr.entity_1_entity_uuid = sgff.sgff_report_id and sr_ff_d_mr.document_information->>'language' = 'mr'
        left join main.document sr_ff_d_ta on sr_ff_d_ta.active = 1000100001 and sr_ff_d_ta.entity_1_type_id = 1000460012
          and sr_ff_d_ta.entity_1_entity_uuid = sgff.sgff_report_id and sr_ff_d_ta.document_information->>'language' = 'ta'
        left join main.document sr_ff_d_te on sr_ff_d_te.active = 1000100001 and sr_ff_d_te.entity_1_type_id = 1000460012
          and sr_ff_d_te.entity_1_entity_uuid = sgff.sgff_report_id and sr_ff_d_te.document_information->>'language' = 'te'
        left join (
          select sgsr.entity_1_entity_uuid as report_id, count(sgsr.entity_2_entity_uuid) as v_g_n_o_a_i_r
          from main.entity_relationship sgsr
          where sgsr.active = 1000100001 and sgsr.entity_relationship_type_id = 1000210032 and sgsr.entity_1_type_id = 1000460012
            and sgsr.entity_2_type_id = 1000460012
          group by sgsr.entity_1_entity_uuid
        ) vgsrtac on vgsrtac.report_id = mg.mg_report_id
        left join (
          select sgsr.entity_1_entity_uuid as report_id, count(sgsr.entity_2_entity_uuid) as v_g_n_o_a_i_r_w_sr
          from main.entity_relationship sgsr
          inner join main.asset_classification alsr_ac on alsr_ac.active = 1000100001
            and alsr_ac.asset_id = sgsr.entity_2_entity_uuid
            and alsr_ac.classifier_id = 2000000419 and alsr_ac.value_reference_id = 1000105001
          where sgsr.active = 1000100001 and sgsr.entity_relationship_type_id = 1000210032 and sgsr.entity_1_type_id = 1000460012
            and sgsr.entity_2_type_id = 1000460012
          group by sgsr.entity_1_entity_uuid
        ) vgsrsrac on vgsrsrac.report_id = mg.mg_report_id
        left join main.document sr_mg_d_en on sr_mg_d_en.active = 1000100001 and sr_mg_d_en.entity_1_type_id = 1000460012
          and sr_mg_d_en.entity_1_entity_uuid = mg.mg_report_id and sr_mg_d_en.document_information->>'language' = 'en'
        left join main.document sr_mg_d_mr on sr_mg_d_mr.active = 1000100001 and sr_mg_d_mr.entity_1_type_id = 1000460012
          and sr_mg_d_mr.entity_1_entity_uuid = mg.mg_report_id and sr_mg_d_mr.document_information->>'language' = 'mr'
        left join main.document sr_mg_d_ta on sr_mg_d_ta.active = 1000100001 and sr_mg_d_ta.entity_1_type_id = 1000460012
          and sr_mg_d_ta.entity_1_entity_uuid = mg.mg_report_id and sr_mg_d_ta.document_information->>'language' = 'ta'
        left join main.document sr_mg_d_te on sr_mg_d_te.active = 1000100001 and sr_mg_d_te.entity_1_type_id = 1000460012
          and sr_mg_d_te.entity_1_entity_uuid = mg.mg_report_id and sr_mg_d_te.document_information->>'language' = 'te'
        where 1 = 1 and c.customer_type_id in (1000220009, 1000220007, 1000220001)
      `

      console.log('be s aF src PCLSRR c 1, innerQuery = ', innerQuery)

      const countQuery = `
        select count(*) as count from (
          ${innerQuery}
        ) outerTable
        ${whereClause}
      `
      const reportResultCount = await mainDBConnection.manager.query(countQuery)
      const count = parseInt(reportResultCount[0].count)

      let query = `
        select * 
        from (
          ${innerQuery}
        ) outerTable
        ${whereClause}
        ${limitString}
      `

      const reportResultUnprocessed = await mainDBConnection.manager.query(query)
      const reportResult = postProcessRecords(undefined, reportResultUnprocessed, {json_columns: ['document_information']})

      returnValue.count = count
      returnValue.report = reportResult

      return returnValue
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class SetAssetDetails {
  async create /*or is it update*/(data, params) {
    try {
      console.log('oc r SAD c 1')
      const returnValue = {return_code: 0}
      const assetIdArray = data.assetIdArray
      const classificationData = data.classificationData
      if (assetIdArray.length === 0) {
        throw new Error('Need at least 1 report for updating')
      }
      const mainDBConnection = dbConnections().main
      const saveResult = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
        try {
          for (const assetId of assetIdArray) {
            const updateClassificationDataResult = await saveClassificationData(transactionalEntityManager, 'ASSET_CLASSIFICATION', assetId, classificationData)
          }
        } catch (error) {
          console.log('oc SAD c 20, error')
          console.log('oc SAD c 20a, error = ', error)
          throw error
        }
      })
      return returnValue
    } catch (error) {
      console.log('oc r SAD c 10, error')
      console.log('oc r SAD c 10a, error = ', error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

module.exports = { SmartRationDocument, SmartRationReportList, PerCustomerLatestSmartRationReport, SetAssetDetails }

