/* eslint-disable camelcase */
const { LibHealthScore } = require('@krushal-it/back-end-lib')
const { extractBasedOnLanguage } = require('@krushal-it/common-core')
const { loadClassificationData } = require('@krushal-it/back-end-lib')

const _ = require('lodash')

function convertBooleanAndNumericValues (obj) {
  if (!_.isObject(obj)) {
    if (_.isString(obj)) {
      if (obj === 'true') return true
      if (obj === 'false') return false
      if (obj === 'null') return null
      if (obj === 'undefined') return undefined
      if (!isNaN(obj)) {
        const parsed = parseFloat(obj)
        if (Number.isInteger(parsed)) return parsed
        return parsed
      }
    }
    return obj
  }
  if (_.isArray(obj)) return _.map(obj, convertBooleanAndNumericValues)
  return _.mapValues(obj, (value) => convertBooleanAndNumericValues(value))
}

function convertBooleanAndNumericValuesDeep (obj) {
  return _.cloneDeep(convertBooleanAndNumericValues(obj))
}
const isEmpty = (value) => {
  if (value === undefined || value == null || value === '' | value === 'null' || value === 'undefined' ||
  value === '[]' || value === '{}' || value === '0' || value === 0 || value === '0.0' || value === 0.0 || value === [] || value === {}) {
    return true
  } else return false
}

// this is to recalculate for given list of animals
class HealthScoreRecalculate {
  async get (animal_id, params) {
    console.log('healthScore/reCalculate GET', params)
    return { return_code: 0, data: 'healthScore/healthScore GET' }
  }

  async find (params) {
    console.log('healthScore/healthScore GET', params)
    return { return_code: 0, data: 'healthScore/healthScore GET' }
  }

  async create (data, params) {
    console.log('healthScore/healthScore POST', data, params)
    // data will be multiple animal_ids, for each calculate persist and return new healthScore value
    const animal_ids = data.animal_ids || [data.animal_id]
    if (!animal_ids || animal_ids.length === 0) {
      return { return_code: 1, error: 'animal_ids not found' }
    }

    // get animal data for given anial_ids
    const hs = new LibHealthScore()
    const healthScores = {}
    for (const animal_id of data.animal_ids) {
      const healthScoreData = await hs.reCalculateHealthScore(animal_id)
      await hs.saveHealthScore({ animal_id, ...healthScoreData }, { headers: params.headers })
      healthScores[animal_id] = healthScoreData
    }
    return { return_code: 0, data: healthScores }
  }
}

module.exports = { HealthScoreRecalculate }
