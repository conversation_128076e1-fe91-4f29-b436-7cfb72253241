const { BadRequest } = require("@feathersjs/errors")
const { ObservationServiceV2 ,ObservationCategoryService, ObservationNotes} = require("@krushal-it/back-end-lib")
class Observation {
    os= new ObservationServiceV2()
    async create(data, params) {
        try {
         let response = await this.os.save(data,params)
         return  response
        } catch (error) {
          return new BadRequest(error).toJSON()
        }
      }
    async get(id, params) {
        try {
            let care_calendar_id = params.query.care_calendar_id
            let response = await this.os.getObservationAndConfig(id,care_calendar_id)
            return response
        } catch (error) {
            return error
        }
        
    }
}

class ObservationCategory {
    ocs= new ObservationCategoryService()
    async get(id, params) {
        try {
            // let care_calendar_id = params.query.care_calendar_id
            let response = await this.ocs.getObservationCategory(id)
            return response
        } catch (error) {   
           return error
        }
        
    }
}

class ObservationNote {
    osn= new ObservationNotes()
    async create(data, params) {
        try {
         let response = await this.osn.saveObservationNote(data,params)
         return  response
        } catch (error) {
          return new BadRequest(error).toJSON()
        }
      }
    async get(id, params) {
        try {
            let response = await this.osn.getObservationNote(id)
            return response
        } catch (error) {
            return error
        }
        
    }
}

module.exports = { Observation ,ObservationCategory , ObservationNote}
