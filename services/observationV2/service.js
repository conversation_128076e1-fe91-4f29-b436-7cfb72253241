
const { Observation ,ObservationCategory , ObservationNote} = require("./observation.class")
const tokenInject = require("../../middleware/tokenInject")


const observationServicesV2 = app => {
  app.use("v2/observations", new Observation(), tokenInject)
  app.use("v2/observations-category", new ObservationCategory(), tokenInject)
  app.use("v2/observation-notes", new ObservationNote(), tokenInject)
}

module.exports = { observationServicesV2 }
