const lodashObject = require("lodash")

const { getDBConnections, dbConnections, postProcessRecords, optionallyAddPrimaryKeyToRecords } = require("@krushal-it/ah-orm")
const { configurationJSON, KrushalError } = require("@krushal-it/common-core")
const { CustomerListLib, CustomerLib } = require("@krushal-it/back-end-lib")
const { loadClassificationData, saveClassificationData } = require("@krushal-it/back-end-lib")
const { adjustStartDatesOfEntityStatusByCategoryAndEntities, reportQueryClassificationJoinConfiguration, generateSelectJoinClauseForClassification, generateWhereClauseForReportInnerQuery: generateWhereClause, generateLimitStringForReportInnerQuery: generateLimitString, generateSortStringForReportInnerQuery: generateSortString, identifyMandatoryColumnsForReportQuery, extractFieldsNeedingFilterData, extractFieldsNeedingPageFilterData } = require("../../utils/query/query.helper")
const { CONST_PARTNER_PHARMACY, INACTIVE, ACTIVE, VILLAGE_TYPE_ID } = require("@krushal-it/back-end-lib/utils/constant")
const { getCompiledQuery } = require("@krushal-it/back-end-lib")
const { validateUUID } = require("@krushal-it/mobile-or-server-lib")
const { In, MoreThan } = require("typeorm")

const createPartnerInnerQuery = ({ restrictedIds, searchWithinRestrictedIds, selectClauseForClassification, joinClauseForClassification, partnerTypeIdsAsCommaSeparatedString }) => {
  const innerQuery = `
    select p.partner_id,
      coalesce(p.partner_name_l10n->>'en', p.partner_name_l10n->>'ul') as partner_name,
      ${selectClauseForClassification !== undefined && selectClauseForClassification !== "" ? selectClauseForClassification + ", " : ""}
      p.mobile_number, p.active as partner_active_id,
      case when p.active = 1000100001 then 'Active' else 'Not Active' end as partner_active
    from main.partner p
    ${joinClauseForClassification}
    where 1 = 1 and p.partner_type_id in (${partnerTypeIdsAsCommaSeparatedString})
    ${restrictedIds ? searchWithinRestrictedIds : ""}
  `
  return innerQuery
}

const defaultPartnerTypeIds = [1000850001]

class PartnerList {
  async create(data, params) {
    try {
      const returnValue = { return_code: 0 }
      const mainDBConnection = dbConnections().main

      const limitString = generateLimitString(data)
      const sortString = generateSortString(data)
      const whereClause = generateWhereClause(data)
      let searchWithinRestrictedIds = ``

      let partnerTypeIds = [...defaultPartnerTypeIds]

      if (data.partnerTypeIds && Array.isArray(data.partnerTypeIds) && data.partnerTypeIds.length > 0) {
        partnerTypeIds = data.partnerTypeIds
      }

      const partnerTypeIdsAsCommaSeparatedString = partnerTypeIds.join(",")

      // identify all columns needed
      const selectedColumns = data.selectedColumns
      // identify mandatory columns needed
      const mandatoryColumnsForSearch = identifyMandatoryColumnsForReportQuery(data.filters, data.sorting, data.globalSearch, data.searchConfiguration)

      let selectClauseForClassification
      let joinClauseForClassification
      ;[selectClauseForClassification, joinClauseForClassification] = generateSelectJoinClauseForClassification(reportQueryClassificationJoinConfiguration, "PARTNER", undefined, mandatoryColumnsForSearch)

      let restrictedIds = false

      let includeWhereClauseInDetailedColumnsQuery = false

      let innerQuery = createPartnerInnerQuery({
        restrictedIds,
        searchWithinRestrictedIds,
        selectClauseForClassification,
        joinClauseForClassification,
        partnerTypeIdsAsCommaSeparatedString
      })

      const countQuery = `
        select count(*) as count from (
          ${innerQuery}
        ) outerTable
        ${whereClause}
      `
      const reportResultCount = await mainDBConnection.manager.query(countQuery)
      const count = parseInt(reportResultCount[0].count)

      if (count === 0) {
        includeWhereClauseInDetailedColumnsQuery = true
      }

      let query = `
          select * 
          from (
            ${innerQuery}
          ) outerTable
          ${whereClause}
          ${sortString}
          ${limitString}
        `

      const reportResultUnprocessed = await mainDBConnection.manager.query(query)
      const reportResult = postProcessRecords(undefined, reportResultUnprocessed, {})

      const primaryKeyToRowMap = {}
      for (const reportRow of reportResult) {
        primaryKeyToRowMap[reportRow["partner_id"]] = reportRow
      }
      const primaryKeysToBeQueried = Object.keys(primaryKeyToRowMap)

      let inPrimaryKeysIdString = ""
      for (const primaryKey of primaryKeysToBeQueried) {
        if (inPrimaryKeysIdString !== "") {
          inPrimaryKeysIdString = inPrimaryKeysIdString + ", "
        }
        inPrimaryKeysIdString = inPrimaryKeysIdString + "'" + primaryKey + "'"
      }
      searchWithinRestrictedIds = inPrimaryKeysIdString !== "" ? ` and p.partner_id in (${inPrimaryKeysIdString})` : ""

      const filtersFieldToReturnKeyMap = extractFieldsNeedingFilterData(data.searchConfiguration, data.selectedColumns)
      const fieldsNeedingFilterData = Object.keys(filtersFieldToReturnKeyMap)

      const mandatoryColumnsPlusColumnsNeededForFilteringWithoutUniq = [...mandatoryColumnsForSearch, ...fieldsNeedingFilterData]
      const mandatoryColumnsPlusColumnsNeededForFiltering = lodashObject.uniq(mandatoryColumnsPlusColumnsNeededForFilteringWithoutUniq)
      const returnValue0 = generateSelectJoinClauseForClassification(reportQueryClassificationJoinConfiguration, "PARTNER", undefined, mandatoryColumnsPlusColumnsNeededForFiltering)
      console.log("s a ALV3 1, returnValue = ", returnValue)
      selectClauseForClassification = returnValue0[0]
      joinClauseForClassification = returnValue0[1]

      innerQuery = createPartnerInnerQuery({
        restrictedIds,
        searchWithinRestrictedIds,
        selectClauseForClassification,
        joinClauseForClassification,
        partnerTypeIdsAsCommaSeparatedString
      })

      restrictedIds = true

      if (fieldsNeedingFilterData.includes("village_name")) {
        const villageNameFilterKey = fieldsNeedingFilterData.includes("village_name") ? "village_name_filter_values" : undefined

        const updatedData = lodashObject.cloneDeep(data)
        delete updatedData["filters"]["village_name"]

        const whereClauseWithoutFilterField = generateWhereClause(updatedData)

        const distinctVillageQuery = `
          select distinct village_id, village_name value, village_name text from (
            ${innerQuery}
          ) outerTable
          ${whereClauseWithoutFilterField}
        `
        const distinctVillageResultUnprocessed = await mainDBConnection.manager.query(distinctVillageQuery)
        const distinctVillageResult = postProcessRecords(undefined, distinctVillageResultUnprocessed, {})

        if (fieldsNeedingFilterData.includes("village_name")) {
          returnValue[filtersFieldToReturnKeyMap["village_name"]] = distinctVillageResult
        }
      }

      const returnValue1 = generateSelectJoinClauseForClassification(reportQueryClassificationJoinConfiguration, "PARTNER", undefined, selectedColumns)
      selectClauseForClassification = returnValue1[0]
      joinClauseForClassification = returnValue1[1]

      innerQuery = createPartnerInnerQuery({
        restrictedIds,
        searchWithinRestrictedIds,
        selectClauseForClassification,
        joinClauseForClassification,
        partnerTypeIdsAsCommaSeparatedString
      })

      query = `
        select * from (
          ${innerQuery}
        ) outerTable
        ${includeWhereClauseInDetailedColumnsQuery ? whereClause : ""}
        ${sortString}
      `

      const reportResultWithSelectedColumnsUnprocessed = await mainDBConnection.manager.query(query)
      const reportResultWithSelectedColumns = postProcessRecords(undefined, reportResultWithSelectedColumnsUnprocessed, {})

      returnValue.count = count
      returnValue.report = reportResultWithSelectedColumns

      return returnValue
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class PartnerListFilter {
  async create(data, params) {
    try {
      const returnValue = { return_code: 0 }
      const tableFilterData = {}
      returnValue.tableFilters = tableFilterData
      const mainDBConnection = dbConnections().main

      let partnerTypeIds = [...defaultPartnerTypeIds]

      if (data.partnerTypeIds && Array.isArray(data.partnerTypeIds) && data.partnerTypeIds.length > 0) {
        partnerTypeIds = data.partnerTypeIds
      }

      const partnerTypeIdsAsCommaSeparatedString = partnerTypeIds.join(",")
      // identify all columns needed
      const selectedColumns = data.selectedColumns
      const pageFiltersFieldToReturnKeyMap = extractFieldsNeedingPageFilterData(data.searchConfiguration.columnConfiguration, Object.keys(data.searchConfiguration.columnConfiguration) !== undefined && Array.isArray(Object.keys(data.searchConfiguration.columnConfiguration)) ? Object.keys(data.searchConfiguration.columnConfiguration) : [])
      const fieldsNeedingPageLoadFilterData = Object.keys(pageFiltersFieldToReturnKeyMap)

      if (fieldsNeedingPageLoadFilterData.includes("taluk_name")) {
        const talukNameAsFieldNeedingPageLoadFilterData = fieldsNeedingPageLoadFilterData.includes("taluk_name")
        const distinctTalukQuery = `
          select distinct taluk_id, taluk_id as value, coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') as text
          from main.partner_classification pc
          inner join main.partner p on p.partner_id = pc.partner_id and p.partner_type_id in (${partnerTypeIdsAsCommaSeparatedString})
          inner join main.ref_sdtv_view rsv on rsv.village_id = pc.value_reference_id
          where pc.active = 1000100001 and pc.classifier_id = 2000000276
        `
        const distinctTalukFilterValuesResultUnprocessed = await mainDBConnection.manager.query(distinctTalukQuery)
        const distinctTalukFilterValuesResult = postProcessRecords(undefined, distinctTalukFilterValuesResultUnprocessed, {})
        if (talukNameAsFieldNeedingPageLoadFilterData) {
          tableFilterData[pageFiltersFieldToReturnKeyMap["taluk_name"]] = distinctTalukFilterValuesResult
        }
      }
      return returnValue
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class GetPartnerDetails {
  async get(id, params) {
    try {
      const returnValue = { return_code: 0 }
      console.log("oc r GFD g 1, id = ", id, ", params = ", params)
      const mainDBConnections = dbConnections().main
      const partnerQuery = `
        select p.partner_id,
          p.partner_name_l10n,
          coalesce(p.mobile_number,'') mobile_number,
          p.active as partner_active_id
        from main.partner p
        where 1 = 1
          and p.partner_id = '${id}'
      `
      const partnerBasicDetailsResultUnprocessed = await mainDBConnections.manager.query(partnerQuery)
      const partnerBasicDetailsResult = postProcessRecords(undefined, partnerBasicDetailsResultUnprocessed, {})
      returnValue.result = partnerBasicDetailsResult[0]

      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async create(data, params) {
    try {
      const returnValue = { return_code: 0 }
      const classificationData = await loadClassificationData("PARTNER_CLASSIFICATION", data.partner_id, data.classifierArray)
      returnValue.data = classificationData
      return returnValue
    } catch (error) {
      console.log("oc UBFS c 10, error")
      console.log("oc UBFS c 10a, error = ", error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class SetPartnerDetails {
  async create /*or is it update*/(data, params) {
    try {
      console.log("oc r SFD c 1")
      const returnValue = { return_code: 0 }
      const mainDBConnection = dbConnections().main
      const saveResult = await mainDBConnection.manager.transaction(async transactionalEntityManager => {
        try {
          const partnerRepo = transactionalEntityManager.getRepository("partner")
          const partnerEntity = mainDBConnection.entities["partner"]
          let partnerIdToUpdate = undefined
          const { partnerId, partner_type_id, partner_name_l10n, mobile_number, ...restOfPartnerDataAsClassificationData } = data

          if (partnerId === "-1") {
            // create partner
            if (partner_type_id === undefined) {
              // cannot create partner without partner type id
              throw new Error("cannot create partner without partner type id")
            }
            const partnerObject = { partner_name_l10n, mobile_number, partner_type_id }
            const createdPartner = await transactionalEntityManager
              .createQueryBuilder()
              .insert()
              .into(partnerEntity)
              .values(partnerObject)
              .execute()

            partnerIdToUpdate = createdPartner.identifiers[0].partner_id
            returnValue.partner_id = partnerIdToUpdate
          } else {
            // update partner
            partnerIdToUpdate = partnerId
            if (partner_name_l10n !== undefined || mobile_number !== undefined) {
              const partnerDataToUpdate = {}
              if (partner_name_l10n !== undefined) {
                partnerDataToUpdate.partner_name_l10n = partner_name_l10n
              }
              if (mobile_number !== undefined) {
                partnerDataToUpdate.mobile_number = mobile_number
              }
              const updatePartnerResult = await partnerRepo
                .createQueryBuilder()
                .update()
                .set(partnerDataToUpdate)
                .where("partner_id = :id ", { id: partnerIdToUpdate })
                .execute()
            }
          }
          if (Object.keys(restOfPartnerDataAsClassificationData).length > 0) {
            const updateClassificationDataResult = await saveClassificationData(transactionalEntityManager, "PARTNER_CLASSIFICATION", partnerIdToUpdate, restOfPartnerDataAsClassificationData)
            returnValue.classificationDataUpdateResult = updateClassificationDataResult
          }
        } catch (error) {
          console.log("oc UASS c 20, error")
          console.log("oc UASS c 20a, error = ", error)
          throw error
        }
      })
      return returnValue
    } catch (error) {
      console.log("oc r SFD c 10, error")
      console.log("oc r SFD c 10a, error = ", error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class PartnerGeography {
  async get(id, params) {
    try {
      const returnValue = { return_code: 0 }
      const partner_id = id
      const { user_type, user_id } = params.headers.token
      const partner_type_id = CONST_PARTNER_PHARMACY
      const entityGeographyMappingQuery = getCompiledQuery("entity-egeo")
      const entityGeographyMappingQueryBuilder = dbConnections()
        .main.manager.createQueryBuilder()
        .from(`(${entityGeographyMappingQuery})`, "entity_geo_data")
      entityGeographyMappingQueryBuilder.setParameters({ entity_uuid: partner_id, entity_type_id: partner_type_id })
      let entityGeographyMappingResult = await entityGeographyMappingQueryBuilder.execute()

      const geo = []
      for (const geography of entityGeographyMappingResult) {
        geo.push({ village_id: geography.village_id, village_name_l10n: geography.village_name_l10n })
      }

      returnValue.data = { geo }

      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async update(id, data, params) {
    try {
      let { geo } = data
      const partner_id = id
      const partner_type_id = CONST_PARTNER_PHARMACY
      const { user_type, user_id } = params.headers.token
      const returnValue = { return_code: 0 }

      if (!validateUUID(partner_id) && !Array.isArray(geo)) {
        throw new GeneralError("Invalid Request", {
          statusCode: 400
        })
      }

      return await dbConnections().main.manager.transaction(async transaction => {
        // If geo key is an empty array => set all geography mapping to inactive
        if (geo.length == 0) {
          await transaction.getRepository("entity_geography").update(
            {
              entity_uuid: partner_id,
              active: ACTIVE
            },
            {
              active: INACTIVE,
              last_modifying_user_id: user_id
            }
          )
        } else {
          const entityGeoRowInDB = await transaction.getRepository("entity_geography").find({
            where: [
              {
                entity_uuid: partner_id,
                active: ACTIVE,
                geography_type_id: VILLAGE_TYPE_ID,
                geography_id: MoreThan(0)
              }
            ],
            select: {
              entity_geography_id: true,
              geography_id: true
            }
          })

          const geoIdInDB = entityGeoRowInDB.map(obj => obj.geography_id)

          const toBeAdded = geo
            .filter(item => !geoIdInDB.includes(item))
            .map(item => ({
              entity_type_id: partner_type_id,
              entity_uuid: partner_id,
              geography_id: item,
              geography_type_id: VILLAGE_TYPE_ID,
              last_modifying_user_id: user_id
            }))

          await transaction.getRepository("entity_geography").insert(toBeAdded)

          const toBeUpdated = geoIdInDB.filter(item => !geo.includes(item))

          await transaction.getRepository("entity_geography").update(
            {
              entity_type_id: partner_type_id,
              entity_uuid: partner_id,
              geography_id: In(toBeUpdated),
              geography_type_id: VILLAGE_TYPE_ID
            },
            {
              active: INACTIVE,
              last_modifying_user_id: user_id
            }
          )
        }
        return returnValue
      })
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

module.exports = { PartnerList, PartnerListFilter, GetPartnerDetails, SetPartnerDetails, PartnerGeography }
