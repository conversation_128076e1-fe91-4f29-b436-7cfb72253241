const { <PERSON><PERSON><PERSON>, PartnerListFilter, GetPartnerDetails, SetPartnerDetails, PartnerGeography
} = require('./partner.class.js')
const auth = require('../../middleware/auth')
const tokenInject = require('../../middleware/tokenInject.js')

const configurePartner = (app) => {
  // app.use(auth)
  app.use('/e/partner-list', new PartnerList(), tokenInject)
  app.use('/e/partner-list-filter', new PartnerListFilter(), tokenInject)
  app.use('/e/get-partner-details', new GetPartnerDetails(), tokenInject)
  app.use('/e/set-partner-details', new SetPartnerDetails(), tokenInject)
  app.use('/e/get-partner-geo/', new PartnerGeography(), tokenInject)
}
module.exports = { configurePartner }
