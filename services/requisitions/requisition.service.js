const { LibRequisitionService, LibMedicineService } = require("@krushal-it/back-end-lib")
const uuid = require("uuid")
const errors = require("@feathersjs/errors")
const { responseStructure } = require("../common/responsestructure")
const fs = require("fs")
// const { createReport } = require('docx-templates')
// const Docxtemplater = require('docxtemplater')
const { getDBConnections, postProcessRecords, preProcessRecords, dbConnections } = require("@krushal-it/ah-orm")
const path = require("path")
// const JSZip = require('jszip');
const ZohoUtils = require("../../utils/common/zoho/zoho.utils")
const { uploadDocument, addDocumentEntry } = require("../document/controllers/document")
const { updateReq } = require("@krushal-it/back-end-lib/services/requisition/controller")
const FormData = require("form-data")
const S3Utils = require("../../utils/common/aws/s3.utils")
const { RELATION_DOC } = require("../../utils/common/namespace/krushal.namespace")

const ZOHO_BILL_TEMPLATE = {
  vendor_id: "1023409000001694037",
  bill_number: "",
  date: "",
  // "due_date": "",
  reference_number: "",
  line_items: [
    {
      item_id: "1023409000000038771",
      quantity: 1
    }
  ]
}

class RequisitionService {
  requisitionServiceHandler = new LibRequisitionService()
  // async find(params) {
  //   try {
  //     let result = await this.requisitionServiceHandler.find(params);
  //     return result;
  //   } catch (error) {
  //     console.error(error);
  //     return { result: false, error };
  //   }
  // }

  async create(data, params) {
    let response = await this.requisitionServiceHandler.create(data, params)
    if (response) {
      return responseStructure(response)
    } else {
      throw new errors.NotFound("data not found")
    }
  }

  async update(id, data, params) {
    let zohocreatebill = null
    if (data.status_id == 1000840002) {
      if (!data.pharmacy_invoice_number || data.pharmacy_invoice_number == "") throw new Error("Invoice number is required")
      let invoicedata = await this.requisitionServiceHandler.getInvoiceData({ query: { id: data.requisition_id } })
      console.log("invoicedata==>", invoicedata, invoicedata.data.items)
      let dt = new Date()
      let et = new Date()
      et.setMonth(dt.getMonth() + 1)
      let invoice = invoicedata.data
      const gst_no = invoice.partner_gst
      let gst_treatment = gst_no ? "business_gst" : "business_none"
      // let created_at=new Date(invoice.REQUISITION_CREATED_AT)
      let zoho_bill = {
        ...ZOHO_BILL_TEMPLATE,
        vendor_id: invoice.PARTNER_ZOHO_ID ? invoice.PARTNER_ZOHO_ID : "1023409000001694037",
        bill_number: data.pharmacy_invoice_number,
        date: dt.getFullYear() + "-" + (dt.getMonth() + 1 < 10 ? "0" + (dt.getMonth() + 1) : dt.getMonth() + 1) + "-" + (dt.getDate() < 10 ? "0" + dt.getDate() : dt.getDate()),
        // due_date:et.getFullYear()+'-'+((et.getMonth()+1)<10?'0'+(et.getMonth()+1):et.getMonth()+1)+'-'+((et.getDate())<10?'0'+et.getDate():et.getDate()),
        // reference_number:invoice.REQUISITIONER_NAME + '/' + created_at.getFullYear()+'-'+((created_at.getMonth()+1)<10?'0'+(created_at.getMonth()+1):created_at.getMonth()+1)+'-'+((created_at.getDate())<10?'0'+created_at.getDate():created_at.getDate())+'/'+((created_at.getHours())<10?'0'+created_at.getHours():created_at.getHours())+':'+((created_at.getMinutes())<10?'0'+created_at.getMinutes():created_at.getMinutes()),
        reference_number: invoice.REQUISITIONER_NAME + "/" + new Date(invoice.REQUISITION_CREATED_AT).toLocaleString("en-IN", { dateStyle: "medium", timeStyle: "long", timeZone: "Asia/Kolkata" }),
        line_items: [],
        gst_treatment,
        gst_no
      }
      for (let i = 0; i < invoice.items.length; i++) {
        zoho_bill.line_items.push({
          ...ZOHO_BILL_TEMPLATE["line_items"][0],
          item_id: invoice.items[i].ZOHO_ITEM_ID,
          quantity: Math.ceil(invoice.items[i].MEDICINE_QNTY),
          unit: invoice.items[i].UNIT
        })
      }
      let zoho = new ZohoUtils()
      console.log("zoho_bill==>", zoho_bill)
      zohocreatebill = await zoho.CreateBill(zoho_bill)
      console.log("after zoho integration", zoho_bill, zohocreatebill)
      data["zoho_bill_id"] = zohocreatebill.bill.bill_id
      // data['pharmacy_invoice_number'] = params.query.invoice_number
    }
    let response = await this.requisitionServiceHandler.update(data, params)
    if (response) {
      if (data.status_id == 1000840002) {
        response.data = {
          requisition_id: response.data,
          zoho_bill_id: zohocreatebill.bill.bill_id
        }
      }
      // if(response.invoice_data){
      //   let templatePath = path.join('invoices/', 'invoice_template.docx');
      //   console.log('Resolved Template Path:', templatePath);
      //   let template = fs.readFileSync(templatePath, 'binary');
      //   let zip = new JSZip(template);
      //   let doc = new Docxtemplater();
      //   doc.loadZip(zip);
      //   doc.setData(response.data);
      //   doc.render();
      //   let buffer=doc.getZip().generate({ type: 'nodebuffer' })
      //   let outputPath = path.join('invoices/', 'invoice_'+params.query.id+'.docx');
      //   fs.writeFileSync(outputPath, buffer);
      // }
      return responseStructure(response)
    } else {
      throw new errors.NotFound("data not found")
    }
  }
}
class RequisitionList {
  requisitionServiceHandler = new LibRequisitionService()
  async create(data, params) {
    let response = await this.requisitionServiceHandler.getReqList(data, params)
    if (response) {
      return responseStructure(response)
    } else {
      throw new errors.NotFound("requisitions not found")
    }
  }
}
class RequisitionStatus {
  requisitionServiceHandler = new LibRequisitionService()
  async find(params) {
    try {
      let result = await this.requisitionServiceHandler.getReqStatus(params)
      return responseStructure(result)
    } catch (error) {
      console.error(error)
      return { result: false, error }
    }
  }
}

class RequisitionInvoiceService {
  requisitionServiceHandler = new LibRequisitionService()
  async find(params) {
    try {
      let zoho = new ZohoUtils()
      let result = await zoho.getBillById(params.query.bill_id)
      return responseStructure({ statuscode: 200, data: result })
    } catch (error) {
      console.error(error)
      return { result: false, error }
    }
  }
}

class DownloadInvoice {
  async find(params) {
    try {
      console.log("get invoice", params)
      let filePath = `invoices/invoice_${params.query.id}.docx`
      return filePath
    } catch (error) {
      console.error(error)
      return { result: false, error }
    }
  }
}

class UploadBillAttachmentService {
  requisitionServiceHandler = new LibRequisitionService()
  async create(data, params) {
    try {
      console.log("uploadfilearr", data, data.files, params)
      if (!data.files) throw new Error("document_entries must be an array")
      if (!params.query.id) throw new Error("Requisition id required")
      let existingfiles = await dbConnections()
        .main.repos[RELATION_DOC].createQueryBuilder()
        .where("entity_1_entity_uuid=:id", {
          id: params.query.id
        })
        .select("document_id")
        .execute()
      console.log("exisitingfiles", existingfiles.length, data.files.file.length)
      if (existingfiles.length + data.files.file.length > 5) throw new Error("No of files exceeds the maximum limit (5)")
      let billid = await dbConnections()
        .main.repos["requisition_classification"].createQueryBuilder()
        .where("requisition_id=:id AND classifier_id = :class_id ", {
          id: params.query.id,
          class_id: 2000000279
        })
        .select("value_string_256")
        .execute()
      console.log("billid", billid)
      if (!billid || billid.length == 0) throw new Error("Bill Id not found")
      billid = billid[0].value_string_256
      let zoho = new ZohoUtils()
      let formdata = new FormData()
      for (let file of data.files.file) {
        formdata.append("attachment", fs.createReadStream(file.path), { filename: file.originalname })
      }
      console.log("formData", formdata)
      let zohoattachment = await zoho.CreateAttachmentForBill(formdata, params, billid)
      console.log("zohoattachment", zohoattachment)
      let uploadkeys = []
      for (let fl of data.files.file) {
        let x = {
          files: { file: [fl] },
          entity: "requisition"
        }
        let invoiceupload = await uploadDocument(x)
        uploadkeys.push({ name: fl.originalname, key: invoiceupload.doc_key })
      }
      console.log("uploadkeys", uploadkeys)

      let document_entries = []
      for (let uploadkey of uploadkeys) {
        document_entries.push({
          document_information: uploadkey.name,
          document_type: "requisition_invoice",
          entity: "requisition",
          entity_1_id: params.query.id,
          url: uploadkey.key
        })
      }
      let document_entry = await addDocumentEntry({ document_entries: document_entries })
      console.log("document_entry", document_entry)
      return responseStructure({ statuscode: 200, data: zohoattachment })
    } catch (error) {
      console.error(error)
      return { result: false, error: error }
    }
  }
}

class BillAttachmentService {
  async find(params) {
    try {
      let zoho = new ZohoUtils()
      let result = await zoho.getAttachmentForBill(params.query.bill_id)
      return responseStructure({ statuscode: 200, data: result })
    } catch (error) {
      console.error(error)
      return { result: false, error }
    }
  }
}

class PharmacyRequisitionService {
  requisitionServiceHandler = new LibRequisitionService()
  async create(data, params) {
    let action = params.route.type
    if (!data.filters.status_l10n || (data.filters.status_l10n && data.filters.status_l10n.length == 0)) {
      if (action == "open") data.filters.status_l10n = [1000820003, 1000830001, 1000830002]
      else data.filters.status_l10n = [1000820005, 1000830003, 1000840002]
    }
    let response = await this.requisitionServiceHandler.getReqList(data, params)
    if (response) {
      return responseStructure(response)
    } else {
      throw new errors.NotFound("requisitions not found")
    }
  }
}

module.exports = { RequisitionService, RequisitionList, RequisitionStatus, RequisitionInvoiceService, DownloadInvoice, UploadBillAttachmentService, BillAttachmentService, PharmacyRequisitionService }
