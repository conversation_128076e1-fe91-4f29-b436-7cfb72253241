const { dbConnections } = require("@krushal-it/ah-orm")
const { getClassificationConfiguration, loadClassificationData, saveClassificationData } = require("@krushal-it/back-end-lib")
const { record_statuses } = require("@krushal-it/back-end-lib/ENUMS")
const { isValidForCreate, isValidForUpdate } = require("./schema/index")

async function createActivity(data) {
  
  const { activity_name, activity_category, activity_information } = data

  if (isValidForCreate(data) === false) {
    throw new Error("Invalid Payload!")
  }

  const response = await dbConnections().main.manager.insert("ref_activity", {
    activity_name,
    activity_category,
    activity_name_l10n: {
      ul: activity_name
    },
    activity_information
  })

  return { activity_id: response.identifiers[0].activity_id, return_code: 0 }
}

async function deactivateActivity(activity_id) {
  if (!activity_id) {
    throw new Error("activity_id is missing!")
  }

  const response = await dbConnections()
    .main.manager.createQueryBuilder()
    .update("ref_activity")
    .set({
      active: record_statuses.INACTIVE
    })
    .where(`ref_activity.activity_id = :activity_id`, { activity_id })
    .execute()

  return true
}

async function updateActivity(activity_id, data) {

  if (!activity_id) {
    throw new Error("activity_id is missing!")
  }

  if (isValidForUpdate(data) === false) {
    throw new Error("Invalid Payload!")
  }

  const activityRow = await dbConnections()
    .main.manager.getRepository("ref_activity")
    .findOne({
      where: {
        activity_id: activity_id
      }
    })

  activityRow.activity_name = data.activity_name

  activityRow.activity_name_l10n = {
    ul: data.activity_name
  }

  activityRow.activity_category = data.activity_category

  activityRow.activity_information = {
    ...activityRow.activity_information,
    ...data.activity_information
  }

  await dbConnections()
    .main.manager.getRepository("ref_activity")
    .save(activityRow)

  return true
}

async function patchActivity(activity_id, data) {
  if (!activity_id) throw new Error("Missing activity_id!")

  if (!data.classifiers) throw new Error("Missing! please provide key 'classifiers' in the data.")

  const classification_entry_obj = getValidatedDataForPatch(data)

  return await dbConnections().main.manager.transaction(async transactionalEntityManager => {
    return await saveClassificationData(transactionalEntityManager, "REF_ACTIVITY_CLASSIFICATION", activity_id, classification_entry_obj)
  })
}

async function getActivity(activity_id, params) {
  const classifiers = Object.values(params.query)

  let ref_activity = dbConnections()
    .main.manager.getRepository("ref_activity")
    .createQueryBuilder()
    .select("activity_id, activity_name, activity_category, rf.reference_name as activity_category_name, activity_information")
    .leftJoin("ref_reference", "rf", "rf.reference_id = activity_category")

  let result = await ref_activity.where("ref_activity.active = :active and activity_id = :activity_id", { activity_id, active: record_statuses.ACTIVE }).execute()

  let classificationData = undefined
  if (classifiers.length) {
    classificationData = await loadClassificationData("REF_ACTIVITY_CLASSIFICATION", activity_id, classifiers)
  }

  return {
    classification: classificationData,
    count: result.length,
    report: result,
    return_code: 0
  }
}
async function getActivities() {
  let ref_activity = dbConnections()
    .main.manager.getRepository("ref_activity")
    .createQueryBuilder()
    .select("activity_id, activity_name, activity_name_l10n, activity_category, rf.reference_name as activity_category_name, rf.reference_name_l10n as activity_category_name_l10n, activity_information")
    .leftJoin("ref_reference", "rf", "rf.reference_id = activity_category")
    .orderBy("ref_activity.updated_at", "DESC")

  let result = await ref_activity.where("ref_activity.active = :active", { active: record_statuses.ACTIVE }).execute()

  return {
    count: result.length,
    report: result,
    return_code: 0
  }
}

module.exports = {
  getActivity,
  getActivities,
  patchActivity,
  updateActivity,
  createActivity,
  deactivateActivity
}

function getValidatedDataForPatch(data) {
  const classifiers = data.classifiers
  const valid_ref_activity_classifiers = getClassificationConfiguration()["REF_ACTIVITY_CLASSIFICATION"].CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP
  let atLeastOneIsFound = false
  const classification_entry_obj = {}

  for (const key in valid_ref_activity_classifiers) {
    if (classifiers[key]) {
      classification_entry_obj[key] = classifiers[key]
      atLeastOneIsFound = true
    }
  }

  if (!atLeastOneIsFound) {
    throw new Error("no valid Classifier found for patching.")
  }
  return classification_entry_obj
}
