const { BadRequest, GeneralError } = require("@feathersjs/errors")
const { TypeORMError } = require("typeorm")
const { getActivity, getActivities, patchActivity, updateActivity, createActivity, deactivateActivity } = require("./controller")

module.exports = {
  async find() {
    try {
      return await getActivities()
    } catch (error) {
      if (error instanceof TypeORMError) throw new BadRequest(error.detail, error)
      else throw new GeneralError(error.message, error)
    }
  },

  async create(data) {
    try {
      return await createActivity(data)
    } catch (error) {
      if (error instanceof TypeORMError) throw new BadRequest(error.detail, error)
      else throw new GeneralError(error.message, error)
    }
  },

  async get(activity_id, params) {
    try {
      return await getActivity(activity_id, params)
    } catch (error) {
      if (error instanceof TypeORMError) throw new BadRequest(error.detail, error)
      else throw new GeneralError(error.message, error)
    }
  },

  async update(activity_id, data) {
    try {
      return await updateActivity(activity_id, data)
    } catch (error) {
      if (error instanceof TypeORMError) throw new BadRequest(error.detail, error)
      else throw new GeneralError(error.message, error)
    }
  },

  async patch(activity_id, data, params) {
    try {
      return await patchActivity(activity_id, data)
    } catch (error) {
      if (error instanceof TypeORMError) throw new BadRequest(error.detail, error)
      else throw new GeneralError(error.message, error)
    }
  },
  async remove(activity_id) {
    try {
      return await deactivateActivity(activity_id)
    } catch (error) {
      if (error instanceof TypeORMError) throw new BadRequest(error.detail, error)
      else throw new GeneralError(error.message, error)
    }
  }
}
