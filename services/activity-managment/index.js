const { GeneralError } = require("@feathersjs/errors")
const { getActivities, getActivity, patchActivity, updateActivity, createActivity, deactivateActivity } = require("./service")

const service = require("./service")
module.exports = function activityManagement(app) {
  app.use("v2/activities/", service, errorHandler)
}
function errorHandler(error, req, res, next) {
  const _error = {
    code: error.code,
    message: error.message
  }

  if (error) {
    return res.status(error.code).json({ error: _error })
  } else {
    next()
  }
}
