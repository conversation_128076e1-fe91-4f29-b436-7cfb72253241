const { GeneralError } = require("@feathersjs/errors")
const { getFarmerCostSummaryPDF, getFarmerCostSummaryHTML } = require("./controllers")
module.exports = {
  costSummaryPDF: {
    async get(id, params) {
      try {
        const data = await getFarmerCostSummaryPDF(id, params)
        return data.toString("base64")
      } catch (error) {
        return new GeneralError(error).toJSON()
      }
    }
  },
  costSummaryHTML: {
    async get(id, params) {
      try {
        const data = await getFarmerCostSummaryHTML(id, params)
        return data
      } catch (error) {
        return new GeneralError(error).toJSON()
      }
    }
  }
}
