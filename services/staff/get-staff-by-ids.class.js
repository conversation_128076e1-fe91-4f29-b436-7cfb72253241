const { GetStaffIdsService } = require("@krushal-it/back-end-lib");

class GetStaffByIds {
  GetStaffServiceHandler = new GetStaffIdsService();
  async create(data, params) {
    try {
      let result = await this.GetStaffServiceHandler.create(data, params);
      return result;
    } catch (error) {
      console.error(error);
      return { result: false, error: error.message };
    }
  }
}

module.exports = { GetStaffByIds }