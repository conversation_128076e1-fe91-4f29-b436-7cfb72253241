const { Staff<PERSON><PERSON>, Staff<PERSON>ist<PERSON>ilter, GetStaffDetails, SetStaffDetails,
  StaffTerritoryList, DeleteTerritoryAssignment, AssignTerritory,
} = require('./staff.class.js')
const auth = require('../../middleware/auth')
const tokenInject = require('../../middleware/tokenInject.js')

const configureStaff = (app) => {
  // app.use(auth)
  app.use('/e/staff-list', new StaffList(), tokenInject)
  app.use('/e/staff-list-filter', new StaffListFilter(), tokenInject)
  app.use('/e/get-staff-details', new GetStaffDetails(), tokenInject)
  app.use('/e/set-staff-details', new SetStaffDetails(), tokenInject)
  app.use('/e/staff-territory-list', new StaffTerritoryList(), tokenInject)
  app.use('/e/delete-territory-assignment', new DeleteTerritoryAssignment(), tokenInject)
  app.use('/e/assign-territory', new AssignTerritory(), tokenInject)
}
module.exports = { configureStaff }
