const { dbConnections } = require("@krushal-it/ah-orm")
const { getCompiledQuery, saveClassificationData, loadClassificationData } = require("@krushal-it/back-end-lib")
const { INACTIVE, ACTIVE, VILLAGE_TYPE_ID, CONST_ID_TYPE_STAFF_PARAVET, CONST_ID_TYPE_STAFF_BACKOFFICE ,PAY_PER_USE_PARAVET } = require("@krushal-it/back-end-lib/utils/constant")
const { validateUUID, infoLog, errorLog } = require("@krushal-it/mobile-or-server-lib")
const { GeneralError } = require("@feathersjs/errors")
const { In, Not, MoreThan } = require("typeorm")
const { staffType } = require("../../utils/configs/entity.config")
const staffAppType = new Set(["ONLINE", "OFFLINE"])
const defaultCountryCodeForMobileNumber = "+91"
const moment = require("moment")
const _ = require("lodash")
const { st } = require("translate-google/languages")
const { FreelanceStaffService } = require("@krushal-it/back-end-lib")

const getStaffList = async (data, params, options = {}) => {
  try {
    const validFilters = [
      { key: "f_staff_name", multi: false },
      { key: "f_active_status", multi: false },
      { key: "f_staff_type", multi: true },
      { key: "f_staff_village", multi: true },
      { key: "f_staff_mobile", multi: false },
      { key: "f_staff_state", multi: false },
      { key: "f_staff_taluk", multi: false },
      { key: "f_staff_district", multi: false }
    ]

    const filters = {}
    const templatePlaceholders = {}

    for (const filter of validFilters) {
      if (data[filter.key]) {
        templatePlaceholders[filter.key] = true
        filters[filter.key] = data[filter.key]
        if (filter.key === "f_staff_name" || filter.key === "f_staff_mobile") {
          filters[filter.key] = `%${filters[filter.key]}%`
        }
        if (filter.multi === true) {
          filters[filter.key] = Array.isArray(filters[filter.key]) ? filters[filter.key] : [filters[filter.key]]
        }
      }
    }

    const staffListQuery = getCompiledQuery("staff-listing", templatePlaceholders)

    const queryBuilder = dbConnections()
      .main.manager.createQueryBuilder()
      .from(`(${staffListQuery})`, "staff_listing")
    queryBuilder.setParameters(filters)

    const staffList = await queryBuilder.execute()
    return { data: staffList }
  } catch (error) {
    console.error(error)
    return { data: -1 }
  }
}

const getStaffTimeline = async (data, staff_id, options = {}) => {
  try {
    if (!validateUUID(staff_id)) {
      throw new GeneralError("Not a valid staff id", {
        statusCode: 400
      })
    }

    const staffInDB = await dbConnections().main.repos["staff"].findOne({
      where: [
        {
          staff_id: staff_id,
          active: ACTIVE
        }
      ]
    })

    if (!staffInDB) {
      throw new GeneralError("Active staff not found", {
        statusCode: 404
      })
    }

    const staffTimelineQuery = getCompiledQuery("staff-timeline")

    const staffTimelineQueryBuilder = dbConnections()
      .main.manager.createQueryBuilder()
      .from(`(${staffTimelineQuery})`, "staff_timeline_data")
      .setParameters({ staff_id })

    let staffTimelineData = await staffTimelineQueryBuilder.execute()

    let response = {
      staff_id: staffInDB.staff_id,
      staff_name_l10n: staffInDB.staff_name_l10n,
      timeline: staffTimelineData
    }

    return { data: response }
  } catch (error) {
    console.error(error)
    if (error instanceof GeneralError) {
      return { data: -1, result: false, error: error.message, status: error.data.statusCode }
    }
    return { data: -1 }
  }
}

const getStaffByID = async (data, staff_id, options = {}) => {
  try {
    const toArray = ["staff_type_id", "active_status_id", "app_user_type"]

    if (!validateUUID(staff_id)) {
      throw new GeneralError("Not a valid staff id", {
        statusCode: 400
      })
    }

    const templatePlaceholders = {}
    if (data && data.thumbnail == 1) templatePlaceholders["thumbnail"] = 1
    const staffByIdQuery = getCompiledQuery("staff-by-id", templatePlaceholders)
    const staffByIdQueryBuilder = dbConnections()
      .main.manager.createQueryBuilder()
      .from(`(${staffByIdQuery})`, "staff_data")
    staffByIdQueryBuilder.setParameters({ staff_id })
    let staffData = await staffByIdQueryBuilder.execute()

    if (staffData.length <= 0) {
      throw new GeneralError("Staff not found", {
        statusCode: 404
      })
    }

    staffData = staffData[0]

    const { staff_type_id } = staffData

    const egeoQuery = getCompiledQuery("entity-egeo");
    const egeoQueryBuilder = dbConnections().main.manager.createQueryBuilder().from(`(${egeoQuery})`, "staff_geo_data");
    egeoQueryBuilder.setParameters({ entity_uuid: staff_id, entity_type_id: staff_type_id });
    let geoData = await egeoQueryBuilder.execute()

    const geo = []
    for (const vill of geoData) {
      geo.push({ village_id: vill.village_id, village_name_l10n: vill.village_name_l10n })
    }

    for (const key of toArray) {
      if (staffData[key]) {
        staffData[key] = [staffData[key]]
      }
    }

    const classifierKeyArray = ['staff_app_access_groups_1'];

    const classData = await loadClassificationData('STAFF_CLASSIFICATION', staff_id, classifierKeyArray);

    for (const classifierKeyArrayElement of classifierKeyArray) {
      staffData[classifierKeyArrayElement] = classData[classifierKeyArrayElement];
    }
    
    const staffTimelineQuery = getCompiledQuery("staff-timeline")

    const staffTimelineQueryBuilder = dbConnections()
      .main.manager.createQueryBuilder()
      .from(`(${staffTimelineQuery})`, "staff_timeline_data")
      .setParameters({ staff_id })

    let staffTimelineData = await staffTimelineQueryBuilder.execute()

    const result = { ...staffData, geo, timeline: staffTimelineData }

    return { data: result }
  } catch (error) {
    console.error(error)
    if (error instanceof GeneralError) {
      return { data: -1, result: false, error: error.message, status: error.data.statusCode }
    }
    return { data: -1 }
  }
}

const createStaff = async (data, params) => {
  try {
    let { staff_name_l10n, country_code, mobile_number, staff_type_id, email_address, notes, staff_pan, staff_aadhar , staff_plan ,subscription_plan_start_date} = data

    return await dbConnections().main.manager.transaction(async transaction => {
      staff_type_id = Array.isArray(staff_type_id) ? staff_type_id[0] : staff_type_id
      staff_plan = Array.isArray(staff_plan) ? staff_plan[0] : staff_plan

      // check for mandatory input parameters in the request
      if (staffType.hasOwnProperty(staff_type_id) && mobile_number && staff_name_l10n ) {
        if (typeof staff_name_l10n === "string") {
          staff_name_l10n = { ul: staff_name_l10n }
        }
        if ( ( !staff_plan || !subscription_plan_start_date) && staff_type_id === PAY_PER_USE_PARAVET) {
          throw new GeneralError("staff plan and start date required for freelance", {
            statusCode: 400
          })
        }
        if (!country_code) {
          mobile_number = defaultCountryCodeForMobileNumber.concat(mobile_number)
        }

        let existingStaff = undefined

        if (email_address) {
          existingStaff = await transaction.getRepository("staff").findOne({
            where: [
              {
                mobile_number: mobile_number,
                active: ACTIVE
              },
              {
                email_address: email_address
              }
            ],
            select: {
              staff_id: true
            }
          })
        } else {
          existingStaff = await transaction.getRepository("staff").findOne({
            where: [
              {
                mobile_number: mobile_number,
                active: ACTIVE
              }
            ],
            select: {
              staff_id: true
            }
          })
        }

        if (existingStaff) {
          errorLog("Create Staff : Mobile Number or Email Address already exists ", data)
          throw new GeneralError("Mobile Number or Email Address already exists", {
            statusCode: 409
          })
        }

        const { user_type, user_id, user_type_resolved } = params.headers.token

        let staffInserted = await transaction.getRepository("staff").insert({
          staff_name_l10n,
          mobile_number,
          staff_type_id,
          email_address,
          last_modifying_user_id: user_id
        })
        let createdStaffId = staffInserted.identifiers[0].staff_id

        let notesToAdd = { activity: "New Staff Created" }
        if (notes) {
          notesToAdd.activity_notes = {}
          notesToAdd.activity_notes.ul = notes
        }
        await transaction.getRepository("note").insert({
          note_type_id: 1000440007,
          note: notesToAdd,
          entity_1_type_id: 1000460003,
          entity_1_uuid: createdStaffId,
          creator_type_id: user_type,
          creator_uuid: user_id,
          last_modifying_user_id: user_id,
          note_time: moment.utc().format("YYYY-MM-DD HH:mm:ss.SSS")
        })

        const staffClassification = {
          staff_pan,
          staff_aadhar
        }
        await saveClassificationData(transaction, "STAFF_CLASSIFICATION", createdStaffId, staffClassification)
        if (staff_type_id !== PAY_PER_USE_PARAVET){
          return { staff_id : createdStaffId , staff_type_id}
        } 
        const freelancer_enrollment_staff_info = { staff_type_id, staff_id: createdStaffId, subscription_plan_start_date, staff_plan }
        const freelancer = new FreelanceStaffService(transaction)
        return await freelancer.enrollToFreelancingPlan(freelancer_enrollment_staff_info)
      } else {
        errorLog("Invalid request for create staff ", data)
        throw new GeneralError("Invalid Request", {
          statusCode: 400
        })
      }
    })
  } catch (error) {
    console.error(error)
    if (error instanceof GeneralError) {
      return { data: -1, result: false, error: error.message, status: error.data.statusCode }
    }
    return { data: -1 }
  }
}

const updateStaff = async (data, params, options = {}) => {
  try {
    let { staff_name_l10n, email_address, mobile_number, country_code, staff_id, notes, staff_pan, staff_aadhar } = data

    if (validateUUID(staff_id) && notes && (staff_name_l10n === undefined || !_.isEmpty(staff_name_l10n)) && (email_address === undefined || !_.isEmpty(email_address)) && (mobile_number === undefined || !_.isEmpty(mobile_number)) && (country_code === undefined || !_.isEmpty(country_code))) {
      return await dbConnections().main.manager.transaction(async transaction => {
        const staffInDB = await transaction.getRepository("staff").findOne({
          where: [
            {
              staff_id: staff_id,
              active: ACTIVE
            }
          ]
        })

        if (!staffInDB) {
          throw new GeneralError("Active staff not found", {
            statusCode: 404
          })
        }

        if (typeof staff_name_l10n === "string") {
          staff_name_l10n = { ul: staff_name_l10n }
        }

        let appliedCountryCode = country_code ? country_code : defaultCountryCodeForMobileNumber

        if (mobile_number) {
          if (!mobile_number.startsWith(appliedCountryCode)) {
            mobile_number = appliedCountryCode.concat(mobile_number)
          }
        }

        const { user_type, user_id, user_type_resolved } = params.headers.token

        await transaction.getRepository("staff").update(
          {
            staff_id
          },
          {
            staff_name_l10n,
            email_address,
            mobile_number,
            last_modifying_user_id: user_id
          }
        )

        if (staffInDB.staff_type_id === 1000230006) {
          let data = []
          if (staff_pan) {
            const staff_panInDB = await transaction.getRepository("staff_classification").findOne({
              where: [
                {
                  staff_id: staff_id,
                  active: ACTIVE,
                  classifier_id: 2000000223
                }
              ]
            })
            let staff_pan_data = {
              classifier_id: 2000000223,
              staff_id: staff_id,
              value_string_256: staff_pan
            }
            if (staff_panInDB) {
              staff_pan_data.staff_classification_id = staff_panInDB.staff_classification_id
            }
            data.push(staff_pan_data)
          }
          if (staff_aadhar) {
            const staff_aadharInDB = await transaction.getRepository("staff_classification").findOne({
              where: [
                {
                  staff_id: staff_id,
                  active: ACTIVE,
                  classifier_id: 2000000224
                }
              ]
            })
            let staff_aadhar_data = {
              classifier_id: 2000000224,
              staff_id: staff_id,
              value_string_256: staff_aadhar
            }
            if (staff_aadharInDB) {
              staff_aadhar_data.staff_classification_id = staff_aadharInDB.staff_classification_id
            }
            data.push(staff_aadhar_data)
          }
          await transaction.getRepository("staff_classification").save(data)
        }

        let notesToAdd = { activity: "Staff Basic Info Updated" }

        if (notes) {
          notesToAdd.activity_notes = {}
          notesToAdd.activity_notes.ul = notes
        }

        await transaction.getRepository("note").insert({
          note_type_id: 1000440007,
          note: notesToAdd,
          entity_1_type_id: 1000460003,
          entity_1_uuid: staffInDB.staff_id,
          creator_type_id: user_type,
          creator_uuid: user_id,
          last_modifying_user_id: user_id,
          note_time: moment.utc().format("YYYY-MM-DD HH:mm:ss.SSS")
        })

        return { data: staff_id }
      })
    } else {
      throw new GeneralError("Invalid Request", {
        statusCode: 400
      })
    }
  } catch (error) {
    console.error(error)
    if (error instanceof GeneralError) {
      return { data: -1, result: false, error: error.message, status: error.data.statusCode, id: error.data.data }
    }
    return { data: -1 }
  }
}

const updateStaffRole = async (data, params, options = {}) => {
  try {
    let { staff_id, staff_type_id, notes } = data

    staff_type_id = Array.isArray(staff_type_id) ? staff_type_id[0] : staff_type_id

    if (validateUUID(staff_id) && staffType.hasOwnProperty(staff_type_id) && notes) {
      return await dbConnections().main.manager.transaction(async transaction => {
        const staffInDB = await transaction.getRepository("staff").findOne({
          where: [
            {
              staff_id: staff_id,
              active: ACTIVE
            }
          ]
        })

        if (!staffInDB) {
          throw new GeneralError("Active staff not found", {
            statusCode: 404
          })
        }

        if (staffInDB.staff_type_id != staff_type_id) {
          infoLog("changing the role of staff ", staff_id, staffInDB.staff_type_id, staff_type_id)
          let notesToAdd = { activity: "Staff Role changed from " + staffInDB.staff_type_id + " To " + staff_type_id }
          const { user_type, user_id, user_type_resolved } = params.headers.token

          await transaction.getRepository("staff").update(
            {
              staff_id: staffInDB.staff_id
            },
            {
              staff_type_id: staff_type_id,
              last_modifying_user_id: user_id
            }
          )

          await transaction.getRepository("entity_geography").update(
            {
              entity_uuid: staffInDB.staff_id,
              active: ACTIVE
            },
            {
              active: INACTIVE,
              last_modifying_user_id: user_id
            }
          )

          if (notes) {
            notesToAdd.activity_notes = {}
            notesToAdd.activity_notes.ul = notes
          }

          await transaction.getRepository("note").insert({
            note_type_id: 1000440007,
            note: notesToAdd,
            entity_1_type_id: 1000460003,
            entity_1_uuid: staffInDB.staff_id,
            creator_type_id: user_type,
            creator_uuid: user_id,
            last_modifying_user_id: user_id,
            note_time: moment.utc().format("YYYY-MM-DD HH:mm:ss.SSS")
          })
        } else {
          throw new GeneralError("Invalid Request. Requested Role is same as existing Role", {
            statusCode: 400
          })
        }
        return { data: staffInDB.staff_id }
      })
    } else {
      throw new GeneralError("Invalid Request", {
        statusCode: 400
      })
    }
  } catch (error) {
    console.error(error)
    if (error instanceof GeneralError) {
      return { data: -1, result: false, error: error.message, status: error.data.statusCode, id: error.data.data }
    }
    return { data: -1 }
  }
}

const updateStaffStatus = async (data, params, options = {}) => {
  try {
    let { staff_id, active_status_id, notes } = data

    active_status_id = Array.isArray(active_status_id) ? active_status_id[0] : active_status_id

    if (validateUUID(staff_id) && (active_status_id === ACTIVE || active_status_id === INACTIVE) && notes) {
      return await dbConnections().main.manager.transaction(async transaction => {
        const staffInDB = await transaction.getRepository("staff").findOne({
          where: [{ staff_id: staff_id }]
        })

        if (staffInDB.active != active_status_id) {
          infoLog("changing the status of staff ", staff_id, staffInDB.active, active_status_id)
          let notesToAdd = { activity: "Staff Status changed from " + staffInDB.active + " To " + active_status_id }
          const { user_type, user_id, user_type_resolved } = params.headers.token

          await transaction.getRepository("staff").update(
            {
              staff_id: staffInDB.staff_id
            },
            {
              active: active_status_id,
              last_modifying_user_id: user_id
            }
          )

          await transaction.getRepository("entity_geography").update(
            {
              entity_uuid: staffInDB.staff_id,
              active: ACTIVE
            },
            {
              active: INACTIVE,
              last_modifying_user_id: user_id
            }
          )

          if (notes) {
            notesToAdd.activity_notes = {}
            notesToAdd.activity_notes.ul = notes
          }

          await transaction.getRepository("note").insert({
            note_type_id: 1000440007,
            note: notesToAdd,
            entity_1_type_id: 1000460003,
            entity_1_uuid: staffInDB.staff_id,
            creator_type_id: user_type,
            creator_uuid: user_id,
            last_modifying_user_id: user_id,
            note_time: moment.utc().format("YYYY-MM-DD HH:mm:ss.SSS")
          })
        } else {
          throw new GeneralError("Invalid Request. Requested Status is same as existing Status", {
            statusCode: 400
          })
        }
        return { data: staffInDB.staff_id }
      })
    } else {
      throw new GeneralError("Invalid Request", {
        statusCode: 400
      })
    }
  } catch (error) {
    console.error(error)
    if (error instanceof GeneralError) {
      return { data: -1, result: false, error: error.message, status: error.data.statusCode, id: error.data.data }
    }
    return { data: -1 }
  }
}

const updateStaffGroups = async (data, params, options = {}) => {
  
  try {
    let { staff_id, app_access_groups, notes } = data

    if (validateUUID(staff_id) && Array.isArray(app_access_groups) && notes) {
      return await dbConnections().main.manager.transaction(async transaction => {
        const staffInDB = await transaction.getRepository("staff").findOne({
          where: [
            {
              staff_id: staff_id,
              active: ACTIVE
            }
          ]
        })

        if (!staffInDB) {
          throw new GeneralError("Active staff not found", {
            statusCode: 404
          })
        }

        const { user_type, user_id, user_type_resolved } = params.headers.token

        // If app_access_groups key is an empty array => set all app_access_groups mapping to inactive
        const staffClassificationData = {
          staff_app_access_groups_1: app_access_groups
        }
        await saveClassificationData(transaction, "STAFF_CLASSIFICATION", staffInDB.staff_id, staffClassificationData)

        let notesToAdd = { activity: "Staff App Access group Changes" }

        if (notes) {
          notesToAdd.activity_notes = {}
          notesToAdd.activity_notes.ul = notes
        }

        await transaction.getRepository("note").insert({
          note_type_id: 1000440007,
          note: notesToAdd,
          entity_1_type_id: 1000460003,
          entity_1_uuid: staffInDB.staff_id,
          creator_type_id: user_type,
          creator_uuid: user_id,
          last_modifying_user_id: user_id,
          note_time: moment.utc().format("YYYY-MM-DD HH:mm:ss.SSS")
        })

        return { data: staffInDB.staff_id }
      })
    } else {
      throw new GeneralError("Invalid Request", {
        statusCode: 400
      })
    }
  } catch (error) {
    console.error(error)
    if (error instanceof GeneralError) {
      return { data: -1, result: false, error: error.message, status: error.data.statusCode, id: error.data.data }
    }
    return { data: -1 }
  }
}

const updateStaffAppType = async (data, params, options = {}) => {
  try {
    let { staff_id, app_user_type, notes } = data

    if (validateUUID(staff_id) && staffAppType.has(app_user_type) && notes) {
      return await dbConnections().main.manager.transaction(async transaction => {
        const staffInDB = await transaction.getRepository("staff").findOne({
          where: [
            {
              staff_id: staff_id,
              active: ACTIVE
            }
          ]
        })

        if (!staffInDB) {
          throw new GeneralError("Active staff not found", {
            statusCode: 404
          })
        }

        if (staffInDB.staff_type_id === CONST_ID_TYPE_STAFF_BACKOFFICE) {
          throw new GeneralError("App user type cannot be changed for back end user", {
            statusCode: 400
          })
        }

        const staffClassificationInDB = await transaction.getRepository("staff_classification").findOne({
          where: [
            {
              staff_id: staff_id,
              classifier_id: 2000000137,
              active: ACTIVE
            }
          ]
        })

        const appUserTypeInDB = staffClassificationInDB?.value_reference_id === 1000105001 ? "ONLINE" : "OFFLINE"

        if (app_user_type != appUserTypeInDB) {
          infoLog("changing the app user type of staff", staff_id, app_user_type)
          let notesToAdd = { activity: "Staff App User Type changed from " + appUserTypeInDB + " To " + app_user_type }
          const { user_type, user_id, user_type_resolved } = params.headers.token

          if (staffClassificationInDB) {
            await transaction.getRepository("staff_classification").update(
              {
                staff_id: staffInDB.staff_id,
                classifier_id: 2000000137,
                active: ACTIVE
              },
              {
                active: INACTIVE,
                last_modifying_user_id: user_id
              }
            )
          }

          if (app_user_type === "ONLINE") {
            const rowToAdd = {
              staff_id: staffInDB.staff_id,
              classifier_id: 2000000137,
              value_reference_id: 1000105001,
              active: ACTIVE,
              last_modifying_user_id: user_id
            }

            await transaction.getRepository("staff_classification").insert(rowToAdd)
          }

          if (notes) {
            notesToAdd.activity_notes = {}
            notesToAdd.activity_notes.ul = notes
          }

          await transaction.getRepository("note").insert({
            note_type_id: 1000440007,
            note: notesToAdd,
            entity_1_type_id: 1000460003,
            entity_1_uuid: staffInDB.staff_id,
            creator_type_id: user_type,
            creator_uuid: user_id,
            last_modifying_user_id: user_id,
            note_time: moment.utc().format("YYYY-MM-DD HH:mm:ss.SSS")
          })
        } else {
          throw new GeneralError("Invalid Request. Requested App Type is same as existing App Type", {
            statusCode: 400
          })
        }
        return { data: staffInDB.staff_id }
      })
    } else {
      throw new GeneralError("Invalid Request", {
        statusCode: 400
      })
    }
  } catch (error) {
    console.error(error)
    if (error instanceof GeneralError) {
      return { data: -1, result: false, error: error.message, status: error.data.statusCode, id: error.data.data }
    }
    return { data: -1 }
  }
}

const updateStaffGeography = async (data, params, options = {}) => {
  const staffTypeForGeoValidation = new Set([CONST_ID_TYPE_STAFF_PARAVET])

  try {
    let { staff_id, geo, notes } = data

    if (validateUUID(staff_id) && Array.isArray(geo) && notes) {
      return await dbConnections().main.manager.transaction(async transaction => {
        const staffInDB = await transaction.getRepository("staff").findOne({
          where: [
            {
              staff_id: staff_id,
              active: ACTIVE
            }
          ]
        })

        if (!staffInDB) {
          throw new GeneralError("Active staff not found", {
            statusCode: 404
          })
        }

        if (staffTypeForGeoValidation.has(staffInDB.staff_type_id)) {
          const [hasInvalidGeo, conflictingGeos] = await isValidGeo(geo, staffInDB.staff_id, staffInDB.staff_type_id, transaction)
          if (hasInvalidGeo === true) {
            const invalidGeos = conflictingGeos.map(value => value.geography_id).join(",")
            throw new GeneralError(`cannot assign ${staffInDB.staff_name_l10n.en} to ${invalidGeos} because someone is already assigned.`, {
              statusCode: 409,
              data: conflictingGeos.map(value => value.geography_id)
            })
          }
        }

        const { user_type, user_id, user_type_resolved } = params.headers.token

        // If geo key is an empty array => set all geography mapping to inactive
        if (geo.length == 0) {
          await transaction.getRepository("entity_geography").update(
            {
              entity_uuid: staffInDB.staff_id,
              active: ACTIVE
            },
            {
              active: INACTIVE,
              last_modifying_user_id: user_id
            }
          )
        } else {
          const entityGeoRowInDB = await transaction.getRepository("entity_geography").find({
            where: [
              {
                entity_uuid: staffInDB.staff_id,
                active: ACTIVE,
                geography_type_id: VILLAGE_TYPE_ID,
                geography_id: MoreThan(0)
              }
            ],
            select: {
              entity_geography_id: true,
              geography_id: true
            }
          })

          const geoIdInDB = entityGeoRowInDB.map(obj => obj.geography_id)

          const toBeAdded = geo
            .filter(item => !geoIdInDB.includes(item))
            .map(item => ({
              entity_type_id: staffInDB.staff_type_id,
              entity_uuid: staffInDB.staff_id,
              geography_id: item,
              geography_type_id: VILLAGE_TYPE_ID,
              last_modifying_user_id: user_id
            }))

          await transaction.getRepository("entity_geography").insert(toBeAdded)

          const toBeUpdated = geoIdInDB.filter(item => !geo.includes(item))

          await transaction.getRepository("entity_geography").update(
            {
              entity_type_id: staffInDB.staff_type_id,
              entity_uuid: staffInDB.staff_id,
              geography_id: In(toBeUpdated),
              geography_type_id: VILLAGE_TYPE_ID
            },
            {
              active: INACTIVE,
              last_modifying_user_id: user_id
            }
          )
        }

        let notesToAdd = { activity: "Staff Geography Changes" }

        if (notes) {
          notesToAdd.activity_notes = {}
          notesToAdd.activity_notes.ul = notes
        }

        await transaction.getRepository("note").insert({
          note_type_id: 1000440007,
          note: notesToAdd,
          entity_1_type_id: 1000460003,
          entity_1_uuid: staffInDB.staff_id,
          creator_type_id: user_type,
          creator_uuid: user_id,
          last_modifying_user_id: user_id,
          note_time: moment.utc().format("YYYY-MM-DD HH:mm:ss.SSS")
        })

        // disable rows in synchornization table since there is a change in staff assignment
        await updateSynchronizationTableForStaff(staffInDB.staff_id)
        return { data: staffInDB.staff_id }
      })
    } else {
      throw new GeneralError("Invalid Request", {
        statusCode: 400
      })
    }
  } catch (error) {
    console.error(error)
    if (error instanceof GeneralError) {
      return { data: -1, result: false, error: error.message, status: error.data.statusCode, id: error.data.data }
    }
    return { data: -1 }
  }
}

const isValidGeo = async (village_ids, staff_id, staff_type_id, transaction) => {
  const previousGeoData = await transaction.getRepository("entity_geography").find({
    where: {
      entity_type_id: staff_type_id,
      active: ACTIVE,
      geography_id: In(village_ids),
      geography_type_id: VILLAGE_TYPE_ID,
      entity_uuid: Not(staff_id)
    },
    select: {
      geography_id: true
    }
  })
  if (previousGeoData.length === 0) return [false, null]
  return [true, previousGeoData]
}

const updateSynchronizationTableForStaff = async staff_id => {
  try {
    infoLog("in-activating device ids for user : ", staff_id)

    const userDeviceIdResponse = await dbConnections().main.repos["user_device"].find({
      where: {
        entity_uuid: staff_id
      },
      select: {
        user_device_id: true
      }
    })

    const deviceIds = userDeviceIdResponse.map(item => item.user_device_id)

    if (deviceIds && deviceIds.length > 0) {
      infoLog("in-activating deviceIds: ", deviceIds)

      const rowsToDisable = await dbConnections()
        .config.repos["synchronization_data"].createQueryBuilder("synchronization_data_rows")
        .where("active = :active", { active: ACTIVE })
        .andWhere("user_device_id IN (:...deviceIds)", { deviceIds })
        .andWhere("table_name NOT IN (:ref_reference_category, :ref_reference, :ref_activity)", {
          ref_reference_category: "ref_reference_category",
          ref_reference: "ref_reference",
          ref_activity: "ref_activity"
        })
        .getMany()

      if (rowsToDisable && rowsToDisable.length > 0) {
        infoLog("rows To in-activate ", rowsToDisable.length)
        rowsToDisable.map(item => (item.active = INACTIVE))
        await dbConnections().config.repos["synchronization_data"].save(rowsToDisable)
      } else {
        infoLog("No rows found to in-activate")
      }
    }
  } catch (error) {
    console.error("error in updateSynchronizationTableForStaff", error)
    errorLog("error in updateSynchronizationTableForStaff", staff_id, error)
  }
}

module.exports = { getStaffList, getStaffByID, getStaffTimeline, updateStaff, createStaff, updateStaffGeography, updateStaffRole, updateStaffStatus, updateStaffAppType, updateStaffGroups }
