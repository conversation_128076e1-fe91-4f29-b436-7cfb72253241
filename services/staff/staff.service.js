const { BACK_OFFICE_PERSON } = require("@krushal-it/back-end-lib/utils/constant");
const { checkUsertype } = require("../../middleware/auth");
const tokenInject = require("../../middleware/tokenInject");
const { StaffList, StaffByID, StaffTimeline, CreateStaff, UpdateStaff, UpdateStaffGeography, UpdateStaffRole, UpdateStaffStatus, UpdateStaffAppType, UpdateStaffGroups } = require("./get-staff.class");
const { handleServerError } = require("../../middleware/server-error");

const loadStaffServices = (app) => {
    app.use("/staffs/all", checkUsertype(BACK_OFFICE_PERSON), new StaffList(), tokenInject, handleServerError);
    app.use("/staffs/create", checkUsertype(BACK_OFFICE_PERSON), new CreateStaff(), tokenInject, handleServerError);
    app.use("/staffs/update", checkUsertype(BACK_OFFICE_PERSON), new UpdateStaff(), tokenInject, handleServerError);
    app.use("/staffs/geo/update", checkUsertype(BACK_OFFICE_PERSON), new UpdateStaffGeography(), tokenInject, handleServerError);
    app.use("/staffs/role/update", checkUsertype(BACK_OFFICE_PERSON), new UpdateStaffRole(), tokenInject, handleServerError);
    app.use("/staffs/status/update", checkUsertype(BACK_OFFICE_PERSON), new UpdateStaffStatus(), tokenInject, handleServerError);
    app.use("/staffs/app-type/update", checkUsertype(BACK_OFFICE_PERSON), new UpdateStaffAppType(), tokenInject, handleServerError);
    app.use("/staffs/groups/update", checkUsertype(BACK_OFFICE_PERSON), new UpdateStaffGroups(), tokenInject, handleServerError);
    app.use("/staffs/:staff_id", checkUsertype(BACK_OFFICE_PERSON), new StaffByID(), tokenInject, handleServerError);
    app.use("/staffs/timeline/:staff_id", checkUsertype(BACK_OFFICE_PERSON), new StaffTimeline(), tokenInject, handleServerError);
};

module.exports = loadStaffServices;