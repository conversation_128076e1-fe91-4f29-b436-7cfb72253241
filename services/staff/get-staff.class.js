const { GetStaffService ,GetStaffTypeIdsService} = require("@krushal-it/back-end-lib");
const { getStaffList, getStaffByID, getStaffTimeline, updateStaff, updateStaffGeography, createStaff, updateStaffRole, updateStaffStatus, updateStaffAppType, updateStaffGroups } = require("./controller");

class GetStaff {
  GetStaffServiceHandler = new GetStaffService();
  async create(data, params) {
    try {
      let result = await this.GetStaffServiceHandler.create(data, params);
      return result;
    } catch (error) {
      console.error(error);
      return { result: false, error: error.message };
    }
  }

  async get(id, params) {
    let result = await this.GetStaffServiceHandler.get(id, params);
    return result;
  }
}
class StaffList {
  async create(data, params) {
    return await getStaffList(data, params) ;
  }
}
class StaffByID {
  async create(data, params) {
    const { staff_id } = params.route;
    return await  getStaffByID(data, staff_id) ;
  }
}

class StaffTimeline {
  async create(data, params) {
    const { staff_id } = params.route;
    return await  getStaffTimeline(data, staff_id) ;
  }
}
  
class CreateStaff {
  async create(data, params) {
    try {
      return  await createStaff(data, params) ;
    }
    catch (error) {
      return { result: false, error: error.message };
    }
  }
}
class UpdateStaff {
  async create(data, params) {
    return  await updateStaff(data, params) ;
  }
}

class UpdateStaffGeography {
  async create(data, params) {
    return  await updateStaffGeography(data, params) ;
  }
}

class UpdateStaffRole {
  async create(data, params) {
    return  await updateStaffRole(data, params) ;
  }
}

class UpdateStaffStatus {
  async create(data, params) {
    return  await updateStaffStatus(data, params) ;
  }
}

class UpdateStaffGroups {
  async create(data, params) {
    return  await updateStaffGroups(data, params) ;
  }
}

class UpdateStaffAppType {
  async create(data, params) {
    return  await updateStaffAppType(data, params) ;
  }
}

class GetStaffByTypeIds {
  GetStaffServiceHandler = new GetStaffTypeIdsService();
  async create(data, params) {
    try {
      let result = await this.GetStaffServiceHandler.create(data, params);
      return result;
    } catch (error) {
      console.error(error);
      return { result: false, error: error.message };
    }
  }
}

module.exports = { GetStaff, StaffList, StaffTimeline, CreateStaff, StaffByID, UpdateStaff, UpdateStaffGeography, GetStaffByTypeIds, UpdateStaffRole, UpdateStaffStatus, UpdateStaffAppType, UpdateStaffGroups };

