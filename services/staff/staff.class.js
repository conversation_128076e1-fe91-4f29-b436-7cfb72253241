const lodashObject = require("lodash");

const { getDBConnections, dbConnections, postProcessRecords, optionallyAddPrimaryKeyToRecords } = require('@krushal-it/ah-orm')
const { configurationJSON, KrushalError } = require('@krushal-it/common-core')
const { CustomerListLib, CustomerLib } = require('@krushal-it/back-end-lib')
const { loadClassificationData, saveClassificationData } = require('@krushal-it/back-end-lib')
const { adjustStartDatesOfEntityStatusByCategoryAndEntities, reportQueryClassificationJoinConfiguration, generateSelectJoinClauseForClassification, generateWhereClauseForReportInnerQuery: generateWhereClause, generateLimitStringForReportInnerQuery: generateLimitString, generateSortStringForReportInnerQuery: generateSortString, identifyMandatoryColumnsForReportQuery, extractFieldsNeedingFilterData, extractFieldsNeedingPageFilterData } = require('../../utils/query/query.helper');

const staffTypeRelatedColumnsQueryConfiguration = [
  `
    s.staff_type_id, coalesce(st_rr.reference_name_l10n->>'en', st_rr.reference_name_l10n->>'ul') as staff_type,
  `,
  `
    inner join main.ref_reference st_rr on s.staff_type_id = st_rr.reference_id
  `
]

const createStaffInnerQuery = ({
  restrictedIds, searchWithinRestrictedIds,
  selectClauseForClassification, joinClauseForClassification,
  includeStaffTypeInQuery,
  }) => {
  const innerQuery = `
    select s.staff_id,
      coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as staff_name,
      ${includeStaffTypeInQuery ? staffTypeRelatedColumnsQueryConfiguration[0] : '' }
      ${selectClauseForClassification !== undefined && selectClauseForClassification !== '' ? selectClauseForClassification + ', ': ''}
      s.mobile_number, s.active as staff_active_id,
      case when s.active = 1000100001 then 'Active' else 'Not Active' end as staff_active
    from main.staff s
    ${includeStaffTypeInQuery ? staffTypeRelatedColumnsQueryConfiguration[1] : '' }
    ${joinClauseForClassification}
    where 1 = 1
    ${restrictedIds ? searchWithinRestrictedIds : ''}
  `
  return innerQuery
}

class StaffList {
  async create (data, params) {
    try {
      const returnValue = { return_code: 0 }
      const mainDBConnection = dbConnections().main

      const limitString = generateLimitString(data)
      const sortString = generateSortString(data) 
      const whereClause = generateWhereClause(data) 
      let searchWithinRestrictedIds = ``

      // identify all columns needed
      const selectedColumns = data.selectedColumns
      // identify mandatory columns needed
      const mandatoryColumnsForSearch = identifyMandatoryColumnsForReportQuery(data.filters, data.sorting, data.globalSearch, data.searchConfiguration)

      let selectClauseForClassification
      let joinClauseForClassification
      [selectClauseForClassification, joinClauseForClassification] = generateSelectJoinClauseForClassification(reportQueryClassificationJoinConfiguration, 'STAFF', undefined, mandatoryColumnsForSearch)

      let includeStaffTypeInQuery = false
      let restrictedIds = false

      if (mandatoryColumnsForSearch.includes('staff_type') || mandatoryColumnsForSearch.includes('staff_type_id')) {
        includeStaffTypeInQuery = true
      }

      let includeWhereClauseInDetailedColumnsQuery = false

      let innerQuery = createStaffInnerQuery({
        restrictedIds, searchWithinRestrictedIds,
        selectClauseForClassification, joinClauseForClassification,
        includeStaffTypeInQuery,
        })

      const countQuery = `
        select count(*) as count from (
          ${innerQuery}
        ) outerTable
        ${whereClause}
      `
      const reportResultCount = await mainDBConnection.manager.query(countQuery)
      const count = parseInt(reportResultCount[0].count)

      if (count === 0) {
        includeWhereClauseInDetailedColumnsQuery = true
      }
      
      let query = `
          select * 
          from (
            ${innerQuery}
          ) outerTable
          ${whereClause}
          ${sortString}
          ${limitString}
        `

      const reportResultUnprocessed = await mainDBConnection.manager.query(query)
      const reportResult = postProcessRecords(undefined, reportResultUnprocessed, {})

      const primaryKeyToRowMap = {}
      for (const reportRow of reportResult) {
        primaryKeyToRowMap[reportRow['staff_id']] = reportRow
      }
      const primaryKeysToBeQueried = Object.keys(primaryKeyToRowMap)

      let inPrimaryKeysIdString = ""
      for (const primaryKey of primaryKeysToBeQueried) {
        if (inPrimaryKeysIdString !== "") {
          inPrimaryKeysIdString = inPrimaryKeysIdString + ", "
        }
        inPrimaryKeysIdString = inPrimaryKeysIdString + "'" + primaryKey + "'"
      }
      searchWithinRestrictedIds = (inPrimaryKeysIdString !== '') ? ` and s.staff_id in (${inPrimaryKeysIdString})` : ''

      const filtersFieldToReturnKeyMap = extractFieldsNeedingFilterData(data.searchConfiguration, data.selectedColumns)
      const fieldsNeedingFilterData = Object.keys(filtersFieldToReturnKeyMap)

      const mandatoryColumnsPlusColumnsNeededForFilteringWithoutUniq = [...mandatoryColumnsForSearch, ...fieldsNeedingFilterData]
      const mandatoryColumnsPlusColumnsNeededForFiltering = lodashObject.uniq(mandatoryColumnsPlusColumnsNeededForFilteringWithoutUniq)
      const returnValue0 = generateSelectJoinClauseForClassification(reportQueryClassificationJoinConfiguration, 'STAFF', undefined, mandatoryColumnsPlusColumnsNeededForFiltering)
      console.log('s a ALV3 1, returnValue = ', returnValue)
      selectClauseForClassification = returnValue0[0]
      joinClauseForClassification = returnValue0[1]

      innerQuery = createStaffInnerQuery({
        restrictedIds, searchWithinRestrictedIds,
        selectClauseForClassification, joinClauseForClassification,
        includeStaffTypeInQuery,
        })

      if (fieldsNeedingFilterData.includes('village_name')) {
        const villageNameFilterKey = fieldsNeedingFilterData.includes('village_name') ? 'village_name_filter_values' : undefined

        const updatedData = lodashObject.cloneDeep(data)
        delete updatedData['filters']['village_name']

        const whereClauseWithoutFilterField = generateWhereClause(updatedData) 

        const distinctVillageQuery = `
          select distinct village_id, village_name value, village_name text from (
            ${innerQuery}
          ) outerTable
          ${whereClauseWithoutFilterField}
        `
        const distinctVillageResultUnprocessed = await mainDBConnection.manager.query(distinctVillageQuery)
        const distinctVillageResult = postProcessRecords(undefined, distinctVillageResultUnprocessed, {})

        if (fieldsNeedingFilterData.includes('village_name')) {
          returnValue[filtersFieldToReturnKeyMap['village_name']] = distinctVillageResult
        }
      }

      includeStaffTypeInQuery = false
      restrictedIds = true

      if (selectedColumns.includes('staff_type') || selectedColumns.includes('staff_type_id')) {
        includeStaffTypeInQuery = true
      }

      const returnValue1 = generateSelectJoinClauseForClassification(reportQueryClassificationJoinConfiguration, 'STAFF', undefined, selectedColumns)
      selectClauseForClassification = returnValue1[0]
      joinClauseForClassification = returnValue1[1]

      innerQuery = createStaffInnerQuery({
        restrictedIds, searchWithinRestrictedIds,
        selectClauseForClassification, joinClauseForClassification,
        includeStaffTypeInQuery,
        })
      
      query = `
        select * from (
          ${innerQuery}
        ) outerTable
        ${includeWhereClauseInDetailedColumnsQuery ? whereClause : ''}
        ${sortString}
      `

      const reportResultWithSelectedColumnsUnprocessed = await mainDBConnection.manager.query(query)
      const reportResultWithSelectedColumns = postProcessRecords(undefined, reportResultWithSelectedColumnsUnprocessed, {})

      returnValue.count = count
      returnValue.report = reportResultWithSelectedColumns

      return returnValue
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class StaffListFilter {
  async create (data, params) {
    try {
      const returnValue = { return_code: 0 }
      const tableFilterData = {}
      returnValue.tableFilters = tableFilterData
      const mainDBConnection = dbConnections().main

      const selectedColumns = data.selectedColumns
      const pageFiltersFieldToReturnKeyMap = extractFieldsNeedingPageFilterData(
        data.searchConfiguration.columnConfiguration,
        Object.keys(data.searchConfiguration.columnConfiguration) !== undefined && Array.isArray(Object.keys(data.searchConfiguration.columnConfiguration))
          ? Object.keys(data.searchConfiguration.columnConfiguration)
          : []
      )
      const fieldsNeedingPageLoadFilterData = Object.keys(pageFiltersFieldToReturnKeyMap)

      if (fieldsNeedingPageLoadFilterData.includes('staff_type')) {
        const staffTypeAsFieldNeedingPageLoadFilterData = fieldsNeedingPageLoadFilterData.includes('staff_type')
        const staffTypeQuery = `
          select st_rr.reference_id as value_int, coalesce(st_rr.reference_name_l10n->>'en', st_rr.reference_name_l10n->>'ul') as value, coalesce(st_rr.reference_name_l10n->>'en', st_rr.reference_name_l10n->>'ul') as text
          from main.ref_reference st_rr
          where st_rr.active = 1000100001 and st_rr.reference_category_id = 10002300
        `
        const staffTypeFilterValuesResultUnprocessed = await mainDBConnection.manager.query(staffTypeQuery)
        const staffTypeFilterValuesResult = postProcessRecords(undefined, staffTypeFilterValuesResultUnprocessed, {})
        if (staffTypeAsFieldNeedingPageLoadFilterData) {
          tableFilterData[pageFiltersFieldToReturnKeyMap['staff_type']] = staffTypeFilterValuesResult
        }
      }
      return returnValue
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class GetStaffDetails {
  async get(id, params) {
    try {
      const returnValue = {return_code: 0}
      console.log('oc r GFD g 1, id = ', id, ', params = ', params)
      const mainDBConnections = dbConnections().main
      const staffQuery = `
        select s.staff_id,
          s.staff_name_l10n,
          coalesce(s.mobile_number,'') mobile_number,
          s.staff_type_id,
          s.active as staff_active_id
        from main.staff s
        where 1 = 1
          and s.staff_id = '${id}'
      `
      const staffBasicDetailsResultUnprocessed = await mainDBConnections.manager.query(staffQuery)
      const staffBasicDetailsResult = postProcessRecords(undefined, staffBasicDetailsResultUnprocessed, {})
      returnValue.result = staffBasicDetailsResult[0]
      if (returnValue.result.staff_type_id && returnValue.result.staff_type_id !== null) {
        returnValue.result.staff_type_id = [returnValue.result.staff_type_id]
      }

      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async create (data, params) {
    try {
      console.log('oc gSD c 1')
      const returnValue = {return_code: 0}
      const classificationData = await loadClassificationData('STAFF_CLASSIFICATION', data.staff_id, data.classifierArray)
      returnValue.data = classificationData
      return returnValue
    } catch (error) {
      console.log('oc gSD c 10, error')
      console.log('oc gSD c 10a, error = ', error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class SetStaffDetails {
  async create /*or is it update*/(data, params) {
    try {
      console.log('oc r SSD c 1')
      const returnValue = {return_code: 0}
      const mainDBConnection = dbConnections().main
      const saveResult = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
        try {
          const staffRepo = transactionalEntityManager.getRepository('staff')
          const staffEntity = mainDBConnection.entities['staff']
          let staffIdToUpdate = undefined
          const {staffId, staff_type_id, staff_name_l10n, mobile_number, ...restOfStaffDataAsClassificationData} = data
          if (staffId === '-1') {
            // create partner
            if (staff_type_id === undefined || !Array.isArray(staff_type_id) || staff_type_id.length === 0) {
              // cannot create partner without partner type id
              throw new Error('cannot create partner without partner type id')
            }
            const staffObject = {staff_name_l10n, mobile_number, staff_type_id: staff_type_id[0]}
            const createdStaff = await transactionalEntityManager
              .createQueryBuilder()
              .insert()
              .into(staffEntity)
              .values(staffObject)
              .execute()

            staffIdToUpdate = createdStaff.identifiers[0].staff_id

          } else {
            // update partner
            staffIdToUpdate = staffId
            if (staff_name_l10n !== undefined || mobile_number !== undefined) {
              const staffDataToUpdate = {}
              if (staff_name_l10n !== undefined) {
                staffDataToUpdate.staff_name_l10n = staff_name_l10n
              }
              if (mobile_number !== undefined) {
                staffDataToUpdate.mobile_number = mobile_number
              }
              if (staff_type_id !== undefined && Array.isArray(staff_type_id)) {
                if (staff_type_id.length > 0) {
                  staffDataToUpdate.staff_type_id = staff_type_id[0]
                } else {
                  staffDataToUpdate.staff_type_id = null
                }
              }
              console.log('staffDataToUpdate = ', staffDataToUpdate)
              const updatePartnerResult = await staffRepo
                .createQueryBuilder()
                .update()
                .set(staffDataToUpdate)
                .where("staff_id = :id ", { id: staffIdToUpdate })
                .execute()
            }
          }
          if (Object.keys(restOfStaffDataAsClassificationData).length > 0) {
            const updateClassificationDataResult = await saveClassificationData(transactionalEntityManager, 'STAFF_CLASSIFICATION', staffIdToUpdate, restOfStaffDataAsClassificationData)
            returnValue.classificationDataUpdateResult = updateClassificationDataResult
          }
        } catch (error) {
          console.log('oc SSD c 20, error')
          console.log('oc SSD c 20a, error = ', error)
          throw error
        }
      })
      return returnValue
    } catch (error) {
      console.log('oc r SSD c 10, error')
      console.log('oc r SSD c 10a, error = ', error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class StaffTerritoryList {
  async create (data, params) {
    try {
      const returnValue = { return_code: 0 }
      const mainDBConnection = dbConnections().main

      const whereClause = ''
      const sortString = ''
      const limitString = ''

      const { staffId } = data

      let innerQuery = `
        select eg.entity_geography_id, coalesce(gt_rr.reference_name_l10n->>'en', gt_rr.reference_name_l10n->>'ul') geography_type,

          coalesce(v_rsv.state_id, coalesce(s_rgt.geography_id, coalesce(s_rgd.geography_id, s_rgs.geography_id))) state_id,
          coalesce(v_rsv.state_name_l10n, coalesce(s_rgt.geography_name_l10n, coalesce(s_rgd.geography_name_l10n,
            s_rgs.geography_name_l10n))) state_name_l10n,
          
          coalesce(v_rsv.district_id, coalesce(d_rgt.geography_id, d_rgd.geography_id)) district_id,
          coalesce(v_rsv.district_name_l10n, coalesce(d_rgt.geography_name_l10n, d_rgd.geography_name_l10n)) district_name_l10n,
          
          coalesce (v_rsv.taluk_id, t_rgt.geography_id) taluk_id,
          coalesce (v_rsv.taluk_name_l10n, t_rgt.geography_name_l10n) taluk_name_l10n,
          
          v_rsv.village_id, v_rsv.village_name_l10n
        
        from main.entity_geography eg 
        inner join main.ref_reference gt_rr on gt_rr.reference_id = eg.geography_type_id
        left join main.ref_sdtv_view_2 v_rsv on eg.geography_type_id = 1000320004 and eg.geography_id = v_rsv.village_id
        
        left join main.ref_geography t_rgt on t_rgt.geography_type_id = 1000320003 and eg.geography_type_id = 1000320003
          and eg.geography_id = t_rgt.geography_id
        left join main.entity_relationship t2d_rgt on t2d_rgt.entity_relationship_type_id = 1000210030 and t2d_rgt.entity_1_type_id = 1000460015
          and t2d_rgt.entity_2_type_id = 1000460015 and t2d_rgt.entity_2_entity_id = t_rgt.geography_id
        left join main.ref_geography d_rgt on d_rgt.geography_type_id = 1000320002 and d_rgt.geography_id = t2d_rgt.entity_1_entity_id
        left join main.entity_relationship d2s_rgt on d2s_rgt.entity_relationship_type_id = 1000210029 and d2s_rgt.entity_1_type_id = 1000460015
          and d2s_rgt.entity_2_type_id = 1000460015 and d2s_rgt.entity_2_entity_id = d_rgt.geography_id
        left join main.ref_geography s_rgt on s_rgt.geography_type_id = 1000320001 and s_rgt.geography_id = d2s_rgt.entity_1_entity_id
        
        left join main.ref_geography d_rgd on d_rgd.geography_type_id = 1000320002 and eg.geography_type_id = 1000320002
          and eg.geography_id = d_rgd.geography_id
        left join main.entity_relationship d2s_rgd on d2s_rgd.entity_relationship_type_id = 1000210029 and d2s_rgd.entity_1_type_id = 1000460015
          and d2s_rgd.entity_2_type_id = 1000460015 and d2s_rgd.entity_2_entity_id = d_rgd.geography_id
        left join main.ref_geography s_rgd on s_rgd.geography_type_id = 1000320001 and s_rgd.geography_id = d2s_rgd.entity_1_entity_id
        
        left join main.ref_geography s_rgs on s_rgs.geography_type_id = 1000320001 and eg.geography_type_id = 1000320001
          and eg.geography_id = s_rgs.geography_id
        
        where eg.entity_type_id = 1000460003 and eg.active = 1000100001 and eg.entity_uuid = '${staffId}'
        order by state_id, district_id, taluk_id
      `

      const countQuery = `
        select count(*) as count from (
          ${innerQuery}
        ) outerTable
        ${whereClause}
      `
      const reportResultCount = await mainDBConnection.manager.query(countQuery)
      const count = parseInt(reportResultCount[0].count)

      let query = `
          select * 
          from (
            ${innerQuery}
          ) outerTable
          ${whereClause}
          ${sortString}
          ${limitString}
        `

      const reportResultUnprocessed = await mainDBConnection.manager.query(query)
      const reportResult = postProcessRecords(undefined, reportResultUnprocessed, {})

      returnValue.count = count
      returnValue.report = reportResult

      return returnValue
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class DeleteTerritoryAssignment {
  async create (data, params) {
    try {
      const returnValue = { return_code: 0 }
      const mainDBConnection = dbConnections().main

      const { to_be_unassigned_territories, staff_type_id, staff_id, geography_type_id, geography_id } = data

      const entityGeographyObjectToUpdate = {
        active: 1000100002,
        end_date: new Date()
      }

      const saveResult = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
        try {
          const entityGeographyRepo = transactionalEntityManager.getRepository('entity_geography')

          if (to_be_unassigned_territories !== undefined && Array.isArray(to_be_unassigned_territories) && to_be_unassigned_territories.length > 0) {
            const updatePartnerResult = await entityGeographyRepo
              .createQueryBuilder()
              .update()
              .set(entityGeographyObjectToUpdate)
              .where("entity_geography_id IN (:...id) ", { id: to_be_unassigned_territories })
              .execute()
          } else if (staff_type_id !== undefined && staff_type_id !== null
            && staff_id !== undefined && staff_id !== null
            && geography_type_id !== undefined && geography_type_id !== null
            && geography_id !== undefined && geography_id !== null) {
            const updatePartnerResult = await entityGeographyRepo
              .createQueryBuilder()
              .update()
              .set(entityGeographyObjectToUpdate)
              .where("active = 1000100001 and entity_type_id = :entityTypeId and entity_uuid = :entityUUID and geography_type_id = :geographyTypeId and geography_id = :geographyId  ", { entityTypeId: staff_type_id, entityUUID: staff_id, geographyTypeId: geography_type_id, geographyId: geography_id })
              .execute()
          } else {
            throw new Error('Inadequate data to update')
          }
        } catch (error) {
          console.log('oc SSD c 20, error')
          console.log('oc SSD c 20a, error = ', error)
          throw error
        }
      })

      return returnValue
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class AssignTerritory {
  async create (data, params) {
    try {
      const returnValue = { return_code: 0 }
      const mainDBConnection = dbConnections().main

      const { villages_to_assign, staffId } = data

      const saveResult = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
        try {
          const entityGeographyRepo = transactionalEntityManager.getRepository('entity_geography')
          const entityGeographyTableEntity = mainDBConnection.entities['entity_geography']

          const entityGeographyObjectsToCreate = []

          const baseEntityGeographyObjectToCreate = {
            entity_type_id: 1000460003,
            entity_uuid: staffId,
            geography_type_id: 1000320004,
          }
          for (const villageId of villages_to_assign) {
            const entityGeographyObjectToCreate = {
              ...baseEntityGeographyObjectToCreate,
              geography_id: villageId,
            }
            entityGeographyObjectsToCreate.push(entityGeographyObjectToCreate)
          }

          const newlyCreatedEntityStatus = await transactionalEntityManager.createQueryBuilder().insert().into(entityGeographyTableEntity).values(entityGeographyObjectsToCreate).execute()
        } catch (error) {
          console.log('oc SSD c 20, error')
          console.log('oc SSD c 20a, error = ', error)
          throw error
        }
      })

      return returnValue
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

module.exports = { StaffList, StaffListFilter, GetStaffDetails, SetStaffDetails, StaffTerritoryList, DeleteTerritoryAssignment, AssignTerritory }
