const { LibAnimalService, loadClassificationData } = require("@krushal-it/back-end-lib")
const { RELATION_REFERENCE } = require("@krushal-it/back-end-lib/utils/constant")
const { record_statuses } = require("@krushal-it/back-end-lib/ENUMS")
const { default: axios } = require("axios")
const { applyRules } = require("../rules/rules.service")
const { dbConnections } = require("@krushal-it/ah-orm")

class Predicor {
  animalServiceHandler = new LibAnimalService()

  async create(data, params) {
    if (!data.animal_id) {
      return { return_code: -101, message: "animal_id is required" }
    }

    const payload = {
      diagnosis: data.diagnosis
    }

    const fields = ["animal_breed_1", "animal_age_1", "number_of_calvings_1", "number_of_months_pregnant_1", "animal_average_lpd_1"]

    const animalClassification = await loadClassificationData("ANIMAL_CLASSIFICATION", data.animal_id, fields)

    const getFirst = field => animalClassification[field]?.[0]

    const breedId = getFirst("animal_breed_1")
    if (breedId) {
      const cattleBreed = await dbConnections()
        .main.manager.getRepository(RELATION_REFERENCE)
        .findOne({
          where: {
            reference_id: breedId,
            active: record_statuses.ACTIVE
          }
        })

      if (cattleBreed) {
        payload.breed = cattleBreed.reference_name_l10n?.en
      }
    }

    const optionalFieldsMap = {
      age: "animal_age_1",
      num_calvings: "number_of_calvings_1",
      months_pregnant: "number_of_months_pregnant_1",
      avg_lpd: "animal_average_lpd_1"
    }

    for (const [key, field] of Object.entries(optionalFieldsMap)) {
      const value = getFirst(field)
      if (value !== undefined) {
        payload[key] = value
      }
    }
    console.log("payload predict", payload)
    const options = {
      method: "POST",
      url: "http://host.docker.internal:5020/predict",
      params: { client: params.route?.client || "amul" },
      headers: { "Content-Type": "application/json" },
      data: payload
    }

    try {
      const response = await axios.request(options)
      return response.data
    } catch (error) {
      console.error("Prediction API error:", error.message)
      return { return_code: -101, message: error.message }
    }
  }
}
class PredicorV2 {
  animalServiceHandler = new LibAnimalService()

  async create(data, params) {
    if (!data.animal_id) {
      return { return_code: -101, message: "animal_id is required" }
    }

    const payload = {
      diagnosis: data.diagnosis.map(d => d.diagnosis_name)
    }

    const fields = ["animal_breed_1", "animal_age_1", "number_of_calvings_1", "number_of_months_pregnant_1", "animal_average_lpd_1"]

    const animalClassification = await loadClassificationData("ANIMAL_CLASSIFICATION", data.animal_id, fields)

    const getFirst = field => animalClassification[field]?.[0]

    const breedId = getFirst("animal_breed_1")
    if (breedId) {
      const cattleBreed = await dbConnections()
        .main.manager.getRepository(RELATION_REFERENCE)
        .findOne({
          where: {
            reference_id: breedId,
            active: record_statuses.ACTIVE
          }
        })

      if (cattleBreed) {
        payload.breed = cattleBreed.reference_name_l10n?.en
      }
    }

    const optionalFieldsMap = {
      age: "animal_age_1",
      num_calvings: "number_of_calvings_1",
      months_pregnant: "number_of_months_pregnant_1",
      avg_lpd: "animal_average_lpd_1"
    }
    const ruleBasedMedicines = []

    if (data.diagnosis && Array.isArray(data.diagnosis) && data.diagnosis.length > 0) {

      const refMap = global.ref_medicines

      for (const diagnosis of data.diagnosis) {
        const facts = {
          diagnosis_id: diagnosis.diagnosis_id
        }

        const matchingRulesData = await applyRules(facts, "medicine_recomendation_rules")

        if (matchingRulesData && matchingRulesData.medicine_names) {
          
          const medicine = matchingRulesData.medicine_names.map(med_id => {
            const medName = refMap[med_id] || med_id;
            return { medicine: medName, confidence_percent: 100, type: "protocol" }
          })

          ruleBasedMedicines.push(...medicine)

        }
      }
    }
    for (const [key, field] of Object.entries(optionalFieldsMap)) {
      const value = getFirst(field)
      if (value !== undefined) {
        payload[key] = value
      }
    }
    console.log("payload predict", payload)
    const options = {
      method: "POST",
      url: "http://host.docker.internal:5020/predict/v2",
      params: { client: params.route?.client || "amul" },
      headers: { "Content-Type": "application/json" },
      data: payload
    }

    try {
      const response = await axios.request(options)
      const bestMatch = response.data.best_match_model_results.map(p => {
        return { ...p, type: "best" }
      })
      const hybridMatch = response.data.hybrid_model_result.map(p => {
        return { ...p, type: "hybrid" }
      })

      // Deduplicate and group by type: protocol > best > hybrid
      const seen = new Set()
      let resultMap = { protocol: [], best: [], hybrid: [] }

      const addUnique = (arr, type) => {
        for (const item of arr) {
          const key = item.medicine?.trim()
          if (key && !seen.has(key)) {
            seen.add(key)
            resultMap[type].push(item)
          }
        }
      }

      addUnique(ruleBasedMedicines, "protocol")
      addUnique(bestMatch, "best")
      addUnique(hybridMatch, "hybrid")
      resultMap = [ ...resultMap.protocol, ...resultMap.best, ...resultMap.hybrid]
      return { predict: resultMap }
    } catch (error) {
      console.error("Prediction API error:", error.message)
      return { return_code: -101, message: error.message }
    }
  }




}



module.exports = { Predicor, PredicorV2 }


