const { getDBConnections, postProcessRecords, preProcessRecords, dbConnections } = require('@krushal-it/ah-orm')
const { Brackets } = require('typeorm')

const { getUserInfoBasedOnHeaderInformation } = require('../../utils/common/auth/auth')
const { configurationJSON } = require('@krushal-it/common-core')

const convertArrayToCommaSeparatedConcatedString = (stringArray) => {
  let commaSeparatedConcatenatedString = ''
  for (const string of stringArray) {
    if (commaSeparatedConcatenatedString !== '') {
      commaSeparatedConcatenatedString = commaSeparatedConcatenatedString + ', '
    }
    commaSeparatedConcatenatedString = commaSeparatedConcatenatedString + "'" + string + "'"
  }
  return commaSeparatedConcatenatedString
}

class ReferenceTime {
  async find (params) {
    try {
      console.log('s s s RT 1')
      const mainDBConnection = getDBConnections().main
      const currentTimeQuery = 'SELECT NOW()'
      const currentTimeQueryResultsResults = await mainDBConnection.manager.query(currentTimeQuery)

      return { return_code: 0, data: currentTimeQueryResultsResults[0] }
    } catch (error) {
      return { return_code: -101, message: error.message }
    }
  }
}

const identifyAndHandleDuplicateMobileNumber = async (recordWithMobileNumber) => {
  try {
    console.log('s s s iDMN 1')
    if (recordWithMobileNumber.mobile_number === undefined) {
      console.log('s s s iDMN 2')
      return
    }
    console.log('s s s iDMN 3')
    const mobileNumber = recordWithMobileNumber.mobile_number
    const customerId = recordWithMobileNumber.customer_id

    const queryForDuplicateMobileNumber = `
        select count(*) as duplicate_mobile_number_count
        from main.customer c
        where c.mobile_number = '${mobileNumber}'
        and c.active = 1000100001
        and c.customer_id != '${customerId}'
      `
    const countRecordsOfDuplicateRecordsForMobileNumber = await dbConnections().main.manager.query(queryForDuplicateMobileNumber)
    console.log('s s s iDMN 4')
    const countOfDuplicateRecordsForMobileNumber = countRecordsOfDuplicateRecordsForMobileNumber[0].duplicate_mobile_number_count
    if (countOfDuplicateRecordsForMobileNumber > 0) {
      // there is a duplicate. We need to inform Admin / Ops
      console.log('s s s iDMN 5')
      recordWithMobileNumber.mobile_number = recordWithMobileNumber.mobile_number + '-duplicate'
    }
  } catch (error) {
    console.log('s s s iDMN 10, error')
  }
}

const preInsertSyncHook = async (tableName, recordsToBeInserted) => {
  try {
    console.log('s s s prISH 1')
    if (tableName === 'customer') {
      console.log('s s s prISH 2')
      for (const recordWithMobileNumber of recordsToBeInserted) {
        console.log('s s s prISH 3')
        await identifyAndHandleDuplicateMobileNumber(recordWithMobileNumber)
        console.log('s s s prISH 4')
      }
      console.log('s s s prISH 8')
    }
    console.log('s s s prISH 9')
  } catch (error) {
    console.log('s s s prISH 100, error')
    console.log('s s s prISH 100, error = ', error)
    throw error
  }
}

const postInsertSyncHook = async (tableName, recordsToBeInserted) => {
  try {
    console.log('s s s poISH 1')
    // if table = customer_classification
    // classifier_id includes village
    // this would mean that the customer and his village are inserted
    // check if customer and visual id exist
    // use this to create farmer_visual_id
    // if customer is not available, inform admin

    // if table = customer_classification
    console.log('s s s poISH 2')
    if (tableName === 'customer_classification') {
      console.log('s s s poISH 3, recordsToBeInserted = ', recordsToBeInserted)
      // classifier_id includes village
      const classifierContainsVillageRecordsAsFullRows = recordsToBeInserted.filter((object) => {
        if (object.classifier_id && object.classifier_id === 2000000055 && object.value_reference_id > 0) {
          return true
        } else {
          return false
        }
      })
      console.log('s s s poISH 4, classifierContainsVillageRecordsAsFullRows = ', classifierContainsVillageRecordsAsFullRows)

      if (classifierContainsVillageRecordsAsFullRows.length > 0) {
        console.log('s s s poISH 5')
        const customerClassifierIdsForCustomerVillage = classifierContainsVillageRecordsAsFullRows.map((object) => object.customer_classification_id)

        // const commaSeparatedCustomerClassifierIds = customerClassifierIdsForCustomerVillage.join(',')
        let commaSeparatedCustomerClassifierIds = ''
        for (const customerClassifierId of customerClassifierIdsForCustomerVillage) {
          if (commaSeparatedCustomerClassifierIds !== '') {
            commaSeparatedCustomerClassifierIds = commaSeparatedCustomerClassifierIds + ', '
          }
          commaSeparatedCustomerClassifierIds = commaSeparatedCustomerClassifierIds + "'" + customerClassifierId + "'"
        }

        // console.log('s s s SCD 1, commaSeparatedCustomerClassifierIds = ', commaSeparatedCustomerClassifierIds)
        // console.log('s SCD 5g, commaSeparatedCustomerClassifierIds = ', commaSeparatedCustomerClassifierIds)

        const queryToGenerateVisualId = `
          with generate_visual_id as (
            select c.customer_id, rsv.state_short_code, rsv.district_short_code, rsv.taluk_short_code,
            tc.tc,
            rsv.state_short_code || rsv.district_short_code || rsv.taluk_short_code || LPAD((COALESCE(tc.tc, 0) + row_number() over(partition by tc.taluk_id))::text, 6, '0') as new_visual_id
            from main.customer_classification vc
            inner join main.ref_sdtv_view rsv on vc.value_reference_id = rsv.village_id
            inner join main.customer c on c.customer_id = vc.customer_id
            left join (
              select taluk_id, max(visual_id_number) tc
              from (
                select rv.taluk_id, c.customer_visual_id, CAST(COALESCE(REGEXP_REPLACE(c.customer_visual_id, '[A-Za-z]+', ''), '0') AS Integer) as visual_id_number
                from main.customer_classification vc
                inner join main.customer c on c.customer_id = vc.customer_id and c.customer_visual_id is not null and c.customer_visual_id != ''
                inner join main.ref_village rv on rv.village_id = vc.value_reference_id
                where vc.classifier_id = 2000000055
              ) rv
              group by rv.taluk_id
            ) tc on tc.taluk_id = rsv.taluk_id
            where vc.classifier_id = 2000000055
            and vc.customer_classification_id in (${commaSeparatedCustomerClassifierIds})
            and (c.customer_visual_id is null or c.customer_visual_id = '')
            order by c.created_at
          )
          update main.customer c
          set customer_visual_id = generate_visual_id.new_visual_id
          from generate_visual_id
          where generate_visual_id.customer_id = c.customer_id
        `
        console.log('s s s poISH 6, queryToGenerateVisualId = ', queryToGenerateVisualId)
        const farmerVisualIdGenerationResult = await dbConnections().main.manager.query(queryToGenerateVisualId)
        console.log('s s s poISH 7, farmerVisualIdGenerationResult = ', farmerVisualIdGenerationResult)
      }
    }
    console.log('s s s poISH 8')

    // if table = entity_relationship
    // if animal or farmer don't exist, inform admin
    // create visual_id based on farmer
    console.log('s s s poISH 9')
    if (tableName === 'entity_relationship') {
      console.log('s s s poISH 10, recordsToBeInserted = ', recordsToBeInserted)
      const f2aEntityRelationshipRecordsAsFullRows = recordsToBeInserted.filter((object) => {
        if ((object.entity_relationship_type_id = 1000210004)) {
          return true
        } else {
          return false
        }
      })
      console.log('s s s poISH 11, f2aEntityRelationshipRecordsAsFullRows = ', f2aEntityRelationshipRecordsAsFullRows)

      if (f2aEntityRelationshipRecordsAsFullRows.length > 0) {
        console.log('s s s poISH 12')
        const entityRelationshipIdsForNewFarmerToAnimalRecords = f2aEntityRelationshipRecordsAsFullRows.map((object) => object.entity_relationship_id)
        console.log('s s s poISH 13, entityRelationshipIdsForNewFarmerToAnimalRecords = ', entityRelationshipIdsForNewFarmerToAnimalRecords)

        // const commaSeparatedEntityRelationshipIds = entityRelationshipIdsForNewFarmerToAnimalRecords.join(',')
        let commaSeparatedEntityRelationshipIds = ''
        for (const entityRelationshipId of entityRelationshipIdsForNewFarmerToAnimalRecords) {
          if (commaSeparatedEntityRelationshipIds !== '') {
            commaSeparatedEntityRelationshipIds = commaSeparatedEntityRelationshipIds + ', '
          }
          commaSeparatedEntityRelationshipIds = commaSeparatedEntityRelationshipIds + "'" + entityRelationshipId + "'"
        }
        console.log('s s s poISH 14, commaSeparatedEntityRelationshipIds = ', commaSeparatedEntityRelationshipIds)

        // console.log('s s s SCD 3, commaSeparatedEntityRelationshipIds = ', commaSeparatedEntityRelationshipIds)

        const queryToGenerateAnimalVisualId = `
            with generate_visual_id as (
              select c.customer_id, c.customer_visual_id, a.animal_id, a.animal_visual_id, vin.max_animal_number,
                (c.customer_visual_id || '/A' || lpad((vin.max_animal_number + row_number() over(partition by vin.max_animal_number))::text, 3, '0')) as new_visual_id
              from main.entity_relationship er
              inner join main.animal a on a.animal_id = er.entity_2_entity_uuid and (a.animal_visual_id is null or a.animal_visual_id = '')
              inner join main.customer c on c.customer_id = er.entity_1_entity_uuid
              inner join (
                select customer_id, max(animal_number) max_animal_number
                from (
                  select er.entity_1_entity_uuid as customer_id, CAST(COALESCE(REGEXP_REPLACE(split_part(a.animal_visual_id, '/', 2), '[A-Za-z]+', ''), '0') AS Integer) as animal_number
                  from main.entity_relationship er
                  left join main.animal a on a.animal_id = er.entity_2_entity_uuid and a.animal_visual_id is not null and a.animal_visual_id != ''
                  where er.entity_relationship_type_id = 1000210004 
                ) farmer_max_animal_number
                group by customer_id
              ) vin on vin.customer_id = c.customer_id
              where er.entity_relationship_type_id = 1000210004 
              and er.entity_relationship_id in (${commaSeparatedEntityRelationshipIds})
            )
            update main.animal a
            set animal_visual_id = generate_visual_id.new_visual_id
            from generate_visual_id
            where generate_visual_id.animal_id = a.animal_id                    
          `
        console.log('s s s poISH 15, queryToGenerateAnimalVisualId = ', queryToGenerateAnimalVisualId)
        const animalVisualIdGenerationResult = await dbConnections().main.manager.query(queryToGenerateAnimalVisualId)
        console.log('s s s poISH 16, animalVisualIdGenerationResult = ', animalVisualIdGenerationResult)
      }
    }

    console.log('s s s poISH 17')
    if (tableName === 'animal') {
      console.log('s s s poISH 18, recordsToBeInserted = ', recordsToBeInserted)

      if (recordsToBeInserted.length > 0) {
        console.log('s s s poISH 19')
        const animalIdsForNewFarmerToAnimalRecords = recordsToBeInserted.map((object) => object.animal_id)
        console.log('s s s poISH 20, animalIdsForNewFarmerToAnimalRecords = ', animalIdsForNewFarmerToAnimalRecords)

        // const commaSeparatedEntityRelationshipIds = entityRelationshipIdsForNewFarmerToAnimalRecords.join(',')
        let commaSeparatedAnimalIds = ''
        for (const animalId of animalIdsForNewFarmerToAnimalRecords) {
          if (commaSeparatedAnimalIds !== '') {
            commaSeparatedAnimalIds = commaSeparatedAnimalIds + ', '
          }
          commaSeparatedAnimalIds = commaSeparatedAnimalIds + "'" + animalId + "'"
        }
        console.log('s s s poISH 21, commaSeparatedAnimalIds = ', commaSeparatedAnimalIds)

        const queryToGenerateAnimalVisualId = `
            with generate_visual_id as (
              select c.customer_id, c.customer_visual_id, a.animal_id, a.animal_visual_id, vin.max_animal_number,
                (c.customer_visual_id || '/A' || lpad((vin.max_animal_number + row_number() over(partition by vin.max_animal_number))::text, 3, '0')) as new_visual_id
              from main.animal a
              inner join main.entity_relationship er on er.entity_relationship_type_id = 1000210004 and er.entity_2_entity_uuid = a.animal_id
              inner join main.customer c on c.customer_id = er.entity_1_entity_uuid
              inner join (
                select customer_id, max(animal_number) max_animal_number
                from (
                  select er.entity_1_entity_uuid as customer_id, CAST(COALESCE(REGEXP_REPLACE(split_part(a.animal_visual_id, '/', 2), '[A-Za-z]+', ''), '0') AS Integer) as animal_number
                  from main.entity_relationship er
                  left join main.animal a on a.animal_id = er.entity_2_entity_uuid and a.animal_visual_id is not null and a.animal_visual_id != ''
                  where er.entity_relationship_type_id = 1000210004 
                ) farmer_max_animal_number
                group by customer_id
              ) vin on vin.customer_id = c.customer_id
              where a.animal_id in (${commaSeparatedAnimalIds})
              and (a.animal_visual_id is null or a.animal_visual_id = '')
            )
            update main.animal a
            set animal_visual_id = generate_visual_id.new_visual_id
            from generate_visual_id
            where generate_visual_id.animal_id = a.animal_id
          `
        console.log('s s s poISH 22, queryToGenerateAnimalVisualId = ', queryToGenerateAnimalVisualId)
        const animalVisualIdGenerationResult = await dbConnections().main.manager.query(queryToGenerateAnimalVisualId)
        console.log('s s s poISH 23, animalVisualIdGenerationResult = ', animalVisualIdGenerationResult)
      }
    }
  } catch (error) {
    console.log('s s s poISH 10, error')
    console.log('s s s poISH 10a, error = ', error)
    throw error
  }
}

const preUpdateSyncHook = async (tableName, recordsToBeUpdated) => {
  try {
    console.log('s s s prUSH 1')
    if (tableName === 'customer') {
      console.log('s s s prUSH 2')
      for (const recordWithMobileNumber of recordsToBeUpdated) {
        console.log('s s s prUSH 3')
        identifyAndHandleDuplicateMobileNumber(recordWithMobileNumber)
        console.log('s s s prUSH 4')
      }
      console.log('s s s prUSH 5')
    }
    console.log('s s s prUSH 9')
  } catch (error) {
    console.log('s s s prUSH 100, error')
    console.log('s s s prUSH 100, error = ', error)
    throw error
  }
}

const postUpdateSyncHook = () => {}

class SetClientData {
  async create /* or is it update */(data, params) {
    try {
      console.log('s s s SCD 1')
      const { entity_type_id: entityTypeId, entity_uuid: entityId, user_device_id: userDeviceId } = await getUserInfoBasedOnHeaderInformation(params.headers)
      const syncReferenceTime = data.sync_reference_time
      const clientDeltaAcrossDatabases = data.client_delta
      const INSERT_BATCH_SIZE = 1000;

      const returnValue = { return_code: 0 }
      const updatedDatabases = []
      // iterate through database data
      //    iterate through tables
      //      upsert data
      //      optional hook on insert

      const dbConnections = getDBConnections()
      const synchronizationDataRepo = dbConnections.config.repos.synchronization_data
      const synchronizationConflictRepo = dbConnections.config.repos.synchronization_conflict
      const synchronizationConflictEntity = dbConnections.config.entities.synchronization_conflict
      for (const databaseDeltaInData of clientDeltaAcrossDatabases) {
        const databaseName = databaseDeltaInData.database_name
        const databaseDelta = databaseDeltaInData.database_delta
        const updatedTables = []
        for (const tableDeltaInDatabaseDelta of databaseDelta) {
          const tableName = tableDeltaInDatabaseDelta.table_name
          const primaryKey = dbConnections[databaseName].entities[tableName].primaryColumns[0].propertyName
          let tableDelta = tableDeltaInDatabaseDelta.table_delta
          const tableEntity = dbConnections[databaseName].entities[tableName]
          const tableRepo = dbConnections[databaseName].repos[tableName]

          if (tableDelta.length > 0) {
            // get last sync time for table
            // check if the records sent have been updated after they were sent
            // extract those records out
            // create entries into conflict table
            // separate entries for insert and update
            // update, add criteria not to update if updated_at is greater than last sync time
            console.log('SCD 1, tableName = ', tableName)
            // console.log('tableDelta = ', tableDelta)

            const synchronizationDataResultsForTable = await synchronizationDataRepo.find({
              where: {
                user_device_id: userDeviceId,
                active: 1000100001,
                table_name: tableName
              }
            })
            let lastSyncTimeForTable = new Date('1970-01-01 01:01:01')
            if (synchronizationDataResultsForTable.length > 0) {
              lastSyncTimeForTable = synchronizationDataResultsForTable[0].last_sync_time
            }
            // re-assigning to the same variable as it is used downstream
            tableDelta = preProcessRecords(tableEntity, tableDelta, { setClientDelta: 1 })
            const clientDeltaPrimaryKeyArray = tableDelta.map((dbValuesInRow) => dbValuesInRow[primaryKey])
            console.log('SCD 1a, primary keys coming from client = ', clientDeltaPrimaryKeyArray)
            const primaryKeysPartOfClientDeltaAvailableOnServerAsFullRows = await tableRepo
              .createQueryBuilder('server_table')
              .select(['server_table.' + primaryKey])
              .where(primaryKey + ' in (:...primaryKeysFromClient)', {
                primaryKeysFromClient: clientDeltaPrimaryKeyArray
              })
              .getMany()
            // console.log('primaryKeysPartOfClientDeltaAvailableOnServerAsFullRows = ', primaryKeysPartOfClientDeltaAvailableOnServerAsFullRows)
            const primaryKeysPartOfClientDeltaAvailableOnServer = primaryKeysPartOfClientDeltaAvailableOnServerAsFullRows.map((object) => object[primaryKey])
            console.log('SCD 1b, primaryKeysPartOfClientDeltaAvailableOnServer = ', primaryKeysPartOfClientDeltaAvailableOnServer)
            const primaryKeysToBeInserted = clientDeltaPrimaryKeyArray.filter((individualPrimaryKey) => !primaryKeysPartOfClientDeltaAvailableOnServer.includes(individualPrimaryKey))
            console.log('SCD 1c, primaryKeysToBeInserted = ', primaryKeysToBeInserted)
            const recordsToBeInserted = tableDelta.filter((individualRow) => primaryKeysToBeInserted.includes(individualRow[primaryKey]))
            // console.log('recordsToBeInserted = ', recordsToBeInserted)

            const primaryKeysToBeUpdated = clientDeltaPrimaryKeyArray.filter((individualPrimaryKey) => primaryKeysPartOfClientDeltaAvailableOnServer.includes(individualPrimaryKey))
            console.log('SCD 1d, primaryKeysToBeUpdated = ', primaryKeysToBeUpdated)

            // no conflict - select * from table where primary_key in primary keys to be updated and updated_at < sync reference and < last sync time
            // conflict - select * from table where primary_key in primary keys to be updated and updated_at < sync reference and > last sync time

            // console.log('s sCD 1')
            let primaryKeysPartOfClientDeltaToBeUpdatedWithoutConflictWithRows = []
            if (primaryKeysToBeUpdated.length > 0) {
              // console.log('s sCD 1a')
              primaryKeysPartOfClientDeltaToBeUpdatedWithoutConflictWithRows = await tableRepo
                .createQueryBuilder('server_table')
                .select(['server_table.' + primaryKey])
                .where(' updated_at < :syncReferenceTime AND updated_at < :lastSyncTime AND ' + primaryKey + ' in (:...primaryKeysFromClientThatNeedToBeUpdated)', {
                  primaryKeysFromClientThatNeedToBeUpdated: primaryKeysToBeUpdated,
                  lastSyncTime: lastSyncTimeForTable,
                  syncReferenceTime
                })
                .getMany()
            }
            // console.log('s sCD 2')
            console.log('s sCD 2, primaryKeysPartOfClientDeltaToBeUpdatedWithoutConflictWithRows = ', primaryKeysPartOfClientDeltaToBeUpdatedWithoutConflictWithRows)

            // console.log('s sCD 3')
            const primaryKeysPartOfClientDeltaToBeUpdatedWithoutConflict = primaryKeysPartOfClientDeltaToBeUpdatedWithoutConflictWithRows.map((object) => object[primaryKey])
            console.log('s SCD 3a, primaryKeysPartOfClientDeltaToBeUpdatedWithoutConflict = ', primaryKeysPartOfClientDeltaToBeUpdatedWithoutConflict)
            const recordsToBeUpdatedWithoutConflict = tableDelta.filter((individualRow) => primaryKeysPartOfClientDeltaToBeUpdatedWithoutConflict.includes(individualRow[primaryKey]))
            // console.log('recordsToBeUpdatedWithoutConflict = ', recordsToBeUpdatedWithoutConflict)

            let primaryKeysPartOfClientDeltaToBeUpdatedWithConflictWithRows = []
            if (primaryKeysToBeUpdated.length > 0) {
              primaryKeysPartOfClientDeltaToBeUpdatedWithConflictWithRows = await tableRepo
                .createQueryBuilder('server_table')
                .where(' updated_at < :syncReferenceTime AND updated_at >= :lastSyncTime AND ' + primaryKey + ' in (:...primaryKeysFromClientThatNeedToBeUpdated)', {
                  primaryKeysFromClientThatNeedToBeUpdated: primaryKeysToBeUpdated,
                  lastSyncTime: lastSyncTimeForTable,
                  syncReferenceTime
                })
                .getMany()
            }
            // console.log('s sCD 3')
            console.log('s SCD 3b, primaryKeysPartOfClientDeltaToBeUpdatedWithConflictWithRows = ', primaryKeysPartOfClientDeltaToBeUpdatedWithConflictWithRows)

            // console.log('s sCD 4')
            const primaryKeysPartOfClientDeltaToBeUpdatedWithConflict = primaryKeysPartOfClientDeltaToBeUpdatedWithConflictWithRows.map((object) => object[primaryKey])
            console.log('s SCD 4a, primaryKeysPartOfClientDeltaToBeUpdatedWithConflict = ', primaryKeysPartOfClientDeltaToBeUpdatedWithConflict)
            const recordsToBeUpdatedWithConflict = tableDelta.filter((individualRow) => primaryKeysPartOfClientDeltaToBeUpdatedWithConflict.includes(individualRow[primaryKey]))
            // console.log('recordsToBeUpdatedWithConflict = ', recordsToBeUpdatedWithConflict)

            const primaryKeyOfRecordsToBeUpdatedWithConflict = []
            const primaryKeyOfRecordsToBeUpdatedWithConflictButCausedNoConflict = []
            for (const primaryKeysPartOfClientDeltaToBeUpdatedWithConflictWithRow of primaryKeysPartOfClientDeltaToBeUpdatedWithConflictWithRows) {
              const primaryKeyValue = primaryKeysPartOfClientDeltaToBeUpdatedWithConflictWithRow[primaryKey]
              const oldNonEncryptedColumns = primaryKeysPartOfClientDeltaToBeUpdatedWithConflictWithRow
              const newNonEncryptedColumnsAsRow = tableDelta.filter((individualRow) => individualRow[primaryKey] === primaryKeyValue)
              const newNonEncryptedColumns = newNonEncryptedColumnsAsRow[0]
              console.log('primaryKeyValue = ', primaryKeyValue)
              console.log('oldNonEncryptedColumns = ', oldNonEncryptedColumns)
              console.log('newNonEncryptedColumns = ', newNonEncryptedColumns)
              const recordToBeUpdatedWithConflict = {
                ...newNonEncryptedColumns
              }
              delete recordToBeUpdatedWithConflict[primaryKey]
              console.log('recordToBeUpdatedWithConflict = ', recordToBeUpdatedWithConflict)
              const updateTableResults = await tableRepo
                .createQueryBuilder()
                .update()
                .set(recordToBeUpdatedWithConflict)
                .where(primaryKey + ' = :primaryKeyValue AND updated_at < :syncReferenceTime', {
                  primaryKeyValue,
                  syncReferenceTime
                })
                .execute()
              // console.log('updateTableResults = ', updateTableResults)
              if (updateTableResults.affected > 0) {
                primaryKeyOfRecordsToBeUpdatedWithConflict.push(primaryKeyValue)

                const syncConflictValue = {
                  user_device_id: userDeviceId,
                  table_name: tableName,
                  table_primary_key_uuid: primaryKeyValue,
                  old_non_encrypted_column_data: oldNonEncryptedColumns,
                  new_non_encrypted_column_data: newNonEncryptedColumns
                }

                const synchronizationConflictEntity = dbConnections.config.entities.synchronization_conflict
                const preProcessedSynchronizationConflictEntity = preProcessRecords(synchronizationConflictEntity, syncConflictValue, synchronizationConflictEntity, synchronizationConflictEntity.additionalAttributes)
                const insertTableResults = await dbConnections.config.manager.createQueryBuilder().insert().into(synchronizationConflictEntity).values(preProcessedSynchronizationConflictEntity).execute()
                // console.log('insertTableResults = ', insertTableResults)
              } else {
                primaryKeyOfRecordsToBeUpdatedWithConflictButCausedNoConflict.push(primaryKeyValue)
              }
            }

            console.log('s SCD 5')
            const insertedRecordPrimaryKeys = []
            if (recordsToBeInserted.length > 0) {
              
              await preInsertSyncHook(tableName, recordsToBeInserted)
              console.log('s SCD 5a')

              // Process records in batches
              for (let i = 0; i < recordsToBeInserted.length; i += INSERT_BATCH_SIZE) {
                const batch = recordsToBeInserted.slice(i, i + INSERT_BATCH_SIZE);
                const insertTableResults = await dbConnections[databaseName].manager
                  .createQueryBuilder()
                  .insert()
                  .into(tableEntity)
                  .values(batch)
                  .execute();

                console.log('s SCD 5b, batch inserted', tableName, batch.length);
                const extractedPrimaryKeysThatWereInserted = insertTableResults.identifiers.map((row) => row[primaryKey]);
                insertedRecordPrimaryKeys.push(...extractedPrimaryKeysThatWereInserted);
              }

              await postInsertSyncHook(tableName, recordsToBeInserted)
            }
            console.log('s SCD 7')

            const primaryKeyOfRecordsToBeUpdatedWithoutConflict = []
            for (const recordToBeUpdatedWithoutConflict of recordsToBeUpdatedWithoutConflict) {
              await preUpdateSyncHook(tableName, [recordToBeUpdatedWithoutConflict])

              const primaryKeyValue = recordToBeUpdatedWithoutConflict[primaryKey]
              delete recordToBeUpdatedWithoutConflict[primaryKey]
              const updateTableResults = await tableRepo
                .createQueryBuilder()
                .update()
                .set(recordToBeUpdatedWithoutConflict)
                .where(primaryKey + ' = :primaryKeyValue AND updated_at < :lastSyncTime', {
                  primaryKeyValue,
                  lastSyncTime: lastSyncTimeForTable
                })
                .execute()
              // console.log('updateTableResults = ', updateTableResults)
              if (updateTableResults.affected > 0) {
                primaryKeyOfRecordsToBeUpdatedWithoutConflict.push(primaryKeyValue)
              }
            }

            const primaryKeysInsertedOrUpdated = [...insertedRecordPrimaryKeys, ...primaryKeyOfRecordsToBeUpdatedWithoutConflict, ...primaryKeyOfRecordsToBeUpdatedWithConflict, ...primaryKeyOfRecordsToBeUpdatedWithConflictButCausedNoConflict]

            updatedTables.push({
              table_name: tableName,
              upserted_primary_keys: primaryKeysInsertedOrUpdated
            })
          }
        }
        if (updatedTables.length > 0) {
          updatedDatabases.push({
            database_name: databaseName,
            updated_table_primary_keys: updatedTables
          })
        }
      }
      if (updatedDatabases.length > 0) {
        returnValue.database_client_data_return_values = updatedDatabases
      }
      return returnValue
    } catch (error) {
      
      console.error("Error occurred during set client delta", error)
      
      if (error.name === 'KrushalError') {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

const checkIfTableExistsAndDecideChangesDone = (finalSetOfTables, dependentTables) => {
  let changesDone = false
  for (const dependentTable of dependentTables) {
    if (!finalSetOfTables.includes(dependentTable)) {
      finalSetOfTables.push(dependentTable)
      changesDone = true
    }
  }
  return changesDone
}

const getRelevantTablesForSyncInDB = (databaseName, response) => {
  // ["customer_self", "staff_self", "staff_main", "staff_related", "customer", "animal", "geo"],

  // console.log('s gRTFSID 1')
  const dbConnections = getDBConnections()
  const dbSpecificSyncConfiguration = dbConnections[databaseName].syncConfiguration
  // console.log('database name = ', databaseName)
  // console.log('database contents = ', dbConnections[databaseName])
  // console.log('database sync configuration = ')
  let universalSetOfTablesToSyncForDB = []
  if (dbSpecificSyncConfiguration && dbSpecificSyncConfiguration.one_way_sync_tables && Array.isArray(dbSpecificSyncConfiguration.one_way_sync_tables)) {
    universalSetOfTablesToSyncForDB = [...universalSetOfTablesToSyncForDB, ...dbSpecificSyncConfiguration.one_way_sync_tables]
  }

  if (dbSpecificSyncConfiguration && dbSpecificSyncConfiguration.two_way_sync_tables && Array.isArray(dbSpecificSyncConfiguration.two_way_sync_tables)) {
    // console.log('s gRTFSID 2')
    universalSetOfTablesToSyncForDB = [...universalSetOfTablesToSyncForDB, ...dbSpecificSyncConfiguration.two_way_sync_tables]
  }
  if (universalSetOfTablesToSyncForDB.length === 0) {
    // console.log('s gRTFSID 3, no tables to sync for database ', databaseName)
  }

  let relevantServerTablesInParams = []
  let relevantClientTablesInDBForSync = []
  if (universalSetOfTablesToSyncForDB.length > 0) {
    // console.log('s gRTFSID 4, response.database_and_table_information = ', response.database_and_table_information)
    if (response.database_and_table_information && response.database_and_table_information[databaseName] && Array.isArray(response.database_and_table_information[databaseName])) {
      // console.log('s gRTFSID 5')
      relevantServerTablesInParams = response.database_and_table_information[databaseName]

      if (relevantServerTablesInParams.length > 0) {
        relevantServerTablesInParams = relevantServerTablesInParams.filter((elem, index) => relevantServerTablesInParams.indexOf(elem) === index)
        relevantServerTablesInParams = relevantServerTablesInParams.filter((object) => universalSetOfTablesToSyncForDB.includes(object))
      }
    }
    // console.log('s gRTFSIDGSD 6')
    relevantClientTablesInDBForSync = relevantServerTablesInParams.length > 0 ? relevantServerTablesInParams : universalSetOfTablesToSyncForDB
    // console.log('s gRTFSID 7, relevantClientTablesInDBForSync = ', relevantClientTablesInDBForSync)
    // relevantClientTablesInDBForSync = updateDependentTableBasedOnSelectedTables(relevantClientTablesInDBForSync)
    // console.log('s gRTFSID 8, relevantClientTablesInDBForSync = ', relevantClientTablesInDBForSync)
    relevantClientTablesInDBForSync.sort((a, b) => {
      return universalSetOfTablesToSyncForDB.indexOf(a) - universalSetOfTablesToSyncForDB.indexOf(b)
    })
    // console.log('s gRTFSID 9, relevantClientTablesInDBForSync = ', relevantClientTablesInDBForSync)
  }
  // console.log('s gRTFSID 10, relevantClientTablesInDBForSync = ', relevantClientTablesInDBForSync)
  return relevantClientTablesInDBForSync
}

/*
  customer onwards -> customer_self or customer
  customer depends on staff_for_customer
  staff_for_customer -> (staff_zo or staff_ro or staff_paravet) -> their villages (from entity_geography)
  also staff_for_customer (if customer_self) is paravet and ro
  state / district / taluk / village -> (staff_zo or staff_ro or staff_paravet) -> their villages (from entity_geography)
  entity_geography (depends on staff_for_customer)
  entity_relationship (depends on staff_for_customer and customer and animal)

  filters
  - geo
  - staff_for_customer
  - customer_self
  - animal
*/
const determineFilterDataNecessary = (entityTypeId, relevantClientTablesInDBForSync) => {
  const filterDataToBeFilled = { data: {} }
  // console.log('s dDTC 1, relevantClientTablesInDBForSync = ', relevantClientTablesInDBForSync)
  for (const relevantClientTableInDBForSync of relevantClientTablesInDBForSync) {
    switch (relevantClientTableInDBForSync) {
      case 'ref_state':
      case 'ref_district':
      case 'ref_taluk':
      case 'ref_village':
        filterDataToBeFilled.geo = 1
        break
      case 'staff_activity':
      case 'care_calendar_classification':
      case 'note':
        filterDataToBeFilled.animal = 1
        filterDataToBeFilled.customer = 1
        filterDataToBeFilled.task = 1
        break
      case 'care_calendar':
        filterDataToBeFilled.animal = 1
        filterDataToBeFilled.customer = 1
        filterDataToBeFilled.task = 1
        break
      case 'animal_disease_classification':
        filterDataToBeFilled.animal_disease = 1
        break
      case 'animal_disease':
        filterDataToBeFilled.animal = 1
        break
      case 'entity_geography':
        filterDataToBeFilled.staff_for_customer = 1
        break
      case 'customer':
        if (entityTypeId === 1000460001) {
          filterDataToBeFilled.customer_self = 1
        } else {
          filterDataToBeFilled.staff_for_customer = 1
        }
        break
      case 'customer_classification':
        if (entityTypeId === 1000460001) {
          filterDataToBeFilled.customer_self = 1
        } else {
          filterDataToBeFilled.customer = 1
          filterDataToBeFilled.additional_customer_based_on_task = 1
        }
        break
      case 'staff_classification':
        filterDataToBeFilled.staff_for_customer = 1
        filterDataToBeFilled.staff = 1
        break
      case 'staff':
        filterDataToBeFilled.staff_for_customer = 1
        filterDataToBeFilled.staff = 1
        break
      case 'animal_classification':
        filterDataToBeFilled.animal = 1
        break
      case 'animal':
        if (entityTypeId === 1000460001) {
          filterDataToBeFilled.customer_self = 1
        } else {
          filterDataToBeFilled.customer = 1
        }
        filterDataToBeFilled.staff_for_customer = 1
        filterDataToBeFilled.animal = 1
        break
      case 'entity_relationship':
        if (entityTypeId === 1000460001) {
          filterDataToBeFilled.customer_self = 1
        } else {
          filterDataToBeFilled.customer = 1
        }
        filterDataToBeFilled.staff_for_customer = 1
        filterDataToBeFilled.animal = 1
        break
      case 'document':
        if (entityTypeId === 1000460001) {
          filterDataToBeFilled.customer_self = 1
        } else {
          filterDataToBeFilled.customer = 1
        }
        filterDataToBeFilled.staff_for_customer = 1
        filterDataToBeFilled.animal = 1
        filterDataToBeFilled.animal_classification = 1
        filterDataToBeFilled.task = 1
        break
      case 'task_question_data':
        if (entityTypeId === 1000460001) {
          filterDataToBeFilled.customer_self = 1
        } else {
          filterDataToBeFilled.customer = 1
        }
        filterDataToBeFilled.staff_for_customer = 1
        filterDataToBeFilled.animal = 1
        filterDataToBeFilled.task = 1
        break
      case 'requisition':
        filterDataToBeFilled.staff_for_customer = 1
        break
      case 'requisition_line_items':
        filterDataToBeFilled.staff_for_customer = 1
        break
      case 'requisition_classification':
        filterDataToBeFilled.staff_for_customer = 1
        break
      case 'warehouse_blockqty':
        filterDataToBeFilled.staff_for_customer = 1
        break
      case 'inventory_ledger':
        filterDataToBeFilled.staff_for_customer = 1
        break
      case 'entity_status':
        filterDataToBeFilled.staff_for_customer = 1
        break
      case 'partner':
        break
      case 'partner_classification':
        break
      }
  }
  /* filters
  - geo
  - staff_for_customer
  - staff
  - customer_self
  - animal
  - task
  */
  let changesDoneToFilterDataToBeFilledStructure = true
  while (changesDoneToFilterDataToBeFilledStructure) {
    changesDoneToFilterDataToBeFilledStructure = false
    for (const filterKey in filterDataToBeFilled) {
      switch (filterKey) {
        case 'geo':
          if (!filterDataToBeFilled.staff_for_customer) {
            filterDataToBeFilled.staff_for_customer = 1
            changesDoneToFilterDataToBeFilledStructure = true
          }
          break
        case 'animal':
          if (entityTypeId === 1000460001) {
            if (!filterDataToBeFilled.customer_self) {
              filterDataToBeFilled.customer_self = 1
              changesDoneToFilterDataToBeFilledStructure = true
            }
          } else {
            if (!filterDataToBeFilled.customer) {
              filterDataToBeFilled.customer = 1
              changesDoneToFilterDataToBeFilledStructure = true
            }
          }
          if (!filterDataToBeFilled.staff_for_customer) {
            filterDataToBeFilled.staff_for_customer = 1
            changesDoneToFilterDataToBeFilledStructure = true
          }
          break
      }
    }
  }
  // console.log('s dDTC 2, filterDataToBeFilled = ', filterDataToBeFilled)
  return filterDataToBeFilled
}
/* filters
  - geo
  - staff_for_customer
  - staff
  - customer_self
  - animal
  */

const getFilterDataForTables = async (entityTypeId, entityId, kindsOfFiltersObject) => {
  const dummyInitial = ['01995d8e-f171-4e96-b7a6-185faff8f1b1']
  let staffs_for_customers = ['01995d8e-f171-4e96-b7a6-185faff8f1b1']
  let staffs = ['01995d8e-f171-4e96-b7a6-185faff8f1b1']
  let staff_paravets = ['01995d8e-f171-4e96-b7a6-185faff8f1b1']
  let staff_ros = ['01995d8e-f171-4e96-b7a6-185faff8f1b1']
  const staff_zos = ['01995d8e-f171-4e96-b7a6-185faff8f1b1']
  let staff_backend = ['01995d8e-f171-4e96-b7a6-185faff8f1b1']
  let staff_vets = ['01995d8e-f171-4e96-b7a6-185faff8f1b1']
  const customer_self = ['01995d8e-f171-4e96-b7a6-185faff8f1b1']
  const customers = ['01995d8e-f171-4e96-b7a6-185faff8f1b1']
  let final_customers = ['01995d8e-f171-4e96-b7a6-185faff8f1b1']
  let villages = [-1]
  let animals = ['01995d8e-f171-4e96-b7a6-185faff8f1b1']
  let animal_classifications = ['01995d8e-f171-4e96-b7a6-185faff8f1b1']
  let diseases = ['01995d8e-f171-4e96-b7a6-185faff8f1b1']
  let tasks = ['01995d8e-f171-4e96-b7a6-185faff8f1b1']
  let tasks_for_documents = ['01995d8e-f171-4e96-b7a6-185faff8f1b1']


  let staffTypeId

  const dbConnections = getDBConnections()
  const mainSchemaAddition = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 'main.' : ''

  const backendUsersStaffIdAsRows = await dbConnections.main.repos.staff.createQueryBuilder().select(['staff_id']).where('active = 1000100001 and staff_type_id = 1000230002').execute()
  // const backendUsersStaffIds = backendUsersStaffIdAsRows.map((object) => object["staff_id"])
  const backendUsersStaffIds = []
  backendUsersStaffIdAsRows.forEach((object, index) => {
    if (object.staff_id !== null) {
      backendUsersStaffIds.push(object.staff_id)
    }
  })
  staff_backend = staff_backend.concat(backendUsersStaffIds)
  if (entityTypeId === 1000460001) {
    // customer_self
    // add customer to customer_self
    // get paravet, ro, zo, vet
    // add paravet to staff for customer
    // add paravet, ro, zo, vet to staff
    customer_self.push(entityId)

    const customerToParavetQuery = `
      select s.staff_id
      from ${mainSchemaAddition}customer_classification cc, ${mainSchemaAddition}entity_geography eg, ${mainSchemaAddition}staff s
      where cc.customer_id = '${entityId}' and cc.classifier_id = 2000000055
      and cc.value_reference_id = eg.geography_id and eg.start_date < now() and eg.end_date > now()
      and eg.geography_type_id = 1000320004 and eg.entity_type_id in (1000460003, 1000230004)
      and eg.entity_uuid = s.staff_id and s.staff_type_id = 1000230004
      `
    const customerToParavetQueryResult = await dbConnections.main.manager.query(customerToParavetQuery)
    if (customerToParavetQueryResult.length > 0) {
      const paravetId = customerToParavetQueryResult[0].staff_id
      staff_paravets.push(paravetId)
      staffs_for_customers.push(paravetId)
    }

    const customerToROQuery = `
      select s.staff_id
      from ${mainSchemaAddition}customer_classification cc, ${mainSchemaAddition}entity_geography eg, ${mainSchemaAddition}staff s
          where cc.customer_id = '${entityId}' and cc.classifier_id = 2000000055
          and cc.value_reference_id = eg.geography_id and eg.start_date < now() and eg.end_date > now()
          and eg.geography_type_id = 1000320004 and eg.entity_type_id in (1000460003, 1000230004)
          and eg.entity_uuid = s.staff_id and s.staff_type_id = 1000230001      
      `
    const customerToROQueryResult = await dbConnections.main.manager.query(customerToROQuery)
    if (customerToROQueryResult.length > 0) {
      const roId = customerToROQueryResult[0].staff_id
      staff_ros.push(roId)
    }

    const customerToZOQuery = `
        select s.staff_id
        from ${mainSchemaAddition}customer_classification cc, ${mainSchemaAddition}entity_geography eg, ${mainSchemaAddition}staff s
        where cc.customer_id = '${entityId}' and cc.classifier_id = 2000000055
        and cc.value_reference_id = eg.geography_id and eg.start_date < now() and eg.end_date > now()
        and eg.geography_type_id = 1000320004 and eg.entity_type_id in (1000460003, 1000230004)
        and eg.entity_uuid = s.staff_id and s.staff_type_id = 1000230005
      `
    const customerToZOQueryResult = await dbConnections.main.manager.query(customerToZOQuery)
    if (customerToZOQueryResult.length > 0) {
      const zoId = customerToZOQueryResult[0].staff_id
      staff_zos.push(zoId)
    }

    const customerToVetQuery = `
      select s.staff_id
      from ${mainSchemaAddition}customer_classification cc, ${mainSchemaAddition}entity_geography eg, ${mainSchemaAddition}staff s
          where cc.customer_id = '${entityId}' and cc.classifier_id = 2000000055
          and cc.value_reference_id = eg.geography_id and eg.start_date < now() and eg.end_date > now()
          and eg.geography_type_id = 1000320004 and eg.entity_type_id in (1000460003, 1000230004)
          and eg.entity_uuid = s.staff_id and s.staff_type_id = 1000230003
      `
    const customerToVetQueryResult = await dbConnections.main.manager.query(customerToVetQuery)
    if (customerToVetQueryResult.length > 0) {
      const vetId = customerToVetQueryResult[0].staff_id
      staff_vets.push(vetId)
    }

    staffs = staffs.concat(staff_paravets)
    staffs = staffs.concat(staff_ros)
    staffs = staffs.concat(staff_zos)
    staffs = staffs.concat(staff_vets)
  } else if (entityTypeId === 1000460003) {
    // determine if paravet or ro or zo
    // if paravet
    // add paravet to staff for customer
    // get ro, zo
    // add paravet ro zo to staff
    // if ro
    // add ro to staff for customer
    // get paravet, zo
    // add paravet ro zo to staff
    // if zo
    // add zo to staff for customer
    // get ro, paravet
    // add paravet ro zo to staff
    const staffResult = await dbConnections.main.repos.staff.createQueryBuilder().select(['staff_type_id']).where({ staff_id: entityId }).getRawMany()
    // console.log('s gFDFT 1, staffResult = ', staffResult)
    staffTypeId = staffResult[0].staff_type_id
    switch (staffTypeId) {
      case 1000230004:
        {
          // paravet
          staffs_for_customers.push(entityId)
          const ROFromParavetQuery = `
              select ros.staff_id
              from ${mainSchemaAddition}entity_geography pseg, ${mainSchemaAddition}entity_geography roseg, ${mainSchemaAddition}staff ros
              where pseg.entity_type_id in (1000460003, 1000230004) and pseg.entity_uuid = '${entityId}'
              and pseg.geography_type_id = 1000320004 and pseg.start_date < now()
              and pseg.end_date > now() and pseg.geography_id = roseg.geography_id
              and roseg.geography_type_id = 1000320004 and roseg.entity_type_id in (1000460003, 1000230001)
              and roseg.start_date < now() and roseg.end_date > now()
              and roseg.entity_uuid = ros.staff_id and ros.staff_type_id = 1000230001
              group by ros.staff_id          
            `
          const ROFromParavetQueryResult = await dbConnections.main.manager.query(ROFromParavetQuery)
          if (ROFromParavetQueryResult.length > 0) {
            staff_ros.push(ROFromParavetQueryResult[0].staff_id)
          }

          const VetFromParavetQuery = `
              select vets.staff_id
              from ${mainSchemaAddition}entity_geography pseg, ${mainSchemaAddition}entity_geography vetseg, ${mainSchemaAddition}staff vets
              where pseg.entity_type_id in (1000460003, 1000230004) and pseg.entity_uuid = '${entityId}'
              and pseg.geography_type_id = 1000320004 and pseg.start_date < now()
              and pseg.end_date > now() and pseg.geography_id = vetseg.geography_id
              and vetseg.geography_type_id = 1000320004 and vetseg.entity_type_id in (1000460003, 1000230003)
              and vetseg.start_date < now() and vetseg.end_date > now()
              and vetseg.entity_uuid = vets.staff_id and vets.staff_type_id = 1000230003
              group by vets.staff_id          
            `
          const VetFromParavetQueryResult = await dbConnections.main.manager.query(VetFromParavetQuery)
          if (VetFromParavetQueryResult.length > 0) {
            const VetFromParavetQueryResultId = VetFromParavetQueryResult.map(staff => staff.staff_id)
            staff_vets = staff_vets.concat(VetFromParavetQueryResultId)
          }

          const ZOFromParavetQuery = `
              select zos.staff_id
              from ${mainSchemaAddition}entity_geography pseg, ${mainSchemaAddition}entity_geography zoseg, ${mainSchemaAddition}staff zos
              where pseg.entity_type_id in (1000460003, 1000230004) and pseg.entity_uuid = '${entityId}'
              and pseg.geography_type_id = 1000320004 and pseg.start_date < now()
              and pseg.end_date > now() and pseg.geography_id = zoseg.geography_id
              and zoseg.geography_type_id = 1000320004 and zoseg.entity_type_id in (1000460003, 1000230005)
              and zoseg.start_date < now() and zoseg.end_date > now()
              and zoseg.entity_uuid = zos.staff_id and zos.staff_type_id = 1000230005
              group by zos.staff_id          
            `
          const ZOFromParavetQueryResult = await dbConnections.main.manager.query(ZOFromParavetQuery)
          if (ZOFromParavetQueryResult.length > 0) {
            staff_zos.push(ZOFromParavetQueryResult[0].staff_id)
          }
          staffs = staffs.concat(staff_paravets)
          staffs = staffs.concat(staff_ros)
          staffs = staffs.concat(staff_vets)
          staffs = staffs.concat(staff_zos)
        }
        break
      case 1000230001:
        {
          // route officer
          // staffs_for_customers.push(entityId)
          staff_ros.push(entityId)
          staffs_for_customers.push(entityId)
          const ZOFromROQuery = `
              select zos.staff_id
              from ${mainSchemaAddition}entity_geography roseg, ${mainSchemaAddition}entity_geography zoseg, ${mainSchemaAddition}staff zos
              where roseg.entity_type_id in (1000460003, 1000230001) and roseg.entity_uuid = '${entityId}'
              and roseg.geography_type_id = 1000320004 and roseg.start_date < now()
              and roseg.end_date > now() and roseg.geography_id = zoseg.geography_id
              and zoseg.geography_type_id = 1000320004 and zoseg.entity_type_id in (1000460003, 1000230005)
              and zoseg.start_date < now() and zoseg.end_date > now()
              and zoseg.entity_uuid = zos.staff_id and zos.staff_type_id = 1000230005
              group by zos.staff_id          
            `
          // console.log('ZOFromROQuery = ', ZOFromROQuery)
          const ZOFromROQueryResult = await dbConnections.main.manager.query(ZOFromROQuery)
          // console.log('ZOFromROQueryResult = ', ZOFromROQueryResult)
          if (ZOFromROQueryResult.length > 0) {
            staff_zos.push(ZOFromROQueryResult[0].staff_id)
          }

          const VetFromROQuery = `
              select vets.staff_id
              from ${mainSchemaAddition}entity_geography roseg, ${mainSchemaAddition}entity_geography vetseg, ${mainSchemaAddition}staff vets
              where roseg.entity_type_id in (1000460003, 1000230001) and roseg.entity_uuid = '${entityId}'
              and roseg.geography_type_id = 1000320004 and roseg.start_date < now()
              and roseg.end_date > now() and roseg.geography_id = vetseg.geography_id
              and vetseg.geography_type_id = 1000320004 and vetseg.entity_type_id in (1000460003, 1000230003)
              and vetseg.start_date < now() and vetseg.end_date > now()
              and vetseg.entity_uuid = vets.staff_id and vets.staff_type_id = 1000230003
              group by vets.staff_id          
            `
          // console.log('VetFromROQuery = ', VetFromROQuery)
          const VetFromROQueryResult = await dbConnections.main.manager.query(VetFromROQuery)
          // console.log('VetFromROQueryResult = ', VetFromROQueryResult)
          if (VetFromROQueryResult.length > 0) {
            staff_vets.push(VetFromROQueryResult[0].staff_id)
          }

          const paravetFromROQuery = `
            select ps.staff_id
            from ${mainSchemaAddition}entity_geography roseg, ${mainSchemaAddition}entity_geography pseg, ${mainSchemaAddition}staff ps
            where roseg.entity_uuid = '${entityId}'
            and roseg.entity_type_id in (1000460003, 1000230001) and roseg.start_date < NOW() and roseg.end_date > NOW()
            and roseg.geography_type_id = 1000320004 and roseg.geography_id = pseg.geography_id
            and pseg.geography_type_id = 1000320004
            and pseg.start_date < NOW() and pseg.end_date > NOW()
            and pseg.entity_type_id in (1000460003, 1000230004) AND pseg.entity_uuid = ps.staff_id
            AND ps.staff_type_id = 1000230004
            group by ps.staff_id
            `
          const paravetFromROQueryRowResult = await dbConnections.main.manager.query(paravetFromROQuery)
          // const paravetFromROQueryResult = paravetFromROQueryRowResult.map((object) => object["staff_id"])
          const paravetFromROQueryResult = []
          paravetFromROQueryRowResult.forEach((object, index) => {
            if (object.staff_id !== null) {
              paravetFromROQueryResult.push(object.staff_id)
            }
          })
          if (paravetFromROQueryResult.length > 0) {
            staff_paravets = staff_paravets.concat(paravetFromROQueryResult)
            staffs_for_customers = staffs_for_customers.concat(staff_paravets)
          }
          staffs = staffs.concat(staff_paravets)
          staffs = staffs.concat(staff_ros)
          staffs = staffs.concat(staff_vets)
          staffs = staffs.concat(staff_zos)
        }
        break
      case 1000230005:
        {
          // zonal officer
          staffs_for_customers.push(entityId)
          staff_zos.push(entityId)

          const paravetFromZOQuery = `
              select ps.staff_id
              from ${mainSchemaAddition}entity_geography zoseg, ${mainSchemaAddition}entity_geography pseg, ${mainSchemaAddition}staff ps
              where zoseg.entity_uuid = '${entityId}'
              and zoseg.entity_type_id in (1000460003, 1000230005) and zoseg.start_date < NOW() and zoseg.end_date > NOW()
              and zoseg.geography_type_id = 1000320004 and zoseg.geography_id = pseg.geography_id
              and pseg.geography_type_id = 1000320004
              and pseg.start_date < NOW() and pseg.end_date > NOW()
              and pseg.entity_type_id in (1000460003, 1000230004) AND pseg.entity_uuid = ps.staff_id
              AND ps.staff_type_id = 1000230004
              group by ps.staff_id
            `
          const paravetFromZOQueryRowResult = await dbConnections.main.manager.query(paravetFromZOQuery)
          // const paravetFromZOQueryResult = paravetFromZOQueryRowResult.map((object) => object["staff_id"])
          const paravetFromZOQueryResult = []
          paravetFromZOQueryRowResult.forEach((object, index) => {
            if (object.staff_id !== null) {
              paravetFromZOQueryResult.push(object.staff_id)
            }
          })
          if (paravetFromZOQueryResult.length > 0) {
            staff_paravets = staff_paravets.concat(paravetFromZOQueryResult)
            staffs_for_customers = staffs_for_customers.concat(paravetFromZOQueryResult)
          }

          const ROFromZOQuery = `
              select ros.staff_id
              from ${mainSchemaAddition}entity_geography zoseg, ${mainSchemaAddition}entity_geography roseg, ${mainSchemaAddition}staff ros
              where zoseg.entity_uuid = '${entityId}'
              and zoseg.entity_type_id in (1000460003, 1000230005) and zoseg.start_date < NOW() and zoseg.end_date > NOW()
              and zoseg.geography_type_id = 1000320004 and zoseg.geography_id = roseg.geography_id
              and roseg.geography_type_id = 1000320004
              and roseg.start_date < NOW() and roseg.end_date > NOW()
              and roseg.entity_type_id in (1000460003, 1000230001) AND roseg.entity_uuid = ros.staff_id
              AND ros.staff_type_id = 1000230001
              group by ros.staff_id
            `
          const ROFromZOQueryRowResult = await dbConnections.main.manager.query(ROFromZOQuery)
          // const ROFromZOQueryResult = ROFromZOQueryRowResult.map((object) => object["staff_id"])
          const ROFromZOQueryResult = []
          ROFromZOQueryRowResult.forEach((object, index) => {
            if (object.staff_id !== null) {
              ROFromZOQueryResult.push(object.staff_id)
            }
          })

          if (ROFromZOQueryResult.length > 0) {
            staff_ros = staff_ros.concat(ROFromZOQueryResult)
          }

          const VetFromZOQuery = `
              select vets.staff_id
              from ${mainSchemaAddition}entity_geography zoseg, ${mainSchemaAddition}entity_geography vetseg, ${mainSchemaAddition}staff vets
              where zoseg.entity_uuid = '${entityId}'
              and zoseg.entity_type_id in (1000460003, 1000230005) and zoseg.start_date < NOW() and zoseg.end_date > NOW()
              and zoseg.geography_type_id = 1000320004 and zoseg.geography_id = vetseg.geography_id
              and vetseg.geography_type_id = 1000320004
              and vetseg.start_date < NOW() and vetseg.end_date > NOW()
              and vetseg.entity_type_id in (1000460003, 1000230003) AND vetseg.entity_uuid = vets.staff_id
              AND vets.staff_type_id = 1000230003
              group by vets.staff_id
            `
          const VetFromZOQueryRowResult = await dbConnections.main.manager.query(VetFromZOQuery)
          // const VetFromZOQueryResult = VetFromZOQueryRowResult.map((object) => object["staff_id"])
          const VetFromZOQueryResult = []
          VetFromZOQueryRowResult.forEach((object, index) => {
            if (object.staff_id !== null) {
              VetFromZOQueryResult.push(object.staff_id)
            }
          })

          if (VetFromZOQueryResult.length > 0) {
            staff_vets = staff_vets.concat(VetFromZOQueryResult)
          }

          staffs = staffs.concat(staff_paravets)
          staffs = staffs.concat(staff_ros)
          staffs = staffs.concat(staff_vets)
          staffs = staffs.concat(staff_zos)
        }
        break
      case 1000230003:
        {
          // veterinarian
          console.log('s s s gFDFT 1')
          staffs_for_customers.push(entityId)
          staff_vets.push(entityId)
          const ZOFromVetQuery = `
              select zos.staff_id
              from ${mainSchemaAddition}entity_geography vetseg, ${mainSchemaAddition}entity_geography zoseg, ${mainSchemaAddition}staff zos
              where vetseg.entity_type_id in (1000460003, 1000230003) and vetseg.entity_uuid = '${entityId}'
              and vetseg.geography_type_id = 1000320004 and vetseg.start_date < now()
              and vetseg.end_date > now() and vetseg.geography_id = zoseg.geography_id
              and zoseg.geography_type_id = 1000320004 and zoseg.entity_type_id in (1000460003, 1000230005)
              and zoseg.start_date < now() and zoseg.end_date > now()
              and zoseg.entity_uuid = zos.staff_id and zos.staff_type_id = 1000230005
              group by zos.staff_id          
            `
          console.log('ZOFromVetQuery = ', ZOFromVetQuery)
          const ZOFromVetQueryResult = await dbConnections.main.manager.query(ZOFromVetQuery)
          console.log('ZOFromVetQueryResult = ', ZOFromVetQueryResult)
          if (ZOFromVetQueryResult.length > 0) {
            staff_zos.push(ZOFromVetQueryResult[0].staff_id)
          }

          const ROFromVetQuery = `
              select ros.staff_id
              from ${mainSchemaAddition}entity_geography vetseg, ${mainSchemaAddition}entity_geography roseg, ${mainSchemaAddition}staff ros
              where vetseg.entity_type_id in (1000460003, 1000230003) and vetseg.entity_uuid = '${entityId}'
              and vetseg.geography_type_id = 1000320004 and vetseg.start_date < now()
              and vetseg.end_date > now() and vetseg.geography_id = roseg.geography_id
              and roseg.geography_type_id = 1000320004 and roseg.entity_type_id in (1000460003, 1000230001)
              and roseg.start_date < now() and roseg.end_date > now()
              and roseg.entity_uuid = ros.staff_id and ros.staff_type_id = 1000230001
              group by ros.staff_id          
            `
          console.log('ROFromVetQuery = ', ROFromVetQuery)
          const ROFromVetQueryResult = await dbConnections.main.manager.query(ROFromVetQuery)
          console.log('ROFromVetQueryResult = ', ROFromVetQueryResult)
          if (ROFromVetQueryResult.length > 0) {
            staff_ros.push(ROFromVetQueryResult[0].staff_id)
          }

          const paravetFromVetQuery = `
            select ps.staff_id
            from ${mainSchemaAddition}entity_geography vetseg, ${mainSchemaAddition}entity_geography pseg, ${mainSchemaAddition}staff ps
            where vetseg.entity_uuid = '${entityId}'
            and vetseg.entity_type_id in (1000460003, 1000230003) and vetseg.start_date < NOW() and vetseg.end_date > NOW()
            and vetseg.geography_type_id = 1000320004 and vetseg.geography_id = pseg.geography_id
            and pseg.geography_type_id = 1000320004
            and pseg.start_date < NOW() and pseg.end_date > NOW()
            and pseg.entity_type_id in (1000460003, 1000230004) AND pseg.entity_uuid = ps.staff_id
            AND ps.staff_type_id = 1000230004
            group by ps.staff_id
            `

          console.log('paravetFromVetQuery = ', paravetFromVetQuery)
          const paravetFromVetQueryRowResult = await dbConnections.main.manager.query(paravetFromVetQuery)
          console.log('paravetFromVetQueryRowResult = ', paravetFromVetQueryRowResult)
          // const paravetFromVetQueryResult = paravetFromVetQueryRowResult.map((object) => object["staff_id"])
          const paravetFromVetQueryResult = []
          paravetFromVetQueryRowResult.forEach((object, index) => {
            if (object.staff_id !== null) {
              paravetFromVetQueryResult.push(object.staff_id)
            }
          })

          if (paravetFromVetQueryResult.length > 0) {
            staffs_for_customers = staffs_for_customers.concat(paravetFromVetQueryResult)
            staff_paravets = staff_paravets.concat(paravetFromVetQueryResult)
          }
          staffs = staffs.concat(staff_paravets)
          staffs = staffs.concat(staff_ros)
          staffs = staffs.concat(staff_vets)
          staffs = staffs.concat(staff_zos)
        }
        break
    }
  }

  staffs = staffs.concat(staff_backend)

  // console.log('kindsOfFiltersObject = ', kindsOfFiltersObject)
  if (kindsOfFiltersObject.customer_self) {
    final_customers = final_customers.concat(customer_self)
  }
  if (kindsOfFiltersObject.staff_for_customer || kindsOfFiltersObject.customer) {
    const staffsForCustomersAsConcatenatedString = convertArrayToCommaSeparatedConcatedString(staffs_for_customers)
    // console.log('staffsForCustomersAsConcatenatedString = ', staffsForCustomersAsConcatenatedString)
    const customerFromStaffQuery = `
        select cc.customer_id
        from ${mainSchemaAddition}entity_geography eg, ${mainSchemaAddition}customer_classification cc
        where eg.geography_type_id = 1000320004 and eg.entity_type_id in (1000460003, 1000230001, 1000230003, 1000230004, 1000230005)
        and eg.entity_uuid in (${staffsForCustomersAsConcatenatedString})
        and eg.geography_id = cc.value_reference_id and cc.classifier_id = 2000000055
        and cc.active = 1000100001
      `
    // console.log('customerFromStaffQuery = ', customerFromStaffQuery)
    const customerIdsAsRows = await dbConnections.main.manager.query(customerFromStaffQuery)
    // console.log('customerIdsAsRows = ', customerIdsAsRows)
    // const customerIds = customerIdsAsRows.map((object) => object["customer_id"])
    const customerIds = []
    customerIdsAsRows.forEach((object, index) => {
      if (object.customer_id !== null) {
        customerIds.push(object.customer_id)
      }
    })

    // console.log('customerIds = ', customerIds)
    final_customers = final_customers.concat(customerIds)
  }
  if (kindsOfFiltersObject.animal) {
    // get animals of final customers
    const animalsForCustomersQuery = `
      select c2a.entity_2_entity_uuid
      from ${mainSchemaAddition}entity_relationship c2a
      where c2a.entity_relationship_type_id = 1000210004
      and c2a.entity_1_entity_uuid in ('0a55ffb3-925f-11ed-ae32-a3c399e82ed0')
      `
    const animalIdArrayAsRows = await dbConnections.main.repos.entity_relationship
      .createQueryBuilder()
      .select(['entity_2_entity_uuid'])
      .where(' entity_relationship_type_id = 1000210004 and entity_1_entity_uuid in (:...primaryKeysForCustomers)', {
        primaryKeysForCustomers: final_customers
      })
      .getRawMany()
    // console.log('animalIdArrayAsRows = ', animalIdArrayAsRows)
    // const animalIdArray = animalIdArrayAsRows.map((object) => object["entity_2_entity_uuid"])
    const animalIdArray = []
    animalIdArrayAsRows.forEach((object, index) => {
      if (object.entity_2_entity_uuid !== null) {
        animalIdArray.push(object.entity_2_entity_uuid)
      }
    })

    // console.log('animalIdArray = ', animalIdArray)
    animals = animals.concat(animalIdArray)
  }
  if (kindsOfFiltersObject.task) {
    // get animals of final customers
    const tasksForAnimalsAndFarmersQuery = `
      select cc.care_calendar_id
      from ${mainSchemaAddition}care_calendar cc
      where (
        cc.entity_type_id = '' and cc.entity_id in ('animal id 1', 'animal id 2', etc.)
        or 
        cc.entity_type_id = '' and cc.entity_id in ('farmer 1 id', 'farmer 2 id', etc.)
      )
      `
    const taskIdArrayAsRows = await dbConnections.main.repos.care_calendar
      .createQueryBuilder()
      .select(['care_calendar_id'])
      // .where(
      //   new Brackets((qb) => {
      //     qb.where('entity_type_id = 1000460002 and entity_uuid in (:...primaryKeysForAnimals)', {
      //       primaryKeysForAnimals: animals,
      //     }).orWhere('entity_type_id = 1000460001 and entity_uuid in (:...primaryKeysForCustomers)', {
      //       primaryKeysForCustomers: final_customers,
      //     })
      //   })
      // )
      .where(' (entity_type_id = 1000460002 and entity_uuid in (:...primaryKeysForAnimals)) or (entity_type_id = 1000460001 and entity_uuid in (:...primaryKeysForCustomers)) ', {
        primaryKeysForAnimals: animals,
        primaryKeysForCustomers: final_customers
      })
      .getRawMany()
    // console.log('taskIdArrayAsRows = ', taskIdArrayAsRows)
    // const taskIdArray = taskIdArrayAsRows.map((object) => object["care_calendar_id"])
    const taskIdArray = []
    taskIdArrayAsRows.forEach((object, index) => {
      if (object.care_calendar_id !== null) {
        taskIdArray.push(object.care_calendar_id)
      }
    })

    const taskIdForDocumentsArrayAsRows = await dbConnections.main.repos.care_calendar
      .createQueryBuilder()
      .select(['care_calendar_id'])
      // .where(
      //   new Brackets((qb) => {
      //     qb.where('entity_type_id = 1000460002 and entity_uuid in (:...primaryKeysForAnimals)', {
      //       primaryKeysForAnimals: animals,
      //     }).orWhere('entity_type_id = 1000460001 and entity_uuid in (:...primaryKeysForCustomers)', {
      //       primaryKeysForCustomers: final_customers,
      //     })
      //   })
      // )
      .where(' (entity_type_id = 1000460002 and calendar_activity_status not in (1000300050, 1000300051, 1000300052) and entity_uuid in (:...primaryKeysForAnimals)) or (entity_type_id = 1000460001 and entity_uuid in (:...primaryKeysForCustomers)) ', {
        primaryKeysForAnimals: animals,
        primaryKeysForCustomers: final_customers
      })
      .getRawMany()
    // console.log('taskIdArrayAsRows = ', taskIdArrayAsRows)
    // const taskIdArray = taskIdArrayAsRows.map((object) => object["care_calendar_id"])
    const taskIdForDocumentsArray = []
    taskIdForDocumentsArrayAsRows.forEach((object, index) => {
      if (object.care_calendar_id !== null) {
        taskIdForDocumentsArray.push(object.care_calendar_id)
      }
    })

    // console.log('taskIdArray = ', taskIdArray)
    tasks = tasks.concat(taskIdArray)
    tasks_for_documents = tasks_for_documents.concat(taskIdForDocumentsArray)
  }
  // 1.0 additional tasks for whom the paravet has done some work which do not fall into the villages assigned
  // 2.0 additional animals for whom the paravet has done some work which do not fall into the villages assigned
  // 3.0 additional customers for whom the paravet has done some work which do not fall into the villages assigned + customers based on 2.0
  // 4.0 additional paravets to whom these tasks were assigned by default
  // 5.0 additional villages to which these paravets belong to
  // 6.0 additional paravets to whom tasks meant for you were assigned

  // if we need correct animals or staff or customers, we will need to do these checks
  if (kindsOfFiltersObject.task || kindsOfFiltersObject.animal || kindsOfFiltersObject.staff_for_customer) {
    // 1.0 additional tasks for whom the paravet has done some work which do not fall into the villages assigned
    const staffsForCustomersAsConcatenatedString = convertArrayToCommaSeparatedConcatedString(staffs_for_customers)
    const additionalTasksDoneByStaffQuery = `
      select cc.care_calendar_id
      from ${mainSchemaAddition}staff_activity sa
      inner join ${mainSchemaAddition}care_calendar cc on sa.care_calendar = cc.care_calendar_id
        and cc.entity_type_id = 1000460002
      inner join ${mainSchemaAddition}entity_relationship er on er.entity_relationship_type_id = 1000210004
        and er.entity_2_entity_uuid = cc.entity_uuid
      inner join ${mainSchemaAddition}customer_classification cstc on cstc.classifier_id = 2000000055
        and cstc.customer_id = er.entity_1_entity_uuid
      inner join ${mainSchemaAddition}entity_geography eg on eg.entity_type_id = 1000230004
        and eg.geography_type_id = 1000320004 and eg.geography_id = cstc.value_reference_id
        and eg.entity_uuid not in (${staffsForCustomersAsConcatenatedString})
      where staff_id in (${staffsForCustomersAsConcatenatedString})
      
      union
      
      select cc.care_calendar_id
      from ${mainSchemaAddition}staff_activity sa
      inner join ${mainSchemaAddition}care_calendar cc on sa.care_calendar = cc.care_calendar_id
        and cc.entity_type_id = 1000460001
      inner join ${mainSchemaAddition}customer_classification cstc on cstc.classifier_id = 2000000055
        and cstc.customer_id = cc.entity_uuid
      inner join ${mainSchemaAddition}entity_geography eg on eg.entity_type_id = 1000230004
        and eg.geography_type_id = 1000320004 and eg.geography_id = cstc.value_reference_id
        and eg.entity_uuid not in (${staffsForCustomersAsConcatenatedString})
      where staff_id in (${staffsForCustomersAsConcatenatedString})
    `

    // console.log('additionalTasksDoneByStaffQuery = ', additionalTasksDoneByStaffQuery)
    const additionalTaskIdsAsRows = await dbConnections.main.manager.query(additionalTasksDoneByStaffQuery)
    // console.log('additionalTaskIdsAsRows = ', additionalTaskIdsAsRows)
    // const additionalTaskIds = additionalTaskIdsAsRows.map((object) => object["care_calendar_id"])
    const additionalTaskIds = []
    additionalTaskIdsAsRows.forEach((object, index) => {
      if (object.care_calendar_id !== null) {
        additionalTaskIds.push(object.care_calendar_id)
      }
    })

    const additionalTasksDoneByStaffForDocumentsQuery = `
      select cc.care_calendar_id
      from ${mainSchemaAddition}staff_activity sa
      inner join ${mainSchemaAddition}care_calendar cc on sa.care_calendar = cc.care_calendar_id
        and cc.entity_type_id = 1000460002
      inner join ${mainSchemaAddition}entity_relationship er on er.entity_relationship_type_id = 1000210004
        and er.entity_2_entity_uuid = cc.entity_uuid
      inner join ${mainSchemaAddition}customer_classification cstc on cstc.classifier_id = 2000000055
        and cstc.customer_id = er.entity_1_entity_uuid
      inner join ${mainSchemaAddition}entity_geography eg on eg.entity_type_id = 1000230004
        and eg.geography_type_id = 1000320004 and eg.geography_id = cstc.value_reference_id
        and eg.entity_uuid not in (${staffsForCustomersAsConcatenatedString})
      where calendar_activity_status not in (1000300050, 1000300051, 1000300052)
        and staff_id in (${staffsForCustomersAsConcatenatedString})
      
      union
      
      select cc.care_calendar_id
      from ${mainSchemaAddition}staff_activity sa
      inner join ${mainSchemaAddition}care_calendar cc on sa.care_calendar = cc.care_calendar_id
        and cc.entity_type_id = 1000460001
      inner join ${mainSchemaAddition}customer_classification cstc on cstc.classifier_id = 2000000055
        and cstc.customer_id = cc.entity_uuid
      inner join ${mainSchemaAddition}entity_geography eg on eg.entity_type_id = 1000230004
        and eg.geography_type_id = 1000320004 and eg.geography_id = cstc.value_reference_id
        and eg.entity_uuid not in (${staffsForCustomersAsConcatenatedString})
      where calendar_activity_status not in (1000300050, 1000300051, 1000300052)
        and staff_id in (${staffsForCustomersAsConcatenatedString})
    `

    // console.log('additionalTasksDoneByStaffQuery = ', additionalTasksDoneByStaffQuery)
    const additionalTaskIdsForDocumentAsRows = await dbConnections.main.manager.query(additionalTasksDoneByStaffForDocumentsQuery)
    // console.log('additionalTaskIdsAsRows = ', additionalTaskIdsAsRows)
    // const additionalTaskIds = additionalTaskIdsAsRows.map((object) => object["care_calendar_id"])
    const additionalTaskIdsForDocuments = []
    additionalTaskIdsForDocumentAsRows.forEach((object, index) => {
      if (object.care_calendar_id !== null) {
        additionalTaskIdsForDocuments.push(object.care_calendar_id)
      }
    })

    // console.log('additionalTaskIds = ', additionalTaskIds)
    tasks = tasks.concat(additionalTaskIds)
    tasks_for_documents = tasks_for_documents.concat(additionalTaskIdsForDocuments)

    // 2.0 additional animals for whom the paravet has done some work which do not fall into the villages assigned
    const additionalAnimalsBasedOnAdditionalTasksQuery = `
      select er.entity_2_entity_uuid as animal_id,
      cstc.customer_id
      from ${mainSchemaAddition}staff_activity sa
      inner join ${mainSchemaAddition}care_calendar cc on sa.care_calendar = cc.care_calendar_id and cc.entity_type_id = 1000460002
      inner join ${mainSchemaAddition}entity_relationship er on er.entity_relationship_type_id = 1000210004 and er.entity_2_entity_uuid = cc.entity_uuid
      inner join ${mainSchemaAddition}customer_classification cstc on cstc.classifier_id = 2000000055 and cstc.customer_id = er.entity_1_entity_uuid
      inner join ${mainSchemaAddition}entity_geography eg on eg.entity_type_id = 1000230004 and eg.geography_type_id = 1000320004
        and eg.geography_id = cstc.value_reference_id and eg.entity_uuid not in (${staffsForCustomersAsConcatenatedString})
      where staff_id in (${staffsForCustomersAsConcatenatedString})
    `

    // console.log('additionalAnimalsBasedOnAdditionalTasksQuery = ', additionalAnimalsBasedOnAdditionalTasksQuery)
    const additionalAnimalIdsAsRows = await dbConnections.main.manager.query(additionalAnimalsBasedOnAdditionalTasksQuery)
    // console.log('additionalAnimalIdsAsRows = ', additionalAnimalIdsAsRows)
    // const additionalAnimalIds = additionalAnimalIdsAsRows.map((object) => object["animal_id"])
    const additionalAnimalIds = []
    additionalAnimalIdsAsRows.forEach((object, index) => {
      if (object.animal_id !== null) {
        additionalAnimalIds.push(object.animal_id)
      }
      if(object.customer_id) {
        final_customers.push(object.customer_id)
      }
    })

    // console.log('additionalAnimalIds = ', additionalAnimalIds)
    animals = animals.concat(additionalAnimalIds)

    // 3.0 additional customers for whom the paravet has done some work which do not fall into the villages assigned + customers based on 2.0
    const additionalCustomersBasedOnAdditionalTasksQuery = `
      select cstc.customer_id
      from ${mainSchemaAddition}staff_activity sa
      inner join ${mainSchemaAddition}care_calendar cc on sa.care_calendar = cc.care_calendar_id and cc.entity_type_id = 1000460001
      inner join ${mainSchemaAddition}customer_classification cstc on cstc.classifier_id = 2000000055 and cstc.customer_id = cc.entity_uuid
      inner join ${mainSchemaAddition}entity_geography eg on eg.entity_type_id = 1000230004 and eg.geography_type_id = 1000320004
        and eg.geography_id = cstc.value_reference_id and eg.entity_uuid not in (${staffsForCustomersAsConcatenatedString})
      where staff_id in (${staffsForCustomersAsConcatenatedString})
    `

    // console.log('additionalCustomersBasedOnAdditionalTasksQuery = ', additionalCustomersBasedOnAdditionalTasksQuery)
    const additionalCustomerIdsAsRows = await dbConnections.main.manager.query(additionalCustomersBasedOnAdditionalTasksQuery)
    // console.log('additionalCustomerIdsAsRows = ', additionalCustomerIdsAsRows)
    // const additionalCustomerIds = additionalCustomerIdsAsRows.map((object) => object["customer_id"])
    const additionalCustomerIds = []
    additionalCustomerIdsAsRows.forEach((object, index) => {
      if (object.customer_id !== null) {
        additionalCustomerIds.push(object.customer_id)
      }
    })
    // console.log('additionalCustomerIds = ', additionalCustomerIds)
    final_customers = final_customers.concat(additionalCustomerIds)

    // 4.0 additional paravets to whom these tasks were assigned by default
    const additionalParavetsBasedOnAdditionalTasks = `
      select eg.entity_uuid as paravet_id
      from ${mainSchemaAddition}staff_activity sa
      inner join ${mainSchemaAddition}care_calendar cc on sa.care_calendar = cc.care_calendar_id and cc.entity_type_id = 1000460002
      inner join ${mainSchemaAddition}entity_relationship er on er.entity_relationship_type_id = 1000210004 and er.entity_2_entity_uuid = cc.entity_uuid
      inner join ${mainSchemaAddition}customer_classification cstc on cstc.classifier_id = 2000000055 and cstc.customer_id = er.entity_1_entity_uuid
      inner join ${mainSchemaAddition}entity_geography eg on eg.entity_type_id = 1000230004 and eg.geography_type_id = 1000320004
        and eg.geography_id = cstc.value_reference_id and eg.entity_uuid not in (${staffsForCustomersAsConcatenatedString})
      where staff_id in (${staffsForCustomersAsConcatenatedString})

      union

      select eg.entity_uuid as paravet_id
      from ${mainSchemaAddition}staff_activity sa
      inner join ${mainSchemaAddition}care_calendar cc on sa.care_calendar = cc.care_calendar_id and cc.entity_type_id = 1000460001
      inner join ${mainSchemaAddition}customer_classification cstc on cstc.classifier_id = 2000000055 and cstc.customer_id = cc.entity_uuid
      inner join ${mainSchemaAddition}entity_geography eg on eg.entity_type_id = 1000230004 and eg.geography_type_id = 1000320004
        and eg.geography_id = cstc.value_reference_id and eg.entity_uuid not in (${staffsForCustomersAsConcatenatedString})
      where staff_id in (${staffsForCustomersAsConcatenatedString})
    `

    // console.log('additionalParavetsBasedOnAdditionalTasks = ', additionalParavetsBasedOnAdditionalTasks)
    const additionalParavetIdsAsRows = await dbConnections.main.manager.query(additionalParavetsBasedOnAdditionalTasks)
    // console.log('additionalParavetIdsAsRows = ', additionalParavetIdsAsRows)
    // const additionalParavetIds = additionalParavetIdsAsRows.map((object) => object["paravet_id"])
    const additionalParavetIds = []
    additionalParavetIdsAsRows.forEach((object, index) => {
      if (object.paravet_id !== null) {
        additionalParavetIds.push(object.paravet_id)
      }
    })
    // console.log('additionalParavetIds = ', additionalParavetIds)
    staffs_for_customers = staffs_for_customers.concat(additionalParavetIds)
  }

  if (kindsOfFiltersObject.task || kindsOfFiltersObject.animal || kindsOfFiltersObject.staff_for_customer) {
    const staffsForCustomersAsConcatenatedString = convertArrayToCommaSeparatedConcatedString(staffs_for_customers)
    // 6.0 additional paravets to whom tasks meant for you were assigned
    const additionalParavetsBasedOnAdditionalTasks = `
      select sa.staff_id
      from ${mainSchemaAddition}entity_geography eg
      inner join ${mainSchemaAddition}customer_classification cstc on cstc.classifier_id = 2000000055 and cstc.value_reference_id = eg.geography_id
      inner join ${mainSchemaAddition}entity_relationship er on er.entity_relationship_type_id = 1000210004 and er.entity_1_entity_uuid = cstc.customer_id
      inner join ${mainSchemaAddition}care_calendar cc on cc.entity_type_id = 1000460002 and cc.entity_uuid = er.entity_2_entity_uuid
      inner join ${mainSchemaAddition}staff_activity sa on sa.care_calendar = cc.care_calendar_id and sa.staff_id is not null and sa.staff_id not in ('488dea1e-c558-11ed-a51d-1ff5a20b683e')
      where eg.entity_type_id = 1000230004 and eg.geography_type_id = 1000320004
      and eg.entity_uuid in (${staffsForCustomersAsConcatenatedString})
      
      union
      
      select sa.staff_id
      from ${mainSchemaAddition}entity_geography eg
      inner join ${mainSchemaAddition}customer_classification cstc on cstc.classifier_id = 2000000055 and cstc.value_reference_id = eg.geography_id
      inner join ${mainSchemaAddition}care_calendar cc on cc.entity_type_id = 1000460001 and cc.entity_uuid = cstc.customer_id
      inner join ${mainSchemaAddition}staff_activity sa on sa.care_calendar = cc.care_calendar_id and sa.staff_id is not null and sa.staff_id not in ('488dea1e-c558-11ed-a51d-1ff5a20b683e')
      where eg.entity_type_id = 1000230004 and eg.geography_type_id = 1000320004
      and eg.entity_uuid in (${staffsForCustomersAsConcatenatedString}) 
    `

    // console.log('additionalParavetsBasedOnAdditionalTasks = ', additionalParavetsBasedOnAdditionalTasks)
    const additionalParavetIdsAsRows = await dbConnections.main.manager.query(additionalParavetsBasedOnAdditionalTasks)
    // console.log('additionalParavetIdsAsRows = ', additionalParavetIdsAsRows)
    // const additionalParavetIds = additionalParavetIdsAsRows.map((object) => object["staff_id"])
    const additionalParavetIds = []
    additionalParavetIdsAsRows.forEach((object, index) => {
      if (object.staff_id !== null) {
        additionalParavetIds.push(object.staff_id)
      }
    })

    // console.log('additionalParavetIds = ', additionalParavetIds)
    staffs_for_customers = staffs_for_customers.concat(additionalParavetIds)
  }
  if (kindsOfFiltersObject.animal_disease) {
    // get animals of final customers
    const diseasesForAnimalsQuery = `
      select ad.animal_disease_id
      from ${mainSchemaAddition}animal_disease ad
      where ad.animal_id in ('animal id 1, animal id 2, etc.')
      `
    const diseaseIdArrayAsRows = await dbConnections.main.repos.animal_disease
      .createQueryBuilder()
      .select(['animal_disease_id'])
      .where(' animal_id in (:...primaryKeysForAnimals)', {
        primaryKeysForAnimals: animals
      })
      .getRawMany()
    // console.log('diseaseIdArrayAsRows = ', diseaseIdArrayAsRows)
    // const diseaseIdArray = diseaseIdArrayAsRows.map((object) => object["animal_disease_id"])
    const diseaseIdArray = []
    diseaseIdArrayAsRows.forEach((object, index) => {
      if (object.animal_disease_id !== null) {
        diseaseIdArray.push(object.animal_disease_id)
      }
    })

    // console.log('diseaseIdArray = ', diseaseIdArray)
    diseases = diseases.concat(diseaseIdArray)
  }
  if (kindsOfFiltersObject.animal_classification) {
    // get animals of final customers
    const animalClassificationsForAnimalsQuery = `
      select ac.animal_classification_id
      from ${mainSchemaAddition}animal_classification ac
      where ac.animal_id in ('animal id 1, animal id 2, etc.')
      `
    const animalClassificationIdArrayAsRows = await dbConnections.main.repos.animal_classification
      .createQueryBuilder()
      .select(['animal_classification_id'])
      .where(' animal_id in (:...primaryKeysForAnimals)', {
        primaryKeysForAnimals: animals
      })
      .getRawMany()
    // console.log('diseaseIdArrayAsRows = ', diseaseIdArrayAsRows)
    // const diseaseIdArray = diseaseIdArrayAsRows.map((object) => object["animal_disease_id"])
    const animalClassificationIdArray = []
    animalClassificationIdArrayAsRows.forEach((object, index) => {
      if (object.animal_classification_id !== null) {
        animalClassificationIdArray.push(object.animal_classification_id)
      }
    })

    // console.log('diseaseIdArray = ', diseaseIdArray)
    animal_classifications = animal_classifications.concat(animalClassificationIdArray)
  }
  staffs = staffs.concat(staffs_for_customers)
  if (kindsOfFiltersObject.geo) {
    const villageIdArrayAsRows = await dbConnections.main.repos.entity_geography
      .createQueryBuilder('eg')
      .select(['geography_id'])
      .where(
        ' eg.entity_type_id in (1000460003, 1000230004, 1000230001) \
                and eg.geography_type_id = 1000320004 \
                and eg.entity_uuid in (:...primaryKeysForStaff)',
        {
          primaryKeysForStaff: entityTypeId === 1000460003 && staffTypeId === 1000230001 ? staff_ros : staffs
        }
      )
      .getRawMany()
    // console.log('villageIdArrayAsRows = ', villageIdArrayAsRows)
    // const villageIdArray = villageIdArrayAsRows.map((object) => object["geography_id"])
    const villageIdArray = []
    villageIdArrayAsRows.forEach((object, index) => {
      if (object.geography_id !== null) {
        villageIdArray.push(object.geography_id)
      }
    })

    // console.log('villageIdArray = ', villageIdArray)
    villages = villages.concat(villageIdArray)
  }

  const requisitionQuery = `
     select requisition_id from main.requisition
       where requisitioner_uuid in (${convertArrayToCommaSeparatedConcatedString(staffs)}) 
  `
  const requisitionQueryAsIdRows = await dbConnections.main.manager.query(requisitionQuery)
  const requisitionIdArray = []
  requisitionQueryAsIdRows.forEach((object, index) => {
    if (object.requisition_id !== null) {
      requisitionIdArray.push(object.requisition_id)
    }
  })



  //get state for which the user is logged in
  const stateStaff = await getUserState(entityId ,dbConnections,mainSchemaAddition)
  if (stateStaff){
    const roOfthatstate = await getUserOfThatState([1000230001,1000230005] ,dbConnections ,mainSchemaAddition ,stateStaff ,staffs);
    staffs = staffs.concat(roOfthatstate)
  }

  const partnerQuery = `
 select partner_id from  main.partner
`
const partnerQueryAsIdRows = await dbConnections.main.manager.query(partnerQuery)
const partnerIdArray = []
partnerQueryAsIdRows.forEach((object, index) => {
 if (object.partner_id !== null) {
   partnerIdArray.push(object.partner_id)
 }
})

  return {
    staff_id_array: staffs,
    customer_id_array: final_customers,
    village_id_array: villages,
    animal_id_array: animals,
    animal_classification_id_array: animal_classifications,
    care_calendar_id_array: tasks,
    care_calendar_id_for_documents_array: entityTypeId === 1000460003 && staffTypeId === 1000230001 ? dummyInitial : tasks_for_documents,
    animal_disease_id_array: diseases,
    requisition_id_array: requisitionIdArray,
    partner_id_array: partnerIdArray
  }
}
const getUserOfThatState = async (staffTypeIds ,dbConnections ,mainSchemaAddition , stateId, staffsExlusion) => {
  const exludeStaff = convertArrayToCommaSeparatedConcatedString(staffsExlusion)
  const inscludeStaffTypeIds = convertArrayToCommaSeparatedConcatedString(staffTypeIds)

   const staffQuery = `
      select  st.staff_id  from 
      ${mainSchemaAddition}entity_geography as geo 
      inner join ${mainSchemaAddition}ref_sdtv_view rsv on geo.geography_id = rsv.village_id
      inner join ${mainSchemaAddition}staff as st on st.staff_id= geo.entity_uuid
      where rsv.state_id = ${stateId} 
      and geo.entity_type_id in (${inscludeStaffTypeIds})
      and geo.entity_uuid not in (${exludeStaff})
      group by st.staff_id
    `;
    console.log('getUserStateQuery = ', staffQuery)
    const staffs = await dbConnections.main.manager.query(staffQuery)
    if (staffs.length > 0) {
     return staffs.map((staff) => staff.staff_id)
    }
    return []
}
const getUserState = async (entityId ,dbConnections ,mainSchemaAddition) => {
  const stateQuery = `
  select rsv.state_id from ${mainSchemaAddition}entity_geography as geo 
  inner join main.ref_sdtv_view rsv on geo.geography_id = rsv.village_id
  group by rsv.state_id         
 `
 console.log('getUserStateQuery = ', stateQuery)
 const getState = await dbConnections.main.manager.query(stateQuery)
 if (getState.length > 0) {
  return getState[0].state_id
 }
 return false

}
class GetServerDelta {
  async create /* or is it update */(data, params) {
    try {
      console.log('s s s GSD 1')
      // console.log('sc GSD 1, params headers = ', params.headers)
      // console.log('sc GSD 2, data = ', data)
      const { user_type } = params.headers.token
      const { entity_type_id: entityTypeId, entity_uuid: entityId, user_device_id: userDeviceId } = await getUserInfoBasedOnHeaderInformation(params.headers)
      console.log('userDeviceId = ', userDeviceId)
      if (Object.keys(data).length === 0) {
        // get all the data
      }
      // const data.database_and_table_information
      // console.log('sc GSD 3, data = ', data)
      const syncReferenceTime = data.sync_reference_time
      console.log('sc GSD 4, syncReferenceTime = ', syncReferenceTime)
      console.log('sc GSD 5, entityTypeId = ', entityTypeId, ', entityId = ', entityId)
      const dbConnections = getDBConnections()
      const synchronizationDataRepo = dbConnections.config.repos.synchronization_data
      const serverDelta = []
      for (const databaseName in dbConnections) {
        if (databaseName !== 'main') {
          continue
        }
        const dbSpecificServerDelta = []
        let relevantClientTablesInDBForSync = getRelevantTablesForSyncInDB(databaseName, data) // tables to be synced are now obtained
        // get necessary filtering criteria
        if(user_type === 1000230001) {
          // remove care_calendar and care_calendar_classification table for sync for BDM User
          const tableNotToBeSynced = ['care_calendar','care_calendar_classification']
          relevantClientTablesInDBForSync = relevantClientTablesInDBForSync.filter((table)=> !tableNotToBeSynced.includes(table))
        }
        const filterDependencyResult = determineFilterDataNecessary(entityTypeId, relevantClientTablesInDBForSync)
        console.log('s GSD 11, filterDependencyResult = ', filterDependencyResult)
        const filterData = await getFilterDataForTables(entityTypeId, entityId, filterDependencyResult)
        console.log('s GSD 12, filterData = ', filterData)

        const replacementValues = {
          ':sync_reference_time': new Date(syncReferenceTime).toISOString(),
          village_id_array: filterData.village_id_array,
          animal_id_array: filterData.animal_id_array,
          staff_id_array: filterData.staff_id_array,
          customer_id_array: filterData.customer_id_array,
          animal_disease_id_array: filterData.animal_disease_id_array,
          care_calendar_id_array: filterData.care_calendar_id_array,
          care_calendar_id_for_documents_array: filterData.care_calendar_id_for_documents_array,
          ':...village_id_array': filterData.village_id_array,
          ':...animal_id_array': filterData.animal_id_array,
          ':...staff_id_array': filterData.staff_id_array,
          ':...customer_id_array': filterData.customer_id_array,
          ':...village_id_array_as_comma_separated_concatenated_strings': filterData.village_id_array.join(),
          ':...animal_id_array_as_comma_separated_concatenated_strings': convertArrayToCommaSeparatedConcatedString(filterData.animal_id_array),
          ':...animal_classification_id_array_as_comma_separated_concatenated_strings': convertArrayToCommaSeparatedConcatedString(filterData.animal_classification_id_array),
          ':...staff_id_array_as_comma_separated_concatenated_strings': convertArrayToCommaSeparatedConcatedString(filterData.staff_id_array),
          ':...customer_id_array_as_comma_separated_concatenated_strings': convertArrayToCommaSeparatedConcatedString(filterData.customer_id_array),
          ':...task_id_array_as_comma_separated_concatenated_strings': convertArrayToCommaSeparatedConcatedString(filterData.care_calendar_id_array),
          ':...task_id_array_for_documents_as_comma_separated_concatenated_strings': convertArrayToCommaSeparatedConcatedString(filterData.care_calendar_id_for_documents_array),
          requisition_id_array: filterData.requisition_id_array,
          partner_id_array: filterData.partner_id_array,
          entity_type_id_staff: 1000230004, // 1000460003,
          geography_type_id_village: 1000320004,
          entity_geography_id_array: filterData.staff_id_array.concat(filterData.partner_id_array)
        }
        for (const tableName of relevantClientTablesInDBForSync) {
          // console.log('tableName = ', tableName)
          const synchronizationDataResultsForTable = await synchronizationDataRepo.find({
            where: { user_device_id: userDeviceId, active: 1000100001, table_name: tableName }
          })
          // console.log('tableName 2 = ', tableName)
          let lastSyncTimeForTable = new Date('1970-01-01 01:01:01')
          if (synchronizationDataResultsForTable.length > 0) {
            lastSyncTimeForTable = synchronizationDataResultsForTable[0].last_sync_time
          }
          replacementValues[':last_sync_time'] = new Date(lastSyncTimeForTable).toISOString()
          const tableRepo = dbConnections[databaseName].repos[tableName]
          const tableEntity = dbConnections[databaseName].entities[tableName]
          // console.log('table sync attributes = ', tableEntity.syncAttributes)

          let serverDeltaForTable
          if (tableEntity.syncAttributes && tableEntity.syncAttributes.customQuery) {
            // console.log('tableEntity.syncAttributes.customQuery = ', tableEntity.syncAttributes.customQuery)
            const arrayReplacements = tableEntity.syncAttributes.customQuery.query.match(/:\.\.\.(\w+)/g)
            const attributeReplacements = tableEntity.syncAttributes.customQuery.query.match(/:(?!(\.\.\.))(\w+)/g)
            // console.log('arrayReplacements = ', arrayReplacements)
            // console.log('attributeReplacements = ', attributeReplacements)
            let finalQuery = tableEntity.syncAttributes.customQuery.query
            for (const arrayReplacement of arrayReplacements) {
              const regex = new RegExp(arrayReplacement, 'g')
              finalQuery = finalQuery.replace(regex, replacementValues[arrayReplacement])
            }
            for (const attributeReplacement of attributeReplacements) {
              const regex = new RegExp(attributeReplacement, 'g')
              finalQuery = finalQuery.replace(regex, replacementValues[attributeReplacement])
            }
            // console.log('finalQuery = ', finalQuery)
            serverDeltaForTable = await dbConnections[databaseName].manager.query(finalQuery)
          } else {
            let whereClause =
              'server_table.updated_at > :lastSyncTimeForTable \
                                and server_table.updated_at <= :syncReferenceTime'
            const whereClauseValues = { lastSyncTimeForTable, syncReferenceTime }
            // console.log('constructing where clause and where clause values')
            if (tableEntity.syncAttributes && tableEntity.syncAttributes.columnFilters) {
              for (const columnFilterIndex in tableEntity.syncAttributes.columnFilters) {
                const columnFilter = tableEntity.syncAttributes.columnFilters[columnFilterIndex]
                const valuesToUse = replacementValues[columnFilter.filter]
                const variableName = 'keyToSelect' + String(columnFilterIndex)
                if (valuesToUse) {
                  if (columnFilter.filter.endsWith('array')) {
                    if (Array.isArray(valuesToUse) && valuesToUse.length > 0) {
                      whereClause = whereClause + " and " + columnFilter.column + " in (:..." + variableName + ") "
                    }
                  } else {
                    whereClause = whereClause + ' and ' + columnFilter.column + ' = :' + variableName + ' '
                  }
                  whereClauseValues[variableName] = valuesToUse
                }
              }
            }
            serverDeltaForTable = await tableRepo.createQueryBuilder('server_table').where(whereClause, whereClauseValues).getMany()
          }
          // console.log('serverDeltaForTable = ', serverDeltaForTable)
          const processed1ServerDeltaForTable = postProcessRecords(tableEntity, serverDeltaForTable, { getServerDelta: 1 })
          // console.log('processed1ServerDeltaForTable = ', processed1ServerDeltaForTable)
          if (processed1ServerDeltaForTable.length > 0) {
            // console.log('serverDeltaForTable = ', serverDeltaForTable)
            dbSpecificServerDelta.push({
              table_name: tableName,
              server_delta_for_table: processed1ServerDeltaForTable
            })
          }
        }
        if (dbSpecificServerDelta.length > 0) {
          serverDelta.push({
            database_name: databaseName,
            server_delta_for_db: dbSpecificServerDelta
          })
        }
      }
      return { return_code: 0, data: serverDelta }
    } catch (error) {
      console.log(error)
      if (error.name === 'KrushalError') {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class SetLastSyncTime {
  async create /* or is it update */(data, params) {
    try {
      // console.log('param headers = ', params.headers)

      const { entity_type_id: entityTypeId, entity_uuid: entityId, user_device_id: userDeviceId } = await getUserInfoBasedOnHeaderInformation(params.headers)
      const returnValue = { return_code: 0 }
      // console.log('data = ', data)
      // console.log('data.database_and_table_information = ', data.database_and_table_information)
      const syncReferenceTime = data.sync_reference_time
      const databaseAndTableInformation = data.database_and_table_information
      const dbConnections = getDBConnections()
      const synchronizationDataRepo = dbConnections.config.repos.synchronization_data
      const synchronizationDataEntity = dbConnections.config.entities.synchronization_data
      for (const databaseInformation of databaseAndTableInformation) {
        const databaseName = databaseInformation.database_name
        const tablesToBeUpdated = databaseInformation.tables

        /* const rows = tablesToBeUpdated.map((name) => ({
          user_device_id: userDeviceId,
          table_name: name,
          last_sync_time: syncReferenceTime,
        }))
        console.log('rows = ', rows) */
        for (const tableToBeUpdated of tablesToBeUpdated) {
          // find if row exists -> device id, active, table name
          const matchingDeviceTableRow = await synchronizationDataRepo
            .createQueryBuilder()
            .select(['synchronization_data_id'])
            .where({
              user_device_id: userDeviceId,
              table_name: tableToBeUpdated,
              active: 1000100001
            })
            .getRawMany()
          if (matchingDeviceTableRow.length > 0) {
            // if so, update last sync time
            await synchronizationDataRepo
              .createQueryBuilder()
              .update('synchronization_data')
              .set({ last_sync_time: syncReferenceTime })
              .where('synchronization_data_id = :id', {
                id: matchingDeviceTableRow[0].synchronization_data_id
              })
              .execute()
          } else {
            // else insert last sync time
            await synchronizationDataRepo
              .createQueryBuilder()
              .insert()
              .into(synchronizationDataEntity)
              .values({
                last_sync_time: syncReferenceTime,
                table_name: tableToBeUpdated,
                user_device_id: userDeviceId
              })
              .execute()
          }
        }

        return returnValue
      }
    } catch (error) {
      console.log(error)
      if (error.name === 'KrushalError') {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class ClearLastSyncTime {
  async create /* or is it update */(data, params) {
    try {
      // console.log('param headers = ', params.headers)

      const { entity_type_id: entityTypeId, entity_uuid: entityId, user_device_id: userDeviceId } = await getUserInfoBasedOnHeaderInformation(params.headers)
      const returnValue = { return_code: 0 }
      // console.log('data = ', data)
      // console.log('data.database_and_table_information = ', data.database_and_table_information)
      const syncReferenceTime = data.sync_reference_time
      const databaseAndTableInformation = data.database_and_table_information
      const dbConnections = getDBConnections()
      const synchronizationDataRepo = dbConnections.config.repos.synchronization_data
      const synchronizationDataEntity = dbConnections.config.entities.synchronization_data

      await synchronizationDataRepo
        .createQueryBuilder()
        .update('synchronization_data')
        .set({ active: 1000100002 })
        .where('user_device_id = :id', {
          id: userDeviceId
        })
        .execute()
      return { return_code: 0 }
    } catch (error) {
      console.log(error)
      if (error.name === 'KrushalError') {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

module.exports = { ReferenceTime, SetClientData, GetServerDelta, SetLastSyncTime, ClearLastSyncTime }
