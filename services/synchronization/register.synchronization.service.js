const {
  ReferenceTime,
  SetClientData,
  GetServerDelta,
  SetLastSyncTime,
  ClearLastSyncTime
} = require('./synchronization.class')
const auth = require('../../middleware/auth')
const configureSynchronization = (app) => {
  // app.use(auth)
  app.use('/sync/get-reference-time', new ReferenceTime())
  app.use('/sync/set-client-delta', new SetClientData())
  app.use('/sync/get-server-delta', new GetServerDelta())
  app.use('/sync/set-last-sync-time', new SetLastSyncTime())
  app.use('/sync/clear-last-sync-time', new ClearLastSyncTime())
  // app.use('/v2/document', new Document())
  // app.use('/v2/document/:documentId', new Document())
}
module.exports = { configureSynchronization }
