const { LibCustomerService,getUpcomingFarmerTasks } = require("@krushal-it/back-end-lib");
const uuid = require("uuid");
const errors = require("@feathersjs/errors");
const { errorLog } = require('@krushal-it/mobile-or-server-lib')
const { KrushalError } = require('@krushal-it/common-core')
const {FARMER, PPU_FARMER} = require('../../utils/common/namespace/krushal.namespace')
class CustomerService {
  customerServiceHandler = new LibCustomerService();
  async get(id, params) {
    try {
      let result = await this.customerServiceHandler.get(id, params);
      return result;
    } catch (error) {
      console.error(error);
      return { result: false, message: error.message };
    }
  }
  async find(params) {
    try {
      const { user_type, user_id } = params.headers.token;
      if ((user_type == FARMER || user_type == PPU_FARMER) && uuid.validate(user_id)) {
        let result = await this.customerServiceHandler.get(user_id, params);
        return result;
      }
      return {};
    } catch (error) {
      console.log(error);
      return { result: false, message: error.message };
    }
  }

  /**
  * This function is used to fetch full / partial data of a customer
  */
  async create(data, params) {

    if (!data.customer_id || !uuid.validate(data.customer_id)) {
      throw new errors.BadRequest('customer_id is mandatory');
    }

    let response = await this.customerServiceHandler.getCustomerDetails(data, params);

    if (response) {
      return response;
    } else {
      throw new errors.NotFound('data not found')
    }

  }
}


class CustomerTermsServices {
  customerServiceHandler = new LibCustomerService();
  async create(data, params) {
    try{
      let {user_id ,user_type} = params.headers.token;
      if(user_type !== 1000220001)  throw new errors.BadRequest("only customer/farmer can accept T & C")
      let termsAndCond= await this.customerServiceHandler.acceptTermsAndCondition(user_id,data)
      return termsAndCond 
    }
    catch(err) {
      return err
    }
  }

}

class CustomerTaskService {
  async find(params) {
    try {
      let { user_id, user_type } = params.headers.token;
      return await getUpcomingFarmerTasks({user_id});
    } catch (error) {
      errorLog('error in CustomerTaskService', params, { message: error.message })
      throw new errors.GeneralError(error.message);
    }
  }

}


module.exports = { CustomerService, CustomerTermsServices, CustomerTaskService }
