const lodashObject = require("lodash");

const { getDBConnections, dbConnections, preProcessRecords, postProcessRecords, optionallyAddPrimaryKeyToRecords } = require('@krushal-it/ah-orm')
const { configurationJSON, KrushalError } = require('@krushal-it/common-core')
const { CustomerListLib, CustomerLib } = require('@krushal-it/back-end-lib')
const { loadClassificationData, saveClassificationData } = require('@krushal-it/back-end-lib')
const { adjustStartDatesOfEntityStatusByCategoryAndEntities, reportQueryClassificationJoinConfiguration, generateSelectJoinClauseForClassification, generateWhereClauseForReportInnerQuery: generateWhereClause, generateLimitStringForReportInnerQuery: generateLimitString, generateSortStringForReportInnerQuery: generateSortString, identifyMandatoryColumnsForReportQuery, extractFieldsNeedingFilterData, extractFieldsNeedingPageFilterData } = require('../../utils/query/query.helper');

class CustomerList {
  async create(data, params) {   
    try {
      const customerListLib = new CustomerListLib()
      return customerListLib.create(data, params)
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class Customer {
  async find(params) {
    return { return_code: -101, message: 'in find' }
  }

  async create /* or is it update */(data, params) {   
    try {
      const customerLib = new CustomerLib()
      return customerLib.create(data, params)
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }

  async get(id, params) {   
    try {
      const customerLib = new CustomerLib()
      return customerLib.get(id, params)
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }

  async patch /* or is it update */(id, data, params) {
    try {
      const customerLib = new CustomerLib()
      return customerLib.patch(id, data, params)
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }

  async update /* or is it update */(id, data, params) {
    // PUT
    try {
      return { return_code: -101, message: 'in update with id as ' + id }
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

const geographyRelatedJoin = [
  `
    coalesce(gcc_v.taluk, gcc_t.taluk) taluk, coalesce(gcc_v.taluk_id, gcc_t.taluk_id) taluk_id, gcc_v.village, gcc_v.village_id,
  `,
  `
    left join (
      select gcc.customer_id,
        coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') as taluk,
        coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') as village,
        rsv.village_id, rsv.taluk_id
      from main.customer_classification gcc, main.ref_sdtv_view_2 rsv
      where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
      and gcc.value_reference_id = rsv.village_id
    ) gcc_v on gcc_v.customer_id = c.customer_id
    left join (
      select gcc.customer_id,
        coalesce(t_rg.geography_name_l10n->>'en', t_rg.geography_name_l10n->>'ul') as taluk,
        t_rg.geography_id as taluk_id
      from main.customer_classification gcc
      inner join main.ref_geography t_rg on gcc.value_reference_id = t_rg.geography_id
        and t_rg.geography_type_id = 1000320003
      where gcc.classifier_id = 2000000054 and gcc.active = 1000100001
    ) gcc_t on gcc_t.customer_id = c.customer_id
  `
]

const bdmRelatedJoin = [
  `
    fbdm.bdm_uuid, fbdm.bdm, fbdm.bdm_mobile,
  `,
  `
    left join (
      select gcc.customer_id, sg.bdm, sg.bdm_mobile, sg.bdm_uuid
      from main.customer_classification gcc
      left join (
        select coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as bdm, s.mobile_number as bdm_mobile, sg.geography_id, s.staff_id bdm_uuid
        from main.staff s, (
          select eg.geography_id, (min(eg.entity_uuid::text))::uuid as staff_id
          from main.entity_geography eg
          where eg.active = 1000100001 and eg.geography_type_id = 1000320004
          and eg.entity_type_id in (1000230001, 1000230005)
          group by eg.geography_id
        ) sg
        where s.staff_id = sg.staff_id and s.active = 1000100001
      ) sg on sg.geography_id = gcc.value_reference_id
      where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
    ) fbdm on fbdm.customer_id = c.customer_id
  `
]

const paravetRelatedJoin = [
  `
    fpv.paravet_uuid, fpv.paravet, fpv.paravet_mobile,
  `,
  `
    left join (
      select gcc.customer_id, pg.paravet, pg.paravet_mobile, pg.paravet_uuid
      from main.customer_classification gcc
      left join (
        select coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as paravet, s.mobile_number as paravet_mobile, pg.geography_id, s.staff_id as paravet_uuid
        from main.staff s, (
          select eg.geography_id, (min(eg.entity_uuid::text))::uuid as staff_id
          from main.entity_geography eg
          where eg.active = 1000100001 and eg.geography_type_id = 1000320004
          and eg.entity_type_id in (1000230004)
          group by eg.geography_id
        ) pg
        where s.staff_id = pg.staff_id and s.active = 1000100001
      ) pg on pg.geography_id = gcc.value_reference_id
      where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
    ) fpv on fpv.customer_id = c.customer_id
  `
]

const farmerCountInVillageLeftJoin = [
  `
    fciv.farmer_count_in_village,
  `,
  `
    left join (
      select gcc.customer_id, fgc.farmer_count_in_village
      from main.customer_classification gcc,
      (
        select gcc.value_reference_id, count(gcc.customer_id) as farmer_count_in_village
        from main.customer_classification gcc
        where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
        group by gcc.value_reference_id 
      ) fgc
      where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
      and gcc.value_reference_id = fgc.value_reference_id
    ) fciv on fciv.customer_id = c.customer_id
  `
]

const farmerCountOfLocationNotRecordedInVillageLeftJoin = [
  `
    fcivnlr.farmer_count_in_village_with_no_location,
  `,
  `
    left join (
      select gcc.customer_id, fwnrlbg.farmer_count_in_village_with_no_location
      from main.customer_classification gcc, (
        select count(gcc.customer_id) farmer_count_in_village_with_no_location, gcc.value_reference_id 
        from main.customer_classification gcc
        where gcc.customer_id not in (
          select customer_id
          from main.customer_classification lcc
          where lcc.active = 1000100001 and lcc.classifier_id = 2000000004
        )
        and gcc.classifier_id = 2000000055 and gcc.active = 1000100001
        group by gcc.value_reference_id 
      ) fwnrlbg
      where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
      and gcc.value_reference_id = fwnrlbg.value_reference_id 
    ) fcivnlr on fcivnlr.customer_id = c.customer_id
  `
]

const noLocationRecordedInnerQuery = `
  and c.customer_id not in (
    select customer_id
    from main.customer_classification cc
    where cc.classifier_id = 2000000004
    and cc.active = 1000100001
  )
`

const numberOfActiveAnimalsRelatedColumnsQueryConfiguration = [
  `
    cac.active_animal_count,
  `,
  `
    left join (
      select f2a.entity_1_entity_uuid customer_id, count(f2a.entity_2_entity_uuid) active_animal_count
      from main.entity_relationship f2a
      inner join main.animal a on a.animal_id = f2a.entity_2_entity_uuid and a.active = 1000100001
      where f2a.active = 1000100001 and f2a.entity_relationship_type_id = 1000210004
      group by f2a.entity_1_entity_uuid
    ) cac on cac.customer_id = c.customer_id
  `
]

const bcoRelatedColumnsAueryConfiguration = [
  `
    bco_v.bco_primary_contact_person_name, bco_v.bco_name, bco_v.village_bco_primary_contact_person_mobile_number,
  `,
  `
    left join (
      select coalesce(bco.partner_name_l10n->>'en', bco.partner_name_l10n->>'ul') bco_name,
        coalesce(pcp_pc.value_l10n->>'en', pcp_pc.value_l10n->>'ul') bco_primary_contact_person_name,
        bco.mobile_number as village_bco_primary_contact_person_mobile_number, v_pc.value_reference_id as village_id
      from main.partner bco
      inner join main.partner_classification v_pc on v_pc.active = 1000100001 and v_pc.classifier_id = 2000000276
        and v_pc.partner_id = bco.partner_id
      left join main.partner_classification pcp_pc on pcp_pc.active = 1000100001 and pcp_pc.classifier_id = 2000000275
        and pcp_pc.partner_id = bco.partner_id
      where bco.partner_type_id = 1000850002 and bco.active = 1000100001
    ) bco_v on bco_v.village_id = gcc.village_id
  `
]

const createCustomerInnerQuery = ({includeGeographyInQuery,
  includeBDMInQuery, includeParavetInQuery, includeFarmerCountInVillageInQuery,
  includeFarmerCountOfLocationNotRecordedInVillageInQuery, includeActiveAnimalCountInQuery,
  includeBCORelatedColumnsInQuery,
  restrictedIds, searchWithinRestrictedIds,
  selectClauseForClassification, joinClauseForClassification,
  noLocationRecordedDefault, noLocationRecordedInnerQuery,
  customerTypeIdsAsCommaSeparatedString}) => {
  const innerQuery = `
    select c.customer_id,
      coalesce(c.customer_name_l10n->>'en', c.customer_name_l10n->>'ul') as customer_name,
      ${includeGeographyInQuery ? geographyRelatedJoin[0] : ''}
      ${includeBDMInQuery ? bdmRelatedJoin[0] : '' }
      ${includeBCORelatedColumnsInQuery ? bcoRelatedColumnsAueryConfiguration[0] : '' }
      ${includeParavetInQuery ? paravetRelatedJoin[0] : '' }
      ${includeFarmerCountInVillageInQuery ? farmerCountInVillageLeftJoin[0] : ''}
      ${includeFarmerCountOfLocationNotRecordedInVillageInQuery ? farmerCountOfLocationNotRecordedInVillageLeftJoin[0] : ''}
      ${includeActiveAnimalCountInQuery ? numberOfActiveAnimalsRelatedColumnsQueryConfiguration[0] : ''}
      ${(selectClauseForClassification !== undefined && selectClauseForClassification !== '') ? selectClauseForClassification + ', ': ''}
      c.mobile_number, c.active as customer_active_id,
      case when c.active = 1000100001 then 'Active' else 'Not Active' end as customer_active
    from main.customer c
    ${includeGeographyInQuery ? geographyRelatedJoin[1] : ''}
    ${includeBDMInQuery ? bdmRelatedJoin[1] : ''}
    ${includeBCORelatedColumnsInQuery ? bcoRelatedColumnsAueryConfiguration[1] : '' }
    ${includeParavetInQuery ? paravetRelatedJoin[1] : ''}
    ${includeFarmerCountInVillageInQuery ? farmerCountInVillageLeftJoin[1] : ''}
    ${includeFarmerCountOfLocationNotRecordedInVillageInQuery ? farmerCountOfLocationNotRecordedInVillageLeftJoin[1] : ''}
    ${includeActiveAnimalCountInQuery ? numberOfActiveAnimalsRelatedColumnsQueryConfiguration[1] : ''}
    ${joinClauseForClassification}
    where 1 = 1 and c.customer_type_id in (${customerTypeIdsAsCommaSeparatedString})
    ${restrictedIds ? searchWithinRestrictedIds : ''}
    ${noLocationRecordedDefault ? noLocationRecordedInnerQuery : ''}
  `
  return innerQuery
}

const defaultCustomerTypeIds = [1000220001]

class CustomerListV3 {
  async create (data, params) {
    try {
      const returnValue = { return_code: 0 }
      const mainDBConnection = dbConnections().main

      const limitString = generateLimitString(data)
      const sortString = generateSortString(data) 
      const whereClause = generateWhereClause(data) 
      let searchWithinRestrictedIds = ``

      let customerTypeIds = [...defaultCustomerTypeIds]

      if (data.customerTypeIds && Array.isArray(data.customerTypeIds) && data.customerTypeIds.length > 0) {
        customerTypeIds = data.customerTypeIds
      }

      const customerTypeIdsAsCommaSeparatedString = customerTypeIds.join(',')

      const excludeBDM = (data.excludeBDM === true) ? true : false
      const restrictToLocationNotRecorded = (data.restrictToLocationNotRecorded === true) ? true : false

      // identify all columns needed
      const selectedColumns = data.selectedColumns
      // identify mandatory columns needed
      const mandatoryColumnsForSearch = identifyMandatoryColumnsForReportQuery(data.filters, data.sorting, data.globalSearch, data.searchConfiguration)

      let selectClauseForClassification
      let joinClauseForClassification
      [selectClauseForClassification, joinClauseForClassification] = generateSelectJoinClauseForClassification(reportQueryClassificationJoinConfiguration, 'CUSTOMER', undefined, mandatoryColumnsForSearch)

      let includeBDMInQuery = false
      let includeParavetInQuery = false
      let includeGeographyInQuery = false
      let includeFarmerCountInVillageInQuery = false
      let includeFarmerCountOfLocationNotRecordedInVillageInQuery = false
      let includeActiveAnimalCountInQuery = false
      let includeBCORelatedColumnsInQuery = false
      let noLocationRecordedDefault = restrictToLocationNotRecorded
      
      let restrictedIds = false

      if ((mandatoryColumnsForSearch.includes('bdm') || mandatoryColumnsForSearch.includes('bdm_uuid')) && !excludeBDM) {
        includeBDMInQuery = true
      }
      if (mandatoryColumnsForSearch.includes('paravet') || mandatoryColumnsForSearch.includes('paravet_uuid')) {
        includeParavetInQuery = true
      }
      if (mandatoryColumnsForSearch.includes('village_bco_name') || mandatoryColumnsForSearch.includes('village_bco_primary_contact_person_name') || mandatoryColumnsForSearch.includes('village_bco_primary_contact_person_mobile_number')) {
        includeBCORelatedColumnsInQuery = true
      }
      if (mandatoryColumnsForSearch.includes('village') || mandatoryColumnsForSearch.includes('taluk') || includeBDMInQuery || includeParavetInQuery || includeBCORelatedColumnsInQuery) {
        includeGeographyInQuery = true
      }
      if (mandatoryColumnsForSearch.includes('farmer_count_in_village')) {
        includeFarmerCountInVillageInQuery = true
      }
      if (mandatoryColumnsForSearch.includes('farmer_count_in_village_with_no_location')) {
        includeFarmerCountOfLocationNotRecordedInVillageInQuery = true
      }
      if (mandatoryColumnsForSearch.includes('active_animal_count')) {
        includeActiveAnimalCountInQuery = true
      }

      let includeWhereClauseInDetailedColumnsQuery = false

      let innerQuery = createCustomerInnerQuery({includeGeographyInQuery,
        includeBDMInQuery, includeParavetInQuery, includeFarmerCountInVillageInQuery,
        includeFarmerCountOfLocationNotRecordedInVillageInQuery, includeActiveAnimalCountInQuery,
        includeBCORelatedColumnsInQuery,
        restrictedIds, searchWithinRestrictedIds,
        selectClauseForClassification, joinClauseForClassification,
        noLocationRecordedDefault, noLocationRecordedInnerQuery,
        customerTypeIdsAsCommaSeparatedString})

      const countQuery = `
        select count(*) as count from (
          ${innerQuery}
        ) outerTable
        ${whereClause}
      `
      const reportResultCount = await mainDBConnection.manager.query(countQuery)
      const count = parseInt(reportResultCount[0].count)

      if (count === 0) {
        includeWhereClauseInDetailedColumnsQuery = true
      }
      
      let query = `
          select * 
          from (
            ${innerQuery}
          ) outerTable
          ${whereClause}
          ${sortString}
          ${limitString}
        `

      const reportResultUnprocessed = await mainDBConnection.manager.query(query)
      const reportResult = postProcessRecords(undefined, reportResultUnprocessed, {})

      const primaryKeyToRowMap = {}
      for (const reportRow of reportResult) {
        primaryKeyToRowMap[reportRow['customer_id']] = reportRow
      }
      const primaryKeysToBeQueried = Object.keys(primaryKeyToRowMap)

      let inPrimaryKeysIdString = ""
      for (const primaryKey of primaryKeysToBeQueried) {
        if (inPrimaryKeysIdString !== "") {
          inPrimaryKeysIdString = inPrimaryKeysIdString + ", "
        }
        inPrimaryKeysIdString = inPrimaryKeysIdString + "'" + primaryKey + "'"
      }
      searchWithinRestrictedIds = (inPrimaryKeysIdString !== '') ? ` and c.customer_id in (${inPrimaryKeysIdString})` : ''

      const filtersFieldToReturnKeyMap = extractFieldsNeedingFilterData(data.searchConfiguration, data.selectedColumns)
      const fieldsNeedingFilterData = Object.keys(filtersFieldToReturnKeyMap)

      if (fieldsNeedingFilterData.includes('village') || fieldsNeedingFilterData.includes('village_name')) {
        includeGeographyInQuery = true
      }

      // let includeBDMInQuery = false
      // let includeParavetInQuery = false
      // let includeGeographyInQuery = false
      // let includeFarmerCountInVillageInQuery = false
      // let includeFarmerCountOfLocationNotRecordedInVillageInQuery = false
      // let noLocationRecordedDefault = restrictToLocationNotRecorded

      const mandatoryColumnsPlusColumnsNeededForFilteringWithoutUniq = [...mandatoryColumnsForSearch, ...fieldsNeedingFilterData]
      const mandatoryColumnsPlusColumnsNeededForFiltering = lodashObject.uniq(mandatoryColumnsPlusColumnsNeededForFilteringWithoutUniq)
      const returnValue0 = generateSelectJoinClauseForClassification(reportQueryClassificationJoinConfiguration, 'CUSTOMER', undefined, mandatoryColumnsPlusColumnsNeededForFiltering)
      selectClauseForClassification = returnValue0[0]
      joinClauseForClassification = returnValue0[1]

      innerQuery = createCustomerInnerQuery({includeGeographyInQuery,
        includeBDMInQuery, includeParavetInQuery, includeFarmerCountInVillageInQuery,
        includeFarmerCountOfLocationNotRecordedInVillageInQuery, includeActiveAnimalCountInQuery,
        includeBCORelatedColumnsInQuery,
        restrictedIds, searchWithinRestrictedIds,
        selectClauseForClassification, joinClauseForClassification,
        noLocationRecordedDefault, noLocationRecordedInnerQuery,
        customerTypeIdsAsCommaSeparatedString})

      if (fieldsNeedingFilterData.includes('village') || fieldsNeedingFilterData.includes('village_name')) {
        const villageNameFilterKey = fieldsNeedingFilterData.includes('village_name') ? 'village_name_filter_values' : undefined
        const villageFilterKey = fieldsNeedingFilterData.includes('village') ? 'village_filter_values' : undefined

        const distinctVillageQuery = `
          select distinct village_id, village value, village text from (
            ${innerQuery}
          ) outerTable
          ${whereClause}
        `
        const distinctVillageResultUnprocessed = await mainDBConnection.manager.query(distinctVillageQuery)
        const distinctVillageResult = postProcessRecords(undefined, distinctVillageResultUnprocessed, {})

        if (fieldsNeedingFilterData.includes('village_name')) {
          returnValue[filtersFieldToReturnKeyMap['village_name']] = distinctVillageResult
        }
        if (fieldsNeedingFilterData.includes('village')) {
          returnValue[filtersFieldToReturnKeyMap['village']] = distinctVillageResult
        }
      }
      
      includeBDMInQuery = false
      includeParavetInQuery = false
      includeGeographyInQuery = false
      includeFarmerCountInVillageInQuery = false
      includeFarmerCountOfLocationNotRecordedInVillageInQuery = false
      includeActiveAnimalCountInQuery = false
      includeBCORelatedColumnsInQuery = false
      noLocationRecordedDefault = restrictToLocationNotRecorded
      restrictedIds = true

      if ((selectedColumns.includes('bdm') || selectedColumns.includes('bdm_uuid')) && !excludeBDM) {
        includeBDMInQuery = true
      }
      if (selectedColumns.includes('paravet') || selectedColumns.includes('paravet_uuid')) {
        includeParavetInQuery = true
      }
      if (selectedColumns.includes('village_bco_name') || selectedColumns.includes('village_bco_primary_contact_person_name') || selectedColumns.includes('village_bco_primary_contact_person_mobile_number')) {
        includeBCORelatedColumnsInQuery = true
      }
      if (selectedColumns.includes('village') || selectedColumns.includes('taluk') || includeBDMInQuery || includeParavetInQuery || includeBCORelatedColumnsInQuery) {
        includeGeographyInQuery = true
      }
      if (selectedColumns.includes('farmer_count_in_village')) {
        includeFarmerCountInVillageInQuery = true
      }
      if (selectedColumns.includes('farmer_count_in_village_with_no_location')) {
        includeFarmerCountOfLocationNotRecordedInVillageInQuery = true
      }
      if (selectedColumns.includes('active_animal_count')) {
        includeActiveAnimalCountInQuery = true
      }
      [selectClauseForClassification, joinClauseForClassification] = generateSelectJoinClauseForClassification(reportQueryClassificationJoinConfiguration, 'CUSTOMER', undefined, selectedColumns)

      innerQuery = createCustomerInnerQuery({includeGeographyInQuery,
        includeBDMInQuery, includeParavetInQuery, includeFarmerCountInVillageInQuery,
        includeFarmerCountOfLocationNotRecordedInVillageInQuery, includeActiveAnimalCountInQuery,
        includeBCORelatedColumnsInQuery,
        restrictedIds, searchWithinRestrictedIds,
        selectClauseForClassification, joinClauseForClassification,
        noLocationRecordedDefault, noLocationRecordedInnerQuery,
        customerTypeIdsAsCommaSeparatedString})
      
      query = `
        select * from (
          ${innerQuery}
        ) outerTable
        ${includeWhereClauseInDetailedColumnsQuery ? whereClause : ''}
        ${sortString}
      `

      const reportResultWithSelectedColumnsUnprocessed = await mainDBConnection.manager.query(query)
      const reportResultWithSelectedColumns = postProcessRecords(undefined, reportResultWithSelectedColumnsUnprocessed, {})

      returnValue.count = count
      returnValue.report = reportResultWithSelectedColumns

      return returnValue
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class CustomerListFilter {
  async create (data, params) {
    try {
      const returnValue = { return_code: 0 }
      const tableFilterData = {}
      returnValue.tableFilters = tableFilterData
      const mainDBConnection = dbConnections().main

      // const limitString = generateLimitString(data)
      // const sortString = generateSortString(data) 
      // const whereClause = generateWhereClause(data) 

      // const excludeBDM = (data.excludeBDM === true) ? true : false
      // const restrictToLocationNotRecorded = (data.restrictToLocationNotRecorded === true) ? true : false

      // identify all columns needed
      const selectedColumns = data.selectedColumns
      // identify mandatory columns needed
      // const mandatoryColumnsForSearch = identifyMandatoryColumnsForReportQuery(data.filters, data.sorting, data.globalSearch, data.searchConfiguration)

      // let selectClauseForClassification
      // let joinClauseForClassification
      // [selectClauseForClassification, joinClauseForClassification] = generateSelectJoinClauseForClassification(reportQueryClassificationJoinConfiguration, 'CUSTOMER', undefined, mandatoryColumnsForSearch)

      // let includeBDMInQuery = false
      // let includeParavetInQuery = false
      // let includeGeographyInQuery = false
      // let includeFarmerCountInVillageInQuery = false
      // let includeFarmerCountOfLocationNotRecordedInVillageInQuery = false
      // let noLocationRecordedDefault = restrictToLocationNotRecorded
      // let restrictedIds = false

      // if ((mandatoryColumnsForSearch.includes('bdm') || mandatoryColumnsForSearch.includes('bdm_uuid')) && !excludeBDM) {
      //   includeBDMInQuery = true
      // }
      // if (mandatoryColumnsForSearch.includes('paravet') || mandatoryColumnsForSearch.includes('paravet_uuid')) {
      //   includeParavetInQuery = true
      // }
      // if (mandatoryColumnsForSearch.includes('village') || mandatoryColumnsForSearch.includes('taluk') || includeBDMInQuery || includeParavetInQuery) {
      //   includeGeographyInQuery = true
      // }
      // if (mandatoryColumnsForSearch.includes('farmer_count_in_village')) {
      //   includeFarmerCountInVillageInQuery = true
      // }
      // if (mandatoryColumnsForSearch.includes('farmer_count_in_village_with_no_location')) {
      //   includeFarmerCountOfLocationNotRecordedInVillageInQuery = true
      // }

      // let includeWhereClauseInDetailedColumnsQuery = false

      // let innerQuery = createCustomerInnerQuery({includeGeographyInQuery,
      //   includeBDMInQuery, includeParavetInQuery, includeFarmerCountInVillageInQuery,
      //   includeFarmerCountOfLocationNotRecordedInVillageInQuery,
      //   restrictedIds, searchWithinRestrictedIds,
      //   selectClauseForClassification, joinClauseForClassification,
      //   noLocationRecordedDefault, noLocationRecordedInnerQuery})

      // const countQuery = `
      //   select count(*) as count from (
      //     ${innerQuery}
      //   ) outerTable
      //   ${whereClause}
      // `
      // const reportResultCount = await mainDBConnection.manager.query(countQuery)
      // const count = parseInt(reportResultCount[0].count)

      // if (count === 0) {
      //   includeWhereClauseInDetailedColumnsQuery = true
      // }
      
      // let query = `
      //     select * 
      //     from (
      //       ${innerQuery}
      //     ) outerTable
      //     ${whereClause}
      //     ${sortString}
      //     ${limitString}
      //   `

      // const reportResultUnprocessed = await mainDBConnection.manager.query(query)
      // const reportResult = postProcessRecords(undefined, reportResultUnprocessed, {})

      // const primaryKeyToRowMap = {}
      // for (const reportRow of reportResult) {
      //   primaryKeyToRowMap[reportRow['customer_id']] = reportRow
      // }
      // const primaryKeysToBeQueried = Object.keys(primaryKeyToRowMap)

      // let inPrimaryKeysIdString = ""
      // for (const primaryKey of primaryKeysToBeQueried) {
      //   if (inPrimaryKeysIdString !== "") {
      //     inPrimaryKeysIdString = inPrimaryKeysIdString + ", "
      //   }
      //   inPrimaryKeysIdString = inPrimaryKeysIdString + "'" + primaryKey + "'"
      // }
      // searchWithinRestrictedIds = (inPrimaryKeysIdString !== '') ? ` and c.customer_id in (${inPrimaryKeysIdString})` : ''

      const pageFiltersFieldToReturnKeyMap = extractFieldsNeedingPageFilterData(
        data.searchConfiguration.columnConfiguration,
        Object.keys(data.searchConfiguration.columnConfiguration) !== undefined && Array.isArray(Object.keys(data.searchConfiguration.columnConfiguration))
          ? Object.keys(data.searchConfiguration.columnConfiguration)
          : []
      )
      const fieldsNeedingPageLoadFilterData = Object.keys(pageFiltersFieldToReturnKeyMap)

      if (fieldsNeedingPageLoadFilterData.includes('taluk') || fieldsNeedingPageLoadFilterData.includes('taluk_name')) {
        const talukAsFieldNeedingPageLoadFilterData = fieldsNeedingPageLoadFilterData.includes('taluk')
        const talukNameAsFieldNeedingPageLoadFilterData = fieldsNeedingPageLoadFilterData.includes('taluk_name')
        const distinctTalukQuery = `
          select distinct taluk_id, taluk_id as value, coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') as text
          from main.customer_classification cc
          inner join main.customer c on c.customer_id = cc.customer_id and c.customer_type_id in (1000220001)
          inner join main.ref_sdtv_view rsv on rsv.village_id = cc.value_reference_id
          where cc.active = 1000100001 and cc.classifier_id = 2000000055
        `
        const distinctTalukFilterValuesResultUnprocessed = await mainDBConnection.manager.query(distinctTalukQuery)
        const distinctTalukFilterValuesResult = postProcessRecords(undefined, distinctTalukFilterValuesResultUnprocessed, {})
        if (talukAsFieldNeedingPageLoadFilterData) {
          tableFilterData[pageFiltersFieldToReturnKeyMap['taluk']] = distinctTalukFilterValuesResult
        }
        if (talukNameAsFieldNeedingPageLoadFilterData) {
          tableFilterData[pageFiltersFieldToReturnKeyMap['taluk_name']] = distinctTalukFilterValuesResult
        }
      }

      if (fieldsNeedingPageLoadFilterData.includes('bdm')) {
        const staffQuery = `
          select s.staff_id as value, coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as text
          from main.staff s
          where s.active = 1000100001 and s.staff_type_id in (1000230001, 1000230005)
        `
        const staffFilterValuesResultUnprocessed = await mainDBConnection.manager.query(staffQuery)
        const staffFilterValuesResult = postProcessRecords(undefined, staffFilterValuesResultUnprocessed, {})
        tableFilterData[pageFiltersFieldToReturnKeyMap['bdm']] = staffFilterValuesResult
      }
      
      return returnValue
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class GetFarmerDetails {
  async get(id, params) {
    try {
      const returnValue = {return_code: 0}
      const mainDBConnections = dbConnections().main
      const customerQuery = `
        select c.customer_id,
          c.customer_type_id,
          c.customer_visual_id,
          c.customer_name_l10n as customer_name,
          c.mobile_number, coalesce(gcc_v.district_name, gcc_t.district_name) district_name,
          coalesce(gcc_v.taluk_name, gcc_t.taluk_name) taluk_name,
          coalesce(gcc_v.village_name, gcc_t.village_name) village_name,
          gcc_v.village_id, coalesce(gcc_v.taluk_id, gcc_t.taluk_id) taluk_id,
          coalesce(gcc_v.district_id, gcc_t.district_id) district_id,
          fbdm.bdm_uuid, fbdm.bdm, fbdm.bdm_mobile, fbdm.paravet_uuid, fbdm.paravet, fbdm.paravet_mobile,
          ct_d.document_id as customer_thumbnail_document_id,
          dfl_cc.value_json dairy_farm_location_position
        from main.customer c
        left join (
          select gcc.customer_id,
            coalesce(rsv.district_name_l10n->>'en', rsv.district_name_l10n->>'ul') as district_name,
            coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') as taluk_name,
            coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') as village_name,
            rsv.district_id, rsv.village_id, rsv.taluk_id
          from main.customer_classification gcc
          inner join main.ref_sdtv_view_2 rsv on gcc.value_reference_id = rsv.village_id
          where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
        ) gcc_v on gcc_v.customer_id = c.customer_id
        left join (
          select gcc.customer_id,
            coalesce(d_rg.geography_name_l10n->>'en', d_rg.geography_name_l10n->>'ul') as district_name,
            coalesce(t_rg.geography_name_l10n->>'en', t_rg.geography_name_l10n->>'ul') as taluk_name,
            NULL as village_name,
            d_rg.geography_id as district_id, NULL as village_id, t_rg.geography_id as taluk_id
          from main.customer_classification gcc
          inner join main.ref_geography t_rg on t_rg.geography_type_id = 1000320003
            and gcc.value_reference_id = t_rg.geography_id
          inner join main.entity_relationship t2d on t2d.active = 1000100001
            and t2d.entity_1_type_id = 1000460015 and t2d.entity_2_type_id = 1000460015
            and t2d.entity_relationship_type_id = 1000210030
            and t2d.entity_2_entity_id = t_rg.geography_id
          inner join main.ref_geography d_rg on d_rg.geography_type_id = 1000320002
            and t2d.entity_1_entity_id = d_rg.geography_id
          where gcc.classifier_id = 2000000054 and gcc.active = 1000100001
          and gcc.value_reference_id = t_rg.geography_id
        ) gcc_t on gcc_t.customer_id = c.customer_id
        left join (
          select gcc.customer_id, sg.bdm, sg.bdm_mobile, sg.bdm_uuid, pg.paravet, pg.paravet_mobile, pg.paravet_uuid
          from main.customer_classification gcc
          left join (
            select coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as bdm, s.mobile_number as bdm_mobile, sg.geography_id, s.staff_id bdm_uuid
            from main.staff s, (
              select eg.geography_id, (min(eg.entity_uuid::text))::uuid as staff_id
              from main.entity_geography eg
              where eg.active = 1000100001 and eg.geography_type_id = 1000320004
              and eg.entity_type_id in (1000230001, 1000230005)
              group by eg.geography_id
            ) sg
            where s.staff_id = sg.staff_id and s.active = 1000100001
          ) sg on sg.geography_id = gcc.value_reference_id
          left join (
            select coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as paravet, s.mobile_number as paravet_mobile, pg.geography_id, s.staff_id as paravet_uuid
            from main.staff s, (
              select eg.geography_id, (min(eg.entity_uuid::text))::uuid as staff_id
              from main.entity_geography eg
              where eg.active = 1000100001 and eg.geography_type_id = 1000320004
              and eg.entity_type_id in (1000230004)
              group by eg.geography_id
            ) pg
            where s.staff_id = pg.staff_id and s.active = 1000100001
          ) pg on pg.geography_id = gcc.value_reference_id
          where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
        ) fbdm on fbdm.customer_id = c.customer_id
        left join main.customer_classification dfl_cc on dfl_cc.active = 1000100001
          and dfl_cc.customer_id = c.customer_id and dfl_cc.classifier_id = 2000000004
        left join (
          select d.document_id, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id,
            coalesce(dt_rr.reference_name_l10n->>'en', dt_rr.reference_name_l10n->>'ul') as document_type,
            d.document_information,
            d.created_at document_upload_time
          from main.document d
          inner join main.ref_reference dt_rr on dt_rr.reference_id = d.document_type_id
          left join (
            select max(d.document_id::text) max_created_at_document_id, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
            from main.document d
            inner join (
              select max(d.created_at) max_created_at, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
              from main.document d
              where 1 = 1 and d.active = 1000100001
              group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id 
            ) mcad on d.entity_1_type_id = mcad.entity_1_type_id and d.entity_1_entity_uuid = mcad.entity_1_entity_uuid
              and d.document_type_id = mcad.document_type_id and d.created_at = mcad.max_created_at
            where 1 = 1  and d.active = 1000100001
            group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
          ) mcadid on d.document_id::text = mcadid.max_created_at_document_id
        ) ct_d on ct_d.entity_1_type_id = 1000220001 and ct_d.entity_1_entity_uuid = c.customer_id and ct_d.document_type_id = 1000260003
        where 1 = 1 /* and c.active = 1000100001 and c.customer_type_id in (1000220001) */
          and c.customer_id = '${id}'
      `
      const customerBasicDetailsUnProcessedResult = await mainDBConnections.manager.query(customerQuery)
      const customerBasicDetailsResult = postProcessRecords(undefined, customerBasicDetailsUnProcessedResult, {})
      returnValue.result = customerBasicDetailsResult[0]

      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async create /*or is it update*/(data, params) {
    try {
      const returnValue = {return_code: 0}
      const classificationData = await loadClassificationData('CUSTOMER_CLASSIFICATION', data.customerId, data.classifierArray)
      returnValue.data = classificationData
      return returnValue
    } catch (error) {
      console.log('oc UBFS c 10, error')
      console.log('oc UBFS c 10a, error = ', error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class SetFarmerDetails {
  async create /*or is it update*/(data, params) {
    try {
      const returnValue = {return_code: 0}
      const mainDBConnection = dbConnections().main
      const customerTableEntity = mainDBConnection.entities['customer']
      const {customer_type_id, customer_name_l10n, mobile_number, farmer_taluka_1, farmer_village_1, farmer_district, ...classificationData } = data.classificationData
      if (farmer_taluka_1 !== undefined) {
        classificationData['farmer_taluka_1'] = farmer_taluka_1
      }
      if (farmer_village_1 !== undefined) {
        classificationData['farmer_village_1'] = farmer_village_1
      }
      let customerId = data.customerId
      if (customerId === undefined) {
        // this is creating a new customer scenario
        if (customer_type_id !== 1000220009) {
          returnValue.return_code = -202
          returnValue.message = 'This API can create only SR customer'
          return returnValue
        }
        // check if customer already exists based on mobile number
        const numberOfActiveCustomersWithMobileNumber = await mainDBConnection.manager
          .createQueryBuilder()
          // .select(['customer_id'])
          .from(customerTableEntity)
          .where('active = :activeId and mobile_number = :mobileNumber', { activeId: 1000100001, mobileNumber: mobile_number })

        if (numberOfActiveCustomersWithMobileNumber.length > 0) {
          return { return_code: -203, message: "Another Customer in the system has the same mobile number" };
        }
      }
      
      const saveResult = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
        try {
          if (customerId === undefined) {
            // create farmer
            if ((farmer_taluka_1 === undefined || farmer_taluka_1 === null || (Array.isArray(farmer_taluka_1) && farmer_taluka_1.length === 0))
                && (farmer_village_1 === undefined || farmer_village_1 === null || (Array.isArray(farmer_village_1) && farmer_village_1.length === 0))) {
              throw new Error('Cannot create customer without taluk or village')
            }
            let farmerTalukArray = []
            let farmerVillageArray = []
            if (farmer_taluka_1 !== undefined) {
              farmerTalukArray = farmer_taluka_1
            }
            if (farmer_village_1 !== undefined) {
              farmerVillageArray = farmer_village_1
            }
            farmerTalukArray.push(-1)
            farmerVillageArray.push(-1)
            const taluksAsCommaSeparatedString = farmerTalukArray.join()
            const villagesAsCommaSeparatedString = farmerVillageArray.join()
            const getCustomerVisualIdQuery = `
              with t2s as (
                select t_rg.geography_id as taluk_id, t_rg.geography_short_code as taluk_code, d_rg.geography_short_code as district_code, s_rg.geography_short_code as state_code,
                  concat(s_rg.geography_short_code, d_rg.geography_short_code, t_rg.geography_short_code) as geo_based_prefix
                from main.ref_geography t_rg
                inner join main.entity_relationship d2t on d2t.active = 1000100001 and d2t.entity_relationship_type_id = 1000210030
                  and d2t.entity_2_entity_id = t_rg.geography_id
                inner join main.ref_geography d_rg on d_rg.active = 1000100001 and d_rg.geography_type_id = 1000320002
                  and d_rg.geography_id = d2t.entity_1_entity_id
                inner join main.entity_relationship s2d on s2d.active = 1000100001 and s2d.entity_relationship_type_id = 1000210029
                  and s2d.entity_2_entity_id = d_rg.geography_id
                inner join main.ref_geography s_rg on s_rg.active = 1000100001 and s_rg.geography_type_id = 1000320001
                  and s_rg.geography_id = s2d.entity_1_entity_id
                where t_rg.active = 1000100001 and t_rg.geography_type_id = 1000320003
              ),
              geocode as (
                select geo_based_prefix
                from (
                select 1 as geo_priority, geo_based_prefix
                from main.ref_geography v_rg
                inner join main.entity_relationship t2v on t2v.active = 1000100001 and t2v.entity_relationship_type_id = 1000210031
                  and t2v.entity_2_entity_id = v_rg.geography_id
                inner join t2s on t2v.entity_1_entity_id = t2s.taluk_id
                where v_rg.geography_id in (${villagesAsCommaSeparatedString})
                union 
                select 2 as geo_priority, geo_based_prefix
                from main.ref_geography t_rg
                inner join t2s on t_rg.geography_id = t2s.taluk_id
                where t_rg.geography_id in (${taluksAsCommaSeparatedString})
                ) geo order by geo_priority limit 1
              ), 
              customer_suffix as (
                select right(c.customer_visual_id, 6) as geo_suffix
                FROM main.customer c
                WHERE c.customer_visual_id like (
                  select concat(geo_based_prefix, '%')
                  from geocode
                )
              ),
              mpo as (
                SELECT lpad((max(TRIM(LEADING '0' FROM geo_suffix)::int)+1)::text, 6, '0') as max_plus_one from (
                  select *
                  from customer_suffix
                ) m_c_i
              )
              select concat(geocode.geo_based_prefix, coalesce(mpo.max_plus_one, '000001')) as customer_visual_id
              from mpo, geocode
            `
            const customerVisualIdQueryResponse = await transactionalEntityManager.query(getCustomerVisualIdQuery)
            const customerVisualId = customerVisualIdQueryResponse[0].customer_visual_id
            // get the customer id from the execution result. set it into return value
            const preProcessedCustomerData = preProcessRecords(customerTableEntity, {customer_type_id, customer_visual_id: customerVisualId, customer_name_l10n, mobile_number}, customerTableEntity.additionalAttributes)
            const createdCustomer = await transactionalEntityManager.createQueryBuilder().insert().into(customerTableEntity).values(preProcessedCustomerData).execute()
            customerId = createdCustomer.identifiers[0].customer_id
            // postUpsertProcessing('main', 'customer', { insertedPrimaryKeys: [customerId] })
            returnValue.customerId = customerId
          }
          const updateClassificationDataResult = await saveClassificationData(transactionalEntityManager, 'CUSTOMER_CLASSIFICATION', customerId, classificationData)
          returnValue.classificationDataUpdateResult = updateClassificationDataResult
        } catch (error) {
          console.log('oc UASS c 20, error')
          console.log('oc UASS c 20a, error = ', error)
          throw error
        }
      })
      return returnValue
    } catch (error) {
      console.log('oc r SFD c 10, error')
      console.log('oc r SFD c 10a, error = ', error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class GetAllCustomerLocations {
  async create (data, params) {
    try {
      const returnValue = { return_code: 0 }
      const mainDBConnection = dbConnections().main

      const [selectClauseForClassification, joinClauseForClassification] = generateSelectJoinClauseForClassification(reportQueryClassificationJoinConfiguration, 'CUSTOMER', undefined, [/* 'location_json',  */'village_name'])

      /* const customerDairyFarmLocationsQuery = `
        select coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') as village_name,
          coalesce(c.customer_name_l10n->>'en', c.customer_name_l10n->>'ul') customer_name,
          c.mobile_number, dfl_cc.value_json as location_json
        from main.customer c 
        inner join main.customer_classification dfl_cc on c.customer_id = dfl_cc.customer_id and dfl_cc.active = 1000100001
          and dfl_cc.classifier_id = 2000000004
        inner join main.customer_classification v_cc on c.customer_id = v_cc.customer_id and v_cc.active = 1000100001
          and v_cc.classifier_id = 2000000055
        inner join main.ref_sdtv_view rsv on rsv.village_id = v_cc.value_reference_id
        where c.active = 1000100001
      ` */

      const customerDairyFarmLocationsQuery = `
        select c.customer_id, coalesce(c.customer_name_l10n->>'en', c.customer_name_l10n->>'ul') customer_name,
          dfl_cc.value_json->>'latitude' as lat, dfl_cc.value_json->>'longitude' as lng, 
          ${selectClauseForClassification !== undefined && selectClauseForClassification !== '' ? selectClauseForClassification + ', ' : ''}
          c.mobile_number
        from main.customer c 
        inner join main.customer_classification dfl_cc on c.customer_id = dfl_cc.customer_id and dfl_cc.active = 1000100001
          and dfl_cc.classifier_id = 2000000004
        ${joinClauseForClassification}
        where c.active = 1000100001
      `

      const customerDairyFarmLocationsQueryResultUnprocessed = await mainDBConnection.manager.query(
        customerDairyFarmLocationsQuery
      )
      const customerDairyFarmLocationsQueryResult = postProcessRecords(undefined, customerDairyFarmLocationsQueryResultUnprocessed,
        {double_columns: [], json_columns: ['village_name', 'customer_name']})



      // identify all columns needed
      returnValue.count = customerDairyFarmLocationsQueryResult.length
      returnValue.report = customerDairyFarmLocationsQueryResult

      return returnValue
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

module.exports = { CustomerList, Customer, CustomerListV3, CustomerListFilter, GetFarmerDetails, SetFarmerDetails, GetAllCustomerLocations }
