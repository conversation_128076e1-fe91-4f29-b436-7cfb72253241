const { getDBConnections, postProcessRecords, preProcessRecords, dbConnections } = require("@krushal-it/ah-orm")
const { generateLimitString, generateSortString, generateWhereClause } = require("../../utils/helpers/query_builder.helper")
const { VILLAGE_CLASSIFIER, VILLAGE_TYPE_ID, PARAVET, ACTIVE } = require("@krushal-it/back-end-lib/utils/constant")

class CustomerListingAntd {
  async create /*or is it update*/(data, params) {
    try {
      const returnValue = { return_code: 0 }
      const mainDBConnections = dbConnections().main

      const limitString = generateLimitString(data)
      const sortString = generateSortString(data)
      const whereClause = generateWhereClause(data)
      let f_additionalFilter = ``
      // if(data && data.filter && data.filter.f_additionalFilter){
      //   if(data.filter.f_additionalFilter[0] && data.filter.f_additionalFilter[0] === 'f_not_subscribed'){
      //     f_additionalFilter += `AND c.customer_id  IN (select * from NOT_SUBSCRIBED)`
      //   }
      //   if(data.filter.f_additionalFilter[0] && data.filter.f_additionalFilter[0] === 'f_not_assigned'){
      //     f_additionalFilter += ` AND STAFF.staff_id is null`
      //   }
      // }
      const innerQuery = `
        WITH ADDITIONAL_DATA AS (
          select
          customer.customer_id,
          count(
            case when animal.active = 1000100001 and animal_classification.value_date is null
            then animal.animal_id
            end
          ) as active_unsubscribed_animals
        from
          main.customer
            left join main.entity_relationship
              on
                customer.customer_id = entity_relationship.entity_1_entity_uuid
                    and 
                entity_relationship.entity_relationship_type_id = 1000210004
            left join main.animal
              on 
                entity_relationship.entity_2_entity_uuid = animal.animal_id
            left join main.animal_classification
              on 
                animal_classification.animal_id = animal.animal_id
                and
                animal_classification.classifier_id = 2000000124
        where
          customer.active = 1000100001
        group by
          customer_id
       ),
       customer_data AS (
        select    c.customer_id, 
        coalesce(c.customer_name_l10n->>'en', c.customer_name_l10n->>'ul') as customer_name,
        c.customer_name_l10n, 
        c.mobile_number,
        alt_customer_number.value_string_256 as alt_contact_number,
        c.customer_visual_id,
        c.updated_at,
        c.created_at,
        gcc.village_id,
        gcc.village,
        gcc.taluk_id,
        gcc.taluk,
        gcc.district_id,
        gcc.district,
        gcc.state_id,
        gcc.state,
        coalesce( STAFF.staff_name_l10n->>'en',  STAFF.staff_name_l10n->>'ul','N/A') as staff_name,
        STAFF.staff_id as staff_id,
        STAFF.staff_name_l10n as paravet_name,
        ad.active_unsubscribed_animals,

        case when TERMS.value_reference_id = 1000105001 then 'Accepted' else 'Not Accepted'
        end  terms

        from main.customer c
        left join main.customer_classification gcc1 on gcc1.active = 1000100001 and gcc1.classifier_id = 2000000004 and gcc1.customer_id = c.customer_id
        left join (
          select gcc.customer_id,
            coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') as village,
            coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') as taluk,
            coalesce(rsv.district_name_l10n->>'en', rsv.district_name_l10n->>'ul') as district,
            coalesce(rsv.state_name_l10n->>'en', rsv.state_name_l10n->>'ul') as state,
            rsv.village_id, rsv.taluk_id, rsv.district_id, rsv.state_id
          from main.customer_classification  gcc, main.ref_sdtv_view rsv
          where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
          and gcc.value_reference_id = rsv.village_id
        ) gcc on gcc.customer_id = c.customer_id
        LEFT JOIN 
          main.customer_classification as TERMS
          ON 
          TERMS.customer_id = c.customer_id
          AND 
          TERMS.classifier_id = 2000000207
        LEFT JOIN 
          main.customer_classification as alt_customer_number
          ON 
            alt_customer_number.customer_id = c.customer_id
            AND 
            alt_customer_number.classifier_id = 2000000453
            AND
            alt_customer_number.active = ${ACTIVE}
        LEFT JOIN 
        main.ENTITY_GEOGRAPHY 
              ON 
                  ENTITY_GEOGRAPHY.geography_id = gcc.village_id
                  AND 
                  ENTITY_GEOGRAPHY.geography_type_id = ${VILLAGE_TYPE_ID}
                  AND 
                  ENTITY_GEOGRAPHY.entity_type_id = ${PARAVET}
                  AND 
                  ENTITY_GEOGRAPHY.ACTIVE = ${ACTIVE}
          LEFT JOIN 
              main.STAFF 
              ON 
                  STAFF.staff_id = ENTITY_GEOGRAPHY.entity_uuid
          LEFT JOIN
           ADDITIONAL_DATA ad on ad.customer_id = c.customer_id
        where c.active = 1000100001 
        
        )
        select    * from customer_data
        `

      const countQuery = `
          select count(*) as count from (
            ${innerQuery}
          ) outerTable
          ${whereClause}
        `
      const complaintResultCount = await mainDBConnections.manager.query(countQuery)
      const count = parseInt(complaintResultCount[0].count)

      const query = `
          select * from (
            ${innerQuery}
          ) outerTable
          ${whereClause}
          ${sortString}
          ${limitString}
        `
      const complaintResult = await mainDBConnections.manager.query(query)
      const complaintResult2 = postProcessRecords(undefined, complaintResult, {})

      const distinctVillageQuery = `
          select distinct village_id, village text , village_id value from (
            ${innerQuery}
          ) outerTable
        `

      // ${whereClause}
      const distinctVillageResult = await mainDBConnections.manager.query(distinctVillageQuery)
      const distinctVillageResult2 = postProcessRecords(undefined, distinctVillageResult, {})

      returnValue.count = count
      returnValue.report = complaintResult2
      returnValue.village_filter_values = distinctVillageResult2
      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async find(params) {
    try {
      const returnValue = { return_code: 0 }
      const tableFilters = {}
      const mainDBConnections = dbConnections().main
      const distinctTalukQuery = `
          select distinct taluk_id, taluk_id as value, coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') as text
          from main.customer_classification cc
          inner join main.ref_sdtv_view rsv on rsv.village_id = cc.value_reference_id
          where cc.active = 1000100001 and cc.classifier_id = 2000000055
        `
      const distinctTalukFilterValuesResult = await mainDBConnections.manager.query(distinctTalukQuery)
      const distinctTalukFilterValuesResult2 = postProcessRecords(undefined, distinctTalukFilterValuesResult, {})

      const distinctVillageQuery = `

        `

      tableFilters.taluk_filter_values = distinctTalukFilterValuesResult2
      const staffQuery = `
          select s.staff_id as value, coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as text
          from main.staff s
          where s.active = 1000100001 and s.staff_type_id in (${PARAVET})
        `
      let staffFilterValuesResult = await mainDBConnections.manager.query(staffQuery)
      staffFilterValuesResult = postProcessRecords(undefined, staffFilterValuesResult, {})
      staffFilterValuesResult.push({
        value: "N/A",
        text: "N/A"
      })
      tableFilters.staff_filter_values = staffFilterValuesResult
      tableFilters.terms_filter_values = [
        {
          value: "Accepted",
          text: "Accepted"
        },
        {
          value: "Not Accepted",
          text: "Not Accepted"
        }
      ]
      returnValue.tableFilters = tableFilters
      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

module.exports = { CustomerListingAntd }
