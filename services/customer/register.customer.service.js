const { Customer, CustomerList,
  CustomerListV3, CustomerListFilter,
  GetFarmerDetails, SetFarmerDetails,
  GetAllCustomerLocations
} = require('./customer.class')
const auth = require('../../middleware/auth')
const tokenInject = require('../../middleware/tokenInject.js')

const configureCustomer = (app) => {
  // app.use(auth)
  app.use('/v2/customer-list', new CustomerList())
  app.use('/v2/customer', new Customer())
  app.use('/v2/customer/:customerId', new Customer())
  app.use('/v3/customer-list', new CustomerListV3(), tokenInject)
  app.use('/v3/customer-list-filter', new CustomerListFilter(), tokenInject)
  app.use('/v3/get-customer-details', new GetFarmerDetails(), tokenInject)
  app.use('/v3/set-customer-details', new SetFarmerDetails(), tokenInject)
  app.use('/get-all-customer-locations', new GetAllCustomerLocations(), tokenInject)
}
module.exports = { configureCustomer }
