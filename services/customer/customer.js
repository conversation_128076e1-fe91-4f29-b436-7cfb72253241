
const { dbConnections} = require('@krushal-it/ah-orm')

const { getCompiledQuery } = require("@krushal-it/back-end-lib");

const customerListing = async (options = {}) => {
    const { user_id, user_type } = options
  
    if (options.f_farmer_name) {
      options.f_farmer_name = '%' + options.f_farmer_name + '%'
    }
  
    if (options.f_farmer_village) {
        options.f_farmer_village = '%' + options.f_farmer_village + '%'
    }
  
    if (options.f_farmer_taluka) {
        options.f_farmer_taluka = '%' + options.f_farmer_taluka + '%'
    }
  
    if (options.f_farmer_visual_id) {
      options.f_farmer_visual_id = '%' + options.f_farmer_visual_id + '%'
    }
  
    if (options.f_farmer_mobile) {
      options.f_farmer_mobile = '%' + options.f_farmer_mobile + '%'
    }
  
    if (options.f_paravet_name) {
      options.f_paravet_name = '%' + options.f_paravet_name + '%'
    }
  
    const queryParameters = { ...options, user_id, user_type }
    const templateParameters = { ...options, user_id, user_type}
  
    let query = getCompiledQuery('web-customer-listing', templateParameters)
    const queryBuilder = await dbConnections().main.manager.createQueryBuilder().from(`(${query})`, 'customer-list').setParameters(queryParameters)
  
    let count = await queryBuilder.select('COUNT(*) as count').execute()
    count = parseInt(count[0].count)
    let total_page = 1
    if (!isNaN(options.page_number) && !isNaN(options.page_limit)) {
      let offset = options.page_limit * (options.page_number - 1)
      if (offset < 0) return { message: 'Invalid page number' }
      queryBuilder.skip(offset).take(options.page_limit)
      total_page = parseInt(count / options.page_limit)
      total_page += count % options.page_limit === 0 ? 0 : 1
    }
  
    const result = await queryBuilder.select('*').execute()
    return { result, total_page }
  }

  module.exports = {
    customerListing

  }