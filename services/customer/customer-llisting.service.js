const { LibCustomerListing } = require("@krushal-it/back-end-lib");
const { getCustomerList_v3 } = require("../../utils/helpers/customer");

class CustomerListing {
  customerListingServiceHandler = new LibCustomerListing();
  async create(data, params) {
    try {
      let result = await this.customerListingServiceHandler.create(data, params);
      return result;
    } catch (error) {
      console.error(error);
      return { result: false, error };
    }
  }
}
class WebCustomerListing {
  async create(data, params) {
    try {
     
      const { page_number, page_limit, f_farmer_name, f_farmer_mobile, f_farmer_village, f_farmer_taluka, f_farmer_visual_id, f_paravet_name,f_not_assigned,f_not_subscribed,terms_condition} = data
      const { token } = params.headers
      let query = {}
      query['user_id'] = token.user_id
      query['user_type'] = token.user_type
      query['user_type_string'] = token.user_type_resolved.type
      query['count'] = 1

      if (page_limit) query['page_limit'] = page_limit
      if (page_number) query['page_number'] = page_number
      query['terms_condition'] = terms_condition
      query = {
        ...query,
        f_farmer_name,
        f_farmer_mobile,
        f_farmer_village,
        f_farmer_taluka,
        f_farmer_visual_id,
        f_paravet_name,
        f_not_assigned,
        f_not_subscribed
      }
      const customers = await getCustomerList_v3(query)
      return customers
    } catch (error) {
      console.log(error)
      return { result: false, error }
    }
  }
}
module.exports = {CustomerListing,WebCustomerListing};
