const Pdf = require("@krushal-it/krushal-pdf-generator/index.cjs")
const { dbConnections } = require("@krushal-it/ah-orm")
const { getCompiledQuery } = require("@krushal-it/back-end-lib")
const { extractBasedOnLanguage } = require("@krushal-it/common-core")
const kurshal_image = require("../../../utils/image.js")
const { templates } = require("../../../templates/index.js")

async function dailyCustomerAntibioticReport({ startDate, endDate, userId, userType }) {
  const dateHeading = getDateString({ year: true, month: true, day: true })
  return await _getResidualReportPdfByType({
    typeOfReport: "daily-customer-antibiotic-report",
    startDate,
    endDate,
    userId,
    userType,
    dateHeading: dateHeading
  })
}
async function dailyCattleRxReport({ startDate, userId, endDate, userType }) {
  const dateHeading = getDateString({ year: true, month: true, day: true })
  return await _getResidualReportPdfByType({
    typeOfReport: "daily-cattle-rx-report",
    startDate,
    endDate,
    userId,
    userType,
    dateHeading: dateHeading
  })
}

async function monthlyCustomerAntibioticReport({ startDate, endDate, userId, userType }) {
  const dateHeading = getDateString({ year: true, month: true, day: true })
  return await _getResidualReportPdfByType({
    typeOfReport: "monthly-customer-antibiotic-report",
    startDate,
    endDate,
    userId,
    userType,
    date: dateHeading
  })
}

async function _getResidualReportPdfByType({ typeOfReport, endDate, startDate, userId, userType, date }) {
  const reportQuery = await dbConnections().main.manager.createQueryBuilder()
  let pdf_payload = undefined

  const query = `(${getCompiledQuery(typeOfReport)})`
  let antibiotic_report = await reportQuery
    .from(`(${query})`)
    .setParameters({ start_date: startDate, end_date: endDate, user_id: userId, user_type: userType })
    .execute()

  const htmlTemplate = templates.html.pdf[`${typeOfReport}.hbs`]
  const pdf = new Pdf(htmlTemplate, {
    language: "en",
    _extractBasedOnLanguage: extractBasedOnLanguage
  })
  pdf_payload = {
    antibiotic_report: antibiotic_report,
    antibiotic_report_date: startDate,
    image: kurshal_image
  }
  const pdfBuffer = await pdf.setData(pdf_payload).convert("buffer")

  return pdfBuffer
}

module.exports = {
  monthlyCustomerAntibioticReport,
  dailyCattleRxReport,
  dailyCustomerAntibioticReport
}

function getDateString({ year = true, month = true, day = true }) {
  const dateConfig = {
    year: year && "numeric",
    month: month && "2-digit",
    day: day && "2-digit"
  }
  return new Date(new Date() - 1 * 24 * 1000 * 60 * 60).toLocaleDateString("en-IN", dateConfig)
}
