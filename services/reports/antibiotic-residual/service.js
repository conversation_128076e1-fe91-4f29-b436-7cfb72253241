const { dailyCustomerAntibioticReport, dailyCattleRxReport, monthlyCustomerAntibioticReport } = require("./controller.js")
const { sendEmail } = require("../../e-mailer/index")
const kurshal_image = require("../../../utils/image.js")
const { templates } = require("../../../templates/index.js")

async function emailMilkWithdrawlReport(recipients, typeOfReport) {
  let buffer = undefined
  let emailHTMLstring = undefined
  switch (typeOfReport) {
    case "daily-cattle-rx-report": {
      buffer = await dailyCattleRxReport({ startDate: "2024-03-20", endDate: undefined, userId: "960f5344-935d-11ee-b08d-2b0f905cd1bd", userType: 1000230011 })
      emailHTMLstring = templates.html.email["daily-cattle-rx-report.hbs"]
      break
    }
    case "daily-customer-antibiotic-report": {
      buffer = await dailyCustomerAntibioticReport({ startDate: "2024-03-20", endDate: undefined, userId: "960f5344-935d-11ee-b08d-2b0f905cd1bd", userType: 1000230011 })
      emailHTMLstring = templates.html.email["daily-customer-antibiotic-report.hbs"]
      break
    }
    case "monthly-customer-antibiotic-report": {
      buffer = await monthlyCustomerAntibioticReport({ startDate: "2024-03-20", endDate: undefined, userId: "960f5344-935d-11ee-b08d-2b0f905cd1bd", userType: 1000230011 })
      emailHTMLstring = templates.html.email["monthly-customer-antibiotic-report.hbs"]
      break
    }
  }

  const emailTemplateText = emailHTMLstring({ image: kurshal_image, data: {} })
  const subject_text = "Testing node mailer"
  const fallBackText = ""
  const attachment = [
    {
      name: "something",
      content: buffer
    }
  ]

  sendEmail(recipients, subject_text, emailTemplateText, fallBackText, attachment)
}

exports.emailDailyCattleRxReport = emailMilkWithdrawlReport
