const { FarmManagement, FarmManagementComplete, FarmManagementReport, FarmManagementWeb } = require("@krushal-it/back-end-lib")
const { BadRequest, GeneralError } = require("@feathersjs/errors")
const tokenInject = require("../../middleware/tokenInject")
const { handleServerError } = require("../../middleware/server-error")
const Pdf = require("@krushal-it/krushal-pdf-generator/index.cjs")
const { extractBasedOnLanguage } = require("@krushal-it/common-core")
const { handlerBarTemplates } = require("../../templates")
const kurshal_image = require("../../utils/image")
const { pipeStreamForGet } = require("../object-management/middleware")
const { writeFileSync, readFileSync, createReadStream, mkdirSync, existsSync, rmSync, rmdirSync } = require("fs")
const { Readable } = require("stream")
const { alignDataForTemplate } = require("./helper")
const { uploadDocument, addDocumentEntry } = require("../document/controllers/document")
const { rm } = require("fs/promises")
const S3Utils = require("../../utils/common/aws/s3.utils")
const {
  entity_type: { CUSTOMER, TASK },
  document_type: { FARM_MANAGEMENT_PDF }
} = require("@krushal-it/back-end-lib/ENUMS")
const task_hirarchy = app => {
  const farm_management_v2 = new FarmManagement()
  const report = new FarmManagementReport()
  const completeTask = new FarmManagementComplete()
  const farm_management_web = new FarmManagementWeb()

  app.use(
    "/task/:type_id/:cc_id/complete",
    {
      async create(data, params) {
        try {
          const { type_id, cc_id } = params.route
          const constructedData = {
            ...data,
            care_calendar_id: cc_id
          }
          const response = await completeTask.completeTask(constructedData, params.headers.token)
          return response
          // const
        } catch (error) {
          return new GeneralError(error).toJSON()
        }
      }
    },
    tokenInject
  )
  app.use(
    "/topic/:type/:cc_id",
    {
      async find(params) {
        try {
          const { type, cc_id } = params.route
          const data = await farm_management_v2.loadQuestions(cc_id)
          return data
        } catch (error) {
          return new GeneralError(error).toJSON()
        }
      }
    },
    tokenInject
  )
  app.use(
    "/question/:question_id/:cc_id",
    {
      async find(params) {
        try {
          const { question_id, cc_id } = params.route
          const data = await farm_management_v2.getTargetByQuestionId(question_id, cc_id)
          return data
        } catch (error) {
          return new GeneralError(error).toJSON()
        }
      },
      async create(data, params) {
        try {
          const { question_id, cc_id } = params.route
          const response = await farm_management_v2.saveAnswers(question_id, cc_id, data)
          return response
          // const
        } catch (error) {
          return new GeneralError(error).toJSON()
        }
      }
    },
    tokenInject
  ),
    app.use(
      "/customer/farm_management_summary_with_lang/:language/:customer_id/:cc_id",
      {
        async find(params) {
          try {
            const { customer_id, cc_id, language } = params.route
            const data = await report.loadCustomerLevelReport(customer_id, cc_id)
            console.log('\x1b[36m%s\x1b[0m','farm_management_summary_with_lang','language', language, JSON.stringify(data, null, 4));
            const pdata = alignDataForTemplate(data)
            pdata.image = kurshal_image
            
            const farm_management_template = handlerBarTemplates["html"]["pdf"]["farm-management.hbs"]
            const pdf = new Pdf(farm_management_template, {
              language,
              _extractBasedOnLanguage: extractBasedOnLanguage
            })
            const pdfBuffer = await pdf.setData(pdata).convert("buffer")

            if (existsSync("./.cache")) await rm("./.cache", { recursive: true })
            const timeStamp = new Date().getTime()
            const fileName = cc_id + timeStamp + ".pdf"
            const path = `./.cache/${fileName}`
            if (!existsSync("./.cache")) mkdirSync("./.cache")
            writeFileSync(path, pdfBuffer)
            const s3 = new S3Utils()
            const key = await s3.s3Uploader({ originalname: fileName, path }, "ah", "task", "pdf")
            const document_entries = [{ entity: "task", url: key, entity_1_id: cc_id, document_type: "farm_management_pdf", document_type_id: FARM_MANAGEMENT_PDF, entity_1_type_id: TASK, fileType: "application/pdf", document_information_json: { type: "application/pdf", fileName: fileName, uri: key } }]
            await addDocumentEntry({ document_entries })
            return Readable.from(pdfBuffer)
          } catch (error) {
            return new GeneralError(error).toJSON()
          }
        }
      },
      pipeStreamForGet
    ),
    app.use(
      "/customer/farm_management_summary/:customer_id/:cc_id",
      {
        async find(params) {
          try {
            const { customer_id, cc_id } = params.route
            const data = await report.loadCustomerLevelReport(customer_id, cc_id)
            return data
          } catch (error) {
            return new GeneralError(error).toJSON()
          }
        }
      },
      tokenInject
    )
  app.use("/customer/farm_management/list", {
    async find(params) {
      try {
        const data = await farm_management_web.loadCustomers({})
        return data
      } catch (error) {
        return new GeneralError(error).toJSON()
      }
    }
  })
  app.use("/customer/farm_management/:customer_id", {
    async find(params) {
      try {
        const { customer_id } = params.route
        const data = await farm_management_web.loadCustomerTaskById(customer_id)
        return data
      } catch (error) {
        return new GeneralError(error).toJSON()
      }
    }
  })
  app.use("/task-data/farm_management/:care_calendar_id",{
    async find(params) {
      try {
        const { care_calendar_id } = params.route
        const data = await farm_management_web.loadTableByTaskId(care_calendar_id)
        return data
      } catch (error) {
        return new GeneralError(error).toJSON()
      }
    }
  })
}
module.exports = { task_hirarchy }
