const {FarmManagement, FarmManagementComplete ,FarmManagementReport ,FarmManagementWeb, FarmManagementConfig} = require("@krushal-it/back-end-lib")
const { BadRequest, GeneralError } = require("@feathersjs/errors")
const tokenInject = require("../../middleware/tokenInject")
const { handleServerError } = require("../../middleware/server-error")
const { loadClassificationConfigurationByMultipleParentIds } = require("@krushal-it/back-end-lib/services/common/classification.helper")

const task_hirarchy_config = app => {
   const farm_management_config = new FarmManagementConfig()
   app.use(
    "/task-config/list",
    {
      async find(params) {
        try {
            const basicResponse = await farm_management_config.getTaskList({})
            return basicResponse
        } catch (error) {
         return new GeneralError(error).toJSON()
        }
     }
    },
    tokenInject
  )
  app.use(
    "/task-config/:question_uuid",
    {
      async find( params) {
        try {
            const { question_uuid  } = params.route
            const basicResponse = await farm_management_config.getTaskByQuestionId(question_uuid)
            return basicResponse
        } catch (error) {
         return new GeneralError(error).toJSON()
        }
     }
    },
    tokenInject
  )
    app.use(
      "/task-config-update/:type",
      {
        async create(data, params) {
          try {
           const { type } = params.route
           const { question_uuid } = data
           switch (type) {
             case "basic" : 
               const basicResponse = await farm_management_config.saveOrEditBasicConfig(data , question_uuid)
               return basicResponse
             case "advice-state": 
               const adviceResponse = await farm_management_config.advicesConfig(data, question_uuid)
               return adviceResponse
            //  case "additional-state": 
            //   const  additionalFormConfig = await farm_management_config.saveAditionalConfig(data, question_uuid)
            //   return additionalFormConfig
             case "states": 
              const currentTargetState = await farm_management_config.targetCurrentConfig(data,question_uuid)
              return currentTargetState
             case "reference_media": 
              const referenceMedia = await farm_management_config.referenceMediaConfig(data, question_uuid)
              return referenceMedia
             default : 
                throw new Error("task config type does not exist")
           }
          } catch (error) {
           return new GeneralError(error).toJSON()
          }
       }
      },
      tokenInject
    )
  

}
module.exports = { task_hirarchy_config }
