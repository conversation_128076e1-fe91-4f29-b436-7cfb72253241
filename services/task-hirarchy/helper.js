const dayjs = require("dayjs")
function alignDataForTemplate(data) {
  const processedData = { ...data }
  processedData.title = { en: "Farm Management Recommendations and Report" ,hi: "किसान प्रबंधन सूचना और प्रगति चार्ट" , mr: "किसान प्रबंधन सूचना और प्रगति चार्ट", te: "కారుకు ప్రబంధన సమాచారం మరియు ప్రగతి చార్ట్", ta: "கார்வியம் மற்றும் தொடர்புத்திறன் பட்டியல்"}
  processedData.months = [{ en: "Jan" }, { en: "Feb" }, { en: "Mar" }, { en: "Apr" }, { en: "May" }, { en: "Jun" }, { en: "Jul" }, { en: "Aug" }, { en: "Sep" }, { en: "Oct" }, { en: "Nov" }, { en: "Dec" }]
  if (data.progressChart && Array.isArray(data.progressChart)) {
    const yearlyData = {}
    const allCategories = [...new Set(data.progressChart.map(item => item.category))].filter(Boolean)
    const allStatsEmpty = data.progressChart.every(item => !item.stats || item.stats.length === 0)

    if (allStatsEmpty) {
      yearlyData[""] = {
        categories: allCategories.map(categoryName => ({
          name: categoryName,
          cells: Array.from({ length: 12 }, () => ({ severities: [] }))
        }))
      }
    } else {
      data.progressChart.forEach(item => {
        if (item.stats && Array.isArray(item.stats)) {
          item.stats.forEach(stat => {
            if (stat.visit_date && stat.severity !== undefined) {
              const date = dayjs(stat.visit_date)
              const year = date.year()
              const month = date.month()

              if (!yearlyData[year]) {
                yearlyData[year] = {
                  categories: allCategories.map(categoryName => ({
                    name: categoryName,
                    cells: Array.from({ length: 12 }, () => ({ severities: [] }))
                  }))
                }
              }

              const categoryData = yearlyData[year].categories.find(c => c.name === item.category)
              if (categoryData) {
                categoryData.cells[month].severities.push({
                  severity: stat.severity,
                  date: date.format("YYYY-MM-DD")
                })
              }
            }
          })
        }
      })
    }
    processedData.progressChart = yearlyData
  }
  return processedData
}

module.exports = { alignDataForTemplate }
