const feathers = require("@feathersjs/feathers")
const express = require("@feathersjs/express")
const cors = require("cors")
const RegisterService = require("../services/registration/register.service")
const { CustomerService, CustomerTermsServices, CustomerTaskService } = require("../services/customer/customer.service")
const { AnimalService, AnimalListForFarm, UpdateAnimalActiveStatus, AnimalHealthRecord, AnimalTaskHistoryBySrId, AnimalMilking } = require("../services/animal/animal.service")
const LocationService = require("../services/location/location.service")
const { multerUp, injectFile, streamPipe, multerError } = require("../middleware/multer/multerUpload")
const tokenInject = require("../middleware/tokenInject")
const { RoutePlanDetailService } = require("./activity/route_plan_detail.service")
const { FCMService } = require("./notification/notification")
const { NotificationService } = require("./notification/message")
const { File, FileByKey, DocumentList, Document } = require("./document/document.class.js")
const { BackendDocumentService, LibMediaService, LibMediaKeyService } = require("./document/document.service")
const { auth, checkUsertype, logReq } = require("../middleware/auth")
const { verifyXAppId } = require("../middleware/verify-app")
const { ActivityService, TaskList, LinkedTaskHistory, AnimalTask, FarmerTask, TaskByID, ActivityObservation, ActivityDiagnosis, ActivityPrescription, ActivityPreview } = require("./activity/activity.service")

const { CommonServices, HealthCheck, References2, ListFilter, LibLoadMeds } = require("./common/common.service")
const ReferenceService = require("./reference/reference.service.js")
const { JobService, TranslationService } = require("./jobs/job.service")
const { GetStaff, GetStaffByTypeIds } = require("./staff/get-staff.class.js")
const { CustomerListing, WebCustomerListing } = require("./customer/customer-llisting.service")
const RoutePlanService = require("./activity/route_plan.service")
const { configureUser } = require("./user/register.user.service")
const { configureSynchronization } = require("./synchronization/register.synchronization.service")

const { CardService, ChildCardsRO } = require("./activity/card.service")
const { CONST_ID_TYPE_STAFF_BACKOFFICE, BACK_OFFICE_PERSON, FARMER } = require("@krushal-it/back-end-lib/utils/constant")
const { validators, handleValidationError } = require("../middleware/validators")
const loadStaffServices = require("./staff/staff.service")
const loadFindServices = require("./find/find.service")
const { configureIntegration } = require("./integration/register.integration.service.js")
const { configureBgLocation } = require("./bgLocation/register.bgLocation.service")
const { configureAnimalFeeds } = require("./animalFeeds/register.animalFeeds.service")
const { configureCommon } = require("./common/register.common.service.js")
const { configureDocument } = require("./document/register.document.service.js")
const { configureCustomer } = require("./customer/register.customer.service.js")
const { configureAnimal } = require("./animal/register.animal.service.js")
const { configurePartner } = require("./partner/register.partner.service.js")
const { configureStaff } = require("./staff/register.staff.service.js")
const { configureAsset } = require("./asset/register.asset.service.js")
const { configureReference } = require("./reference/register.reference.service.js")
const { configureGeo } = require("./geo/register.geo.service.js")
const { configureAHV2 } = require("./ahv2/register.ahv2.service.js")
const { configureHealthScore } = require("./healthScore/register.healthScore.service")
const { configureAnimalClassificationHistory } = require("./animalClassificationHistory/register.animalClassificationHistory.service")
const { CustomerListingAntd } = require("./customer/customer-listing_antd")
const { configureOpenCommerce } = require("./opencommerce/register.opencommerce.service")
const configureFarmerUserForPpu = require("./ppuparavet/farmer.service.js")
const { loadStaffFinanceServices, loadKhataServices } = require("./ppuparavet/finance.service.js")
const { servicesV2 } = require("./pay-per-user/servicesV2.js")
const servicesV3 = require("./version-3-apis/activity/service.js")
const activityManagement = require("./activity-managment/index.js")
const { scheduler } = require("./schedule/schedule")
const geographyManagement = require("./geography-management/index.js")

const { configureAssetFeature } = require("./assetFeature/register.assetFeature.service.js")
const { configureSmartFeed } = require("./smartFeed/register.smartFeed.service.js")
const configureFarmerFinance = require("./finance/farmer/index.js")
const objectManagement = require("./object-management/index.js")
const { loadInventoryService } = require("./find/inventory.service.js")
const { observationServicesV2 } = require("./observationV2/service.js")
const { FlaggedCustomers } = require("@krushal-it/back-end-lib")
const { configureInventoryV2 } = require("@krushal-it/back-end-lib")

const { LoginFromMobile } = require("./user/user.class.js")
const { AnimalFilterDetails, AnimalType, AnimalMilkingDetails } = require("../v2/services/animals/animals.class.js")

const { FarmerTaskV2, AnimalTaskV2, AnimalTaskHistoryV2, AllCattleTasksV2, AnimalActivityDetailsSummary } = require("../v2/services/activity.service.js")
const { UpdateUserLatLong, LogoutUserDevice, LogoutUserDeviceFromOffice } = require("../v2/services/user/user.class.js")
const { TriggerApi } = require("../socket.js")
const { otp } = require("./otp/index.js")
const { task_hirarchy } = require("./task-hirarchy/route.js")
const { task_hirarchy_config } = require("./task-hirarchy/configRoute.js")
const { TaskPaymentDetails } = require("./activity/task-payment-service.js")
const { prescriptionPredictor } = require("./prescription-predictor/prescription.service.js")
const { diagnosisPredictor } = require("./diagnosis-predictor/diagnosis.service.js")

class Register {
  constructor() {
    this.app = express(feathers())
    this.app.use(express.json({ limit: "100mb" }))
    this.app.use(express.urlencoded({ extended: true, limit: "100mb" }))
    this.app.options("*", cors())
    this.app.use(cors())
    this.app.configure(express.rest())
  }

  #registerServices() {
    this.app.configure(app => {
      configureUser(app)
    })

    this.app.use("/v2/client/trigger-socket", new TriggerApi())

    this.app.use("/health", new HealthCheck())
    this.app.use("/media", new LibMediaService(), streamPipe)
    this.app.use("/media-key", new LibMediaKeyService(), streamPipe)
    this.app.configure(app => {
      configureIntegration(app)
    })

    // Restricted
    this.app.use(auth)
    this.app.use(logReq)

    this.app.use(verifyXAppId("WEB_APP", new Set([CONST_ID_TYPE_STAFF_BACKOFFICE])))
    this.app.use("/create-or-update/:entityType", new RegisterService(), tokenInject)
    this.app.use("/references/:entityType", new ReferenceService(), tokenInject)
    this.app.use("/get-staff", new GetStaff(), tokenInject)
    this.app.use("/customer", new CustomerService(), tokenInject)
    this.app.use("/customer-task", checkUsertype(FARMER), new CustomerTaskService(), tokenInject)
    this.app.use("/customer-listing", new CustomerListing(), tokenInject)
    this.app.use("/admin-customers", checkUsertype(BACK_OFFICE_PERSON), new WebCustomerListing(), tokenInject)
    this.app.use("/common/:subPath", new CommonServices(), tokenInject)
    this.app.use("/animal", new AnimalService(), tokenInject)
    this.app.use("/farmtask-animal-list", new AnimalListForFarm())
    this.app.use("/animal/change-status", new UpdateAnimalActiveStatus(), tokenInject)
    this.app.use("/location/:type", new LocationService(), tokenInject)
    this.app.use("/activity/task-list", new TaskList(), tokenInject)
    this.app.use("/activity/preview", new ActivityPreview(), tokenInject)
    this.app.use("/activity/observation", new ActivityObservation(), tokenInject)
    this.app.use("/activity/diagnosis", new ActivityDiagnosis(), tokenInject)
    this.app.use("/activity/prescription", new ActivityPrescription(), tokenInject)
    this.app.use("/activity/:object", new ActivityService(), tokenInject)
    this.app.use("/task/payment-details", new TaskPaymentDetails(), tokenInject)

    this.app.use("/get-staff-by-types", new GetStaffByTypeIds(), tokenInject)
    this.app.use("/animal-health-record", new AnimalHealthRecord(), tokenInject)
    this.app.use("/medical-history-for-task", new AnimalTaskHistoryBySrId(), tokenInject)

    this.app.use("customer-listingantd", new CustomerListingAntd(), tokenInject)

    this.app.use("/document", multerUp.fields([{ name: "file", maxCount: 1 }]), multerError, injectFile, new BackendDocumentService(), tokenInject)

    this.app.use("/v2/file", multerUp.fields([{ name: "file", maxCount: 1 }]), injectFile, new File(), streamPipe)

    this.app.use("/v2/document-list", new DocumentList())
    this.app.use("/v2/document", new Document())
    this.app.use("/v2/document/:documentId", new Document())
    this.app.use("/v2/refs", new References2())
    this.app.use("/get-file-by-key", injectFile, new FileByKey(), streamPipe)
    this.app.use("/v2/document-inactive", new Document())

    this.app.use("/jobs/translate/v2", checkUsertype(BACK_OFFICE_PERSON), new TranslationService(), tokenInject)
    this.app.use("/jobs", new JobService(), tokenInject)
    this.app.use("/route-plan-detail", new RoutePlanDetailService(), tokenInject)
    this.app.use("/linked-task-history", new LinkedTaskHistory(), tokenInject)
    this.app.use("/route-plan", new RoutePlanService(), tokenInject)
    this.app.use("/notification", new FCMService(), tokenInject)
    this.app.use("/send-notification", checkUsertype(BACK_OFFICE_PERSON), validators.pushNotification, handleValidationError, new NotificationService(), tokenInject)

    this.app.configure(app => {
      prescriptionPredictor(app)
    })

    this.app.configure(app => {
      diagnosisPredictor(app)
    })

    this.app.configure(app => {
      configureSynchronization(app)
      loadStaffServices(app)
      loadFindServices(app)
    })
    this.app.configure(app => {
      configureCommon(app)
    })
    this.app.configure(app => {
      configureDocument(app)
    })
    this.app.configure(app => {
      configureCustomer(app)
    })
    this.app.configure(app => {
      configureAnimal(app)
    })
    this.app.configure(app => {
      configurePartner(app)
    })
    this.app.configure(app => {
      configureStaff(app)
    })
    this.app.configure(app => {
      configureReference(app)
    })

    this.app.configure(app => {
      configureGeo(app)
    })
    this.app.configure(app => {
      configureBgLocation(app)
    })
    this.app.configure(app => {
      configureAHV2(app)
    })
    this.app.configure(app => {
      configureAnimalFeeds(app)
    })
    this.app.configure(app => {
      configureHealthScore(app)
    })
    this.app.configure(app => {
      configureAnimalClassificationHistory(app)
    })
    this.app.configure(app => {
      configureOpenCommerce(app)
    })
    this.app.configure(app => {
      configureFarmerUserForPpu(app)
    })
    this.app.configure(app => {
      loadInventoryService(app)
    })
    this.app.configure(app => {
      loadStaffFinanceServices(app)
    })
    this.app.configure(app => {
      loadKhataServices(app)
    })
    this.app.configure(app => {
      servicesV2(app)
    })
    this.app.configure(app => {
      servicesV3(app)
    })
    this.app.configure(app => {
      configureAsset(app)
    })
    this.app.configure(app => {
      configureAssetFeature(app)
    })
    this.app.configure(app => {
      configureSmartFeed(app)
    })

    this.app.configure(activityManagement)
    this.app.configure(app => {
      scheduler(app)
    })

    this.app.configure(app => {
      task_hirarchy(app)
    })
    this.app.configure(app => {
      task_hirarchy_config(app)
    })

    this.app.use("/cards/:subPath", new ChildCardsRO(), tokenInject)
    this.app.use("/card/:subPath", new CardService(), tokenInject)
    this.app.use("/filters", new ListFilter(), tokenInject)
    this.app.use("/tasks/animal", new AnimalTask(), tokenInject)
    this.app.use("/tasks/farmer", new FarmerTask(), tokenInject)
    this.app.use("/tasks/details", new TaskByID(), tokenInject)

    this.app.use("/animal-milking", new AnimalMilking(), tokenInject)
    this.app.use("/terms-and-condition", new CustomerTermsServices(), tokenInject)
    this.app.use("/medicine", new LibLoadMeds())
    this.app.use("/customers/flagged", new FlaggedCustomers(), tokenInject)
    this.app.configure(configureFarmerFinance)
    this.app.configure(geographyManagement)

    /* api for managing object storage */
    this.app.configure(objectManagement)
    this.app.configure(observationServicesV2)

    this.app.use("/inventory/requests/:type", new configureInventoryV2(), tokenInject)
    this.app.use("/v2/client/login-mobile-user", new LoginFromMobile(), tokenInject)

    this.app.use("/v2/client/animal-list", new AnimalFilterDetails(), tokenInject)

    this.app.use("/v2/client/animal-filters", new AnimalType(), tokenInject)
    this.app.use("/v2/client/tasks/farmer", new FarmerTaskV2(), tokenInject)
    this.app.use("/v2/client/tasks/animal", new AnimalTaskV2(), tokenInject)

    this.app.use("/v2/client/animal-milking", new AnimalMilkingDetails(), tokenInject)
    this.app.use("/v2/client/tasks/animal-history", new AnimalTaskHistoryV2(), tokenInject)

    this.app.use("/v2/client/tasks/get-all", new AllCattleTasksV2(), tokenInject)

    this.app.use("/v2/client/update/lat-long", new UpdateUserLatLong(), tokenInject)
    this.app.use("/v2/client/summaryDetails", new AnimalActivityDetailsSummary(), tokenInject)
    this.app.use("/v2/client/logout-mobile-user", new LogoutUserDevice(), tokenInject)
    this.app.use("/v2/client/logout-customer", new LogoutUserDeviceFromOffice(), tokenInject)
    this.app.configure(otp)
  }

  getServer() {
    this.#registerServices()
    const mainApp = express(feathers()).use("/api", this.app)
    this.app.use(express.errorHandler())

    return mainApp
  }
}

module.exports = Register
