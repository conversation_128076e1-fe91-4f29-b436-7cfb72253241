const { LibAsset } = require('@krushal-it/back-end-lib')
const { getDBConnections } = require('@krushal-it/ah-orm')

class Asset {
  async find (params) {
    try {
      console.log('Asset find called with params: ', params)
      const assetLib = new LibAsset()
      return assetLib.find(params)
    } catch (error) {
      return { return_code: -101, message: error.message }
    }
  }

  async create /* or is it update */(data, params) {
    try {
      console.log('Asset create called with data: ', data, ' and params: ', params)
      const result = await getDBConnections().main.manager.transaction(async (transactionalEntityManager) => {
        const assetLib = new LibAsset()
        return assetLib.create(data, params, transactionalEntityManager)
      })
      return result
    } catch (error) {
      return { return_code: -101, message: error.message }
    }
  }

  async get (id, params) {
    try {
      console.log('Asset get called with id: ', id, ' and params: ', params)
      const result = await getDBConnections().main.manager.transaction(async (transactionalEntityManager) => {
        const assetLib = new LibAsset()
        return assetLib.get(id, params, transactionalEntityManager)
      })
      return result
    } catch (error) {
      return { return_code: -101, message: error.message }
    }
  }

  async patch /* or is it update */(id, data, params) {
    try {
      console.log('Asset patch called with id: ', id, ' data: ', data, ' and params: ', params)
      const assetLib = new LibAsset()
      return assetLib.update(id, data, params)
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }

  async update /* or is it update */(id, data, params) {
    // PUT
    try {
      console.log('Asset update called with id: ', id, ' data: ', data, ' and params: ', params)
      const assetLib = new LibAsset()
      return assetLib.update(id, data, params)
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }

  async remove (id, params) {
    try {
      console.log('Asset remove called with id: ', id, ' and params: ', params)
      const assetLib = new LibAsset()
      return assetLib.remove(id, params)
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

module.exports = { Asset }
