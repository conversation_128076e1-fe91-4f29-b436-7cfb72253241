
const moment = require("moment");
const { postProcessRecords, dbConnections } = require("@krushal-it/ah-orm");
const { loadClassificationData, saveClassificationData } = require('@krushal-it/back-end-lib')
const { reportQueryClassificationJoinConfiguration, generateSelectJoinClauseForClassification, generateWhereClauseForReportInnerQuery: generateWhereClause, generateLimitStringForReportInnerQuery: generateLimitString, generateSortStringForReportInnerQuery: generateSortString, identifyMandatoryColumnsForReportQuery } = require('../../utils/query/query.helper')

/* class OnboardingAnimalsHealthScoreVerification {
  async create (data, params) {
    try {
      const returnValue = { return_code: 0 }
      const mainDBConnections = dbConnections().main
      // console.log('oc r SAR c 1, data = ', data)

      const limitString = generateLimitString(data)
      const sortString = generateSortString(data) 
      const whereClause = generateWhereClause(data)

      const shouldRestrictToPrimaryKeyArray = (data.primaryKeysForRemainingColumns !== undefined && data.primaryKeysForRemainingColumns !== null && Array.isArray(data.primaryKeysForRemainingColumns)) ? true : false
      const forceLoadAllColumns = data.forceLoadAllColumns
      const shouldRestrictSearch = (shouldRestrictToPrimaryKeyArray !== true && forceLoadAllColumns !== true) ? true : false
      let inAnimalIdString = ""
      if (shouldRestrictToPrimaryKeyArray) {
        for (const  animalId of data.primaryKeysForRemainingColumns) {
          if (inAnimalIdString !== "") {
            inAnimalIdString = inAnimalIdString + ", "
          }
          inAnimalIdString = inAnimalIdString + "'" + animalId + "'"
        }
      }
      const inAnimalIdWhereClause = (inAnimalIdString !== '') ? ` and a.animal_id in (${inAnimalIdString})` : ''

      const mandatoryColumnsForSearch = shouldRestrictSearch ? identifyMandatoryColumnsForReportQuery(data.filters, data.sorting, data.globalSearch, data.searchConfiguration) : undefined

      const excludeBDM = (data.excludeBDM === true) ? true : false

      const cowScoreAttributeCountColumnConfiguration = [
        'coalesce(a_a_c.cow_score_attribute_count, 0) cow_score_attribute_count,',
        ' \
          left join ( \
          select count(animal_classification_id) as cow_score_attribute_count, animal_id \
          from main.animal_classification ac \
          where ac.active = 1000100001  and ac.classifier_id in (2000000197, 2000000198, 2000000199, 2000000200, 2000000201, 2000000202, 2000000203, 2000000204, 2000000205, 2000000210, 2000000211, 2000000212, 2000000213, 2000000214, 2000000215) \
          group by ac.animal_id \
        ) a_a_c on a_a_c.animal_id = a.animal_id \
        '
      ]
      const cowScoreImageCountColumnConfiguration = [
        'coalesce(a_d_c.cow_score_image_count, 0) cow_score_image_count,',
        ' \
          left join ( \
            select count(document_id) as cow_score_image_count, animal_id \
            from ( \
              select ac.animal_id, d.* \
              from main.document d \
              inner join main.animal_classification ac on ac.animal_classification_id = d.entity_1_entity_uuid \
              where d.entity_1_type_id = 1000220004 and d.active = 1000100001 and d.document_type_id in (1000260016) \
              union \
              select d.entity_1_entity_uuid as animal_id, d.* \
              from main.document d \
              where d.active = 1000100001 and ( \
                d.entity_1_type_id in (1000220002, 1000220003) and d.document_type_id in (1000260002, 1000260016, 1000260001, 1000260012) \
              ) \
            ) a_d_c \
            group by a_d_c.animal_id \
          ) a_d_c on a_d_c.animal_id = a.animal_id \
        '
      ]
      const locationRecordedStatusColumnConfiguration = [
        "case when dfl_cc.value_json is not null then 'Location Recorded' else 'Location Not Recorded' end as farmer_location_recorded_status, dfl_cc.updated_at as location_updated_timestamp, ",
        ' \
          left join main.customer_classification dfl_cc on dfl_cc.active = 1000100001 and dfl_cc.classifier_id = 2000000004 and dfl_cc.customer_id = c.customer_id \
        '
      ]
      const snoozedStatusColumnConfiguration = [
        "case when sa.entity_relationship_id is not null or sf.entity_relationship_id is not null then 'Snoozed' else 'Not Snoozed' end as snooze_status,",
        ' \
          left join main.entity_relationship sa on sa.entity_relationship_type_id = 1000210014 and sa.entity_1_entity_uuid = a.animal_id \
            and sa.active = 1000100001 and sa.start_date < now() and sa.end_date > now() \
          left join main.entity_relationship sf on sf.entity_relationship_type_id = 1000210015 and sf.entity_1_entity_uuid = c.customer_id \
            and sf.active = 1000100001 and sf.start_date < now() and sf.end_date > now() \
        '
      ]
      const [selectClauseForClassification, joinClauseForClassification] = generateSelectJoinClauseForClassification(reportQueryClassificationJoinConfiguration, 'POTENTIALYSELLABLEANIMALSREPORT', undefined, mandatoryColumnsForSearch, ['body_score', 'udder_and_teat_score', 'animal_information_tracker_link'])
      const innerQuery = `
        select a.animal_id, a.active as animal_active_id,
          case when a.active = 1000100001 then 'Active' else 'Not Active' end as animal_active,
          ${(!shouldRestrictSearch || mandatoryColumnsForSearch.includes('cow_score_image_count')) ? cowScoreImageCountColumnConfiguration[0]: ''}
          ${(!shouldRestrictSearch || mandatoryColumnsForSearch.includes('cow_score_attribute_count')) ? cowScoreAttributeCountColumnConfiguration[0]: ''}
          c.customer_id, coalesce(c.customer_name_l10n->>'en', c.customer_name_l10n->>'ul') customer_name, c.mobile_number,
          rsv.village_id, coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') village_name,
          rsv.taluk_id, coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') taluk_name,
          ${!excludeBDM ? 's_g.bdm_uuid, s_g.bdm, s_g.bdm_mobile,' : ''}
          ${selectClauseForClassification !== undefined ? selectClauseForClassification + ', ': ''}
          ${(!shouldRestrictSearch || mandatoryColumnsForSearch.includes('snooze_status')) ? snoozedStatusColumnConfiguration[0]: ''}
          ${(!shouldRestrictSearch || mandatoryColumnsForSearch.includes('farmer_location_recorded_status') || mandatoryColumnsForSearch.includes('location_updated_timestamp')) ? locationRecordedStatusColumnConfiguration[0]: ''}

          case when (pm_ac.value_double > 0.5 and (EXTRACT(YEAR FROM age(now(), pm_ac.updated_at)) * 12 + EXTRACT(MONTH FROM age(now(), pm_ac.updated_at)) + pm_ac.value_double) < 9) then (EXTRACT(YEAR FROM age(now(), pm_ac.updated_at)) * 12 + EXTRACT(MONTH FROM age(now(), pm_ac.updated_at)) + pm_ac.value_double) else 0 end as number_of_months_pregnant
        from main.animal a
        inner join main.entity_relationship er on er.active = 1000100001 and er.entity_relationship_type_id = 1000210004
          and er.entity_2_entity_uuid = a.animal_id
        inner join main.customer c on c.active = 1000100001 and c.customer_id = er.entity_1_entity_uuid
        inner join main.customer_classification v_cc on v_cc.active = 1000100001 and c.customer_id = v_cc.customer_id
          and v_cc.classifier_id = 2000000055
        inner join main.ref_sdtv_view rsv on rsv.village_id = v_cc.value_reference_id
        ${(!shouldRestrictSearch || mandatoryColumnsForSearch.includes('farmer_location_recorded_status') || mandatoryColumnsForSearch.includes('location_updated_timestamp')) ? locationRecordedStatusColumnConfiguration[1]: ''}
        ${!excludeBDM ? 
          " \
            left join ( \
              select s.staff_id as bdm_uuid, coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') bdm, \
                s.mobile_number bdm_mobile, eg.geography_id \
              from main.entity_geography eg \
              inner join main.staff s on s.staff_id = eg.entity_uuid and s.active = 1000100001 \
              where eg.entity_type_id in (1000230001, 1000230005) and eg.active = 1000100001 \
                and eg.geography_type_id = 1000320004 \
            ) s_g on s_g.geography_id = rsv.village_id \
          "
          : ''}
        ${joinClauseForClassification}
        ${(!shouldRestrictSearch || mandatoryColumnsForSearch.includes('snooze_status')) ? snoozedStatusColumnConfiguration[1]: ''}
        ${(!shouldRestrictSearch || mandatoryColumnsForSearch.includes('cow_score_attribute_count')) ? cowScoreAttributeCountColumnConfiguration[1]: ''}
        ${(!shouldRestrictSearch || mandatoryColumnsForSearch.includes('cow_score_image_count')) ? cowScoreImageCountColumnConfiguration[1]: ''}
        left join main.animal_classification pm_ac on pm_ac.active = 1000100001 and pm_ac.classifier_id = 2000000067 and pm_ac.animal_id = a.animal_id
        where 1 = 1
        ${(shouldRestrictToPrimaryKeyArray) ? inAnimalIdWhereClause : ''}
        
      `
      let count
      if (!shouldRestrictToPrimaryKeyArray) {
        const countQuery = `
          select count(*) as count from (
            ${innerQuery}
          ) outerTable
          ${whereClause}
        `
        const reportResultCount = await mainDBConnections.manager.query(countQuery)
        count = parseInt(reportResultCount[0].count)
      }

      let query
      if (shouldRestrictToPrimaryKeyArray) {
        query = `
          select * from (
            ${innerQuery}
          ) outerTable
        `
      } else {
        query = `
          select * from (
            ${innerQuery}
          ) outerTable
          ${whereClause}
          ${sortString}
          ${limitString}
        `
      }
      const reportResultUnprocessed = await mainDBConnections.manager.query(query)
      const reportResult = postProcessRecords(undefined, reportResultUnprocessed, {})

      // ${whereClause}
      if (!shouldRestrictToPrimaryKeyArray) {
        const distinctVillageQuery = `
          select distinct village_id, village_name value, village_name text from (
            ${innerQuery}
          ) outerTable
        `
        const distinctVillageResultUnprocessed = await mainDBConnections.manager.query(distinctVillageQuery)
        const distinctVillageResult = postProcessRecords(undefined, distinctVillageResultUnprocessed, {})
        returnValue.village_name_filter_values = distinctVillageResult
      }

      if (!shouldRestrictToPrimaryKeyArray) {
        returnValue.count = count
      }
      if (!shouldRestrictToPrimaryKeyArray) {
        returnValue.report = reportResult
        if (shouldRestrictSearch) {
          const primaryKeysToBeQueried = []
          for (const reportRow of reportResult) {
            primaryKeysToBeQueried.push(reportRow['animal_id'])
          }
          returnValue.primaryKeysForRemainingColumns = primaryKeysToBeQueried
        }
      } else {
        const primaryKeyToRowMap = {}
        for (const reportRow of reportResult) {
          const {animal_id, ...remainingKeys } = reportRow
          primaryKeyToRowMap[reportRow['animal_id']] = remainingKeys
        }
        returnValue.rowsForPrimaryKeys = primaryKeyToRowMap
      }
      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async find(params) {
    try {
      const returnValue = {return_code: 0}    
      const tableFilterData = {}
      returnValue.tableFilters = tableFilterData
      const mainDBConnections = dbConnections().main
      const distinctTalukQuery = `
        select distinct taluk_id, taluk_id as value, coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') as text
        from main.customer_classification cc
        inner join main.ref_sdtv_view rsv on rsv.village_id = cc.value_reference_id
        where cc.active = 1000100001 and cc.classifier_id = 2000000055
      `
      const distinctTalukFilterValuesResult = await mainDBConnections.manager.query(distinctTalukQuery)
      const distinctTalukFilterValuesResult2 = postProcessRecords(undefined, distinctTalukFilterValuesResult, {})
      tableFilterData.taluk_name_filter_values = distinctTalukFilterValuesResult2

      const staffQuery = `
        select s.staff_id as value, coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as text
        from main.staff s
        where s.active = 1000100001 and s.staff_type_id in (1000230001, 1000230005)
      `
      const staffFilterValuesResult = await mainDBConnections.manager.query(staffQuery)
      const staffFilterValuesResult2 = postProcessRecords(undefined, staffFilterValuesResult, {})
      tableFilterData.staff_filter_values = staffFilterValuesResult2

      return returnValue;
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}
 */
class SnoozeAnimalForDataCollection {
  async create /*or is it update*/(data, params) {
    try {
      console.log('oc SFSA c 1, data = ', data)
      const entityRelationship = {
        start_date: new Date(),
        end_date: new Date(data.snooze_till_date)
      }
      if (data.farmer_level_snooze_choice[0] === 1000105001) {
        // snooze farmer
        entityRelationship.entity_relationship_type_id = 1000210015
        entityRelationship.entity_1_entity_uuid = data.customer_id
      } else {
        // snooze animal
        entityRelationship.entity_relationship_type_id = 1000210014
        entityRelationship.entity_1_entity_uuid = data.animal_id

      }
      const returnValue = { return_code: 0 }
      const mainDBConnection = dbConnections().main
      const saveResult = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
        // const entityRelationshipRepo = transactionalEntityManager.getRepository('entity_relationship')
        // const results = await entityRelationshipRepo.save(entityRelationship)
        const entityRelationshipTableEntity = mainDBConnection.entities['entity_relationship']
        const results = await transactionalEntityManager.createQueryBuilder().insert().into(entityRelationshipTableEntity).values(entityRelationship).execute()

        const noteTableEntity = mainDBConnection.entities['note']
        const commentObject = {
          note_type_id: 1000440013,
          entity_1_type_id: 1000220002,
          note_time: new Date(),
          note: {ul: data.snooze_comments},
          entity_1_uuid: data.animal_id
        }
        const notesInsertResults = await transactionalEntityManager.createQueryBuilder().insert().into(noteTableEntity).values(commentObject).execute()
      })
      returnValue.save_result = saveResult
      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class SnoozeAnimalForCustomerServiceTask {
  async create /*or is it update*/(data, params) {
    try {
      console.log('oc SFSA c 1, data = ', data)
      const entityRelationship = {
        start_date: new Date(),
        end_date: new Date(data.snooze_till_date)
      }
      if (data.farmer_level_snooze_choice[0] === 1000105001) {
        // snooze farmer
        entityRelationship.entity_relationship_type_id = 1000210015
        entityRelationship.entity_1_entity_uuid = data.customer_id
        entityRelationship.entity_2_entity_uuid = data.care_calendar_id
        entityRelationship.entity_1_type_id = 1000460001
      } else {
        // snooze animal
        entityRelationship.entity_relationship_type_id = 1000210014
        entityRelationship.entity_1_entity_uuid = data.animal_id
        entityRelationship.entity_2_entity_uuid = data.care_calendar_id
        entityRelationship.entity_1_type_id = 1000460004
      }
      const returnValue = { return_code: 0 }
      const mainDBConnection = dbConnections().main
      const saveResult = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
        // const entityRelationshipRepo = transactionalEntityManager.getRepository('entity_relationship')
        // const results = await entityRelationshipRepo.save(entityRelationship)
        const entityRelationshipTableEntity = mainDBConnection.entities['entity_relationship']
        const results = await transactionalEntityManager.createQueryBuilder().insert().into(entityRelationshipTableEntity).values(entityRelationship).execute()

        const noteTableEntity = mainDBConnection.entities['note']
        const commentObject = {
          note_type_id: 1000440016,
          entity_1_type_id: 1000220003,
          note_time: new Date(),
          note: {ul: data.snooze_comments},
          entity_1_uuid: data.care_calendar_id
        }
        const notesInsertResults = await transactionalEntityManager.createQueryBuilder().insert().into(noteTableEntity).values(commentObject).execute()
      })
      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class UnsnoozeAnimalForDataCollection {
  async create /*or is it update*/(data, params) {
    try {
      console.log('oc USFSA c 1, data = ', data)
      const animalIds = data.to_be_unsnoozed_animal_array
      let commaSeparatedAnimalIds = ''
      animalIds.forEach((animalId, index) => {
        if (commaSeparatedAnimalIds !== '') {
          commaSeparatedAnimalIds = commaSeparatedAnimalIds + ', '
        }
        commaSeparatedAnimalIds = commaSeparatedAnimalIds + "'" + animalId + "'"
      })

      const updateToStopSnoozeCustomerQuery = `
        update main.entity_relationship er
        set end_date = now()
        where er.entity_relationship_type_id = 1000210015
        and er.entity_1_entity_uuid in (
          select er.entity_1_entity_uuid
          from main.entity_relationship er
          where er.entity_relationship_type_id = 1000210004
          and er.entity_2_entity_uuid in (
            ${commaSeparatedAnimalIds}
          )
        )
      `
      console.log('oc USFSA c 2, updateToStopSnoozeCustomerQuery = ', updateToStopSnoozeCustomerQuery)

      const updateToStopSnoozeForAnimalsQuery = `
        update main.entity_relationship er
        set end_date = now()
        where er.entity_relationship_type_id = 1000210014
        and er.entity_1_entity_uuid in (
          ${commaSeparatedAnimalIds}
        )
      `
      console.log('oc USFSA c 3, updateToStopSnoozeForAnimalsQuery = ', updateToStopSnoozeForAnimalsQuery)

      const returnValue = { return_code: 0 }
      const mainDBConnection = dbConnections().main
      const saveResult = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
        console.log('oc USFSA c 4')
        const queryResult = await transactionalEntityManager.query(updateToStopSnoozeCustomerQuery)
        console.log('oc USFSA c 5, queryResult = ', queryResult)
        const queryResult2 = await transactionalEntityManager.query(updateToStopSnoozeForAnimalsQuery)
        console.log('oc USFSA c 6, queryResult2 = ', queryResult2)
      })
      returnValue.save_result = saveResult
      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class UpdateAnimalSellingStatus {
  async create /*or is it update*/(data, params) {
    try {
      const returnValue = {return_code: 0}
      console.log('oc UASS c 1, data = ', data)
      // create item for animal
      // copy item UBFSfication from animal
      // copy item UBFSument
      // create item listing linking to item
      const mainDBConnection = dbConnections().main
      const saveResult = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
        try {
          // const entityStatusRepo = transactionalEntityManager.getRepository('entity_status')
          const entityStatusTableEntity = mainDBConnection.entities['entity_status']
          const newAnimalStatus = {
            status_category_id: 10004900,
            status_id: data.selling_animal_next_status[0],
            entity_1_type_id: 1000220002,
            entity_1_entity_uuid: data.animal_id,
            start_date: new Date(),
            end_date: moment().add(1, 'M').toDate()
          }

          if (data.animal_information_tracker_link) {
            await saveClassificationData(transactionalEntityManager, 'ANIMAL_CLASSIFICATION', data.animal_id, {information_tracker_link: data.animal_information_tracker_link});
          }
          console.log('oc UASS c 2, newAnimalStatus = ', newAnimalStatus)
          const newlyCreatedEntityStatus = await transactionalEntityManager.createQueryBuilder().insert().into(entityStatusTableEntity).values(newAnimalStatus).execute()

          const noteTableEntity = mainDBConnection.entities['note']
          const commentObject = {
            note_type_id: 1000440009,
            entity_1_type_id: 1000220002,
            note_time: new Date(),
            note: {ul: data.update_status_comments},
            entity_1_uuid: data.animal_id
          }
          const notesInsertResults = await transactionalEntityManager.createQueryBuilder().insert().into(noteTableEntity).values(commentObject).execute()


          console.log('oc UASS c 3, newlyCreatedEntityStatus = ', newlyCreatedEntityStatus)
          const queryResult = await transactionalEntityManager.query(updateEntityStatusToAdjustEndTimeForEntity2AsNull)
          console.log('oc UASS c 4, newlyCreatedEntityStatusqueryResult = ', queryResult)
          const queryResult2 = await transactionalEntityManager.query(updateEntityStatusToAdjustEndTimeForEntity2AsNotNull)
          console.log('oc UASS c 4a, newlyCreatedEntityStatusqueryResult = ', queryResult2)
          
        } catch (error) {
          console.log('oc UASS c 20, error')
          console.log('oc UASS c 20a, error = ', error)
          throw error
        }
      })
      console.log('oc MAS c 1a, saveResult = ', saveResult)
      returnValue.save_result = saveResult
      return returnValue
    } catch (error) {
      console.log('oc MAS c 10, error')
      console.log('oc MAS c 10a, error = ', error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}


class GetAnimalDetails {
  async get(id, params) {
    try {
      const returnValue = {return_code: 0}
      console.log('oc r GAD g 1, id = ', id, ', params = ', params)
      const mainDBConnections = dbConnections().main
      const animalQuery = `
        select a.animal_id, et_ac.value_string_256 animal_ear_tag,
          concat(coalesce(b_rr.reference_name_l10n->>'en', b_rr.reference_name_l10n->>'ul'),'-',
            coalesce(at_rr.reference_name_l10n->>'en', at_rr.reference_name_l10n->>'ul')) animal_breed_and_type,
          c.customer_id,
          concat(coalesce(c.customer_name_l10n->>'en', c.customer_name_l10n->>'ul'), '-', c.mobile_number) customer_name_and_mobile,
          concat(gcc.district_name, '-', gcc.taluk_name, '-', gcc.village_name) district_taluk_village,
          concat(fbdm.bdm, '-', fbdm.bdm_mobile) bdm_and_mobile, concat(fbdm.paravet, '-', fbdm.paravet_mobile) paravet_and_mobile,
          ct_d.document_id as customer_thumbnail_document_id, at_d.document_id as animal_thumbnail_document_id,
          aet_d.document_id as animal_eartag_document_id,
          dfl_cc.value_json dairy_farm_location_position
        from main.animal a
        left join main.animal_classification et_ac on et_ac.active = 1000100001 and et_ac.animal_id = a.animal_id and et_ac.classifier_id = 2000000034
        left join main.animal_classification b_ac on b_ac.active = 1000100001 and b_ac.animal_id = a.animal_id and b_ac.classifier_id = 2000000036
        left join main.ref_reference b_rr on b_rr.reference_id = b_ac.value_reference_id
        left join main.animal_classification at_ac on at_ac.active = 1000100001 and at_ac.animal_id = a.animal_id and at_ac.classifier_id = 2000000035
        left join main.ref_reference at_rr on at_rr.reference_id = at_ac.value_reference_id
        inner join main.entity_relationship f2a on f2a.active = 1000100001 and f2a.entity_relationship_type_id = 1000210004 and f2a.entity_2_entity_uuid = a.animal_id
        inner join main.customer c on c.customer_id = f2a.entity_1_entity_uuid
        left join (
          select gcc.customer_id,
          coalesce(rsv.district_name_l10n->>'en', rsv.district_name_l10n->>'ul') as district_name,
          coalesce(rsv.taluk_name_l10n->>'en', rsv.taluk_name_l10n->>'ul') as taluk_name,
          coalesce(rsv.village_name_l10n->>'en', rsv.village_name_l10n->>'ul') as village_name,
            rsv.district_id, rsv.village_id, rsv.taluk_id
          from main.customer_classification gcc, main.ref_sdtv_view rsv
          where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
          and gcc.value_reference_id = rsv.village_id
        ) gcc on gcc.customer_id = c.customer_id
        left join (
          select gcc.customer_id, sg.bdm, sg.bdm_mobile, sg.bdm_uuid, pg.paravet, pg.paravet_mobile, pg.paravet_uuid
          from main.customer_classification gcc
          left join (
            select coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as bdm, s.mobile_number as bdm_mobile, sg.geography_id, s.staff_id bdm_uuid
            from main.staff s, (
              select eg.geography_id, (min(eg.entity_uuid::text))::uuid as staff_id
              from main.entity_geography eg
              where eg.active = 1000100001 and eg.geography_type_id = 1000320004
              and eg.entity_type_id in (1000230001, 1000230005)
              group by eg.geography_id
            ) sg
            where s.staff_id = sg.staff_id and s.active = 1000100001
          ) sg on sg.geography_id = gcc.value_reference_id
          left join (
            select coalesce(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as paravet, s.mobile_number as paravet_mobile, pg.geography_id, s.staff_id as paravet_uuid
            from main.staff s, (
              select eg.geography_id, (min(eg.entity_uuid::text))::uuid as staff_id
              from main.entity_geography eg
              where eg.active = 1000100001 and eg.geography_type_id = 1000320004
              and eg.entity_type_id in (1000230004)
              group by eg.geography_id
            ) pg
            where s.staff_id = pg.staff_id and s.active = 1000100001
          ) pg on pg.geography_id = gcc.value_reference_id
          where gcc.classifier_id = 2000000055 and gcc.active = 1000100001
        ) fbdm on fbdm.customer_id = c.customer_id
        left join main.customer_classification dfl_cc on dfl_cc.active = 1000100001
          and dfl_cc.customer_id = c.customer_id and dfl_cc.classifier_id = 2000000004
        left join (
          select d.document_id, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id,
            coalesce(dt_rr.reference_name_l10n->>'en', dt_rr.reference_name_l10n->>'ul') as document_type,
            d.document_information,
            d.created_at document_upload_time
          from main.document d
          inner join main.ref_reference dt_rr on dt_rr.reference_id = d.document_type_id
          left join (
            select max(d.document_id::text) max_created_at_document_id, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
            from main.document d
            inner join (
              select max(d.created_at) max_created_at, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
              from main.document d
              where 1 = 1 and d.active = 1000100001
              group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id 
            ) mcad on d.entity_1_type_id = mcad.entity_1_type_id and d.entity_1_entity_uuid = mcad.entity_1_entity_uuid
              and d.document_type_id = mcad.document_type_id and d.created_at = mcad.max_created_at
            where 1 = 1  and d.active = 1000100001
            group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
          ) mcadid on d.document_id::text = mcadid.max_created_at_document_id
        ) ct_d on ct_d.entity_1_type_id = 1000220001 and ct_d.entity_1_entity_uuid = c.customer_id and ct_d.document_type_id = 1000260003
        left join (
          select d.document_id, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id,
            coalesce(dt_rr.reference_name_l10n->>'en', dt_rr.reference_name_l10n->>'ul') as document_type,
            d.document_information,
            d.created_at document_upload_time
          from main.document d
          inner join main.ref_reference dt_rr on dt_rr.reference_id = d.document_type_id
          left join (
            select max(d.document_id::text) max_created_at_document_id, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
            from main.document d
            inner join (
              select max(d.created_at) max_created_at, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
              from main.document d
              where 1 = 1 and d.active = 1000100001
              group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id 
            ) mcad on d.entity_1_type_id = mcad.entity_1_type_id and d.entity_1_entity_uuid = mcad.entity_1_entity_uuid
              and d.document_type_id = mcad.document_type_id and d.created_at = mcad.max_created_at
            where 1 = 1  and d.active = 1000100001
            group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
          ) mcadid on d.document_id::text = mcadid.max_created_at_document_id
        ) at_d on at_d.entity_1_type_id = 1000220002 and at_d.entity_1_entity_uuid = a.animal_id and at_d.document_type_id = 1000260001
        left join (
          select d.document_id, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id,
            coalesce(dt_rr.reference_name_l10n->>'en', dt_rr.reference_name_l10n->>'ul') as document_type,
            d.document_information,
            d.created_at document_upload_time
          from main.document d
          inner join main.ref_reference dt_rr on dt_rr.reference_id = d.document_type_id
          left join (
            select max(d.document_id::text) max_created_at_document_id, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
            from main.document d
            inner join (
              select max(d.created_at) max_created_at, d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
              from main.document d
              where 1 = 1 and d.active = 1000100001
              group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id 
            ) mcad on d.entity_1_type_id = mcad.entity_1_type_id and d.entity_1_entity_uuid = mcad.entity_1_entity_uuid
              and d.document_type_id = mcad.document_type_id and d.created_at = mcad.max_created_at
            where 1 = 1  and d.active = 1000100001
            group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
          ) mcadid on d.document_id::text = mcadid.max_created_at_document_id
        ) aet_d on aet_d.entity_1_type_id = 1000220002 and aet_d.entity_1_entity_uuid = a.animal_id and aet_d.document_type_id = 1000260012
        where a.animal_id = '${id}'
      `
      console.log('oc r GAD g 2, animalQuery = ', animalQuery)
      const animalBasicDetailsUnProcessedResult = await mainDBConnections.manager.query(animalQuery)
      const animalBasicDetailsResult = postProcessRecords(undefined, animalBasicDetailsUnProcessedResult, {})
      console.log('oc r GAD g 3, animalBasicDetailsResult = ', animalBasicDetailsResult)
      returnValue.result = animalBasicDetailsResult[0]

      return returnValue
    } catch (error) {
      console.log(error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async create (data, params) {
    try {
      console.log('oc r GAD c 1, data = ', data)
      const returnValue = {return_code: 0}
      const classificationData = await loadClassificationData('ANIMAL_CLASSIFICATION', data.animalId, data.classifierArray)
      console.log('oc r GAD c 2, classificationData = ', classificationData)
      returnValue.data = classificationData
      return returnValue
    } catch (error) {
      console.log('oc r GAD c 10, error')
      console.log('oc r GAD c 10a, error = ', error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class SetAnimalDetails {
  async create /*or is it update*/(data, params) {
    try {
      console.log('oc r SFD c 1')
      const returnValue = {return_code: 0}
      const mainDBConnection = dbConnections().main
      const saveResult = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
        try {
          const updateClassificationDataResult = await saveClassificationData(transactionalEntityManager, 'ANIMAL_CLASSIFICATION', data.animalId, data.classificationData);
          returnValue.classificationDataUpdateResult = updateClassificationDataResult
        } catch (error) {
          console.log('oc UASS c 20, error')
          console.log('oc UASS c 20a, error = ', error)
          throw error
        }
      })
      return returnValue
    } catch (error) {
      console.log('oc r SFD c 10, error')
      console.log('oc r SFD c 10a, error = ', error)
      if (error.name === "KrushalError") {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

module.exports = {
  SnoozeAnimalForDataCollection, SnoozeAnimalForCustomerServiceTask, UnsnoozeAnimalForDataCollection, UpdateAnimalSellingStatus, 
  GetAnimalDetails, SetAnimalDetails
}