const {
  SnoozeAnimalForDataCollection,
  SnoozeAnimalForCustomerServiceTask,
  UnsnoozeAnimalForDataCollection
} = require('./ahv2report.class.js')
const tokenInject = require('../../middleware/tokenInject.js')

const configureAHV2 = (app) => {
  app.use('/oc/snooze-animal-for-data-collection', new SnoozeAnimalForDataCollection(), tokenInject)
  app.use('/oc/unsnooze-animal-for-data-collection', new UnsnoozeAnimalForDataCollection(), tokenInject)
  app.use('/oc/snooze-animal-for-customer-service-task', new SnoozeAnimalForCustomerServiceTask(), tokenInject)
  app.use('/oc/unsnooze-animal-for-data-collection', new UnsnoozeAnimalForDataCollection(), tokenInject)
  // app.use('/report/set-client-delta', new SetClientData())
}
module.exports = { configureAHV2 }
